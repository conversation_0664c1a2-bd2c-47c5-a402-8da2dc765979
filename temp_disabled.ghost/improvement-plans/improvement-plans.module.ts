import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImprovementPlansService } from './improvement-plans.service';
import { ImprovementPlansController } from './improvement-plans.controller';
import { 
  ImprovementPlan, 
  ImprovementPlanGoal, 
  ImprovementPlanReview 
} from './entities/improvement-plan.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ImprovementPlan, ImprovementPlanGoal, ImprovementPlanReview])],
  controllers: [ImprovementPlansController],
  providers: [ImprovementPlansService],
  exports: [ImprovementPlansService],
})
export class ImprovementPlansModule {}
