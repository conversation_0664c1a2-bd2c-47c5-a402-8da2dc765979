import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON><PERSON>er,
  CardContent,
  Box,
  Typography,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Assessment as SkillIcon,
  TrendingUp as ImprovingIcon,
  Warning as CriticalIcon,
  Info as ModerateIcon,
  CheckCircle as MinorIcon,
  Lightbulb as OpportunityIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface SkillGapAnalysisWidgetProps {
  filters: any;
}

interface SkillGap {
  skill: string;
  frequency: number;
  percentage: number;
  severity: 'critical' | 'moderate' | 'minor';
}

interface ImprovementOpportunity {
  type: string;
  title: string;
  description: string;
  impact: string;
  effort: string;
  timeline: string;
}

const SkillGapAnalysisWidget: React.FC<SkillGapAnalysisWidgetProps> = ({ filters }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [skillGapData, setSkillGapData] = useState<any>(null);
  const [viewMode, setViewMode] = useState('overview');

  useEffect(() => {
    loadSkillGapData();
  }, [filters]);

  const loadSkillGapData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ApiService.get('/ai/competency/skill-gap-analysis', {
        params: {
          teamId: filters.team !== 'all' ? filters.team : undefined,
          role: filters.department !== 'all' ? filters.department : undefined,
        },
      });

      if (response.data) {
        setSkillGapData(response.data);
      }
    } catch (err) {
      console.error('Error loading skill gap data:', err);
      setError('Failed to load skill gap analysis');
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'moderate':
        return 'warning';
      case 'minor':
        return 'info';
      default:
        return 'default';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <CriticalIcon color="error" />;
      case 'moderate':
        return <ModerateIcon color="warning" />;
      case 'minor':
        return <MinorIcon color="info" />;
      default:
        return <SkillIcon />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader title="Skill Gap Analysis" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Skill Gap Analysis"
        subheader="AI-powered analysis of competency gaps and development opportunities"
        action={
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>View</InputLabel>
            <Select
              value={viewMode}
              label="View"
              onChange={(e) => setViewMode(e.target.value)}
            >
              <MenuItem value="overview">Overview</MenuItem>
              <MenuItem value="detailed">Detailed</MenuItem>
              <MenuItem value="opportunities">Opportunities</MenuItem>
            </Select>
          </FormControl>
        }
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Overview Statistics */}
        {skillGapData && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {skillGapData.totalSkillsAssessed || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Skills Assessed
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="error">
                  {skillGapData.criticalGaps || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Critical Gaps
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {skillGapData.moderateGaps || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Moderate Gaps
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" color="info.main">
                  {skillGapData.minorGaps || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Minor Gaps
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        )}

        {/* Overview Mode */}
        {viewMode === 'overview' && skillGapData && (
          <>
            {/* Top Skill Gaps */}
            <Paper sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Top Skill Gaps
              </Typography>
              <Grid container spacing={2}>
                {skillGapData.topSkillGaps?.slice(0, 6).map((gap: SkillGap, index: number) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Box
                      sx={{
                        p: 2,
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        height: '100%',
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        {getSeverityIcon(gap.severity)}
                        <Typography variant="subtitle2" noWrap>
                          {gap.skill}
                        </Typography>
                      </Box>
                      <Typography variant="h6" gutterBottom>
                        {gap.frequency} employees
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={gap.percentage}
                        color={getSeverityColor(gap.severity) as any}
                        sx={{ mb: 1 }}
                      />
                      <Typography variant="body2" color="text.secondary">
                        {gap.percentage.toFixed(1)}% of workforce
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Paper>

            {/* Department Breakdown */}
            {skillGapData.departmentBreakdown && (
              <Paper sx={{ p: 2, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Gaps by Department
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Department</TableCell>
                        <TableCell align="right">Total Gaps</TableCell>
                        <TableCell align="right">Critical</TableCell>
                        <TableCell align="right">Moderate</TableCell>
                        <TableCell align="right">Minor</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {skillGapData.departmentBreakdown.map((dept: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell component="th" scope="row">
                            {dept.department}
                          </TableCell>
                          <TableCell align="right">{dept.total}</TableCell>
                          <TableCell align="right">
                            <Chip
                              label={dept.critical}
                              color="error"
                              size="small"
                              variant={dept.critical > 0 ? 'filled' : 'outlined'}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={dept.moderate}
                              color="warning"
                              size="small"
                              variant={dept.moderate > 0 ? 'filled' : 'outlined'}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={dept.minor}
                              color="info"
                              size="small"
                              variant={dept.minor > 0 ? 'filled' : 'outlined'}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            )}
          </>
        )}

        {/* Detailed Mode */}
        {viewMode === 'detailed' && skillGapData?.skillGapDetails && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Detailed Skill Gap Analysis
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Employee</TableCell>
                    <TableCell>Department</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell>Skill</TableCell>
                    <TableCell align="right">Current Level</TableCell>
                    <TableCell align="right">Required Level</TableCell>
                    <TableCell align="right">Gap Size</TableCell>
                    <TableCell align="center">Severity</TableCell>
                    <TableCell align="center">Priority</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {skillGapData.skillGapDetails.map((gap: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell>{gap.userName}</TableCell>
                      <TableCell>{gap.department}</TableCell>
                      <TableCell>{gap.role}</TableCell>
                      <TableCell>{gap.competency}</TableCell>
                      <TableCell align="right">{gap.currentLevel}</TableCell>
                      <TableCell align="right">{gap.requiredLevel}</TableCell>
                      <TableCell align="right">{gap.gapSize}</TableCell>
                      <TableCell align="center">
                        <Chip
                          label={gap.severity}
                          color={getSeverityColor(gap.severity) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={gap.priority}
                          color={gap.priority === 'urgent' ? 'error' : gap.priority === 'high' ? 'warning' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}

        {/* Opportunities Mode */}
        {viewMode === 'opportunities' && skillGapData?.improvementOpportunities && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Improvement Opportunities
            </Typography>
            <Grid container spacing={2}>
              {skillGapData.improvementOpportunities.map((opportunity: ImprovementOpportunity, index: number) => (
                <Grid item xs={12} md={6} key={index}>
                  <Paper
                    sx={{
                      p: 2,
                      border: 1,
                      borderColor: 'divider',
                      height: '100%',
                    }}
                  >
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <OpportunityIcon color="primary" />
                      <Typography variant="subtitle1">{opportunity.title}</Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {opportunity.description}
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap">
                      <Chip
                        label={`Impact: ${opportunity.impact}`}
                        color={opportunity.impact === 'high' ? 'success' : opportunity.impact === 'medium' ? 'warning' : 'default'}
                        size="small"
                      />
                      <Chip
                        label={`Effort: ${opportunity.effort}`}
                        color={opportunity.effort === 'low' ? 'success' : opportunity.effort === 'medium' ? 'warning' : 'error'}
                        size="small"
                      />
                      <Chip
                        label={opportunity.timeline}
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Paper>
        )}

        {/* Action Buttons */}
        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<SkillIcon />}
            onClick={() => {/* Navigate to skill development */}}
          >
            Create Development Plan
          </Button>
          <Button
            variant="outlined"
            onClick={() => {/* Export report */}}
          >
            Export Report
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default SkillGapAnalysisWidget;
