import React, { useState, useEffect } from 'react';
import {
  User,
  Assessment,
  AssessmentStatus,
  UserDashboard
} from '../../types';
import { Line, Bar } from 'react-chartjs-2';
import api from '../../services/api';

interface EmployeeDashboardProps {
  userId: number;
}

const EmployeeDashboard: React.FC<EmployeeDashboardProps> = ({ userId }) => {
  const [dashboardData, setDashboardData] = useState<UserDashboard | null>(null);
  const [assessmentHistory, setAssessmentHistory] = useState<Assessment[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // In a real implementation, these would be API calls
        // For now, we'll mock this data

        // Mock user dashboard data
        setDashboardData({
          pendingAssessments: 1,
          completedAssessments: 8,
          latestAssessment: {
            id: 123,
            templateId: 1,
            templateName: 'Quarterly Performance Review',
            employeeId: userId,
            employeeName: 'John Doe',
            evaluatorId: 5,
            evaluatorName: 'Sarah Manager',
            status: AssessmentStatus.COMPLETED,
            assessmentDate: '2025-03-15',
            notes: 'Great overall performance this quarter with significant improvements in communication.',
            totalScore: 87.5,
            responses: [
              {
                id: 501,
                areaId: 1,
                areaName: 'Communication',
                score: 92,
                evaluatorComments: 'Excellent communication skills demonstrated in client meetings.',
                areaWeight: 0.25,
                weightedScore: 23
              },
              {
                id: 502,
                areaId: 2,
                areaName: 'Technical Skills',
                score: 85,
                evaluatorComments: 'Strong technical performance, completed all assigned tasks.',
                areaWeight: 0.3,
                weightedScore: 25.5
              },
              {
                id: 503,
                areaId: 3,
                areaName: 'Teamwork',
                score: 90,
                evaluatorComments: 'Very collaborative, helped new team members adjust.',
                areaWeight: 0.2,
                weightedScore: 18
              },
              {
                id: 504,
                areaId: 4,
                areaName: 'Innovation',
                score: 78,
                evaluatorComments: 'Good ideas contributed, could be more proactive with suggestions.',
                areaWeight: 0.15,
                weightedScore: 11.7
              },
              {
                id: 505,
                areaId: 5,
                areaName: 'Leadership',
                score: 82,
                evaluatorComments: 'Showing growth in leading small group tasks.',
                areaWeight: 0.1,
                weightedScore: 8.2
              }
            ]
          },
          strengths: [
            { areaId: 1, areaName: 'Communication', averageScore: 90.5 },
            { areaId: 3, areaName: 'Teamwork', averageScore: 88.7 }
          ],
          weaknesses: [
            { areaId: 4, areaName: 'Innovation', averageScore: 76.3 },
            { areaId: 5, areaName: 'Leadership', averageScore: 80.1 }
          ]
        });

        // Mock assessment history
        const mockAssessmentHistory = [];
        const startDate = new Date('2024-01-01');

        for (let i = 0; i < 8; i++) {
          const assessmentDate = new Date(startDate);
          assessmentDate.setMonth(startDate.getMonth() + i);

          mockAssessmentHistory.push({
            id: 100 + i,
            templateId: 1,
            templateName: i % 3 === 0 ? 'Quarterly Review' : 'Monthly Check-in',
            employeeId: userId,
            employeeName: 'John Doe',
            evaluatorId: 5,
            status: AssessmentStatus.COMPLETED,
            assessmentDate: assessmentDate.toISOString().split('T')[0],
            totalScore: Math.floor(Math.random() * 15) + 75, // Random score between 75-90
            responses: []
          });
        }

        setAssessmentHistory(mockAssessmentHistory);
        setError(null);
      } catch (err) {
        console.error('Error fetching employee dashboard data:', err);
        setError('Failed to load your dashboard data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [userId]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading your dashboard...</p>
      </div>
    </div>;
  }

  if (error) {
    return <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <p>{error}</p>
      <button
        className="mt-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
        onClick={() => window.location.reload()}
      >
        Retry
      </button>
    </div>;
  }

  if (!dashboardData) {
    return <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
      <p>No dashboard data available.</p>
    </div>;
  }

  // Prepare chart data for assessment history
  const sortedHistory = [...assessmentHistory].sort((a, b) =>
    new Date(a.assessmentDate).getTime() - new Date(b.assessmentDate).getTime()
  );

  const historyLabels = sortedHistory.map(assessment => {
    const date = new Date(assessment.assessmentDate);
    return `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
  });

  const historyData = {
    labels: historyLabels,
    datasets: [
      {
        label: 'Score',
        data: sortedHistory.map(assessment => assessment.totalScore),
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      }
    ]
  };

  // Prepare chart data for last assessment scores by area
  const lastAssessmentData = {
    labels: dashboardData.latestAssessment?.responses.map(r => r.areaName) || [],
    datasets: [
      {
        label: 'Score',
        data: dashboardData.latestAssessment?.responses.map(r => r.score) || [],
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
      }
    ]
  };

  return (
    <div className="p-6">
      {/* Top stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Latest Score</h3>
          <p className="text-3xl font-bold text-blue-600">{dashboardData.latestAssessment?.totalScore?.toFixed(1) || 'N/A'}</p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Completed Assessments</h3>
          <p className="text-3xl font-bold text-green-600">{dashboardData.completedAssessments}</p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Pending Assessments</h3>
          <p className="text-3xl font-bold text-orange-500">{dashboardData.pendingAssessments}</p>
        </div>
      </div>

      {/* Latest assessment overview */}
      {dashboardData.latestAssessment && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Latest Assessment</h3>
          <div className="mb-4">
            <p><strong>Assessment:</strong> {dashboardData.latestAssessment.templateName}</p>
            <p><strong>Date:</strong> {new Date(dashboardData.latestAssessment.assessmentDate).toLocaleDateString()}</p>
            <p><strong>Evaluator:</strong> {dashboardData.latestAssessment.evaluatorName}</p>
            <p><strong>Total Score:</strong> {dashboardData.latestAssessment.totalScore?.toFixed(1) || 'N/A'}</p>
          </div>

          {dashboardData.latestAssessment.notes && (
            <div className="mb-4">
              <h4 className="font-medium text-gray-700">Notes</h4>
              <p className="text-gray-600 italic">{dashboardData.latestAssessment.notes}</p>
            </div>
          )}

          <div className="h-64">
            <Bar
              data={lastAssessmentData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    min: 0,
                    max: 100
                  }
                }
              }}
            />
          </div>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Assessment History */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Performance History</h3>
          <div className="h-64">
            <Line
              data={historyData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    min: 0,
                    max: 100
                  }
                }
              }}
            />
          </div>
        </div>

        {/* Strengths & Areas for Improvement */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Strengths & Areas for Improvement</h3>

          <div className="mb-4">
            <h4 className="font-medium text-green-700 mb-2">Strengths</h4>
            <ul className="list-disc pl-5">
              {dashboardData.strengths.map(strength => (
                <li key={strength.areaId} className="mb-1">
                  <span className="font-medium">{strength.areaName}:</span> {strength.averageScore.toFixed(1)}
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-orange-700 mb-2">Areas for Improvement</h4>
            <ul className="list-disc pl-5">
              {dashboardData.weaknesses.map(weakness => (
                <li key={weakness.areaId} className="mb-1">
                  <span className="font-medium">{weakness.areaName}:</span> {weakness.averageScore.toFixed(1)}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Action Items */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">Action Items</h3>

        {/* Mock action items */}
        <ul className="divide-y divide-gray-200">
          <li className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Complete Advanced Communication Training</h4>
                <p className="text-sm text-gray-500">Due: July 15, 2025</p>
              </div>
              <span className="inline-flex px-2 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                In Progress
              </span>
            </div>
          </li>

          <li className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Schedule Innovation Workshop with Team</h4>
                <p className="text-sm text-gray-500">Due: August 1, 2025</p>
              </div>
              <span className="inline-flex px-2 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                Pending
              </span>
            </div>
          </li>

          <li className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Submit Project Leadership Report</h4>
                <p className="text-sm text-gray-500">Due: July 30, 2025</p>
              </div>
              <span className="inline-flex px-2 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                Pending
              </span>
            </div>
          </li>

          <li className="py-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Complete React Certification Course</h4>
                <p className="text-sm text-gray-500">Due: June 30, 2025</p>
              </div>
              <span className="inline-flex px-2 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                Completed
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default EmployeeDashboard;
