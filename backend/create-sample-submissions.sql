-- <PERSON>reate sample monthly dashboard submissions for the last 12 months

-- First, let's get the team and KPI IDs
SET @frontend_team_id = 53;
SET @backend_team_id = 54;
SET @devops_team_id = 55;
SET @manager_user_id = 5; -- <EMAIL>

-- Create submissions for Frontend Development team (last 12 months)
INSERT INTO monthly_dashboard_submissions (
    organizational_unit_id, submitted_by_user_id, submission_month, submission_year,
    submission_date, completion_date, status, notes, created_at, updated_at
) VALUES
-- 2024 submissions
(@frontend_team_id, @manager_user_id, 12, 2024, '2024-12-15 10:00:00', '2024-12-14 16:30:00', 'submitted', 'December 2024 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 11, 2024, '2024-11-15 10:00:00', '2024-11-14 16:30:00', 'submitted', 'November 2024 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 10, 2024, '2024-10-15 10:00:00', '2024-10-14 16:30:00', 'submitted', 'October 2024 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 9, 2024, '2024-09-15 10:00:00', '2024-09-14 16:30:00', 'submitted', 'September 2024 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 8, 2024, '2024-08-15 10:00:00', '2024-08-14 16:30:00', 'submitted', 'August 2024 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 7, 2024, '2024-07-15 10:00:00', '2024-07-14 16:30:00', 'submitted', 'July 2024 submission', NOW(), NOW()),
-- 2025 submissions
(@frontend_team_id, @manager_user_id, 6, 2025, '2025-06-15 10:00:00', '2025-06-14 16:30:00', 'submitted', 'June 2025 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 5, 2025, '2025-05-15 10:00:00', '2025-05-14 16:30:00', 'submitted', 'May 2025 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 4, 2025, '2025-04-15 10:00:00', '2025-04-14 16:30:00', 'submitted', 'April 2025 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 3, 2025, '2025-03-15 10:00:00', '2025-03-14 16:30:00', 'submitted', 'March 2025 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 2, 2025, '2025-02-15 10:00:00', '2025-02-14 16:30:00', 'submitted', 'February 2025 submission', NOW(), NOW()),
(@frontend_team_id, @manager_user_id, 1, 2025, '2025-01-15 10:00:00', '2025-01-14 16:30:00', 'submitted', 'January 2025 submission', NOW(), NOW());

-- Create submissions for Backend Development team
INSERT INTO monthly_dashboard_submissions (
    organizational_unit_id, submitted_by_user_id, submission_month, submission_year,
    submission_date, completion_date, status, notes, created_at, updated_at
) VALUES
-- 2024 submissions
(@backend_team_id, 6, 12, 2024, '2024-12-16 14:00:00', '2024-12-16 14:00:00', 'submitted', 'December 2024 submission', NOW(), NOW()),
(@backend_team_id, 6, 11, 2024, '2024-11-16 14:00:00', '2024-11-16 14:00:00', 'submitted', 'November 2024 submission', NOW(), NOW()),
(@backend_team_id, 6, 10, 2024, '2024-10-16 14:00:00', '2024-10-16 14:00:00', 'submitted', 'October 2024 submission', NOW(), NOW()),
(@backend_team_id, 6, 9, 2024, '2024-09-16 14:00:00', '2024-09-16 14:00:00', 'submitted', 'September 2024 submission', NOW(), NOW()),
(@backend_team_id, 6, 8, 2024, '2024-08-16 14:00:00', '2024-08-16 14:00:00', 'submitted', 'August 2024 submission', NOW(), NOW()),
(@backend_team_id, 6, 7, 2024, '2024-07-16 14:00:00', '2024-07-16 14:00:00', 'submitted', 'July 2024 submission', NOW(), NOW()),
-- 2025 submissions
(@backend_team_id, 6, 6, 2025, '2025-06-16 14:00:00', '2025-06-16 14:00:00', 'submitted', 'June 2025 submission', NOW(), NOW()),
(@backend_team_id, 6, 5, 2025, '2025-05-16 14:00:00', '2025-05-16 14:00:00', 'submitted', 'May 2025 submission', NOW(), NOW()),
(@backend_team_id, 6, 4, 2025, '2025-04-16 14:00:00', '2025-04-16 14:00:00', 'submitted', 'April 2025 submission', NOW(), NOW()),
(@backend_team_id, 6, 3, 2025, '2025-03-16 14:00:00', '2025-03-16 14:00:00', 'submitted', 'March 2025 submission', NOW(), NOW()),
(@backend_team_id, 6, 2, 2025, '2025-02-16 14:00:00', '2025-02-16 14:00:00', 'submitted', 'February 2025 submission', NOW(), NOW()),
(@backend_team_id, 6, 1, 2025, '2025-01-16 14:00:00', '2025-01-16 14:00:00', 'submitted', 'January 2025 submission', NOW(), NOW());

-- Create submissions for DevOps team
INSERT INTO monthly_dashboard_submissions (
    organizational_unit_id, submitted_by_user_id, submission_month, submission_year,
    submission_date, completion_date, status, notes, created_at, updated_at
) VALUES
-- 2024 submissions
(@devops_team_id, 9, 12, 2024, '2024-12-14 09:00:00', '2024-12-13 17:00:00', 'submitted', 'December 2024 submission', NOW(), NOW()),
(@devops_team_id, 9, 11, 2024, '2024-11-14 09:00:00', '2024-11-13 17:00:00', 'submitted', 'November 2024 submission', NOW(), NOW()),
(@devops_team_id, 9, 10, 2024, '2024-10-14 09:00:00', '2024-10-13 17:00:00', 'submitted', 'October 2024 submission', NOW(), NOW()),
(@devops_team_id, 9, 9, 2024, '2024-09-14 09:00:00', '2024-09-13 17:00:00', 'submitted', 'September 2024 submission', NOW(), NOW()),
(@devops_team_id, 9, 8, 2024, '2024-08-14 09:00:00', '2024-08-13 17:00:00', 'submitted', 'August 2024 submission', NOW(), NOW()),
(@devops_team_id, 9, 7, 2024, '2024-07-14 09:00:00', '2024-07-13 17:00:00', 'submitted', 'July 2024 submission', NOW(), NOW()),
-- 2025 submissions
(@devops_team_id, 9, 6, 2025, '2025-06-14 09:00:00', '2025-06-13 17:00:00', 'submitted', 'June 2025 submission', NOW(), NOW()),
(@devops_team_id, 9, 5, 2025, '2025-05-14 09:00:00', '2025-05-13 17:00:00', 'submitted', 'May 2025 submission', NOW(), NOW()),
(@devops_team_id, 9, 4, 2025, '2025-04-14 09:00:00', '2025-04-13 17:00:00', 'submitted', 'April 2025 submission', NOW(), NOW()),
(@devops_team_id, 9, 3, 2025, '2025-03-14 09:00:00', '2025-03-13 17:00:00', 'submitted', 'March 2025 submission', NOW(), NOW()),
(@devops_team_id, 9, 2, 2025, '2025-02-14 09:00:00', '2025-02-13 17:00:00', 'submitted', 'February 2025 submission', NOW(), NOW()),
(@devops_team_id, 9, 1, 2025, '2025-01-14 09:00:00', '2025-01-13 17:00:00', 'submitted', 'January 2025 submission', NOW(), NOW());

-- Now create sample KPI values for July 2025 (current month)
-- Get the submission IDs for July 2025
SET @july_frontend_submission = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @frontend_team_id AND submission_month = 7 AND submission_year = 2025 LIMIT 1);
SET @july_backend_submission = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @backend_team_id AND submission_month = 7 AND submission_year = 2025 LIMIT 1);
SET @july_devops_submission = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @devops_team_id AND submission_month = 7 AND submission_year = 2025 LIMIT 1);

-- Get KPI IDs
SET @fte_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'FTE' LIMIT 1);
SET @attrition_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'ATTRITION' LIMIT 1);
SET @sla_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'SLA' LIMIT 1);
SET @utilization_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION' LIMIT 1);
SET @time_reg_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'TIME_REGISTRATION' LIMIT 1);
SET @compliance_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'COMPLIANCE' LIMIT 1);
SET @ab_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'AB' LIMIT 1);
SET @pto_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO' LIMIT 1);
SET @rto_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO' LIMIT 1);

-- Frontend Development KPI values (July 2025)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(@july_frontend_submission, @fte_kpi, 12, 12, 'green', NULL, NOW(), NOW()),
(@july_frontend_submission, @attrition_kpi, 4.2, 5.0, 'green', '{"resignations_this_month": 0, "annual_rate": 4.2}', NOW(), NOW()),
(@july_frontend_submission, @sla_kpi, 98.5, 100.0, 'green', NULL, NOW(), NOW()),
(@july_frontend_submission, @utilization_kpi, 87.3, 85.0, 'green', NULL, NOW(), NOW()),
(@july_frontend_submission, @time_reg_kpi, 95.8, 100.0, 'green', NULL, NOW(), NOW()),
(@july_frontend_submission, @compliance_kpi, 0, 0, 'green', NULL, NOW(), NOW()),
(@july_frontend_submission, @ab_kpi, 15000, 0, 'green', NULL, NOW(), NOW()),
(@july_frontend_submission, @pto_kpi, 78.2, 75.0, 'green', '{"days_taken_this_month": 8, "ytd_percentage": 78.2}', NOW(), NOW()),
(@july_frontend_submission, @rto_kpi, 92.1, 100.0, 'yellow', '{"raw_attendance_percentage": 88.1, "leave_days_adjustment": 4, "adjusted_percentage": 92.1}', NOW(), NOW());

-- Backend Development KPI values (July 2025)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(@july_backend_submission, @fte_kpi, 8, 8, 'green', NULL, NOW(), NOW()),
(@july_backend_submission, @attrition_kpi, 8.1, 5.0, 'yellow', '{"resignations_this_month": 1, "annual_rate": 8.1}', NOW(), NOW()),
(@july_backend_submission, @sla_kpi, 87.2, 100.0, 'red', NULL, NOW(), NOW()),
(@july_backend_submission, @utilization_kpi, 82.7, 85.0, 'green', NULL, NOW(), NOW()),
(@july_backend_submission, @time_reg_kpi, 89.3, 100.0, 'yellow', NULL, NOW(), NOW()),
(@july_backend_submission, @compliance_kpi, 1, 0, 'red', NULL, NOW(), NOW()),
(@july_backend_submission, @ab_kpi, -8500, 0, 'yellow', NULL, NOW(), NOW()),
(@july_backend_submission, @pto_kpi, 68.9, 75.0, 'yellow', '{"days_taken_this_month": 5, "ytd_percentage": 68.9}', NOW(), NOW()),
(@july_backend_submission, @rto_kpi, 85.4, 100.0, 'red', '{"raw_attendance_percentage": 81.4, "leave_days_adjustment": 4, "adjusted_percentage": 85.4}', NOW(), NOW());

-- DevOps Team KPI values (July 2025)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(@july_devops_submission, @fte_kpi, 6, 6, 'green', NULL, NOW(), NOW()),
(@july_devops_submission, @attrition_kpi, 3.8, 5.0, 'green', '{"resignations_this_month": 0, "annual_rate": 3.8}', NOW(), NOW()),
(@july_devops_submission, @sla_kpi, 99.2, 100.0, 'green', NULL, NOW(), NOW()),
(@july_devops_submission, @utilization_kpi, 91.5, 85.0, 'green', NULL, NOW(), NOW()),
(@july_devops_submission, @time_reg_kpi, 97.1, 100.0, 'green', NULL, NOW(), NOW()),
(@july_devops_submission, @compliance_kpi, 0, 0, 'green', NULL, NOW(), NOW()),
(@july_devops_submission, @ab_kpi, 22000, 0, 'green', NULL, NOW(), NOW()),
(@july_devops_submission, @pto_kpi, 72.4, 75.0, 'green', '{"days_taken_this_month": 4, "ytd_percentage": 72.4}', NOW(), NOW()),
(@july_devops_submission, @rto_kpi, 96.8, 100.0, 'green', '{"raw_attendance_percentage": 94.8, "leave_days_adjustment": 2, "adjusted_percentage": 96.8}', NOW(), NOW());
