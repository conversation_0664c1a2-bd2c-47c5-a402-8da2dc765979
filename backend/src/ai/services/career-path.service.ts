import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CareerPath } from '../entities/career-path.entity';
import { User } from '../../users/entities/user.entity';
import { AssessmentInstance } from '../../assessments/entities/assessment-instance.entity';
import { PerformanceMetric } from '../../analytics/entities/performance-metric.entity';

@Injectable()
export class CareerPathService {
  constructor(
    @InjectRepository(CareerPath)
    private readonly careerPathRepository: Repository<CareerPath>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AssessmentInstance)
    private readonly assessmentInstanceRepository: Repository<AssessmentInstance>,
    @InjectRepository(PerformanceMetric)
    private readonly performanceMetricRepository: Repository<PerformanceMetric>,
  ) { }

  /**
   * Get career paths with filtering
   */
  async getCareerPaths(fromRole?: string, toRole?: string, orgUnitId?: number) {
    try {
      const queryBuilder = this.careerPathRepository.createQueryBuilder('path')
        .where('path.isActive = :isActive', { isActive: true });

      if (fromRole) {
        queryBuilder.andWhere('path.fromRole = :fromRole', { fromRole });
      }

      if (toRole) {
        queryBuilder.andWhere('path.toRole = :toRole', { toRole });
      }

      if (orgUnitId) {
        queryBuilder.andWhere('path.organizationalUnitId = :orgUnitId', { orgUnitId });
      }

      queryBuilder.orderBy('path.createdAt', 'DESC');

      const careerPaths = await queryBuilder.getMany();

      return {
        success: true,
        data: careerPaths.map(path => ({
          id: path.id,
          fromRole: path.fromRole,
          toRole: path.toRole,
          requirements: path.requiredSkills,
          timeline: path.typicalTimelineMonths,
          difficulty: path.successRate,
          orgUnitId: path.organizationalUnitId,
          isActive: path.isActive,
          createdAt: path.createdAt,
        })),
      };
    } catch (error) {
      console.error('Error getting career paths:', error);
      return {
        success: false,
        error: 'Failed to get career paths',
      };
    }
  }

  /**
   * Get career recommendations for a specific user
   */
  async getCareerRecommendations(userId: number) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['orgUnit'],
      });

      if (!user) {
        return {
          success: false,
          error: 'User not found',
        };
      }

      // Get user's current performance and skills
      const userProfile = await this.buildUserCareerProfile(user);

      // Get available career paths from user's current role
      const availablePaths = await this.careerPathRepository.find({
        where: {
          fromRole: user.role,
          isActive: true,
        },
      });

      // Analyze and score each path for the user
      const recommendations = [];
      for (const path of availablePaths) {
        const recommendation = await this.analyzeCareerPathFit(user, userProfile, path);
        recommendations.push(recommendation);
      }

      // Sort by fit score
      recommendations.sort((a, b) => b.fitScore - a.fitScore);

      // Generate development plan for top recommendations
      const topRecommendations = recommendations.slice(0, 5);
      for (const rec of topRecommendations) {
        rec.developmentPlan = await this.generateDevelopmentPlan(user, userProfile, rec.path);
      }

      return {
        success: true,
        data: {
          userId,
          currentRole: user.role,
          currentDepartment: user.organizationalUnit?.name,
          recommendations: topRecommendations,
          totalPathsAnalyzed: availablePaths.length,
          generatedAt: new Date(),
        },
      };
    } catch (error) {
      console.error('Error getting career recommendations:', error);
      return {
        success: false,
        error: 'Failed to get career recommendations',
      };
    }
  }

  /**
   * Create a new career path
   */
  async createCareerPath(careerPathData: any, userId: number) {
    try {
      const careerPath = this.careerPathRepository.create({
        fromRole: careerPathData.fromRole,
        toRole: careerPathData.toRole,
        requiredSkills: careerPathData.requirements,
        typicalTimelineMonths: careerPathData.timeline,
        successRate: careerPathData.difficulty,
        organizationalUnitId: careerPathData.orgUnitId,
        createdById: userId,
        isActive: true,
      });

      const savedPath = await this.careerPathRepository.save(careerPath);

      return {
        success: true,
        data: savedPath,
      };
    } catch (error) {
      console.error('Error creating career path:', error);
      return {
        success: false,
        error: 'Failed to create career path',
      };
    }
  }

  /**
   * Build comprehensive user career profile
   */
  private async buildUserCareerProfile(user: User): Promise<any> {
    // Get user's assessment scores
    const assessments = await this.assessmentInstanceRepository.find({
      where: { employeeId: user.id, status: 'completed' },
      relations: ['template'],
      order: { updatedAt: 'DESC' },
    });

    // Get user's performance metrics
    const performanceMetrics = await this.performanceMetricRepository.find({
      where: { userId: user.id },
      order: { calculationDate: 'DESC' },
      take: 12, // Last 12 months
    });

    // Calculate tenure
    const tenure = this.calculateTenure(user.createdAt);

    // Build skill profile
    const skillProfile = new Map();
    assessments.forEach(assessment => {
      const skillName = assessment.template?.name || 'General';
      const score = assessment.totalScore || 0;
      skillProfile.set(skillName, score);
    });

    // Calculate performance trend
    const performanceTrend = this.calculatePerformanceTrend(performanceMetrics);

    // Calculate leadership potential
    const leadershipPotential = this.calculateLeadershipPotential(user, assessments, performanceMetrics);

    return {
      userId: user.id,
      currentRole: user.role,
      department: user.organizationalUnit?.name,
      tenure,
      skillProfile,
      performanceTrend,
      leadershipPotential,
      averagePerformance: performanceMetrics.length > 0
        ? performanceMetrics.reduce((sum, m) => sum + m.metricValue, 0) / performanceMetrics.length
        : 70,
      assessmentCount: assessments.length,
      lastAssessmentDate: assessments.length > 0 ? assessments[0].updatedAt : null,
    };
  }

  /**
   * Analyze career path fit for a user
   */
  private async analyzeCareerPathFit(user: User, userProfile: any, path: CareerPath): Promise<any> {
    let fitScore = 0;
    const fitFactors = [];
    const gaps = [];

    // Analyze requirements
    const requirements = path.requiredSkills as any;
    if (requirements && typeof requirements === 'object') {

      // Check experience requirements
      if (requirements.minExperience) {
        const hasExperience = userProfile.tenure >= requirements.minExperience;
        fitScore += hasExperience ? 20 : 0;
        fitFactors.push({
          factor: 'Experience',
          required: `${requirements.minExperience} months`,
          current: `${userProfile.tenure} months`,
          met: hasExperience,
          weight: 20,
        });

        if (!hasExperience) {
          gaps.push({
            type: 'experience',
            description: `Need ${requirements.minExperience - userProfile.tenure} more months of experience`,
            priority: 'high',
          });
        }
      }

      // Check performance requirements
      if (requirements.minPerformance) {
        const hasPerformance = userProfile.averagePerformance >= requirements.minPerformance;
        fitScore += hasPerformance ? 25 : (userProfile.averagePerformance / requirements.minPerformance) * 25;
        fitFactors.push({
          factor: 'Performance',
          required: requirements.minPerformance,
          current: userProfile.averagePerformance,
          met: hasPerformance,
          weight: 25,
        });

        if (!hasPerformance) {
          gaps.push({
            type: 'performance',
            description: `Need to improve performance from ${userProfile.averagePerformance} to ${requirements.minPerformance}`,
            priority: 'high',
          });
        }
      }

      // Check skill requirements
      if (requirements.skills && Array.isArray(requirements.skills)) {
        let skillScore = 0;
        const skillGaps = [];

        requirements.skills.forEach(skill => {
          const userSkillLevel = userProfile.skillProfile.get(skill.name) || 0;
          const requiredLevel = skill.level || 80;

          if (userSkillLevel >= requiredLevel) {
            skillScore += skill.weight || 10;
          } else {
            skillScore += (userSkillLevel / requiredLevel) * (skill.weight || 10);
            skillGaps.push({
              skill: skill.name,
              required: requiredLevel,
              current: userSkillLevel,
              gap: requiredLevel - userSkillLevel,
            });
          }
        });

        fitScore += Math.min(30, skillScore);
        fitFactors.push({
          factor: 'Skills',
          details: requirements.skills.map(skill => ({
            skill: skill.name,
            required: skill.level || 80,
            current: userProfile.skillProfile.get(skill.name) || 0,
            met: (userProfile.skillProfile.get(skill.name) || 0) >= (skill.level || 80),
          })),
          weight: 30,
        });

        gaps.push(...skillGaps.map(gap => ({
          type: 'skill',
          description: `Need to improve ${gap.skill} from ${gap.current} to ${gap.required}`,
          priority: gap.gap > 30 ? 'high' : 'medium',
        })));
      }

      // Check leadership requirements
      if (requirements.leadership && path.toRole.toLowerCase().includes('lead') || path.toRole.toLowerCase().includes('manager')) {
        const hasLeadership = userProfile.leadershipPotential >= (requirements.leadership || 70);
        fitScore += hasLeadership ? 15 : (userProfile.leadershipPotential / (requirements.leadership || 70)) * 15;
        fitFactors.push({
          factor: 'Leadership',
          required: requirements.leadership || 70,
          current: userProfile.leadershipPotential,
          met: hasLeadership,
          weight: 15,
        });

        if (!hasLeadership) {
          gaps.push({
            type: 'leadership',
            description: `Need to develop leadership skills from ${userProfile.leadershipPotential} to ${requirements.leadership || 70}`,
            priority: 'medium',
          });
        }
      }
    }

    // Calculate readiness timeline
    const readinessTimeline = this.calculateReadinessTimeline(gaps, path.typicalTimelineMonths?.toString() || '12');

    return {
      path,
      fitScore: Math.min(100, fitScore),
      readiness: this.calculateReadinessLevel(fitScore),
      fitFactors,
      gaps,
      readinessTimeline,
      recommendation: this.generatePathRecommendation(fitScore, gaps),
    };
  }

  /**
   * Generate development plan for a career path
   */
  private async generateDevelopmentPlan(user: User, userProfile: any, path: CareerPath): Promise<any> {
    const plan = {
      pathId: path.id,
      targetRole: path.toRole,
      estimatedTimeline: path.typicalTimelineMonths,
      phases: [],
      milestones: [],
      resources: [],
    };

    // Generate phases based on gaps
    const requirements = path.requiredSkills as any;
    if (requirements) {

      // Phase 1: Foundation building (0-6 months)
      const foundationPhase = {
        phase: 1,
        name: 'Foundation Building',
        duration: '0-6 months',
        objectives: [],
        actions: [],
      };

      // Phase 2: Skill development (6-18 months)
      const skillPhase = {
        phase: 2,
        name: 'Skill Development',
        duration: '6-18 months',
        objectives: [],
        actions: [],
      };

      // Phase 3: Leadership preparation (18-24 months)
      const leadershipPhase = {
        phase: 3,
        name: 'Leadership Preparation',
        duration: '18-24 months',
        objectives: [],
        actions: [],
      };

      // Add specific objectives based on requirements
      if (requirements.skills) {
        requirements.skills.forEach(skill => {
          const currentLevel = userProfile.skillProfile.get(skill.name) || 0;
          const requiredLevel = skill.level || 80;

          if (currentLevel < requiredLevel) {
            skillPhase.objectives.push(`Develop ${skill.name} from ${currentLevel} to ${requiredLevel}`);
            skillPhase.actions.push(`Complete ${skill.name} training program`);
            skillPhase.actions.push(`Practice ${skill.name} in current role`);
          }
        });
      }

      plan.phases = [foundationPhase, skillPhase, leadershipPhase];
    }

    return plan;
  }

  /**
   * Calculate tenure in months
   */
  private calculateTenure(startDate: Date): number {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30));
  }

  /**
   * Calculate performance trend
   */
  private calculatePerformanceTrend(metrics: PerformanceMetric[]): string {
    if (metrics.length < 2) return 'stable';

    const recent = metrics.slice(0, 3).reduce((sum, m) => sum + m.metricValue, 0) / 3;
    const older = metrics.slice(-3).reduce((sum, m) => sum + m.metricValue, 0) / 3;

    if (recent > older + 5) return 'improving';
    if (recent < older - 5) return 'declining';
    return 'stable';
  }

  /**
   * Calculate leadership potential
   */
  private calculateLeadershipPotential(user: User, assessments: AssessmentInstance[], metrics: PerformanceMetric[]): number {
    let potential = 50; // Base score

    // Factor in performance consistency
    if (metrics.length > 0) {
      const avgPerformance = metrics.reduce((sum, m) => sum + m.metricValue, 0) / metrics.length;
      potential += (avgPerformance - 70) * 0.5;
    }

    // Factor in assessment scores
    const leadershipAssessments = assessments.filter(a =>
      a.template?.name?.toLowerCase().includes('leadership') ||
      a.template?.name?.toLowerCase().includes('management')
    );

    if (leadershipAssessments.length > 0) {
      const avgLeadershipScore = leadershipAssessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / leadershipAssessments.length;
      potential = (potential + avgLeadershipScore) / 2;
    }

    return Math.min(100, Math.max(0, potential));
  }

  /**
   * Calculate readiness level
   */
  private calculateReadinessLevel(fitScore: number): string {
    if (fitScore >= 80) return 'ready';
    if (fitScore >= 60) return 'nearly-ready';
    if (fitScore >= 40) return 'developing';
    return 'early-stage';
  }

  /**
   * Calculate readiness timeline
   */
  private calculateReadinessTimeline(gaps: any[], pathTimeline: string): string {
    const highPriorityGaps = gaps.filter(g => g.priority === 'high').length;
    const mediumPriorityGaps = gaps.filter(g => g.priority === 'medium').length;

    const totalGapMonths = (highPriorityGaps * 6) + (mediumPriorityGaps * 3);

    if (totalGapMonths <= 6) return '3-6 months';
    if (totalGapMonths <= 12) return '6-12 months';
    if (totalGapMonths <= 24) return '1-2 years';
    return '2+ years';
  }

  /**
   * Generate path recommendation
   */
  private generatePathRecommendation(fitScore: number, gaps: any[]): string {
    if (fitScore >= 80) {
      return 'Excellent fit! You are ready to pursue this career path.';
    }
    if (fitScore >= 60) {
      return 'Good fit with some development needed. Focus on addressing key gaps.';
    }
    if (fitScore >= 40) {
      return 'Potential fit with significant development required. Consider a longer-term plan.';
    }
    return 'This path requires substantial development. Consider alternative paths or extended preparation.';
  }
}
