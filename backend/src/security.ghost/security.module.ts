import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { SecurityMiddleware } from './middleware/security.middleware';
import { RateLimitingService } from './services/rate-limiting.service';
import { SecurityAuditService } from './services/security-audit.service';

/**
 * 🔐 NIS2-Compliant Security Module
 * 
 * Implements enterprise-grade security measures including:
 * - Rate limiting and DDoS protection
 * - Security headers (HSTS, CSP, etc.)
 * - Audit logging for security events
 * - Input validation and sanitization
 * - Session security management
 */
@Module({
  imports: [
    // 🔐 NIS2 Requirement: Rate limiting for authentication endpoints
    ThrottlerModule.forRoot({
      ttl: 60, // 1 minute
      limit: 10, // 10 requests per minute
    }),
  ],
  providers: [
    // 🔐 Global rate limiting guard
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    RateLimitingService,
    SecurityAuditService,
  ],
  exports: [
    RateLimitingService,
    SecurityAuditService,
  ],
})
export class SecurityModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // 🔐 Apply security middleware to all routes
    consumer
      .apply(SecurityMiddleware)
      .forRoutes('*');
  }
}
