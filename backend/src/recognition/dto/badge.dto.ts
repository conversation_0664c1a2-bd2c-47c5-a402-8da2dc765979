import { IsString, <PERSON>Optional, <PERSON>E<PERSON>, IsNumber, IsObject, IsBoolean } from 'class-validator';
import { BadgeType } from '../entities/recognition-badge.entity';

export class CreateBadgeDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  iconUrl?: string;

  @IsEnum(BadgeType)
  badgeType: BadgeType;

  @IsOptional()
  @IsNumber()
  pointValue?: number;

  @IsOptional()
  @IsObject()
  criteria?: object;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateBadgeDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  iconUrl?: string;

  @IsOptional()
  @IsEnum(BadgeType)
  badgeType?: BadgeType;

  @IsOptional()
  @IsNumber()
  pointValue?: number;

  @IsOptional()
  @IsObject()
  criteria?: object;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
