import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { UpdateScoringRuleDto } from './update-scoring-rule.dto';

export class UpdateAssessmentAreaDto {
  @IsOptional()
  id?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(10.0)
  weight?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxScore?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  orderIndex?: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateScoringRuleDto)
  scoringRules?: UpdateScoringRuleDto[];
}
