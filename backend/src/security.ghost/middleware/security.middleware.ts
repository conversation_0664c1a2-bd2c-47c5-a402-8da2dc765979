import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * 🔐 NIS2-Compliant Security Middleware
 * 
 * Implements security headers and protections required by NIS2:
 * - HTTPS enforcement (HSTS)
 * - Content Security Policy (CSP)
 * - XSS protection
 * - CSRF protection
 * - Clickjacking protection
 * - Content type sniffing protection
 */
@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 🔐 NIS2 Requirement: Enforce HTTPS in production
    if (process.env.NODE_ENV === 'production') {
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }

    // 🔐 Content Security Policy - Prevent XSS attacks
    res.setHeader(
      'Content-Security-Policy',
      [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Note: In production, remove unsafe-inline and unsafe-eval
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "font-src 'self' https://fonts.gstatic.com",
        "img-src 'self' data: https:",
        "connect-src 'self'",
        "frame-ancestors 'none'",
        "base-uri 'self'",
        "form-action 'self'"
      ].join('; ')
    );

    // 🔐 XSS Protection
    res.setHeader('X-XSS-Protection', '1; mode=block');

    // 🔐 Prevent content type sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // 🔐 Clickjacking protection
    res.setHeader('X-Frame-Options', 'DENY');

    // 🔐 Referrer policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

    // 🔐 Permissions policy
    res.setHeader(
      'Permissions-Policy',
      'camera=(), microphone=(), geolocation=(), payment=()'
    );

    // 🔐 Remove server information
    res.removeHeader('X-Powered-By');

    // 🔐 CORS headers for API security
    if (req.path.startsWith('/api')) {
      res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3080');
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    }

    // 🔐 Security audit logging for sensitive endpoints
    if (this.isSensitiveEndpoint(req.path)) {
      console.log('🔐 [SECURITY-AUDIT]', {
        timestamp: new Date().toISOString(),
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
      });
    }

    next();
  }

  private isSensitiveEndpoint(path: string): boolean {
    const sensitivePatterns = [
      '/api/auth/',
      '/api/users/',
      '/api/database/',
      '/api/admin/',
    ];
    
    return sensitivePatterns.some(pattern => path.includes(pattern));
  }
}
