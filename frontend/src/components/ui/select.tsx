import React from 'react';
import { FormControl, Select as MuiSelect, MenuItem, SelectProps as MuiSelectProps, InputLabel, SelectChangeEvent } from '@mui/material';

interface SelectProps extends Omit<MuiSelectProps, 'children' | 'onChange' | 'variant'> {
  children: React.ReactNode;
  onValueChange?: (value: string) => void;
  onChange?: (event: SelectChangeEvent<unknown>, child: React.ReactNode) => void;
}

export const Select: React.FC<SelectProps> = ({ children, onValueChange, onChange, value, ...props }) => {
  const handleChange = (event: SelectChangeEvent<unknown>, child: React.ReactNode) => {
    if (onValueChange) {
      onValueChange(event.target.value as string);
    }
    if (onChange) {
      onChange(event, child);
    }
  };

  return (
    <FormControl fullWidth size="small">
      <MuiSelect
        {...props}
        variant="outlined"
        value={value || ''}
        onChange={handleChange}
        displayEmpty
      >
        {children}
      </MuiSelect>
    </FormControl>
  );
};

export const SelectContent: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <>{children}</>
);

export const SelectItem: React.FC<{ value: string; children: React.ReactNode }> = ({ value, children }) => (
  <MenuItem value={value}>{children}</MenuItem>
);

export const SelectTrigger: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <div className={className}>{children}</div>
);

export const SelectValue: React.FC<{ placeholder?: string }> = ({ placeholder }) => (
  <span>{placeholder}</span>
);
