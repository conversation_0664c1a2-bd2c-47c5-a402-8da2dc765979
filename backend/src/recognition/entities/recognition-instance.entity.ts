import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { RecognitionBadge } from './recognition-badge.entity';

@Entity('recognition_instances')
export class RecognitionInstance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'badge_id', nullable: true })
  badgeId: number;

  @Column({ name: 'giver_id' })
  giverId: number;

  @Column({ name: 'receiver_id' })
  receiverId: number;

  @Column({ type: 'text', nullable: true })
  message: string;

  @Column({ name: 'points_awarded', default: 0 })
  pointsAwarded: number;

  @Column({ name: 'is_public', default: true })
  isPublic: boolean;

  @CreateDateColumn({ name: 'given_at' })
  givenAt: Date;

  // Relations
  @ManyToOne(() => RecognitionBadge, badge => badge.recognitionInstances, { nullable: true })
  @JoinC<PERSON>umn({ name: 'badge_id' })
  badge: RecognitionBadge;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'giver_id' })
  giver: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'receiver_id' })
  receiver: User;
}
