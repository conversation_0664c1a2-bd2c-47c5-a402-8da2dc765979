import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>N<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class UpdateAssessmentResponseDto {
  @IsOptional()
  id?: number;

  @IsOptional()
  @IsNumber()
  areaId?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  score?: number;

  @IsOptional()
  @IsString()
  evaluatorComments?: string;

  @IsOptional()
  @IsString()
  employeeComments?: string;
}
