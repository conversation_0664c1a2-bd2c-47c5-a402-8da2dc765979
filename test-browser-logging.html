<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EHRX Logging System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #1565c0;
        }
        .error-button {
            background: #d32f2f;
        }
        .warning-button {
            background: #f57c00;
        }
        .info-button {
            background: #388e3c;
        }
        .results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #1976d2;
            background: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 EHRX Logging System Test</h1>
        <p>This page tests the EHRX logging system functionality. Open browser Developer Tools (F12) to see console output and network requests.</p>
        
        <h3>Test Controls</h3>
        <button class="test-button error-button" onclick="testJavaScriptError()">Test JS Error</button>
        <button class="test-button error-button" onclick="testPromiseRejection()">Test Promise Rejection</button>
        <button class="test-button warning-button" onclick="testManualWarning()">Test Manual Warning</button>
        <button class="test-button info-button" onclick="testManualInfo()">Test Manual Info</button>
        <button class="test-button" onclick="testUserAction()">Test User Action</button>
        <button class="test-button" onclick="testPerformance()">Test Performance</button>
        <button class="test-button" onclick="getSessionInfo()">Get Session Info</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        
        <div class="results" id="results">
            <div class="log-entry">Ready to test logging system...</div>
        </div>
    </div>

    <script>
        // Test results container
        const resultsContainer = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.style.borderLeftColor = type === 'error' ? '#d32f2f' : type === 'warning' ? '#f57c00' : '#1976d2';
            entry.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsContainer.appendChild(entry);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }
        
        // Test 1: JavaScript Error
        function testJavaScriptError() {
            addResult('Testing JavaScript error capture...', 'error');
            try {
                // This will cause a ReferenceError
                nonExistentFunction();
            } catch (error) {
                addResult('JavaScript error caught: ' + error.message, 'error');
                console.error('Test error:', error);
            }
        }
        
        // Test 2: Unhandled Promise Rejection
        function testPromiseRejection() {
            addResult('Testing unhandled promise rejection...', 'warning');
            Promise.reject(new Error('Test unhandled promise rejection'));
            addResult('Unhandled promise rejection triggered', 'warning');
        }
        
        // Test 3: Manual Warning
        function testManualWarning() {
            addResult('Testing manual warning log...', 'warning');
            console.warn('Test manual warning message', {
                testType: 'manual_warning',
                timestamp: new Date(),
                source: 'test-page'
            });
            addResult('Manual warning logged to console', 'warning');
        }
        
        // Test 4: Manual Info
        function testManualInfo() {
            addResult('Testing manual info log...', 'info');
            console.info('Test manual info message', {
                testType: 'manual_info',
                timestamp: new Date(),
                source: 'test-page'
            });
            addResult('Manual info logged to console', 'info');
        }
        
        // Test 5: User Action
        function testUserAction() {
            addResult('Testing user action logging...', 'info');
            console.log('User Action: button_click', {
                action: 'test_user_action',
                element: 'test-button',
                timestamp: new Date(),
                page: 'logging-test'
            });
            addResult('User action logged', 'info');
        }
        
        // Test 6: Performance Test
        function testPerformance() {
            addResult('Testing performance logging...', 'info');
            const start = performance.now();
            
            // Simulate some work
            for (let i = 0; i < 100000; i++) {
                Math.random();
            }
            
            const duration = performance.now() - start;
            console.log('Performance Test Completed', {
                operation: 'random_generation',
                duration: duration,
                iterations: 100000,
                timestamp: new Date()
            });
            addResult(`Performance test completed in ${duration.toFixed(2)}ms`, 'info');
        }
        
        // Test 7: Session Info
        function getSessionInfo() {
            addResult('Getting session information...', 'info');
            const sessionInfo = {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date(),
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                connection: navigator.connection ? {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink
                } : 'unknown'
            };
            console.log('Session Info:', sessionInfo);
            addResult('Session info logged to console', 'info');
        }
        
        // Clear results
        function clearResults() {
            resultsContainer.innerHTML = '<div class="log-entry">Results cleared...</div>';
        }
        
        // Global error handler
        window.addEventListener('error', function(event) {
            addResult(`Global error captured: ${event.message}`, 'error');
            console.error('Global error:', event);
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(event) {
            addResult(`Unhandled promise rejection: ${event.reason}`, 'error');
            console.error('Unhandled promise rejection:', event);
        });
        
        // Page load performance
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            addResult(`Page loaded in ${loadTime.toFixed(2)}ms`, 'info');
            console.log('Page Load Performance:', {
                loadTime: loadTime,
                timestamp: new Date(),
                page: 'logging-test'
            });
        });
        
        addResult('Logging test page initialized successfully', 'info');
    </script>
</body>
</html>
