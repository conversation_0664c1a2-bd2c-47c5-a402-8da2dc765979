# 🧪 EHRX Logging System Testing Guide

## Overview
This guide provides step-by-step instructions for testing the enterprise-grade logging system implemented in the EHRX application.

## 🌐 **Frontend Logging System Testing**

### **Step 1: Access the Test Dashboard**
1. Open https://dev.trusthansen.dk/
2. Look for "Test Logging" in the left sidebar menu (with bug icon)
3. Click on "Test Logging" to access the logging test dashboard

### **Step 2: Test JavaScript Error Capture**
1. Click the "Test JS Error" button
2. Open browser Developer Tools (F12)
3. Check the Console tab for error messages
4. Check the Network tab for POST requests to `/api/logs/browser-error`
5. Verify the error is captured and logged

### **Step 3: Test Unhandled Promise Rejection**
1. Click "Test Promise Rejection" button
2. Check browser console for unhandled promise rejection
3. Verify the error is automatically captured by the logging system

### **Step 4: Test React Error Boundary**
1. Click "Test React Error" button
2. This should trigger the Error Boundary component
3. You should see a user-friendly error page instead of a blank screen
4. Check that the error is logged to the backend

### **Step 5: Test Manual Logging**
1. Click "Log Manual Error" button
2. Click "Log Manual Warning" button  
3. Click "Log Manual Info" button
4. Check browser Network tab for API calls to `/api/logs/custom`
5. Verify each log level is properly sent

### **Step 6: Test User Action Logging**
1. Click "Log User Action" button
2. Check Network tab for user action logging
3. Verify user actions are tracked

### **Step 7: Test Session Information**
1. Click "Get Session Info" button
2. Check the test results area for session information
3. Verify session ID, user agent, and other details are captured

## 🔍 **Browser Console Testing**

### **Manual Console Testing**
Open browser console (F12) and run these commands:

```javascript
// Test 1: Check if logging service is available
console.log('Logging service available:', !!window.loggingService);

// Test 2: Manual error logging
if (window.loggingService) {
  window.loggingService.logError('Console test error', { 
    testType: 'console_test',
    timestamp: new Date() 
  }, 'console');
}

// Test 3: Manual info logging
if (window.loggingService) {
  window.loggingService.logInfo('Console test info', { 
    testType: 'console_test' 
  }, 'console');
}

// Test 4: Get session info
if (window.loggingService) {
  console.log('Session Info:', window.loggingService.getSessionInfo());
}

// Test 5: Trigger JavaScript error
nonExistentFunction(); // This will cause an error

// Test 6: Trigger promise rejection
Promise.reject(new Error('Console test promise rejection'));
```

## 📊 **Network Tab Verification**

### **Expected API Calls**
When testing, you should see these API calls in the Network tab:

1. **Browser Error Logging**
   - URL: `/api/logs/browser-error`
   - Method: POST
   - Content-Type: application/json

2. **Custom Log Entries**
   - URL: `/api/logs/custom`
   - Method: POST
   - Content-Type: application/json

3. **Log Health Check**
   - URL: `/api/logs/health`
   - Method: GET

### **Request Payload Examples**

**Browser Error:**
```json
{
  "message": "nonExistentFunction is not defined",
  "filename": "https://dev.trusthansen.dk/static/js/bundle.js",
  "lineno": 123,
  "colno": 45,
  "error": {
    "name": "ReferenceError",
    "message": "nonExistentFunction is not defined",
    "stack": "ReferenceError: nonExistentFunction is not defined..."
  },
  "userAgent": "Mozilla/5.0...",
  "url": "https://dev.trusthansen.dk/",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Custom Log Entry:**
```json
{
  "level": "error",
  "source": "frontend",
  "message": "Manual test error",
  "details": {
    "testType": "manual_error",
    "timestamp": "2024-01-15T10:30:00.000Z"
  },
  "component": "error-test-component"
}
```

## 🎯 **Component Testing**

### **Test Dashboard Navigation**
1. Click "Dashboard" in sidebar
2. Verify quicklink cards load
3. Check system status panel
4. Verify no console errors

### **Test Analytics Dashboard**
1. Click "Analytics" in sidebar
2. Verify monthly analytics load
3. Test month/year selection
4. Check for loading states
5. Verify no console errors

### **Test Organization Management**
1. Click "Organization" in sidebar
2. Verify organizational tree loads
3. Test team and employee views
4. Check edit functionality
5. Verify no console errors

### **Test Settings Page**
1. Click "Settings" in sidebar
2. Verify user management loads
3. Test tabbed interface
4. Check database views
5. Verify no console errors

## 🔧 **Performance Testing**

### **Page Load Performance**
1. Open browser Developer Tools
2. Go to Performance tab
3. Reload the page
4. Check for performance logs in console
5. Verify page load metrics are captured

### **API Performance**
1. Navigate between different pages
2. Check Network tab for API response times
3. Verify API performance is logged
4. Check for any slow requests

## 📝 **Expected Results**

### **Successful Test Indicators**
- ✅ No console errors during navigation
- ✅ Error Boundary displays on React errors
- ✅ Network requests to logging endpoints
- ✅ Session information is captured
- ✅ User actions are tracked
- ✅ Performance metrics are logged

### **Error Indicators**
- ❌ Console errors not being captured
- ❌ Network requests failing
- ❌ Error Boundary not displaying
- ❌ Session info not available
- ❌ Missing logging service

## 🚨 **Troubleshooting**

### **If Logging Service Not Available**
1. Check browser console for import errors
2. Verify logging service file exists
3. Check for TypeScript compilation errors
4. Restart frontend development server

### **If API Calls Failing**
1. Check if backend is running
2. Verify API endpoints are correct
3. Check CORS configuration
4. Check network connectivity

### **If Error Boundary Not Working**
1. Verify ErrorBoundary is imported
2. Check React version compatibility
3. Verify component hierarchy
4. Check for console errors

## 📊 **Success Metrics**

A successful test should demonstrate:
1. **Error Capture**: All JavaScript errors are caught and logged
2. **User Tracking**: User actions and sessions are tracked
3. **Performance Monitoring**: Page load and API performance is measured
4. **Error Recovery**: Error Boundary provides graceful error handling
5. **Offline Support**: Logs are queued when offline and sent when online

## 🎉 **Conclusion**

The EHRX logging system provides comprehensive error tracking, performance monitoring, and user activity logging. This testing guide ensures all components are working correctly and providing valuable insights into application behavior.
