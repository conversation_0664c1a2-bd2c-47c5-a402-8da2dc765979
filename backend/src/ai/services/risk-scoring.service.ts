import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { PerformanceMetric } from '../../analytics/entities/performance-metric.entity';
import { SurveyResponse } from '../../analytics/entities/survey-response.entity';

@Injectable()
export class RiskScoringService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PerformanceMetric)
    private readonly performanceMetricRepository: Repository<PerformanceMetric>,
    @InjectRepository(SurveyResponse)
    private readonly surveyResponseRepository: Repository<SurveyResponse>,
  ) { }

  /**
   * Advanced multi-dimensional risk scoring
   */
  async calculateMultiDimensionalRisk(userId: number) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['performanceMetrics', 'surveyResponses'],
      });

      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Calculate risk scores across multiple dimensions
      const performanceRisk = await this.calculatePerformanceRisk(userId);
      const engagementRisk = await this.calculateEngagementRisk(userId);
      const behavioralRisk = await this.calculateBehavioralRisk(userId);
      const careerRisk = await this.calculateCareerRisk(user);
      const teamRisk = await this.calculateTeamRisk(user);
      const organizationalRisk = await this.calculateOrganizationalRisk(user);

      // Advanced ensemble scoring with dynamic weights
      const weights = this.calculateDynamicWeights(user, {
        performance: performanceRisk,
        engagement: engagementRisk,
        behavioral: behavioralRisk,
        career: careerRisk,
        team: teamRisk,
        organizational: organizationalRisk,
      });

      const compositeScore = this.calculateCompositeScore({
        performance: performanceRisk,
        engagement: engagementRisk,
        behavioral: behavioralRisk,
        career: careerRisk,
        team: teamRisk,
        organizational: organizationalRisk,
      }, weights);

      // Risk level classification with confidence intervals
      const riskClassification = this.classifyRiskWithConfidence(compositeScore);

      // Generate risk factors and mitigation strategies
      const riskFactors = this.identifyRiskFactors({
        performance: performanceRisk,
        engagement: engagementRisk,
        behavioral: behavioralRisk,
        career: careerRisk,
        team: teamRisk,
        organizational: organizationalRisk,
      });

      const mitigationStrategies = this.generateMitigationStrategies(riskFactors, riskClassification);

      return {
        success: true,
        data: {
          userId,
          compositeScore,
          riskLevel: riskClassification.level,
          confidence: riskClassification.confidence,
          confidenceInterval: riskClassification.interval,
          dimensions: {
            performance: performanceRisk,
            engagement: engagementRisk,
            behavioral: behavioralRisk,
            career: careerRisk,
            team: teamRisk,
            organizational: organizationalRisk,
          },
          weights,
          riskFactors,
          mitigationStrategies,
          lastUpdated: new Date(),
        },
      };
    } catch (error) {
      console.error('Error calculating multi-dimensional risk:', error);
      return { success: false, error: 'Failed to calculate risk score' };
    }
  }

  /**
   * Calculate performance-based risk
   */
  private async calculatePerformanceRisk(userId: number) {
    const metrics = await this.performanceMetricRepository.find({
      where: { userId },
      order: { calculationDate: 'DESC' },
      take: 12,
    });

    if (metrics.length === 0) {
      return { score: 0.5, confidence: 0.1, factors: ['No performance data'] };
    }

    const scores = metrics.map(m => m.metricValue);
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    // Calculate trend
    const recentScores = scores.slice(0, 3);
    const olderScores = scores.slice(3, 6);
    const recentAvg = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length;
    const olderAvg = olderScores.length > 0 ?
      olderScores.reduce((sum, score) => sum + score, 0) / olderScores.length : recentAvg;

    const trend = recentAvg - olderAvg;

    // Calculate volatility
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / scores.length;
    const volatility = Math.sqrt(variance);

    // Risk calculation
    let riskScore = 0;
    const factors = [];

    if (average < 2.5) {
      riskScore += 0.4;
      factors.push('Low average performance');
    } else if (average < 3.5) {
      riskScore += 0.2;
      factors.push('Below average performance');
    }

    if (trend < -0.3) {
      riskScore += 0.3;
      factors.push('Declining performance trend');
    }

    if (volatility > 1.0) {
      riskScore += 0.2;
      factors.push('High performance volatility');
    }

    return {
      score: Math.min(1.0, riskScore),
      confidence: Math.min(1.0, metrics.length / 6),
      factors,
      metrics: {
        average,
        trend,
        volatility,
        dataPoints: metrics.length,
      },
    };
  }

  /**
   * Calculate engagement-based risk
   */
  private async calculateEngagementRisk(userId: number) {
    const responses = await this.surveyResponseRepository.find({
      where: { respondentId: userId },
      order: { submittedAt: 'DESC' },
      take: 6,
    });

    if (responses.length === 0) {
      return { score: 0.3, confidence: 0.1, factors: ['No engagement data'] };
    }

    const ratings = responses.map(r => (r.responses as any)?.overallRating || 3);
    const average = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;

    // Calculate engagement trend
    const recent = ratings.slice(0, 2);
    const older = ratings.slice(2, 4);
    const recentAvg = recent.reduce((sum, r) => sum + r, 0) / recent.length;
    const olderAvg = older.length > 0 ?
      older.reduce((sum, r) => sum + r, 0) / older.length : recentAvg;

    const trend = recentAvg - olderAvg;

    // Participation rate
    const participationRate = responses.length / 6; // Assuming 6 surveys sent

    let riskScore = 0;
    const factors = [];

    if (average < 2.5) {
      riskScore += 0.5;
      factors.push('Low engagement scores');
    } else if (average < 3.5) {
      riskScore += 0.25;
      factors.push('Below average engagement');
    }

    if (trend < -0.5) {
      riskScore += 0.3;
      factors.push('Declining engagement trend');
    }

    if (participationRate < 0.5) {
      riskScore += 0.2;
      factors.push('Low survey participation');
    }

    return {
      score: Math.min(1.0, riskScore),
      confidence: Math.min(1.0, responses.length / 3),
      factors,
      metrics: {
        average,
        trend,
        participationRate,
        dataPoints: responses.length,
      },
    };
  }

  /**
   * Calculate behavioral risk (simulated)
   */
  private async calculateBehavioralRisk(userId: number) {
    // In a real implementation, this would analyze:
    // - Login patterns
    // - System usage
    // - Communication patterns
    // - Collaboration metrics

    // Simulated behavioral analysis
    const loginFrequency = Math.random() * 0.5 + 0.5; // 0.5-1.0
    const systemUsage = Math.random() * 0.4 + 0.6; // 0.6-1.0
    const collaborationLevel = Math.random() * 0.3 + 0.7; // 0.7-1.0

    let riskScore = 0;
    const factors = [];

    if (loginFrequency < 0.6) {
      riskScore += 0.3;
      factors.push('Decreased login frequency');
    }

    if (systemUsage < 0.7) {
      riskScore += 0.2;
      factors.push('Reduced system usage');
    }

    if (collaborationLevel < 0.8) {
      riskScore += 0.2;
      factors.push('Lower collaboration activity');
    }

    return {
      score: Math.min(1.0, riskScore),
      confidence: 0.7, // Moderate confidence in behavioral data
      factors,
      metrics: {
        loginFrequency,
        systemUsage,
        collaborationLevel,
      },
    };
  }

  /**
   * Calculate career-related risk
   */
  private async calculateCareerRisk(user: User) {
    const now = new Date();
    const hireDate = new Date(user.hireDate);
    const tenureMonths = Math.floor((now.getTime() - hireDate.getTime()) / (1000 * 60 * 60 * 24 * 30));

    let riskScore = 0;
    const factors = [];

    // Tenure-based risk
    if (tenureMonths < 6) {
      riskScore += 0.4;
      factors.push('New employee (high early turnover risk)');
    } else if (tenureMonths > 60) {
      riskScore += 0.3;
      factors.push('Long tenure (potential stagnation risk)');
    }

    // Role-based risk
    const roleRisk = {
      'EMPLOYEE': 0.3,
      'MANAGER': 0.2,
      'DIRECTOR': 0.15,
      'CEO': 0.1,
    };
    riskScore += roleRisk[user.role] || 0.3;

    // Career progression analysis (simulated)
    const lastPromotion = Math.random() * 36; // Months since last promotion
    if (lastPromotion > 24) {
      riskScore += 0.2;
      factors.push('No recent career progression');
    }

    return {
      score: Math.min(1.0, riskScore),
      confidence: 0.8,
      factors,
      metrics: {
        tenureMonths,
        role: user.role,
        lastPromotion,
      },
    };
  }

  /**
   * Calculate team-related risk
   */
  private async calculateTeamRisk(user: User) {
    // Simulated team dynamics analysis
    const teamStability = Math.random() * 0.4 + 0.6; // 0.6-1.0
    const managerRelationship = Math.random() * 0.3 + 0.7; // 0.7-1.0
    const peerSupport = Math.random() * 0.2 + 0.8; // 0.8-1.0

    let riskScore = 0;
    const factors = [];

    if (teamStability < 0.7) {
      riskScore += 0.25;
      factors.push('Team instability');
    }

    if (managerRelationship < 0.8) {
      riskScore += 0.3;
      factors.push('Poor manager relationship');
    }

    if (peerSupport < 0.85) {
      riskScore += 0.15;
      factors.push('Limited peer support');
    }

    if (!user.managerId) {
      riskScore += 0.2;
      factors.push('No assigned manager');
    }

    return {
      score: Math.min(1.0, riskScore),
      confidence: 0.6,
      factors,
      metrics: {
        teamStability,
        managerRelationship,
        peerSupport,
        hasManager: !!user.managerId,
      },
    };
  }

  /**
   * Calculate organizational risk
   */
  private async calculateOrganizationalRisk(user: User) {
    // Simulated organizational factors
    const departmentStability = Math.random() * 0.3 + 0.7; // 0.7-1.0
    const organizationalChange = Math.random() * 0.5 + 0.5; // 0.5-1.0
    const marketConditions = Math.random() * 0.4 + 0.6; // 0.6-1.0

    let riskScore = 0;
    const factors = [];

    if (departmentStability < 0.8) {
      riskScore += 0.2;
      factors.push('Department restructuring');
    }

    if (organizationalChange > 0.8) {
      riskScore += 0.15;
      factors.push('High organizational change');
    }

    if (marketConditions < 0.7) {
      riskScore += 0.1;
      factors.push('Challenging market conditions');
    }

    return {
      score: Math.min(1.0, riskScore),
      confidence: 0.5,
      factors,
      metrics: {
        departmentStability,
        organizationalChange,
        marketConditions,
      },
    };
  }

  /**
   * Calculate dynamic weights based on data quality and relevance
   */
  private calculateDynamicWeights(user: User, dimensions: any) {
    const baseWeights = {
      performance: 0.25,
      engagement: 0.20,
      behavioral: 0.15,
      career: 0.15,
      team: 0.15,
      organizational: 0.10,
    };

    // Adjust weights based on confidence levels
    const adjustedWeights = { ...baseWeights };

    Object.keys(dimensions).forEach(key => {
      const confidence = dimensions[key].confidence;
      adjustedWeights[key] *= confidence;
    });

    // Normalize weights to sum to 1
    const totalWeight = Object.values(adjustedWeights).reduce((sum, weight) => sum + weight, 0);
    Object.keys(adjustedWeights).forEach(key => {
      adjustedWeights[key] /= totalWeight;
    });

    return adjustedWeights;
  }

  /**
   * Calculate composite score using weighted dimensions
   */
  private calculateCompositeScore(dimensions: any, weights: any): number {
    let compositeScore = 0;

    Object.keys(dimensions).forEach(key => {
      compositeScore += dimensions[key].score * weights[key];
    });

    return Math.min(1.0, Math.max(0.0, compositeScore));
  }

  /**
   * Classify risk with confidence intervals
   */
  private classifyRiskWithConfidence(score: number) {
    let level = 'low';
    let confidence = 0.8;
    let interval = [score - 0.05, score + 0.05];

    if (score >= 0.7) {
      level = 'high';
      confidence = 0.9;
    } else if (score >= 0.4) {
      level = 'medium';
      confidence = 0.85;
    }

    // Adjust confidence based on score certainty
    if (score > 0.8 || score < 0.2) {
      confidence += 0.05;
    }

    return {
      level,
      confidence: Math.min(1.0, confidence),
      interval: [Math.max(0, interval[0]), Math.min(1, interval[1])],
    };
  }

  /**
   * Identify top risk factors across all dimensions
   */
  private identifyRiskFactors(dimensions: any) {
    const allFactors = [];

    Object.entries(dimensions).forEach(([dimension, data]: [string, any]) => {
      data.factors.forEach((factor: string) => {
        allFactors.push({
          dimension,
          factor,
          impact: data.score,
          confidence: data.confidence,
        });
      });
    });

    // Sort by impact and confidence
    return allFactors
      .sort((a, b) => (b.impact * b.confidence) - (a.impact * a.confidence))
      .slice(0, 10); // Top 10 risk factors
  }

  /**
   * Generate targeted mitigation strategies
   */
  private generateMitigationStrategies(riskFactors: any[], riskClassification: any) {
    const strategies = [];

    // High-level strategies based on risk level
    if (riskClassification.level === 'high') {
      strategies.push({
        priority: 'urgent',
        action: 'Schedule immediate retention conversation',
        timeline: 'Within 1 week',
        owner: 'Direct Manager',
      });
    }

    // Specific strategies based on risk factors
    riskFactors.slice(0, 5).forEach(factor => {
      switch (factor.dimension) {
        case 'performance':
          strategies.push({
            priority: 'high',
            action: 'Implement performance improvement plan',
            timeline: '30 days',
            owner: 'HR & Manager',
          });
          break;
        case 'engagement':
          strategies.push({
            priority: 'medium',
            action: 'Conduct engagement deep-dive interview',
            timeline: '2 weeks',
            owner: 'HR',
          });
          break;
        case 'career':
          strategies.push({
            priority: 'medium',
            action: 'Discuss career development opportunities',
            timeline: '2 weeks',
            owner: 'Manager',
          });
          break;
        case 'team':
          strategies.push({
            priority: 'medium',
            action: 'Facilitate team dynamics assessment',
            timeline: '3 weeks',
            owner: 'HR',
          });
          break;
      }
    });

    return strategies.slice(0, 8); // Limit to 8 strategies
  }
}
