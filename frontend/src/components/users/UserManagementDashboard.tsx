import React, { useState, useEffect } from 'react';
import { UserRole, User } from '../../types';
import api from '../../services/api';
import { ApiService } from '../../services/api';

interface UserManagementDashboardProps {
  currentUserRole: UserRole;
}

interface UserSearchFilters {
  role?: UserRole;
  isActive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  byRole: Record<UserRole, number>;
  recentlyCreated: number;
  recentlyUpdated: number;
}

const UserManagementDashboard: React.FC<UserManagementDashboardProps> = ({
  currentUserRole
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [statistics, setStatistics] = useState<UserStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Search and filter state
  const [filters, setFilters] = useState<UserSearchFilters>({
    page: 1,
    limit: 20
  });
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);

  // Selection state
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Modal state
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [showBulkCreate, setShowBulkCreate] = useState(false);

  useEffect(() => {
    fetchUsers();
    if (currentUserRole === UserRole.HR_ADMIN) {
      fetchStatistics();
    }
  }, [filters]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use centralized database API for users
      const response = await ApiService.getTableData('users', filters.page || 1, filters.limit || 20);

      if (response && response.records) {
        // Transform database records to match expected format
        const transformedUsers = response.records.map((user: any) => ({
          id: user.id,
          firstName: user.firstName || user.first_name,
          lastName: user.lastName || user.last_name,
          email: user.email,
          role: user.role,
          isActive: user.isActive !== false,
          createdAt: user.createdAt || user.created_at,
          lastLoginAt: user.lastLoginAt || user.last_login_at
        }));

        setUsers(transformedUsers);
        setTotalPages(response.pagination?.totalPages || 1);
        setTotalUsers(response.pagination?.total || transformedUsers.length);
      } else {
        setUsers([]);
        setTotalPages(1);
        setTotalUsers(0);
      }
    } catch (err: any) {
      console.error('Error fetching users from database API:', err);
      setError(err.response?.data?.message || 'Failed to fetch users from database');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      // Use centralized database API to calculate user statistics
      const response = await ApiService.getTableData('users', 1, 1000); // Get all users for stats

      if (response && response.records) {
        const users = response.records;
        const stats: UserStatistics = {
          totalUsers: users.length,
          activeUsers: users.filter((user: any) => user.isActive !== false).length,
          inactiveUsers: users.filter((user: any) => user.isActive === false).length,
          byRole: {
            [UserRole.HR_ADMIN]: users.filter((user: any) => user.role === 'hr_admin' || user.role === 'HR_ADMIN').length,
            [UserRole.MANAGER]: users.filter((user: any) => user.role === 'manager' || user.role === 'MANAGER').length,
            [UserRole.EMPLOYEE]: users.filter((user: any) => user.role === 'employee' || user.role === 'EMPLOYEE').length,
            [UserRole.GUEST]: users.filter((user: any) => user.role === 'guest' || user.role === 'GUEST').length,
          },
          recentlyCreated: users.filter((user: any) => {
            const createdDate = new Date(user.createdAt || user.created_at);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return createdDate > thirtyDaysAgo;
          }).length,
          recentlyUpdated: users.filter((user: any) => {
            const updatedDate = new Date(user.updatedAt || user.updated_at);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return updatedDate > thirtyDaysAgo;
          }).length
        };
        setStatistics(stats);
      }
    } catch (err) {
      console.error('Error fetching user statistics from database API:', err);
      // Fallback to empty statistics
      setStatistics({
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        byRole: {
          [UserRole.HR_ADMIN]: 0,
          [UserRole.MANAGER]: 0,
          [UserRole.EMPLOYEE]: 0,
          [UserRole.GUEST]: 0,
        },
        recentlyCreated: 0,
        recentlyUpdated: 0,
      });
    }
  };

  const handleFilterChange = (key: keyof UserSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset to page 1 when changing filters
    }));
  };

  const handleUserSelection = (userId: number, selected: boolean) => {
    const newSelection = new Set(selectedUsers);
    if (selected) {
      newSelection.add(userId);
    } else {
      newSelection.delete(userId);
    }
    setSelectedUsers(newSelection);
    setSelectAll(newSelection.size === users.length);
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedUsers(new Set(users.map(u => u.id)));
    } else {
      setSelectedUsers(new Set());
    }
    setSelectAll(selected);
  };

  const handleBulkDeactivate = async () => {
    if (selectedUsers.size === 0) return;

    try {
      const response = await api.post('/users/bulk/deactivate', {
        userIds: Array.from(selectedUsers)
      });

      setSuccess(`Successfully deactivated ${response.data.deactivated.length} users`);
      setSelectedUsers(new Set());
      setSelectAll(false);
      await fetchUsers();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to deactivate users');
    }
  };

  const handleBulkReactivate = async () => {
    if (selectedUsers.size === 0) return;

    try {
      const response = await api.post('/users/bulk/reactivate', {
        userIds: Array.from(selectedUsers)
      });

      setSuccess(`Successfully reactivated ${response.data.reactivated.length} users`);
      setSelectedUsers(new Set());
      setSelectAll(false);
      await fetchUsers();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to reactivate users');
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.HR_ADMIN:
        return 'bg-purple-100 text-purple-800';
      case UserRole.MANAGER:
        return 'bg-blue-100 text-blue-800';
      case UserRole.EMPLOYEE:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canManageUsers = currentUserRole === UserRole.HR_ADMIN;
  const canViewUsers = [UserRole.HR_ADMIN, UserRole.MANAGER].includes(currentUserRole);

  if (!canViewUsers) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">You don't have permission to view user management.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600 mt-1">
            Manage users, roles, and permissions across the organization
          </p>
        </div>

        {canManageUsers && (
          <div className="flex space-x-3">
            <button
              onClick={() => setShowBulkCreate(true)}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Bulk Import
            </button>
            <button
              onClick={() => setShowCreateUser(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700"
            >
              Add User
            </button>
          </div>
        )}
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Users</h3>
            <p className="text-2xl font-bold text-gray-900">{statistics.totalUsers}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Active Users</h3>
            <p className="text-2xl font-bold text-green-600">{statistics.activeUsers}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Inactive Users</h3>
            <p className="text-2xl font-bold text-red-600">{statistics.inactiveUsers}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Recently Added</h3>
            <p className="text-2xl font-bold text-blue-600">{statistics.recentlyCreated}</p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Users
            </label>
            <input
              type="text"
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search by name or email..."
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Role
            </label>
            <select
              value={filters.role || ''}
              onChange={(e) => handleFilterChange('role', e.target.value || undefined)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Roles</option>
              <option value={UserRole.HR_ADMIN}>HR Admin</option>
              <option value={UserRole.MANAGER}>Manager</option>
              <option value={UserRole.EMPLOYEE}>Employee</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.isActive === undefined ? '' : filters.isActive.toString()}
              onChange={(e) => handleFilterChange('isActive', e.target.value === '' ? undefined : e.target.value === 'true')}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setFilters({ page: 1, limit: 20 })}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      {/* Bulk Actions */}
      {canManageUsers && selectedUsers.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-blue-800">
              {selectedUsers.size} user(s) selected
            </span>
            <div className="flex space-x-2">
              <button
                onClick={handleBulkDeactivate}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Deactivate
              </button>
              <button
                onClick={handleBulkReactivate}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Reactivate
              </button>
              <button
                onClick={() => setShowBulkActions(true)}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                More Actions
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Users ({totalUsers})
          </h3>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : users.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No users found matching your criteria.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {canManageUsers && (
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                  )}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    {canManageUsers && (
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedUsers.has(user.id)}
                          onChange={(e) => handleUserSelection(user.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {user.firstName?.[0]}{user.lastName?.[0]}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role as UserRole)}`}>
                        {user.role.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                        }`}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 mr-3">
                        View
                      </button>
                      {canManageUsers && (
                        <button className="text-blue-600 hover:text-blue-900">
                          Edit
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handleFilterChange('page', Math.max(1, (filters.page || 1) - 1))}
                disabled={(filters.page || 1) <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handleFilterChange('page', Math.min(totalPages, (filters.page || 1) + 1))}
                disabled={(filters.page || 1) >= totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{filters.page || 1}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handleFilterChange('page', Math.max(1, (filters.page || 1) - 1))}
                    disabled={(filters.page || 1) <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handleFilterChange('page', Math.min(totalPages, (filters.page || 1) + 1))}
                    disabled={(filters.page || 1) >= totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserManagementDashboard;
