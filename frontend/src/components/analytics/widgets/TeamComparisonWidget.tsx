import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Grid,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as RemoveIcon,
  Groups as GroupsIcon,
  Insights as InsightsIcon,
  CompareArrows as CompareArrowsIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface TeamData {
  teamId: number;
  teamName: string;
  totalMembers: number;
  avgScore: number;
  vsCompanyAverage: number;
  vsIndustryBenchmark: number;
  participationRate: number;
  performanceLevel: 'excellent' | 'good' | 'satisfactory' | 'needs-improvement';
}

interface BenchmarkData {
  companyAverage: number;
  industryBenchmark: number;
  topTeamScore: number;
}

interface InsightsData {
  totalTeams: number;
  teamsAboveAverage: number;
  teamsAboveBenchmark: number;
  highPerformingTeams: number;
}

interface TeamComparisonData {
  teams: TeamData[];
  benchmarks: BenchmarkData;
  insights: InsightsData;
}

interface TeamComparisonWidgetProps {
  filters?: {
    period?: string;
    includeInactive?: boolean;
  };
}

const TeamComparisonWidget: React.FC<TeamComparisonWidgetProps> = ({ filters }) => {
  const [data, setData] = useState<TeamComparisonData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState(filters?.period || '3months');
  const [sortBy, setSortBy] = useState<'score' | 'participation' | 'members'>('score');

  useEffect(() => {
    loadTeamData();
  }, [period, filters]);

  const loadTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 🔐 NIS2-COMPLIANT: Use real data from centralized API
      const response = await ApiService.getTeamPerformanceComparison({
        period,
        includeInactive: filters?.includeInactive
      });

      if (response && response.success) {
        setData(response.data);
      } else {
        throw new Error(response?.error || 'Failed to load team comparison data');
      }
    } catch (err) {
      setError('Failed to load team comparison data');
      console.error('Error loading team data:', err);
      
      // Fallback data structure
      setData({
        teams: [],
        benchmarks: {
          companyAverage: 0,
          industryBenchmark: 75,
          topTeamScore: 0
        },
        insights: {
          totalTeams: 0,
          teamsAboveAverage: 0,
          teamsAboveBenchmark: 0,
          highPerformingTeams: 0
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const getPerformanceColor = (level: string) => {
    switch (level) {
      case 'excellent': return '#4caf50';
      case 'good': return '#2196f3';
      case 'satisfactory': return '#ff9800';
      case 'needs-improvement': return '#f44336';
      default: return '#9e9e9e';
    }
  };

  const getTrendIcon = (value: number) => {
    if (value > 2) return <TrendingUpIcon color="success" />;
    if (value < -2) return <TrendingDownIcon color="error" />;
    return <RemoveIcon color="disabled" />;
  };

  const sortedTeams = data?.teams ? [...data.teams].sort((a, b) => {
    switch (sortBy) {
      case 'participation':
        return b.participationRate - a.participationRate;
      case 'members':
        return b.totalMembers - a.totalMembers;
      default:
        return b.avgScore - a.avgScore;
    }
  }) : [];

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">No team comparison data available</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h3">
            <CompareArrowsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Team Performance Comparison
          </Typography>
          
          <Box display="flex" gap={1}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Period</InputLabel>
              <Select
                value={period}
                label="Period"
                onChange={(e) => setPeriod(e.target.value)}
              >
                <MenuItem value="1month">1 Month</MenuItem>
                <MenuItem value="3months">3 Months</MenuItem>
                <MenuItem value="6months">6 Months</MenuItem>
                <MenuItem value="1year">1 Year</MenuItem>
              </Select>
            </FormControl>
            
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Sort By</InputLabel>
              <Select
                value={sortBy}
                label="Sort By"
                onChange={(e) => setSortBy(e.target.value as any)}
              >
                <MenuItem value="score">Performance</MenuItem>
                <MenuItem value="participation">Participation</MenuItem>
                <MenuItem value="members">Team Size</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* Benchmarks Overview */}
        <Grid container spacing={2} mb={3}>
          <Grid item xs={12} md={4}>
            <Box p={2} bgcolor="primary.light" borderRadius={1} color="white">
              <Typography variant="subtitle2">Company Average</Typography>
              <Typography variant="h5">{data.benchmarks.companyAverage.toFixed(1)}</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box p={2} bgcolor="secondary.light" borderRadius={1} color="white">
              <Typography variant="subtitle2">Industry Benchmark</Typography>
              <Typography variant="h5">{data.benchmarks.industryBenchmark}</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box p={2} bgcolor="success.light" borderRadius={1} color="white">
              <Typography variant="subtitle2">Top Team Score</Typography>
              <Typography variant="h5">{data.benchmarks.topTeamScore.toFixed(1)}</Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Insights Summary */}
        <Grid container spacing={2} mb={3}>
          <Grid item xs={6} md={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="primary">{data.insights.totalTeams}</Typography>
              <Typography variant="body2" color="textSecondary">Total Teams</Typography>
            </Box>
          </Grid>
          <Grid item xs={6} md={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="success.main">{data.insights.teamsAboveAverage}</Typography>
              <Typography variant="body2" color="textSecondary">Above Average</Typography>
            </Box>
          </Grid>
          <Grid item xs={6} md={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="info.main">{data.insights.teamsAboveBenchmark}</Typography>
              <Typography variant="body2" color="textSecondary">Above Benchmark</Typography>
            </Box>
          </Grid>
          <Grid item xs={6} md={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="warning.main">{data.insights.highPerformingTeams}</Typography>
              <Typography variant="body2" color="textSecondary">High Performing</Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Team Comparison Table */}
        <TableContainer component={Paper} variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Team</TableCell>
                <TableCell align="center">Members</TableCell>
                <TableCell align="center">Avg Score</TableCell>
                <TableCell align="center">vs Company</TableCell>
                <TableCell align="center">vs Industry</TableCell>
                <TableCell align="center">Participation</TableCell>
                <TableCell align="center">Level</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedTeams.slice(0, 10).map((team, index) => (
                <TableRow key={team.teamId} sx={{ '&:nth-of-type(odd)': { bgcolor: 'action.hover' } }}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Typography variant="body2" fontWeight="medium">
                        {team.teamName}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      icon={<GroupsIcon />}
                      label={team.totalMembers}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Typography 
                      variant="body2" 
                      fontWeight="bold"
                      color={getPerformanceColor(team.performanceLevel)}
                    >
                      {team.avgScore.toFixed(1)}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box display="flex" alignItems="center" justifyContent="center">
                      {getTrendIcon(team.vsCompanyAverage)}
                      <Typography 
                        variant="body2" 
                        color={team.vsCompanyAverage > 0 ? 'success.main' : 'error.main'}
                        ml={0.5}
                      >
                        {team.vsCompanyAverage > 0 ? '+' : ''}{team.vsCompanyAverage.toFixed(1)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Box display="flex" alignItems="center" justifyContent="center">
                      {getTrendIcon(team.vsIndustryBenchmark)}
                      <Typography 
                        variant="body2" 
                        color={team.vsIndustryBenchmark > 0 ? 'success.main' : 'error.main'}
                        ml={0.5}
                      >
                        {team.vsIndustryBenchmark > 0 ? '+' : ''}{team.vsIndustryBenchmark.toFixed(1)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Box>
                      <Typography variant="body2">{team.participationRate}%</Typography>
                      <LinearProgress
                        variant="determinate"
                        value={team.participationRate}
                        sx={{ width: 40, height: 4 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={team.performanceLevel.replace('-', ' ')}
                      size="small"
                      sx={{
                        bgcolor: getPerformanceColor(team.performanceLevel),
                        color: 'white',
                        textTransform: 'capitalize'
                      }}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {sortedTeams.length > 10 && (
          <Box mt={2} textAlign="center">
            <Typography variant="body2" color="textSecondary">
              Showing top 10 teams. Total: {sortedTeams.length} teams
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default TeamComparisonWidget;
