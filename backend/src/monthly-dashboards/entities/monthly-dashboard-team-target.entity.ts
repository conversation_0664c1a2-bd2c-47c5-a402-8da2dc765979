import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { MonthlyDashboardKpi } from './monthly-dashboard-kpi.entity';
import { User } from '../../users/entities/user.entity';

@Entity('monthly_dashboard_team_targets')
export class MonthlyDashboardTeamTarget {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'organizational_unit_id' })
  organizationalUnitId: number;

  @Column({ name: 'kpi_id' })
  kpiId: number;

  @Column({ name: 'target_value', type: 'decimal', precision: 12, scale: 4 })
  targetValue: number;

  @Column({ name: 'effective_from', type: 'date' })
  effectiveFrom: Date;

  @Column({ name: 'effective_to', type: 'date', nullable: true })
  effectiveTo: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'created_by_user_id' })
  createdByUserId: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrganizationalUnit, { eager: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @ManyToOne(() => MonthlyDashboardKpi, (kpi) => kpi.teamTargets, { eager: true })
  @JoinColumn({ name: 'kpi_id' })
  kpi: MonthlyDashboardKpi;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'created_by_user_id' })
  createdByUser: User;

  // Helper methods
  isActive(date: Date = new Date()): boolean {
    const checkDate = new Date(date);
    const fromDate = new Date(this.effectiveFrom);
    const toDate = this.effectiveTo ? new Date(this.effectiveTo) : null;

    return checkDate >= fromDate && (!toDate || checkDate <= toDate);
  }

  getFormattedTargetValue(): string {
    if (!this.kpi) {
      return this.targetValue.toString();
    }

    switch (this.kpi.unit) {
      case '%':
        return `${this.targetValue.toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(this.targetValue);
      case 'count':
        return Math.round(this.targetValue).toString();
      case 'days':
        return `${Math.round(this.targetValue)} days`;
      default:
        return this.targetValue.toString();
    }
  }

  getEffectivePeriod(): string {
    const fromDate = this.effectiveFrom.toLocaleDateString();
    const toDate = this.effectiveTo ? this.effectiveTo.toLocaleDateString() : 'Ongoing';
    return `${fromDate} - ${toDate}`;
  }
}
