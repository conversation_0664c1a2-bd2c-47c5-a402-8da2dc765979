import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { AssessmentTemplateService } from '../services/assessment-template.service';
import { TemplateLevel } from '../entities/assessment-template.entity';

export class CreateTemplateDto {
  name: string;
  description: string;
  templateLevel: TemplateLevel;
  organizationalUnitId?: number;
  createdById: number;
  criteriaIds: number[];
}

export class UpdateTemplateDto {
  name?: string;
  description?: string;
  isActive?: boolean;
  criteriaUpdates?: {
    criteriaId: number;
    isRequired?: boolean;
    orderIndex?: number;
    weightOverride?: number;
  }[];
}

@Controller('assessments/templates')
export class AssessmentTemplateController {
  constructor(
    private readonly templateService: AssessmentTemplateService,
  ) {}

  /**
   * Get all templates organized by level
   */
  @Get()
  async getTemplatesByLevel() {
    try {
      const templates = await this.templateService.getTemplatesByLevel();
      return {
        success: true,
        data: templates
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get templates for a specific organizational unit
   */
  @Get('organizational-unit/:unitId')
  async getTemplatesForOrganizationalUnit(@Param('unitId') unitId: number) {
    try {
      const templates = await this.templateService.getTemplatesForOrganizationalUnit(unitId);
      return {
        success: true,
        data: templates
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get a specific template with its criteria
   */
  @Get(':id')
  async getTemplate(@Param('id') id: number) {
    try {
      const template = await this.templateService.getTemplateWithCriteria(id);
      return {
        success: true,
        data: template
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a new assessment template
   */
  @Post()
  async createTemplate(@Body() createTemplateDto: CreateTemplateDto) {
    try {
      const template = await this.templateService.createTemplate(createTemplateDto);
      return {
        success: true,
        data: template,
        message: 'Template created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update an existing template
   */
  @Put(':id')
  async updateTemplate(@Param('id') id: number, @Body() updateTemplateDto: UpdateTemplateDto) {
    try {
      // Update basic template info
      if (updateTemplateDto.name || updateTemplateDto.description || updateTemplateDto.isActive !== undefined) {
        await this.templateService.updateTemplate(id, {
          name: updateTemplateDto.name,
          description: updateTemplateDto.description,
          isActive: updateTemplateDto.isActive
        });
      }

      // Update criteria if provided
      if (updateTemplateDto.criteriaUpdates) {
        await this.templateService.updateTemplateCriteria(id, updateTemplateDto.criteriaUpdates);
      }

      const updatedTemplate = await this.templateService.getTemplateWithCriteria(id);
      return {
        success: true,
        data: updatedTemplate,
        message: 'Template updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete/deactivate a template
   */
  @Delete(':id')
  async deleteTemplate(@Param('id') id: number) {
    try {
      await this.templateService.deactivateTemplate(id);
      return {
        success: true,
        message: 'Template deactivated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Duplicate a template
   */
  @Post(':id/duplicate')
  async duplicateTemplate(@Param('id') id: number, @Body() duplicateData: { name: string; createdById: number }) {
    try {
      const template = await this.templateService.duplicateTemplate(id, duplicateData.name, duplicateData.createdById);
      return {
        success: true,
        data: template,
        message: 'Template duplicated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Seed mock templates to database
   */
  @Post('seed-mock-data')
  async seedMockTemplates() {
    try {
      await this.templateService.seedMockTemplates();
      return {
        success: true,
        message: 'Mock templates seeded successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
