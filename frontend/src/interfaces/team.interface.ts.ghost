import { User } from '../types';

export enum TeamMemberRole {
  TEAM_LEAD = 'team_lead',
  MEMBER = 'member',
  GUEST = 'guest',
}

export interface TeamMember {
  id: number;
  teamId: number;
  userId: number;
  role: TeamMemberRole;
  addedAt: Date;
  user?: User;
}

export interface Team {
  id: number;
  name: string;
  description?: string;
  createdById: number;
  createdBy?: User;
  members: TeamMember[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTeamDto {
  name: string;
  description?: string;
  memberIds?: number[];
}

export interface UpdateTeamDto {
  name?: string;
  description?: string;
  addMemberIds?: number[];
  removeMemberIds?: number[];
}

export interface AddTeamMemberDto {
  userId: number;
  role?: TeamMemberRole;
}

export interface UpdateTeamMemberRoleDto {
  role: TeamMemberRole;
}
