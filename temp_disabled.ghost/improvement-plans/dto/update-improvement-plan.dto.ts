import { PartialType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsNumber, IsDateString, IsString, IsBoolean, Min, Max } from 'class-validator';
import { CreateImprovementPlanDto } from './create-improvement-plan.dto';
import { ImprovementPlanStatus } from '../entities/improvement-plan.entity';

export class UpdateImprovementPlanDto extends PartialType(CreateImprovementPlanDto) {
  @IsEnum(ImprovementPlanStatus)
  @IsOptional()
  status?: ImprovementPlanStatus;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  progressPercentage?: number;

  @IsDateString()
  @IsOptional()
  actualCompletionDate?: string;
}

export class UpdateImprovementPlanGoalDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsDateString()
  @IsOptional()
  targetDate?: string;

  @IsDateString()
  @IsOptional()
  completionDate?: string;

  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  progressPercentage?: number;

  @IsString()
  @IsOptional()
  notes?: string;

  @IsNumber()
  @IsOptional()
  orderIndex?: number;
}

export class CreateImprovementPlanReviewDto {
  @IsDateString()
  reviewDate: string;

  @IsString()
  progressSummary: string;

  @IsString()
  @IsOptional()
  achievements?: string;

  @IsString()
  @IsOptional()
  challenges?: string;

  @IsString()
  @IsOptional()
  nextSteps?: string;

  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  overallRating?: number;

  @IsBoolean()
  @IsOptional()
  isOnTrack?: boolean;

  @IsString()
  @IsOptional()
  recommendedActions?: string;
}
