import { Module } from '@nestjs/common';
import { LoggingService } from './logging.service';
import { LoggingController } from './logging.controller';
import { LogCleanupService } from './log-cleanup.service';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [ScheduleModule.forRoot()],
  controllers: [LoggingController],
  providers: [LoggingService, LogCleanupService],
  exports: [LoggingService],
})
export class LoggingModule {}
