import React from 'react';
import { Alert, AlertTitle, Box, Button } from '@mui/material';

interface ErrorAlertProps {
  error: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  severity?: 'error' | 'warning' | 'info' | 'success';
  title?: string;
}

const ErrorAlert: React.FC<ErrorAlertProps> = ({ 
  error, 
  onRetry, 
  onDismiss,
  severity = 'error',
  title = 'Error'
}) => {
  return (
    <Alert 
      severity={severity} 
      onClose={onDismiss}
      action={
        onRetry && (
          <Button color="inherit" size="small" onClick={onRetry}>
            Retry
          </Button>
        )
      }
    >
      <AlertTitle>{title}</AlertTitle>
      {error}
    </Alert>
  );
};

export default ErrorAlert;
