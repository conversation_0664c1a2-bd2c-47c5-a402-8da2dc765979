import React from 'react';
import { 
  Dialog as <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  DialogTitle as <PERSON><PERSON>D<PERSON>ogTitle, 
  DialogContent as <PERSON><PERSON><PERSON><PERSON>ogContent,
  DialogActions,
  IconButton,
  Typography
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

export const Dialog: React.FC<DialogProps> = ({ open, onOpenChange, children }) => (
  <MuiDialog 
    open={open} 
    onClose={() => onOpenChange(false)}
    maxWidth="md"
    fullWidth
  >
    {children}
  </MuiDialog>
);

export const DialogContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <MuiDialogContent className={className}>
    {children}
  </MuiDialogContent>
);

export const DialogHeader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MuiDialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
    {children}
  </MuiDialogTitle>
);

export const DialogTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Typography variant="h6" component="h2">
    {children}
  </Typography>
);

export const DialogTrigger: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <>{children}</>
);
