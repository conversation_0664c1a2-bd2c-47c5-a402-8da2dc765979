import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { MonthlyDashboardKpiValue } from './monthly-dashboard-kpi-value.entity';

export enum SubmissionStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity('monthly_dashboard_submissions')
export class MonthlyDashboardSubmission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'organizational_unit_id' })
  organizationalUnitId: number;

  @Column({ name: 'submitted_by_user_id' })
  submittedByUserId: number;

  @Column({ name: 'submission_month' })
  submissionMonth: number;

  @Column({ name: 'submission_year' })
  submissionYear: number;

  @Column({ name: 'submission_date', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  submissionDate: Date;

  @Column({ name: 'completion_date', type: 'date', nullable: true })
  completionDate: Date;

  @Column({
    type: 'enum',
    enum: SubmissionStatus,
    default: SubmissionStatus.DRAFT
  })
  status: SubmissionStatus;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'approved_by_user_id', nullable: true })
  approvedByUserId: number;

  @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
  approvedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrganizationalUnit, { eager: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'submitted_by_user_id' })
  submittedByUser: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by_user_id' })
  approvedByUser: User;

  @OneToMany(() => MonthlyDashboardKpiValue, (kpiValue) => kpiValue.submission, { cascade: true })
  kpiValues: MonthlyDashboardKpiValue[];

  // Helper methods
  getDisplayPeriod(): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return `${months[this.submissionMonth - 1]} ${this.submissionYear}`;
  }

  isEditable(): boolean {
    return this.status === SubmissionStatus.DRAFT || this.status === SubmissionStatus.REJECTED;
  }

  canBeApproved(): boolean {
    return this.status === SubmissionStatus.SUBMITTED;
  }

  getCompletionStatus(): 'on_time' | 'late' | 'very_late' | 'pending' {
    if (!this.completionDate) {
      return 'pending';
    }

    const targetDate = new Date(this.submissionYear, this.submissionMonth - 1, 14); // 14th of the month
    const completionDate = new Date(this.completionDate);

    if (completionDate <= targetDate) {
      return 'on_time';
    } else if (completionDate <= new Date(targetDate.getTime() + 3 * 24 * 60 * 60 * 1000)) { // 3 days late
      return 'late';
    } else {
      return 'very_late';
    }
  }

  getOverallTrafficLightStatus(): 'green' | 'yellow' | 'red' | 'na' {
    if (!this.kpiValues || this.kpiValues.length === 0) {
      return 'na';
    }

    const statusCounts = {
      red: 0,
      yellow: 0,
      green: 0,
      na: 0
    };

    this.kpiValues.forEach(kpiValue => {
      statusCounts[kpiValue.trafficLightStatus]++;
    });

    // If any red, overall is red
    if (statusCounts.red > 0) {
      return 'red';
    }
    // If any yellow, overall is yellow
    if (statusCounts.yellow > 0) {
      return 'yellow';
    }
    // If all green, overall is green
    if (statusCounts.green > 0) {
      return 'green';
    }
    // Otherwise, no data
    return 'na';
  }
}
