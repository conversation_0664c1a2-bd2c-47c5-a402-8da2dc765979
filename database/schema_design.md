# EHRX Database Schema Design

## Overview
This document outlines the database schema for the Employee Performance Management Dashboard. The schema is designed to support all the features specified in the PRD including role-based access control, team management, assessment templates, and performance tracking.

## Tables

### 1. Users
`
users
- id (PK) INT AUTO_INCREMENT
- email VARCHAR(255) UNIQUE
- password_hash VARCHAR(255)
- first_name VA<PERSON>HA<PERSON>(100)
- last_name VA<PERSON>HA<PERSON>(100)
- role ENUM('hr_admin', 'manager', 'employee', 'guest')
- is_active BOOLEAN DEFAULT true
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 2. Teams
`
teams
- id (PK) INT AUTO_INCREMENT
- name VARCHAR(100)
- description TEXT
- created_by INT (FK to users.id)
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 3. Team Membership
`
team_members
- id (PK) INT AUTO_INCREMENT
- team_id INT (FK to teams.id)
- user_id INT (FK to users.id)
- role ENUM('team_lead', 'member', 'guest')
- added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- UNIQUE KEY (team_id, user_id)
`

### 4. Assessment Templates
`
assessment_templates
- id (PK) INT AUTO_INCREMENT
- name VARCHAR(100)
- description TEXT
- created_by INT (FK to users.id)
- is_active BOOLEAN DEFAULT true
- version INT DEFAULT 1
- parent_template_id INT (FK to assessment_templates.id, for template versioning)
- is_global BOOLEAN DEFAULT false
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 5. Assessment Areas
`
assessment_areas
- id (PK) INT AUTO_INCREMENT
- template_id INT (FK to assessment_templates.id)
- name VARCHAR(100)
- description TEXT
- weight DECIMAL(5,2) DEFAULT 1.00
- max_score INT
- order_index INT
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 6. Scoring Rules
`
scoring_rules
- id (PK) INT AUTO_INCREMENT
- area_id INT (FK to assessment_areas.id)
- rule_type ENUM('addition', 'subtraction', 'multiplication', 'division', 'conditional')
- condition_field VARCHAR(100) (e.g., 'nc_incidents', 'training_completed')
- condition_operator ENUM('equals', 'not_equals', 'greater_than', 'less_than', etc.)
- condition_value VARCHAR(255)
- score_adjustment INT
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 7. Performance Assessments
`
performance_assessments
- id (PK) INT AUTO_INCREMENT
- employee_id INT (FK to users.id)
- template_id INT (FK to assessment_templates.id)
- assessor_id INT (FK to users.id)
- team_id INT (FK to teams.id)
- assessment_date DATE
- status ENUM('draft', 'in_review', 'completed')
- total_score DECIMAL(7,2)
- comments TEXT
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 8. Assessment Scores
`
assessment_scores
- id (PK) INT AUTO_INCREMENT
- assessment_id INT (FK to performance_assessments.id)
- area_id INT (FK to assessment_areas.id)
- raw_score DECIMAL(7,2)
- adjusted_score DECIMAL(7,2)
- comments TEXT
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 9. Action Items
`
action_items
- id (PK) INT AUTO_INCREMENT
- employee_id INT (FK to users.id)
- assessment_id INT (FK to performance_assessments.id, nullable)
- title VARCHAR(255)
- description TEXT
- due_date DATE
- status ENUM('pending', 'in_progress', 'completed', 'cancelled')
- created_by INT (FK to users.id)
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 10. Process Improvements
`
process_improvements
- id (PK) INT AUTO_INCREMENT
- employee_id INT (FK to users.id)
- title VARCHAR(255)
- description TEXT
- status ENUM('proposed', 'in_review', 'approved', 'implemented', 'rejected')
- created_by INT (FK to users.id)
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 11. AI Initiatives
`
ai_initiatives
- id (PK) INT AUTO_INCREMENT
- employee_id INT (FK to users.id)
- title VARCHAR(255)
- description TEXT
- status ENUM('proposed', 'in_development', 'completed', 'on_hold')
- created_by INT (FK to users.id)
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

### 12. Stakeholder Feedback
`
stakeholder_feedback
- id (PK) INT AUTO_INCREMENT
- employee_id INT (FK to users.id)
- source VARCHAR(255)
- feedback_text TEXT
- received_date DATE
- recorded_by INT (FK to users.id)
- created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
`

## Relationships

1. Users can be members of multiple teams (via team_members)
2. Teams can have multiple users (via team_members)
3. Teams can have one default assessment template
4. Assessment templates contain multiple assessment areas
5. Assessment areas can have multiple scoring rules
6. Performance assessments are linked to one employee, one template, and one assessor
7. Performance assessments contain multiple assessment scores (one per area)
8. Action items, process improvements, AI initiatives, and stakeholder feedback are linked to specific employees

## Indexes

For performance optimization, the following indexes will be created:
- users(email)
- team_members(team_id, user_id)
- performance_assessments(employee_id, assessment_date)
- performance_assessments(team_id, assessment_date)
- assessment_scores(assessment_id, area_id)
- action_items(employee_id, status)

## Security Considerations

1. Passwords are stored as hashes (bcrypt)
2. Database access is restricted based on role permissions
3. All sensitive data is encrypted at rest
4. Regular backups will be configured
5. Audit trails for critical operations will be implemented

This schema provides the foundation for implementing the features described in the PRD while maintaining data integrity and security.
