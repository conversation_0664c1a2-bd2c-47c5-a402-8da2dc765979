import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, UpdateDateColumn, OneToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('user_gamification')
export class UserGamification {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', unique: true })
  userId: number;

  @Column({ name: 'total_points', default: 0 })
  totalPoints: number;

  @Column({ name: 'current_level', default: 1 })
  currentLevel: number;

  @Column({ name: 'badges_earned', default: 0 })
  badgesEarned: number;

  @Column({ name: 'recognitions_given', default: 0 })
  recognitionsGiven: number;

  @Column({ name: 'recognitions_received', default: 0 })
  recognitionsReceived: number;

  @Column({ name: 'last_activity_date', type: 'date', nullable: true })
  lastActivityDate: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
