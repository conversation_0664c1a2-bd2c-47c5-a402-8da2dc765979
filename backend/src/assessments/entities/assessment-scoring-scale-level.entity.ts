import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AssessmentScoringScale } from './assessment-scoring-scale.entity';
import { AssessmentCriteriaResponse } from './assessment-criteria-response.entity';

@Entity('assessment_scoring_scale_levels')
export class AssessmentScoringScaleLevel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'scoring_scale_id' })
  scoringScaleId: number;

  @ManyToOne(() => AssessmentScoringScale, scale => scale.levels)
  @JoinColumn({ name: 'scoring_scale_id' })
  scoringScale: AssessmentScoringScale;

  @Column({ name: 'level_name' })
  levelName: string;

  @Column({ name: 'level_code' })
  levelCode: string;

  @Column({ 
    name: 'min_performance_score', 
    type: 'decimal', 
    precision: 5, 
    scale: 2 
  })
  minPerformanceScore: number;

  @Column({ 
    name: 'max_performance_score', 
    type: 'decimal', 
    precision: 5, 
    scale: 2 
  })
  maxPerformanceScore: number;

  @Column({ name: 'points_awarded' })
  pointsAwarded: number;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'order_index', default: 1 })
  orderIndex: number;

  @OneToMany(() => AssessmentCriteriaResponse, response => response.scoringScaleLevel)
  criteriaResponses: AssessmentCriteriaResponse[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
