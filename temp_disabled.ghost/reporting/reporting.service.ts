import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
// import * as PDFDocument from 'pdfkit';
// import * as ExcelJS from 'exceljs';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { ActionItem } from '../action-items/entities/action-item.entity';
import { ImprovementPlan } from '../improvement-plans/entities/improvement-plan.entity';
import { User } from '../users/entities/user.entity';
import { AnalyticsService } from '../analytics/analytics.service';

export interface ReportConfig {
  title: string;
  description?: string;
  type: 'performance' | 'assessment' | 'action_items' | 'improvement_plans' | 'comprehensive';
  format: 'pdf' | 'excel' | 'csv' | 'json';
  filters: {
    startDate?: Date;
    endDate?: Date;
    userIds?: number[];
    teamIds?: number[];
    departmentIds?: number[];
    templateIds?: number[];
  };
  sections: {
    summary: boolean;
    details: boolean;
    charts: boolean;
    recommendations: boolean;
    appendix: boolean;
  };
  customFields?: string[];
}

export interface ReportData {
  metadata: {
    title: string;
    generatedAt: Date;
    generatedBy: string;
    period: string;
    filters: any;
  };
  summary: {
    totalUsers: number;
    totalAssessments: number;
    averageScore: number;
    completionRate: number;
    keyMetrics: { label: string; value: number; unit?: string }[];
  };
  performance: {
    topPerformers: any[];
    improvementAreas: any[];
    scoreDistribution: any[];
    trends: any[];
  };
  assessments: {
    completed: any[];
    pending: any[];
    overdue: any[];
  };
  actionItems: {
    total: number;
    completed: number;
    pending: number;
    overdue: number;
    byCategory: any[];
  };
  improvementPlans: {
    active: number;
    completed: number;
    onTrack: number;
    behindSchedule: number;
  };
  recommendations: string[];
}

@Injectable()
export class ReportingService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(ActionItem)
    private actionItemRepository: Repository<ActionItem>,
    @InjectRepository(ImprovementPlan)
    private improvementPlanRepository: Repository<ImprovementPlan>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private analyticsService: AnalyticsService,
  ) { }

  async generateReport(config: ReportConfig, generatedBy: string): Promise<Buffer> {
    const reportData = await this.collectReportData(config, generatedBy);

    switch (config.format) {
      case 'pdf':
        return this.generatePDFReport(reportData, config);
      case 'excel':
        return this.generateExcelReport(reportData, config);
      case 'csv':
        return this.generateCSVReport(reportData, config);
      case 'json':
        return Buffer.from(JSON.stringify(reportData, null, 2));
      default:
        throw new Error(`Unsupported report format: ${config.format}`);
    }
  }

  private async collectReportData(config: ReportConfig, generatedBy: string): Promise<ReportData> {
    const { filters } = config;

    // Get analytics data
    const performanceMetrics = await this.analyticsService.getPerformanceMetrics(filters);
    const trendAnalysis = await this.analyticsService.getTrendAnalysis('monthly', filters);

    // Get assessments data
    const assessmentsQuery = this.assessmentRepository.createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.employee', 'employee')
      .leftJoinAndSelect('assessment.template', 'template')
      .leftJoinAndSelect('assessment.responses', 'responses');

    this.applyFilters(assessmentsQuery, filters);
    const assessments = await assessmentsQuery.getMany();

    const completedAssessments = assessments.filter(a => a.status === 'completed');
    const pendingAssessments = assessments.filter(a => a.status === 'in_progress' || a.status === 'draft');
    const overdueAssessments = assessments.filter(a =>
      a.status !== 'completed' && a.dueDate && new Date(a.dueDate) < new Date()
    );

    // Get action items data
    const actionItemsQuery = this.actionItemRepository.createQueryBuilder('actionItem')
      .leftJoinAndSelect('actionItem.assignedTo', 'assignedTo');

    this.applyActionItemFilters(actionItemsQuery, filters);
    const actionItems = await actionItemsQuery.getMany();

    const completedActionItems = actionItems.filter(ai => ai.status === 'completed').length;
    const pendingActionItems = actionItems.filter(ai => ai.status !== 'completed' && ai.status !== 'cancelled').length;
    const overdueActionItems = actionItems.filter(ai =>
      ai.status !== 'completed' && ai.dueDate && new Date(ai.dueDate) < new Date()
    ).length;

    // Get improvement plans data
    const improvementPlansQuery = this.improvementPlanRepository.createQueryBuilder('plan')
      .leftJoinAndSelect('plan.employee', 'employee')
      .leftJoinAndSelect('plan.goals', 'goals');

    this.applyImprovementPlanFilters(improvementPlansQuery, filters);
    const improvementPlans = await improvementPlansQuery.getMany();

    const activePlans = improvementPlans.filter(ip => ip.status === 'active').length;
    const completedPlans = improvementPlans.filter(ip => ip.status === 'completed').length;
    const onTrackPlans = improvementPlans.filter(ip =>
      ip.status === 'active' && ip.progressPercentage >= 50
    ).length;
    const behindSchedulePlans = improvementPlans.filter(ip =>
      ip.status === 'active' && ip.progressPercentage < 50 &&
      ip.targetCompletionDate && new Date(ip.targetCompletionDate) < new Date()
    ).length;

    // Calculate action items by category
    const actionItemsByCategory = actionItems.reduce((acc, item) => {
      const category = item.category || 'other';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const actionItemsByCategoryArray = Object.entries(actionItemsByCategory).map(([category, count]) => ({
      category: category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count
    }));

    // Generate recommendations
    const recommendations = this.generateRecommendations({
      averageScore: performanceMetrics.averageScore,
      completionRate: assessments.length > 0 ? (completedAssessments.length / assessments.length) * 100 : 0,
      overdueItems: overdueActionItems,
      behindSchedulePlans
    });

    return {
      metadata: {
        title: config.title,
        generatedAt: new Date(),
        generatedBy,
        period: this.formatPeriod(filters.startDate, filters.endDate),
        filters
      },
      summary: {
        totalUsers: await this.userRepository.count(),
        totalAssessments: assessments.length,
        averageScore: performanceMetrics.averageScore,
        completionRate: assessments.length > 0 ? (completedAssessments.length / assessments.length) * 100 : 0,
        keyMetrics: [
          { label: 'Active Action Items', value: pendingActionItems, unit: 'items' },
          { label: 'Overdue Items', value: overdueActionItems, unit: 'items' },
          { label: 'Active Improvement Plans', value: activePlans, unit: 'plans' },
          { label: 'Top Performer Score', value: performanceMetrics.topPerformers[0]?.averageScore || 0, unit: '%' }
        ]
      },
      performance: {
        topPerformers: performanceMetrics.topPerformers,
        improvementAreas: performanceMetrics.improvementAreas,
        scoreDistribution: performanceMetrics.scoreDistribution,
        trends: trendAnalysis.data
      },
      assessments: {
        completed: completedAssessments,
        pending: pendingAssessments,
        overdue: overdueAssessments
      },
      actionItems: {
        total: actionItems.length,
        completed: completedActionItems,
        pending: pendingActionItems,
        overdue: overdueActionItems,
        byCategory: actionItemsByCategoryArray
      },
      improvementPlans: {
        active: activePlans,
        completed: completedPlans,
        onTrack: onTrackPlans,
        behindSchedule: behindSchedulePlans
      },
      recommendations
    };
  }

  private async generatePDFReport(data: ReportData, config: ReportConfig): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 });
        const chunks: Buffer[] = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Header
        doc.fontSize(20).text(data.metadata.title, { align: 'center' });
        doc.fontSize(12).text(`Generated on: ${data.metadata.generatedAt.toLocaleDateString()}`, { align: 'center' });
        doc.text(`Period: ${data.metadata.period}`, { align: 'center' });
        doc.moveDown(2);

        // Executive Summary
        if (config.sections.summary) {
          doc.fontSize(16).text('Executive Summary', { underline: true });
          doc.moveDown();

          doc.fontSize(12);
          doc.text(`Total Users: ${data.summary.totalUsers}`);
          doc.text(`Total Assessments: ${data.summary.totalAssessments}`);
          doc.text(`Average Score: ${data.summary.averageScore.toFixed(1)}%`);
          doc.text(`Completion Rate: ${data.summary.completionRate.toFixed(1)}%`);
          doc.moveDown();

          // Key Metrics
          doc.text('Key Metrics:', { underline: true });
          data.summary.keyMetrics.forEach(metric => {
            doc.text(`• ${metric.label}: ${metric.value} ${metric.unit || ''}`);
          });
          doc.moveDown(2);
        }

        // Performance Analysis
        if (config.sections.details) {
          doc.fontSize(16).text('Performance Analysis', { underline: true });
          doc.moveDown();

          // Top Performers
          doc.fontSize(14).text('Top Performers:', { underline: true });
          doc.fontSize(12);
          data.performance.topPerformers.slice(0, 5).forEach((performer, index) => {
            doc.text(`${index + 1}. ${performer.user.firstName} ${performer.user.lastName} - ${performer.averageScore.toFixed(1)}%`);
          });
          doc.moveDown();

          // Improvement Areas
          doc.fontSize(14).text('Areas for Improvement:', { underline: true });
          doc.fontSize(12);
          data.performance.improvementAreas.forEach(area => {
            doc.text(`• ${area.area}: ${area.averageScore.toFixed(1)}% (${area.assessmentCount} assessments)`);
          });
          doc.moveDown(2);
        }

        // Action Items Summary
        doc.fontSize(16).text('Action Items Summary', { underline: true });
        doc.moveDown();
        doc.fontSize(12);
        doc.text(`Total Action Items: ${data.actionItems.total}`);
        doc.text(`Completed: ${data.actionItems.completed}`);
        doc.text(`Pending: ${data.actionItems.pending}`);
        doc.text(`Overdue: ${data.actionItems.overdue}`);
        doc.moveDown();

        // Action Items by Category
        doc.fontSize(14).text('Action Items by Category:', { underline: true });
        doc.fontSize(12);
        data.actionItems.byCategory.forEach(category => {
          doc.text(`• ${category.category}: ${category.count}`);
        });
        doc.moveDown(2);

        // Improvement Plans Summary
        doc.fontSize(16).text('Improvement Plans Summary', { underline: true });
        doc.moveDown();
        doc.fontSize(12);
        doc.text(`Active Plans: ${data.improvementPlans.active}`);
        doc.text(`Completed Plans: ${data.improvementPlans.completed}`);
        doc.text(`On Track: ${data.improvementPlans.onTrack}`);
        doc.text(`Behind Schedule: ${data.improvementPlans.behindSchedule}`);
        doc.moveDown(2);

        // Recommendations
        if (config.sections.recommendations) {
          doc.fontSize(16).text('Recommendations', { underline: true });
          doc.moveDown();
          doc.fontSize(12);
          data.recommendations.forEach((recommendation, index) => {
            doc.text(`${index + 1}. ${recommendation}`);
            doc.moveDown(0.5);
          });
        }

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  private async generateExcelReport(data: ReportData, config: ReportConfig): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();

    // Summary Sheet
    const summarySheet = workbook.addWorksheet('Summary');
    summarySheet.addRow(['Performance Report Summary']);
    summarySheet.addRow(['Generated:', data.metadata.generatedAt.toLocaleDateString()]);
    summarySheet.addRow(['Period:', data.metadata.period]);
    summarySheet.addRow([]);

    summarySheet.addRow(['Metric', 'Value']);
    summarySheet.addRow(['Total Users', data.summary.totalUsers]);
    summarySheet.addRow(['Total Assessments', data.summary.totalAssessments]);
    summarySheet.addRow(['Average Score', `${data.summary.averageScore.toFixed(1)}%`]);
    summarySheet.addRow(['Completion Rate', `${data.summary.completionRate.toFixed(1)}%`]);

    summarySheet.addRow([]);
    summarySheet.addRow(['Key Metrics']);
    data.summary.keyMetrics.forEach(metric => {
      summarySheet.addRow([metric.label, `${metric.value} ${metric.unit || ''}`]);
    });

    // Top Performers Sheet
    const performersSheet = workbook.addWorksheet('Top Performers');
    performersSheet.addRow(['Rank', 'Name', 'Email', 'Average Score', 'Assessment Count']);
    data.performance.topPerformers.forEach((performer, index) => {
      performersSheet.addRow([
        index + 1,
        `${performer.user.firstName} ${performer.user.lastName}`,
        performer.user.email,
        performer.averageScore.toFixed(1),
        performer.assessmentCount
      ]);
    });

    // Action Items Sheet
    const actionItemsSheet = workbook.addWorksheet('Action Items');
    actionItemsSheet.addRow(['Summary']);
    actionItemsSheet.addRow(['Total', data.actionItems.total]);
    actionItemsSheet.addRow(['Completed', data.actionItems.completed]);
    actionItemsSheet.addRow(['Pending', data.actionItems.pending]);
    actionItemsSheet.addRow(['Overdue', data.actionItems.overdue]);
    actionItemsSheet.addRow([]);

    actionItemsSheet.addRow(['Category', 'Count']);
    data.actionItems.byCategory.forEach(category => {
      actionItemsSheet.addRow([category.category, category.count]);
    });

    // Improvement Plans Sheet
    const plansSheet = workbook.addWorksheet('Improvement Plans');
    plansSheet.addRow(['Status', 'Count']);
    plansSheet.addRow(['Active', data.improvementPlans.active]);
    plansSheet.addRow(['Completed', data.improvementPlans.completed]);
    plansSheet.addRow(['On Track', data.improvementPlans.onTrack]);
    plansSheet.addRow(['Behind Schedule', data.improvementPlans.behindSchedule]);

    // Recommendations Sheet
    const recommendationsSheet = workbook.addWorksheet('Recommendations');
    recommendationsSheet.addRow(['Recommendations']);
    data.recommendations.forEach((recommendation, index) => {
      recommendationsSheet.addRow([`${index + 1}.`, recommendation]);
    });

    // Style the headers
    [summarySheet, performersSheet, actionItemsSheet, plansSheet, recommendationsSheet].forEach(sheet => {
      sheet.getRow(1).font = { bold: true };
      sheet.columns.forEach(column => {
        column.width = 20;
      });
    });

    return workbook.xlsx.writeBuffer() as Promise<Buffer>;
  }

  private async generateCSVReport(data: ReportData, config: ReportConfig): Promise<Buffer> {
    let csvContent = '';

    // Summary
    csvContent += 'Performance Report Summary\n';
    csvContent += `Generated,${data.metadata.generatedAt.toLocaleDateString()}\n`;
    csvContent += `Period,${data.metadata.period}\n\n`;

    csvContent += 'Metric,Value\n';
    csvContent += `Total Users,${data.summary.totalUsers}\n`;
    csvContent += `Total Assessments,${data.summary.totalAssessments}\n`;
    csvContent += `Average Score,${data.summary.averageScore.toFixed(1)}%\n`;
    csvContent += `Completion Rate,${data.summary.completionRate.toFixed(1)}%\n\n`;

    // Top Performers
    csvContent += 'Top Performers\n';
    csvContent += 'Rank,Name,Email,Average Score,Assessment Count\n';
    data.performance.topPerformers.forEach((performer, index) => {
      csvContent += `${index + 1},"${performer.user.firstName} ${performer.user.lastName}",${performer.user.email},${performer.averageScore.toFixed(1)},${performer.assessmentCount}\n`;
    });
    csvContent += '\n';

    // Action Items
    csvContent += 'Action Items Summary\n';
    csvContent += `Total,${data.actionItems.total}\n`;
    csvContent += `Completed,${data.actionItems.completed}\n`;
    csvContent += `Pending,${data.actionItems.pending}\n`;
    csvContent += `Overdue,${data.actionItems.overdue}\n\n`;

    csvContent += 'Action Items by Category\n';
    csvContent += 'Category,Count\n';
    data.actionItems.byCategory.forEach(category => {
      csvContent += `"${category.category}",${category.count}\n`;
    });
    csvContent += '\n';

    // Recommendations
    csvContent += 'Recommendations\n';
    data.recommendations.forEach((recommendation, index) => {
      csvContent += `${index + 1},"${recommendation}"\n`;
    });

    return Buffer.from(csvContent, 'utf-8');
  }

  private applyFilters(queryBuilder: any, filters: any): void {
    if (filters.startDate) {
      queryBuilder.andWhere('assessment.assessmentDate >= :startDate', { startDate: filters.startDate });
    }

    if (filters.endDate) {
      queryBuilder.andWhere('assessment.assessmentDate <= :endDate', { endDate: filters.endDate });
    }

    if (filters.userIds && filters.userIds.length > 0) {
      queryBuilder.andWhere('assessment.employeeId IN (:...userIds)', { userIds: filters.userIds });
    }

    if (filters.templateIds && filters.templateIds.length > 0) {
      queryBuilder.andWhere('assessment.templateId IN (:...templateIds)', { templateIds: filters.templateIds });
    }
  }

  private applyActionItemFilters(queryBuilder: any, filters: any): void {
    if (filters.startDate) {
      queryBuilder.andWhere('actionItem.createdAt >= :startDate', { startDate: filters.startDate });
    }

    if (filters.endDate) {
      queryBuilder.andWhere('actionItem.createdAt <= :endDate', { endDate: filters.endDate });
    }

    if (filters.userIds && filters.userIds.length > 0) {
      queryBuilder.andWhere('actionItem.assignedToId IN (:...userIds)', { userIds: filters.userIds });
    }
  }

  private applyImprovementPlanFilters(queryBuilder: any, filters: any): void {
    if (filters.startDate) {
      queryBuilder.andWhere('plan.startDate >= :startDate', { startDate: filters.startDate });
    }

    if (filters.endDate) {
      queryBuilder.andWhere('plan.startDate <= :endDate', { endDate: filters.endDate });
    }

    if (filters.userIds && filters.userIds.length > 0) {
      queryBuilder.andWhere('plan.employeeId IN (:...userIds)', { userIds: filters.userIds });
    }
  }

  private formatPeriod(startDate?: Date, endDate?: Date): string {
    if (!startDate && !endDate) {
      return 'All Time';
    }

    if (startDate && endDate) {
      return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
    }

    if (startDate) {
      return `From ${startDate.toLocaleDateString()}`;
    }

    if (endDate) {
      return `Until ${endDate.toLocaleDateString()}`;
    }

    return 'Custom Period';
  }

  private generateRecommendations(metrics: {
    averageScore: number;
    completionRate: number;
    overdueItems: number;
    behindSchedulePlans: number;
  }): string[] {
    const recommendations = [];

    if (metrics.averageScore < 70) {
      recommendations.push('Consider implementing comprehensive training programs to improve overall performance scores');
    }

    if (metrics.completionRate < 80) {
      recommendations.push('Improve assessment completion rates through better scheduling and follow-up processes');
    }

    if (metrics.overdueItems > 0) {
      recommendations.push('Address overdue action items immediately and implement better deadline management');
    }

    if (metrics.behindSchedulePlans > 0) {
      recommendations.push('Review improvement plans that are behind schedule and provide additional support');
    }

    if (metrics.averageScore >= 85 && metrics.completionRate >= 90) {
      recommendations.push('Excellent performance levels maintained. Consider setting higher targets for continued growth');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance metrics are within acceptable ranges. Continue monitoring and maintain current practices');
    }

    return recommendations;
  }
}
