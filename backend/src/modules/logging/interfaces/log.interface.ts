export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace',
}

export enum LogSource {
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  DATABASE = 'database',
  EXTERNAL_API = 'external_api',
  SYSTEM = 'system',
}

export interface LogEntry {
  id?: string;
  timestamp: Date;
  level: LogLevel;
  source: LogSource;
  message: string;
  details?: any;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  ip?: string;
  url?: string;
  method?: string;
  statusCode?: number;
  responseTime?: number;
  stackTrace?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface BrowserError {
  message: string;
  filename?: string;
  lineno?: number;
  colno?: number;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  userAgent: string;
  url: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

export interface LogQuery {
  level?: LogLevel;
  source?: LogSource;
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  component?: string;
  limit?: number;
  offset?: number;
  search?: string;
}

export interface LogStats {
  totalLogs: number;
  errorCount: number;
  warnCount: number;
  infoCount: number;
  debugCount: number;
  traceCount: number;
  sourceBreakdown: Record<LogSource, number>;
  recentErrors: LogEntry[];
  topErrors: Array<{ message: string; count: number; lastOccurrence: Date }>;
}
