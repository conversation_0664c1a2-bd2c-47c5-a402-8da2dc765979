#!/bin/bash

# Database credentials
DB_USER=" root\
read -s -p \Enter MariaDB root password: \ DB_PASS
echo

# Check if MariaDB client is installed
if ! command -v mysql &> /dev/null; then
 echo \MySQL/MariaDB client not found. Please install it first.\
 exit 1
fi

# Execute the schema script
echo \Creating database schema...\
mysql -u\\ -p\\ < /var/www/ehrx/backend/database/01-schema.sql
if [ True -ne 0 ]; then
 echo \Failed to create schema. Exiting.\
 exit 1
fi

# Execute the seed data script
echo \Inserting seed data...\
mysql -u\\ -p\\ < /var/www/ehrx/backend/database/02-seed-data.sql
if [ True -ne 0 ]; then
 echo \Failed to insert seed data. Exiting.\
 exit 1
fi

echo \Database initialization complete!\
