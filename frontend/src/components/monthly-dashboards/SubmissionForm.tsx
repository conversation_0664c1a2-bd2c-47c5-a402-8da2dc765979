import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  Chip
} from '@mui/material';
import { Save, Close, Info } from '@mui/icons-material';
import { monthlyDashboardsApi } from '../../services/api';
import { toast } from '../ui/toast';

interface KpiValue {
  kpiId: number;
  value: number;
  comments?: string;
}

interface SubmissionFormData {
  organizationalUnitId: number;
  submissionMonth: number;
  submissionYear: number;
  kpiValues: KpiValue[];
  comments?: string;
}

interface Kpi {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  targetValue?: number;
  unit?: string;
  calculationMethod: string;
  helpText: string;
  isActive: boolean;
}

interface SubmissionFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingSubmission?: any;
  organizationalUnitId?: number;
  month?: number;
  year?: number;
}

export const SubmissionForm: React.FC<SubmissionFormProps> = ({
  open,
  onClose,
  onSuccess,
  editingSubmission,
  organizationalUnitId,
  month = new Date().getMonth() + 1,
  year = new Date().getFullYear()
}) => {
  const [loading, setLoading] = useState(false);
  const [kpis, setKpis] = useState<Kpi[]>([]);
  const [formData, setFormData] = useState<SubmissionFormData>({
    organizationalUnitId: organizationalUnitId || 0,
    submissionMonth: month,
    submissionYear: year,
    kpiValues: [],
    comments: ''
  });

  useEffect(() => {
    if (open) {
      fetchKpis();
      if (editingSubmission) {
        setFormData({
          organizationalUnitId: editingSubmission.organizationalUnitId,
          submissionMonth: editingSubmission.submissionMonth,
          submissionYear: editingSubmission.submissionYear,
          kpiValues: editingSubmission.kpiValues || [],
          comments: editingSubmission.comments || ''
        });
      }
    }
  }, [open, editingSubmission]);

  const fetchKpis = async () => {
    try {
      const response = await monthlyDashboardsApi.getAllKpis();
      const activeKpis = response.data || response;
      setKpis(activeKpis);
      
      // Initialize KPI values if not editing
      if (!editingSubmission) {
        const initialKpiValues = activeKpis.map((kpi: Kpi) => ({
          kpiId: kpi.id,
          value: kpi.targetValue || 0,
          comments: ''
        }));
        setFormData(prev => ({ ...prev, kpiValues: initialKpiValues }));
      }
    } catch (error) {
      console.error('Error fetching KPIs:', error);
      toast.error('Failed to load KPIs');
    }
  };

  const handleKpiValueChange = (kpiId: number, field: 'value' | 'comments', value: string | number) => {
    setFormData(prev => ({
      ...prev,
      kpiValues: prev.kpiValues.map(kv => 
        kv.kpiId === kpiId 
          ? { ...kv, [field]: field === 'value' ? parseFloat(value as string) || 0 : value }
          : kv
      )
    }));
  };

  const getTrafficLightColor = (kpi: Kpi, value: number) => {
    if (!kpi.targetValue) return '#gray';
    
    const variance = ((value - kpi.targetValue) / kpi.targetValue) * 100;
    
    if (variance >= -5 && variance <= 5) return '#4caf50'; // Green
    if (variance >= -10 && variance <= 10) return '#ff9800'; // Orange/Yellow
    return '#f44336'; // Red
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      const submissionData = {
        ...formData,
        kpiValues: formData.kpiValues.map(kv => ({
          kpiId: kv.kpiId,
          value: kv.value,
          comments: kv.comments || null
        }))
      };

      if (editingSubmission) {
        // Update existing submission
        await monthlyDashboardsApi.updateSubmission(editingSubmission.id, submissionData);
        toast.success('Submission updated successfully');
      } else {
        // Create new submission
        await monthlyDashboardsApi.createSubmission(submissionData);
        toast.success('Submission created successfully');
      }
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error saving submission:', error);
      toast.error('Failed to save submission');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Info color="primary" />
          <Typography variant="h6">
            {editingSubmission ? 'Edit' : 'Create'} Monthly Dashboard Submission
          </Typography>
        </Box>
        <Typography variant="subtitle2" color="text.secondary">
          {new Date(0, month - 1).toLocaleString('default', { month: 'long' })} {year}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          {/* General Comments */}
          <TextField
            fullWidth
            label="General Comments"
            multiline
            rows={3}
            value={formData.comments}
            onChange={(e) => setFormData(prev => ({ ...prev, comments: e.target.value }))}
            sx={{ mb: 3 }}
            placeholder="Add any general comments about this month's performance..."
          />

          <Divider sx={{ mb: 3 }}>
            <Chip label="KPI Values" color="primary" />
          </Divider>

          {/* KPI Values */}
          <Grid container spacing={3}>
            {kpis.map((kpi) => {
              const kpiValue = formData.kpiValues.find(kv => kv.kpiId === kpi.id);
              const currentValue = kpiValue?.value || 0;
              const trafficLightColor = getTrafficLightColor(kpi, currentValue);

              return (
                <Grid item xs={12} md={6} key={kpi.id}>
                  <Box 
                    sx={{ 
                      p: 2, 
                      border: '1px solid #e0e0e0', 
                      borderRadius: 2,
                      borderLeft: `4px solid ${trafficLightColor}`,
                      backgroundColor: '#fafafa'
                    }}
                  >
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {kpi.displayName}
                      </Typography>
                      <Chip 
                        label={kpi.calculationMethod} 
                        size="small" 
                        variant="outlined"
                        color={kpi.calculationMethod === 'AUTO_FTE' ? 'info' : 'default'}
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {kpi.helpText}
                    </Typography>

                    {kpi.calculationMethod === 'AUTO_FTE' ? (
                      <Alert severity="info" sx={{ mb: 2 }}>
                        This value is automatically calculated. No input required.
                      </Alert>
                    ) : (
                      <>
                        <TextField
                          fullWidth
                          type="number"
                          label={`${kpi.displayName} Value`}
                          value={currentValue}
                          onChange={(e) => handleKpiValueChange(kpi.id, 'value', e.target.value)}
                          InputProps={{
                            endAdornment: kpi.unit && (
                              <Typography variant="body2" color="text.secondary">
                                {kpi.unit}
                              </Typography>
                            )
                          }}
                          sx={{ mb: 2 }}
                        />

                        {kpi.targetValue && (
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            Target: {kpi.targetValue} {kpi.unit}
                            {currentValue !== kpi.targetValue && (
                              <span style={{ color: trafficLightColor, fontWeight: 'bold', marginLeft: 8 }}>
                                ({currentValue > kpi.targetValue ? '+' : ''}
                                {((currentValue - kpi.targetValue) / kpi.targetValue * 100).toFixed(1)}%)
                              </span>
                            )}
                          </Typography>
                        )}

                        <TextField
                          fullWidth
                          multiline
                          rows={2}
                          label="Comments"
                          value={kpiValue?.comments || ''}
                          onChange={(e) => handleKpiValueChange(kpi.id, 'comments', e.target.value)}
                          placeholder="Add comments about this KPI..."
                          size="small"
                        />
                      </>
                    )}
                  </Box>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          startIcon={loading ? <CircularProgress size={16} /> : <Save />}
          disabled={loading}
        >
          {loading ? 'Saving...' : (editingSubmission ? 'Update' : 'Create')} Submission
        </Button>
      </DialogActions>
    </Dialog>
  );
};
