import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      usernameField: 'email', // Using email instead of username
      passReqToCallback: true, // Enable access to request object
    });
  }

  async validate(req: any, email: string, password: string): Promise<any> {
    const clientIp = req.ip || req.connection.remoteAddress;
    const user = await this.authService.validateUser(email, password, clientIp);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    return user;
  }
}
