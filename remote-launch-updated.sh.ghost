#!/bin/bash

# EHRX Remote-Friendly Application Starter
# This script launches the application in a way that's friendly for remote connections

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting EHRX Application in Remote-Friendly Mode...${NC}"

# Launch the app in a detached tmux session
if command -v tmux &> /dev/null; then
    # Kill existing session if it exists
    tmux kill-session -t ehrx 2>/dev/null || true
    
    # Create new detached session
    echo -e "Creating tmux session for EHRX application..."
    tmux new-session -d -s ehrx "cd /var/www/ehrx && node launch.js; read -p 'Press enter to close this window'"
    
    echo -e "${GREEN}EHRX Application launched in detached tmux session!${NC}"
    echo -e "You can attach to view logs with:${YELLOW} tmux attach -t ehrx${NC}"
    echo -e "Your application will be available at: ${YELLOW}http://localhost:3080${NC}"
    echo -e "Status page available at: ${YELLOW}http://localhost:3030${NC}"
else
    # Fallback to nohup if tmux not available
    echo -e "${YELLOW}tmux not found, using nohup as fallback...${NC}"
    nohup node /var/www/ehrx/launch.js > /var/www/ehrx/app.log 2>&1 &
    
    echo -e "${GREEN}EHRX Application launched in background!${NC}"
    echo -e "You can view logs with:${YELLOW} tail -f /var/www/ehrx/app.log${NC}"
    echo -e "Your application will be available at: ${YELLOW}http://localhost:3080${NC}"
    echo -e "Status page available at: ${YELLOW}http://localhost:3030${NC}"
fi

# Open the preview in browser
echo -e "${GREEN}Opening browser preview...${NC}"
sleep 3
echo -e "Access your application now at: ${YELLOW}http://localhost:3080${NC}"
