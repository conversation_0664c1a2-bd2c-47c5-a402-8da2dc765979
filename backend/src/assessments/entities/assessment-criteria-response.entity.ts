import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AssessmentInstance } from './assessment-instance.entity';
import { AssessmentCriteria } from './assessment-criteria.entity';
import { AssessmentScoringScaleLevel } from './assessment-scoring-scale-level.entity';

@Entity('assessment_criteria_responses')
export class AssessmentCriteriaResponse {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'assessment_id' })
  assessmentId: number;

  @ManyToOne(() => AssessmentInstance, assessment => assessment.criteriaResponses)
  @JoinColumn({ name: 'assessment_id' })
  assessment: AssessmentInstance;

  @Column({ name: 'criteria_id' })
  criteriaId: number;

  @ManyToOne(() => AssessmentCriteria, criteria => criteria.responses)
  @JoinColumn({ name: 'criteria_id' })
  criteria: AssessmentCriteria;

  @Column({
    name: 'base_performance_score',
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0
  })
  basePerformanceScore: number;

  @Column({ name: 'scoring_scale_level_id' })
  scoringScaleLevelId: number;

  @ManyToOne(() => AssessmentScoringScaleLevel, level => level.criteriaResponses)
  @JoinColumn({ name: 'scoring_scale_level_id' })
  scoringScaleLevel: AssessmentScoringScaleLevel;

  @Column({ name: 'base_points' })
  basePoints: number;

  @Column({ name: 'manager_adjustment_points', default: 0 })
  managerAdjustmentPoints: number;

  @Column({
    name: 'final_score',
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0
  })
  finalScore: number;

  @Column({
    name: 'max_possible_score',
    type: 'decimal',
    precision: 5,
    scale: 2
  })
  maxPossibleScore: number;

  @Column({
    name: 'weight_applied',
    type: 'decimal',
    precision: 5,
    scale: 2
  })
  weightApplied: number;

  @Column({
    name: 'weighted_score',
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0
  })
  weightedScore: number;

  @Column({ name: 'evaluator_comments', nullable: true })
  evaluatorComments: string;

  @Column({ name: 'manager_adjustment_reason', nullable: true })
  managerAdjustmentReason: string;

  @Column({ name: 'employee_comments', nullable: true })
  employeeComments: string;

  @Column({ name: 'evidence_links', nullable: true })
  evidenceLinks: string; // JSON array of evidence/document links

  @Column({ name: 'improvement_suggestions', nullable: true })
  improvementSuggestions: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
