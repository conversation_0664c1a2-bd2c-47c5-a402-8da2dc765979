import { IsString, IsOptional, <PERSON>E<PERSON>, IsISO8601, Is<PERSON><PERSON><PERSON> } from 'class-validator';
import { ActionItemStatus, ActionItemPriority } from '../../entities/action-item.entity';

export class CreateActionItemDto {
  @IsString()
  title: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(ActionItemStatus)
  @IsOptional()
  status?: ActionItemStatus;

  @IsEnum(ActionItemPriority)
  @IsOptional()
  priority?: ActionItemPriority;

  @IsISO8601()
  @IsOptional()
  dueDate?: string;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;

  @IsNumber()
  @IsOptional()
  assessmentId?: number;
}
