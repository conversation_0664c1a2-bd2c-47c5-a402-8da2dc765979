import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as request from 'supertest';
import { ActionItemsModule } from '../action-items.module';
import { UsersModule } from '../../users/users.module';
import { ActionItem, ActionItemUpdate, ActionItemStatus, ActionItemPriority, ActionItemCategory } from '../entities/action-item.entity';
import { User, UserRole } from '../../users/entities/user.entity';

describe('Action Items Integration', () => {
  let app: INestApplication;
  let actionItemRepository: Repository<ActionItem>;
  let userRepository: Repository<User>;

  const hrAdmin = {
    userId: 1,
    role: UserRole.HR_ADMIN,
    email: '<EMAIL>'
  };

  const manager = {
    userId: 2,
    role: UserRole.MANAGER,
    email: '<EMAIL>'
  };

  const employee = {
    userId: 3,
    role: UserRole.EMPLOYEE,
    email: '<EMAIL>'
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [ActionItem, ActionItemUpdate, User],
          synchronize: true,
        }),
        ActionItemsModule,
        UsersModule,
      ],
    })
    .overrideGuard('JwtAuthGuard')
    .useValue({
      canActivate: (context) => {
        const request = context.switchToHttp().getRequest();
        request.user = request.headers['test-user'] === 'manager' ? manager : 
                      request.headers['test-user'] === 'employee' ? employee : hrAdmin;
        return true;
      },
    })
    .overrideGuard('RolesGuard')
    .useValue({
      canActivate: () => true,
    })
    .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    actionItemRepository = moduleFixture.get('ActionItemRepository');
    userRepository = moduleFixture.get('UserRepository');

    // Create test users
    await userRepository.save([
      { id: 1, email: '<EMAIL>', firstName: 'HR', lastName: 'Admin', role: UserRole.HR_ADMIN, is_active: true, password_hash: 'hash' },
      { id: 2, email: '<EMAIL>', firstName: 'Team', lastName: 'Manager', role: UserRole.MANAGER, is_active: true, password_hash: 'hash' },
      { id: 3, email: '<EMAIL>', firstName: 'Test', lastName: 'Employee', role: UserRole.EMPLOYEE, is_active: true, password_hash: 'hash' }
    ]);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Action Item CRUD Operations', () => {
    let actionItemId: number;

    it('should create a new action item', async () => {
      const actionItemData = {
        title: 'Improve Communication Skills',
        description: 'Work on improving verbal and written communication',
        priority: ActionItemPriority.HIGH,
        category: ActionItemCategory.SKILL_DEVELOPMENT,
        assignedToId: 3,
        dueDate: '2024-12-31',
        estimatedHours: 10,
        tags: ['communication', 'skills']
      };

      const response = await request(app.getHttpServer())
        .post('/action-items')
        .set('test-user', 'manager')
        .send(actionItemData)
        .expect(201);

      actionItemId = response.body.id;
      expect(response.body.title).toBe(actionItemData.title);
      expect(response.body.status).toBe(ActionItemStatus.OPEN);
      expect(response.body.assignedTo.id).toBe(3);
      expect(response.body.createdBy.id).toBe(2);
    });

    it('should get all action items with filters', async () => {
      const response = await request(app.getHttpServer())
        .get('/action-items?status=open&priority=high')
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body.items).toBeDefined();
      expect(response.body.total).toBeGreaterThan(0);
      expect(response.body.items[0].status).toBe(ActionItemStatus.OPEN);
      expect(response.body.items[0].priority).toBe(ActionItemPriority.HIGH);
    });

    it('should get a specific action item', async () => {
      const response = await request(app.getHttpServer())
        .get(`/action-items/${actionItemId}`)
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body.id).toBe(actionItemId);
      expect(response.body.title).toBe('Improve Communication Skills');
      expect(response.body.assignedTo).toBeDefined();
      expect(response.body.createdBy).toBeDefined();
    });

    it('should update an action item', async () => {
      const updateData = {
        title: 'Enhanced Communication Skills Development',
        progressPercentage: 25,
        notes: 'Started with online course'
      };

      const response = await request(app.getHttpServer())
        .patch(`/action-items/${actionItemId}`)
        .set('test-user', 'manager')
        .send(updateData)
        .expect(200);

      expect(response.body.title).toBe(updateData.title);
      expect(response.body.progressPercentage).toBe(25);
      expect(response.body.status).toBe(ActionItemStatus.IN_PROGRESS);
    });

    it('should update action item progress', async () => {
      const progressData = {
        progressPercentage: 50,
        comments: 'Completed first module',
        hoursLogged: 5
      };

      const response = await request(app.getHttpServer())
        .patch(`/action-items/${actionItemId}/progress`)
        .set('test-user', 'employee')
        .send(progressData)
        .expect(200);

      expect(response.body.progressPercentage).toBe(50);
      expect(response.body.actualHours).toBe(5);
    });

    it('should complete action item when progress reaches 100%', async () => {
      const progressData = {
        progressPercentage: 100,
        comments: 'All modules completed successfully'
      };

      const response = await request(app.getHttpServer())
        .patch(`/action-items/${actionItemId}/progress`)
        .set('test-user', 'employee')
        .send(progressData)
        .expect(200);

      expect(response.body.progressPercentage).toBe(100);
      expect(response.body.status).toBe(ActionItemStatus.COMPLETED);
      expect(response.body.completedDate).toBeDefined();
    });
  });

  describe('Action Item Permissions', () => {
    let restrictedActionItemId: number;

    beforeEach(async () => {
      // Create an action item assigned to employee
      const actionItem = await actionItemRepository.save({
        title: 'Test Permission Item',
        description: 'Testing permissions',
        assignedToId: 3,
        createdById: 2,
        status: ActionItemStatus.OPEN,
        priority: ActionItemPriority.MEDIUM,
        category: ActionItemCategory.OTHER,
        progressPercentage: 0
      });
      restrictedActionItemId = actionItem.id;
    });

    it('should allow assigned user to update progress', async () => {
      const progressData = { progressPercentage: 30, comments: 'Making progress' };

      await request(app.getHttpServer())
        .patch(`/action-items/${restrictedActionItemId}/progress`)
        .set('test-user', 'employee')
        .send(progressData)
        .expect(200);
    });

    it('should allow manager to update action item', async () => {
      const updateData = { title: 'Updated by Manager' };

      await request(app.getHttpServer())
        .patch(`/action-items/${restrictedActionItemId}`)
        .set('test-user', 'manager')
        .send(updateData)
        .expect(200);
    });

    it('should allow HR admin to delete action item', async () => {
      await request(app.getHttpServer())
        .delete(`/action-items/${restrictedActionItemId}`)
        .expect(200);
    });
  });

  describe('Action Item Statistics', () => {
    beforeEach(async () => {
      // Create multiple action items for statistics testing
      await actionItemRepository.save([
        {
          title: 'Stats Item 1',
          description: 'For statistics',
          assignedToId: 3,
          createdById: 2,
          status: ActionItemStatus.OPEN,
          priority: ActionItemPriority.HIGH,
          category: ActionItemCategory.PERFORMANCE_IMPROVEMENT,
          progressPercentage: 0
        },
        {
          title: 'Stats Item 2',
          description: 'For statistics',
          assignedToId: 3,
          createdById: 2,
          status: ActionItemStatus.COMPLETED,
          priority: ActionItemPriority.MEDIUM,
          category: ActionItemCategory.SKILL_DEVELOPMENT,
          progressPercentage: 100,
          completedDate: new Date()
        },
        {
          title: 'Stats Item 3',
          description: 'For statistics',
          assignedToId: 3,
          createdById: 2,
          status: ActionItemStatus.OVERDUE,
          priority: ActionItemPriority.CRITICAL,
          category: ActionItemCategory.COMPLIANCE,
          progressPercentage: 25,
          dueDate: new Date('2023-01-01') // Past date
        }
      ]);
    });

    it('should return action item statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/action-items/statistics')
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body).toHaveProperty('totalItems');
      expect(response.body).toHaveProperty('byStatus');
      expect(response.body).toHaveProperty('byPriority');
      expect(response.body).toHaveProperty('byCategory');
      expect(response.body).toHaveProperty('overdueItems');
      expect(response.body).toHaveProperty('completedThisMonth');

      expect(typeof response.body.totalItems).toBe('number');
      expect(response.body.byStatus).toHaveProperty(ActionItemStatus.OPEN);
      expect(response.body.byStatus).toHaveProperty(ActionItemStatus.COMPLETED);
      expect(response.body.byPriority).toHaveProperty(ActionItemPriority.HIGH);
      expect(response.body.byCategory).toHaveProperty(ActionItemCategory.PERFORMANCE_IMPROVEMENT);
    });

    it('should filter statistics by user role', async () => {
      const employeeResponse = await request(app.getHttpServer())
        .get('/action-items/statistics')
        .set('test-user', 'employee')
        .expect(200);

      const managerResponse = await request(app.getHttpServer())
        .get('/action-items/statistics')
        .set('test-user', 'manager')
        .expect(200);

      // Employee should see only their items, manager should see more
      expect(employeeResponse.body.totalItems).toBeLessThanOrEqual(managerResponse.body.totalItems);
    });
  });

  describe('Bulk Operations', () => {
    let bulkActionItemIds: number[];

    beforeEach(async () => {
      // Create multiple action items for bulk operations
      const items = await actionItemRepository.save([
        {
          title: 'Bulk Item 1',
          description: 'For bulk operations',
          assignedToId: 3,
          createdById: 2,
          status: ActionItemStatus.OPEN,
          priority: ActionItemPriority.MEDIUM,
          category: ActionItemCategory.OTHER,
          progressPercentage: 0
        },
        {
          title: 'Bulk Item 2',
          description: 'For bulk operations',
          assignedToId: 3,
          createdById: 2,
          status: ActionItemStatus.OPEN,
          priority: ActionItemPriority.MEDIUM,
          category: ActionItemCategory.OTHER,
          progressPercentage: 0
        }
      ]);
      bulkActionItemIds = items.map(item => item.id);
    });

    it('should perform bulk status update', async () => {
      const bulkUpdateData = {
        actionItemIds: bulkActionItemIds,
        status: ActionItemStatus.IN_PROGRESS,
        comments: 'Bulk status update'
      };

      const response = await request(app.getHttpServer())
        .patch('/action-items/bulk/update')
        .set('test-user', 'manager')
        .send(bulkUpdateData)
        .expect(200);

      expect(response.body.updated).toBeDefined();
      expect(response.body.updated.length).toBe(2);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors.length).toBe(0);

      response.body.updated.forEach(item => {
        expect(item.status).toBe(ActionItemStatus.IN_PROGRESS);
      });
    });

    it('should handle bulk update with some errors', async () => {
      const bulkUpdateData = {
        actionItemIds: [...bulkActionItemIds, 99999], // Include non-existent ID
        status: ActionItemStatus.COMPLETED
      };

      const response = await request(app.getHttpServer())
        .patch('/action-items/bulk/update')
        .set('test-user', 'manager')
        .send(bulkUpdateData)
        .expect(200);

      expect(response.body.updated.length).toBe(2);
      expect(response.body.errors.length).toBe(1);
      expect(response.body.errors[0].id).toBe(99999);
    });
  });

  describe('My Action Items', () => {
    beforeEach(async () => {
      // Create action items assigned to different users
      await actionItemRepository.save([
        {
          title: 'Employee Item',
          description: 'Assigned to employee',
          assignedToId: 3,
          createdById: 2,
          status: ActionItemStatus.OPEN,
          priority: ActionItemPriority.MEDIUM,
          category: ActionItemCategory.OTHER,
          progressPercentage: 0
        },
        {
          title: 'Manager Item',
          description: 'Assigned to manager',
          assignedToId: 2,
          createdById: 1,
          status: ActionItemStatus.IN_PROGRESS,
          priority: ActionItemPriority.HIGH,
          category: ActionItemCategory.OTHER,
          progressPercentage: 50
        }
      ]);
    });

    it('should return only items assigned to the current user', async () => {
      const response = await request(app.getHttpServer())
        .get('/action-items/my-items')
        .set('test-user', 'employee')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(item => {
        expect(item.assignedTo.id).toBe(3);
      });
    });

    it('should filter my items by status', async () => {
      const response = await request(app.getHttpServer())
        .get('/action-items/my-items?status=open')
        .set('test-user', 'employee')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(item => {
        expect(item.status).toBe(ActionItemStatus.OPEN);
        expect(item.assignedTo.id).toBe(3);
      });
    });
  });

  describe('Automated Tasks', () => {
    beforeEach(async () => {
      // Create overdue action items
      await actionItemRepository.save([
        {
          title: 'Overdue Item',
          description: 'Should be marked as overdue',
          assignedToId: 3,
          createdById: 2,
          status: ActionItemStatus.IN_PROGRESS,
          priority: ActionItemPriority.MEDIUM,
          category: ActionItemCategory.OTHER,
          progressPercentage: 25,
          dueDate: new Date('2023-01-01') // Past date
        }
      ]);
    });

    it('should mark overdue items', async () => {
      const response = await request(app.getHttpServer())
        .post('/action-items/mark-overdue')
        .expect(201);

      expect(typeof response.body).toBe('number');
      expect(response.body).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent action item', async () => {
      await request(app.getHttpServer())
        .get('/action-items/99999')
        .set('test-user', 'manager')
        .expect(404);
    });

    it('should validate required fields', async () => {
      const invalidData = {
        title: '', // Empty title
        description: '',
        assignedToId: 999 // Non-existent user
      };

      await request(app.getHttpServer())
        .post('/action-items')
        .set('test-user', 'manager')
        .send(invalidData)
        .expect(400);
    });

    it('should validate progress percentage range', async () => {
      const actionItem = await actionItemRepository.save({
        title: 'Test Item',
        description: 'For validation testing',
        assignedToId: 3,
        createdById: 2,
        status: ActionItemStatus.OPEN,
        priority: ActionItemPriority.MEDIUM,
        category: ActionItemCategory.OTHER,
        progressPercentage: 0
      });

      const invalidProgressData = {
        progressPercentage: 150 // Invalid percentage
      };

      await request(app.getHttpServer())
        .patch(`/action-items/${actionItem.id}/progress`)
        .set('test-user', 'employee')
        .send(invalidProgressData)
        .expect(400);
    });
  });
});
