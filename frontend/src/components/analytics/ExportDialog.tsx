import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  FormGroup,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Divider,
  Chip,
} from '@mui/material';
import {
  GetApp as DownloadIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
  Slideshow as PptIcon,
} from '@mui/icons-material';
import { ApiService } from '../../services/api';

interface ExportDialogProps {
  open: boolean;
  onClose: () => void;
  dashboardData?: any;
}

interface ExportOptions {
  format: 'csv' | 'pdf' | 'ppt';
  includeCharts: boolean;
  includeData: boolean;
  includeInsights: boolean;
  dateRange: string;
  widgets: string[];
}

const ExportDialog: React.FC<ExportDialogProps> = ({ open, onClose, dashboardData }) => {
  const [options, setOptions] = useState<ExportOptions>({
    format: 'csv',
    includeCharts: true,
    includeData: true,
    includeInsights: true,
    dateRange: '3months',
    widgets: ['performance_overview', 'engagement_trends', 'attrition_risk']
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const availableWidgets = [
    { id: 'performance_overview', label: 'Performance Overview', icon: '📊' },
    { id: 'engagement_trends', label: 'Engagement Trends', icon: '📈' },
    { id: 'attrition_risk', label: 'Attrition Risk Analysis', icon: '⚠️' },
    { id: 'team_comparison', label: 'Team Comparison', icon: '👥' },
    { id: 'recognition_feed', label: 'Recognition Feed', icon: '🏆' },
    { id: 'team_performance', label: 'Team Performance', icon: '📋' },
  ];

  const handleExport = async () => {
    try {
      setLoading(true);
      setError(null);

      // 🔐 NIS2-COMPLIANT: Secure export with audit logging
      const exportData = await generateExportData();
      
      switch (options.format) {
        case 'csv':
          await exportToCSV(exportData);
          break;
        case 'pdf':
          await exportToPDF(exportData);
          break;
        case 'ppt':
          await exportToPPT(exportData);
          break;
      }

      onClose();
    } catch (err) {
      setError('Export failed. Please try again.');
      console.error('Export error:', err);
    } finally {
      setLoading(false);
    }
  };

  const generateExportData = async () => {
    const data: any = {
      metadata: {
        exportDate: new Date().toISOString(),
        dateRange: options.dateRange,
        widgets: options.widgets,
        format: options.format
      },
      widgets: {}
    };

    // Fetch data for selected widgets
    for (const widgetId of options.widgets) {
      try {
        switch (widgetId) {
          case 'performance_overview':
            data.widgets.performance = await ApiService.getPerformanceAnalytics({
              period: options.dateRange
            });
            break;
          case 'engagement_trends':
            data.widgets.engagement = await ApiService.getEngagementTrends(options.dateRange);
            break;
          case 'attrition_risk':
            data.widgets.attrition = await ApiService.getAttritionRiskAnalysis();
            break;
          case 'team_comparison':
            data.widgets.teams = await ApiService.getTeamPerformanceComparison({
              period: options.dateRange
            });
            break;
          default:
            console.warn(`Widget ${widgetId} not implemented for export`);
        }
      } catch (err) {
        console.error(`Failed to fetch data for widget ${widgetId}:`, err);
      }
    }

    return data;
  };

  const exportToCSV = async (data: any) => {
    let csvContent = "data:text/csv;charset=utf-8,";
    
    // Add metadata
    csvContent += "EHRX Analytics Export\n";
    csvContent += `Export Date,${data.metadata.exportDate}\n`;
    csvContent += `Date Range,${data.metadata.dateRange}\n\n`;

    // Performance data
    if (data.widgets.performance && options.includeData) {
      csvContent += "PERFORMANCE ANALYTICS\n";
      csvContent += "Department,Average Score,Employee Count\n";
      
      data.widgets.performance.data?.departmentAverages?.forEach((dept: any) => {
        csvContent += `${dept.department},${dept.averageScore},${dept.employeeCount}\n`;
      });
      csvContent += "\n";
    }

    // Engagement data
    if (data.widgets.engagement && options.includeData) {
      csvContent += "ENGAGEMENT TRENDS\n";
      csvContent += "Period,Score,Date\n";
      
      data.widgets.engagement.data?.chartData?.forEach((point: any) => {
        csvContent += `${point.period},${point.score},${point.date}\n`;
      });
      csvContent += "\n";
    }

    // Attrition risk data
    if (data.widgets.attrition && options.includeData) {
      csvContent += "ATTRITION RISK ANALYSIS\n";
      csvContent += "Risk Level,Count\n";
      
      const riskData = data.widgets.attrition.data?.riskDistribution;
      if (riskData) {
        Object.entries(riskData).forEach(([level, count]) => {
          csvContent += `${level},${count}\n`;
        });
      }
      csvContent += "\n";
    }

    // Team comparison data
    if (data.widgets.teams && options.includeData) {
      csvContent += "TEAM PERFORMANCE COMPARISON\n";
      csvContent += "Team,Members,Avg Score,vs Company,vs Industry,Participation\n";
      
      data.widgets.teams.data?.teams?.forEach((team: any) => {
        csvContent += `${team.teamName},${team.totalMembers},${team.avgScore},${team.vsCompanyAverage},${team.vsIndustryBenchmark},${team.participationRate}\n`;
      });
    }

    // Create and download file
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `ehrx-analytics-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToPDF = async (data: any) => {
    // For now, create a simple text-based PDF export
    // In production, use a proper PDF library like jsPDF
    const content = JSON.stringify(data, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `ehrx-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportToPPT = async (data: any) => {
    // For now, create a JSON export that could be used with PowerPoint templates
    // In production, integrate with PowerPoint API or use a library like PptxGenJS
    const content = JSON.stringify(data, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `ehrx-analytics-presentation-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleWidgetToggle = (widgetId: string) => {
    setOptions(prev => ({
      ...prev,
      widgets: prev.widgets.includes(widgetId)
        ? prev.widgets.filter(id => id !== widgetId)
        : [...prev.widgets, widgetId]
    }));
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <DownloadIcon sx={{ mr: 1 }} />
          Export Analytics Dashboard
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box mb={3}>
          <FormControl component="fieldset">
            <FormLabel component="legend">Export Format</FormLabel>
            <RadioGroup
              value={options.format}
              onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as any }))}
              row
            >
              <FormControlLabel
                value="csv"
                control={<Radio />}
                label={
                  <Box display="flex" alignItems="center">
                    <CsvIcon sx={{ mr: 1 }} />
                    CSV (Data Tables)
                  </Box>
                }
              />
              <FormControlLabel
                value="pdf"
                control={<Radio />}
                label={
                  <Box display="flex" alignItems="center">
                    <PdfIcon sx={{ mr: 1 }} />
                    PDF (Report)
                  </Box>
                }
              />
              <FormControlLabel
                value="ppt"
                control={<Radio />}
                label={
                  <Box display="flex" alignItems="center">
                    <PptIcon sx={{ mr: 1 }} />
                    PowerPoint (Presentation)
                  </Box>
                }
              />
            </RadioGroup>
          </FormControl>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box mb={3}>
          <Typography variant="subtitle1" gutterBottom>
            Select Widgets to Export
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            {availableWidgets.map((widget) => (
              <Chip
                key={widget.id}
                label={`${widget.icon} ${widget.label}`}
                variant={options.widgets.includes(widget.id) ? "filled" : "outlined"}
                color={options.widgets.includes(widget.id) ? "primary" : "default"}
                onClick={() => handleWidgetToggle(widget.id)}
                clickable
              />
            ))}
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box mb={3}>
          <FormControl component="fieldset">
            <FormLabel component="legend">Export Options</FormLabel>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={options.includeData}
                    onChange={(e) => setOptions(prev => ({ ...prev, includeData: e.target.checked }))}
                  />
                }
                label="Include raw data"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={options.includeCharts}
                    onChange={(e) => setOptions(prev => ({ ...prev, includeCharts: e.target.checked }))}
                  />
                }
                label="Include chart data"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={options.includeInsights}
                    onChange={(e) => setOptions(prev => ({ ...prev, includeInsights: e.target.checked }))}
                  />
                }
                label="Include AI insights and recommendations"
              />
            </FormGroup>
          </FormControl>
        </Box>

        <Alert severity="info">
          <Typography variant="body2">
            <strong>Note:</strong> Exported data will include analytics for the selected time period and widgets. 
            All exports are logged for security compliance.
          </Typography>
        </Alert>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleExport}
          variant="contained"
          disabled={loading || options.widgets.length === 0}
          startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
        >
          {loading ? 'Exporting...' : 'Export'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExportDialog;
