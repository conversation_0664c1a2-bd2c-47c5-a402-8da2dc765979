import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { RecognitionInstance } from './recognition-instance.entity';

export enum BadgeType {
  ACHIEVEMENT = 'achievement',
  APPRECIATION = 'appreciation',
  MILESTONE = 'milestone',
  SKILL = 'skill',
}

@Entity('recognition_badges')
export class RecognitionBadge {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'icon_url', length: 500, nullable: true })
  iconUrl: string;

  @Column({
    name: 'badge_type',
    type: 'enum',
    enum: BadgeType,
  })
  badgeType: BadgeType;

  @Column({ name: 'point_value', default: 0 })
  pointValue: number;

  @Column({ type: 'json', nullable: true })
  criteria: object;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @OneToMany(() => RecognitionInstance, instance => instance.badge)
  recognitionInstances: RecognitionInstance[];
}
