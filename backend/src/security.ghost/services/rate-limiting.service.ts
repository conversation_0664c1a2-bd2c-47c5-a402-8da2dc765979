import { Injectable } from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';

/**
 * 🔐 NIS2-Compliant Rate Limiting Service
 * 
 * Implements advanced rate limiting strategies:
 * - Authentication endpoint protection
 * - API endpoint throttling
 * - User-specific rate limits
 * - IP-based blocking
 * - Adaptive rate limiting based on threat level
 */
@Injectable()
export class RateLimitingService {
  private readonly suspiciousIPs = new Map<string, number>();
  private readonly userAttempts = new Map<string, number>();

  /**
   * 🔐 Check if IP should be blocked due to suspicious activity
   */
  isIPBlocked(ip: string): boolean {
    const attempts = this.suspiciousIPs.get(ip) || 0;
    return attempts >= 50; // Block after 50 failed attempts
  }

  /**
   * 🔐 Record failed authentication attempt
   */
  recordFailedAttempt(ip: string, userId?: string): void {
    // Track IP-based attempts
    const ipAttempts = this.suspiciousIPs.get(ip) || 0;
    this.suspiciousIPs.set(ip, ipAttempts + 1);

    // Track user-based attempts if user ID available
    if (userId) {
      const userAttempts = this.userAttempts.get(userId) || 0;
      this.userAttempts.set(userId, userAttempts + 1);
    }

    // Log security event
    console.log('🔐 [SECURITY-AUDIT] Failed authentication attempt:', {
      ip,
      userId,
      ipAttempts: ipAttempts + 1,
      userAttempts: userId ? (this.userAttempts.get(userId) || 0) : 0,
      timestamp: new Date().toISOString(),
    });

    // Auto-cleanup old entries (simple implementation)
    if (this.suspiciousIPs.size > 10000) {
      this.cleanupOldEntries();
    }
  }

  /**
   * 🔐 Record successful authentication (reset counters)
   */
  recordSuccessfulAttempt(ip: string, userId: string): void {
    this.suspiciousIPs.delete(ip);
    this.userAttempts.delete(userId);

    console.log('🔐 [SECURITY-AUDIT] Successful authentication:', {
      ip,
      userId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 🔐 Get current threat level for IP
   */
  getThreatLevel(ip: string): 'low' | 'medium' | 'high' | 'critical' {
    const attempts = this.suspiciousIPs.get(ip) || 0;
    
    if (attempts >= 50) return 'critical';
    if (attempts >= 20) return 'high';
    if (attempts >= 10) return 'medium';
    return 'low';
  }

  /**
   * 🔐 Apply adaptive rate limiting based on threat level
   */
  getAdaptiveRateLimit(ip: string): { ttl: number; limit: number } {
    const threatLevel = this.getThreatLevel(ip);
    
    switch (threatLevel) {
      case 'critical':
        return { ttl: 3600000, limit: 1 }; // 1 request per hour
      case 'high':
        return { ttl: 600000, limit: 5 }; // 5 requests per 10 minutes
      case 'medium':
        return { ttl: 300000, limit: 10 }; // 10 requests per 5 minutes
      default:
        return { ttl: 60000, limit: 20 }; // 20 requests per minute
    }
  }

  /**
   * 🔐 Clean up old entries to prevent memory leaks
   */
  private cleanupOldEntries(): void {
    // Simple cleanup - remove half of the entries
    // In production, implement time-based cleanup
    const ipsToRemove = Array.from(this.suspiciousIPs.keys()).slice(0, 5000);
    ipsToRemove.forEach(ip => this.suspiciousIPs.delete(ip));
    
    const usersToRemove = Array.from(this.userAttempts.keys()).slice(0, 5000);
    usersToRemove.forEach(user => this.userAttempts.delete(user));
  }
}
