/**
 * Types for the reporting and export functionality
 */

export interface PerformanceDistribution {
  excellent: number;
  good: number; 
  satisfactory: number;
  needsImprovement: number;
  poor: number;
}

export interface StatusBreakdown {
  draft: number;
  inProgress: number;
  completed: number;
  approved: number;
  rejected: number;
}

export interface EmployeeReport {
  employeeId: number;
  assessmentCount: number;
  averageScore: number;
  scoreHistory: Array<{
    id: number;
    date: string;
    score: number;
    status: string;
  }>;
  latestAssessment: {
    id: number;
    date: string;
    score: number;
    status: string;
  } | null;
}

export interface TeamReport {
  teamId: number;
  assessmentCount: number;
  averageScore: number;
  employeeScores: Array<{
    employeeId: number;
    averageScore: number;
    assessmentCount: number;
  }>;
  performanceDistribution: PerformanceDistribution;
}

export interface OrganizationReport {
  assessmentCount: number;
  averageScore: number;
  completionRate: number;
  statusBreakdown: StatusBreakdown;
  performanceDistribution: PerformanceDistribution;
}

export interface DashboardMetrics {
  totalAssessments: number;
  pendingAssessments: number;
  completedAssessments: number;
  averageScore: number;
  recentAssessments: Array<{
    id: number;
    employeeId: number;
    status: string;
    score: number;
    date: string;
  }>;
  performanceDistribution: PerformanceDistribution;
  statusBreakdown: StatusBreakdown;
}

export interface PerformanceTrend {
  period: string;
  averageScore: number;
  completionRate: number;
  assessmentCount: number;
}

export interface TeamBenchmark {
  teamId: number;
  teamName: string;
  teamSize: number;
  averageScore: number;
  completionRate: number;
  assessmentCount: number;
}

export interface AssessmentExportFilters {
  startDate?: string;
  endDate?: string;
  employeeIds?: number[];
  teamIds?: number[];
  statuses?: string[];
}
