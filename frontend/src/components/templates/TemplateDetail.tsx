import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { AssessmentTemplate, AssessmentArea, UserRole, ScoringRule, RuleType } from '../../types';
import { ApiService } from '../../services/api';

interface TemplateDetailProps {
  userRole: UserRole;
}

const TemplateDetail: React.FC<TemplateDetailProps> = ({ userRole }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [template, setTemplate] = useState<AssessmentTemplate | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        setIsLoading(true);

        // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
        const response = await ApiService.getAssessmentTemplate(parseInt(id!));
        const template = response.data || response;

        if (template) {
          setTemplate(template);
        } else {
          setError('Template not found');
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching template:', err);
        setError('Failed to load template details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplate();
  }, [id]);

  const handleDeleteTemplate = async () => {
    if (!template?.id) return;

    if (window.confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      try {
        // In a real implementation, this would be an API call
        // await api.delete(`/templates/${template.id}`);

        // Mock success and redirect
        setTimeout(() => {
          navigate('/templates');
        }, 300);
      } catch (err) {
        console.error('Error deleting template:', err);
        alert('Failed to delete template. Please try again.');
      }
    }
  };

  const handleToggleStatus = async () => {
    if (!template?.id) return;

    try {
      // In a real implementation, this would be an API call
      // await api.patch(`/templates/${template.id}`, { isActive: !template.isActive });

      // Mock success and update local state
      setTemplate({
        ...template,
        isActive: !template.isActive
      });
    } catch (err) {
      console.error('Error updating template status:', err);
      alert('Failed to update template status. Please try again.');
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading template details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !template) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p>{error || 'Template not found'}</p>
        <Link
          to="/templates"
          className="mt-2 inline-block bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
        >
          Back to Templates
        </Link>
      </div>
    );
  }

  // Calculate total weight to check if the weights sum to 1
  const totalWeight = template.areas.reduce((sum, area) => sum + area.weight, 0);
  const isWeightValid = Math.abs(totalWeight - 1) < 0.001; // Allow a small margin for floating point errors

  return (
    <div className="p-6">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2">
            <h2 className="text-2xl font-bold text-gray-800">{template.name}</h2>
            {template.isGlobal && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Global Template
              </span>
            )}
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${template.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
              {template.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          <p className="text-sm text-gray-500 mt-1">Version {template.version} • Last updated {new Date(template.updatedAt || '').toLocaleDateString()}</p>
        </div>

        {/* Actions */}
        {(userRole === UserRole.HR_ADMIN || (userRole === UserRole.MANAGER && !template.isGlobal)) && (
          <div className="flex gap-2">
            <button
              onClick={handleToggleStatus}
              className={`py-2 px-4 rounded-lg text-white ${template.isActive ? 'bg-amber-500 hover:bg-amber-600' : 'bg-green-500 hover:bg-green-600'
                }`}
            >
              {template.isActive ? 'Deactivate' : 'Activate'}
            </button>

            <Link
              to={`/templates/${template.id}/edit`}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg"
            >
              Edit Template
            </Link>

            <button
              onClick={handleDeleteTemplate}
              className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg"
            >
              Delete
            </button>
          </div>
        )}
      </div>

      {/* Description card */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Description</h3>
        <p className="text-gray-600">{template.description || 'No description provided'}</p>

        {!isWeightValid && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-yellow-700 font-medium">Warning: Area weights don't sum to 1.0</p>
            <p className="text-yellow-600">Current sum: {totalWeight.toFixed(2)}</p>
          </div>
        )}
      </div>

      {/* Assessment Areas */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">Assessment Areas</h3>

        <div className="space-y-6">
          {template.areas.map((area) => (
            <div key={area.id} className="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-medium text-gray-800">{area.name}</h4>
                  <p className="text-sm text-gray-600 mb-1">{area.description}</p>
                </div>
                <div className="text-right">
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                    Weight: {(area.weight * 100).toFixed(0)}%
                  </span>
                  <p className="text-xs text-gray-500 mt-1">Max Score: {area.maxScore}</p>
                </div>
              </div>

              {/* Scoring rules */}
              {area.scoringRules && area.scoringRules.length > 0 && (
                <div className="mt-3 pl-4 border-l-2 border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Scoring Rules:</h5>
                  <ul className="space-y-2">
                    {area.scoringRules.map((rule, ruleIndex) => (
                      <li key={rule.id || ruleIndex} className="text-sm">
                        {rule.ruleType === 'threshold' && (
                          <span>
                            {rule.scoreAdjustment > 0 ? 'Bonus' : 'Penalty'} of{' '}
                            <strong className={rule.scoreAdjustment > 0 ? 'text-green-600' : 'text-red-600'}>
                              {rule.scoreAdjustment > 0 ? '+' : ''}{rule.scoreAdjustment}
                            </strong>{' '}
                            points when score is {(rule.threshold || 0) < 50 ? 'below' : 'above'} {rule.threshold || 0}
                            {rule.description && ` - ${rule.description}`}
                          </span>
                        )}
                        {rule.ruleType === 'addition' && (
                          <span>
                            Fixed adjustment of{' '}
                            <strong className={rule.scoreAdjustment > 0 ? 'text-green-600' : 'text-red-600'}>
                              {rule.scoreAdjustment > 0 ? '+' : ''}{rule.scoreAdjustment}
                            </strong>{' '}
                            points
                            {rule.description && ` - ${rule.description}`}
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Example questions/metrics */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">Usage</h3>

        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-1">
            <h4 className="font-medium text-gray-700 mb-2">Used in {Math.floor(Math.random() * 20) + 1} assessments</h4>
            <Link
              to="/assessments/create"
              className="inline-flex items-center text-blue-600 hover:text-blue-800"
            >
              <span className="material-icons mr-1" style={{ fontSize: '18px' }}>add_circle</span>
              Create New Assessment
            </Link>
          </div>

          <div className="flex-1">
            <h4 className="font-medium text-gray-700 mb-2">Permissions</h4>
            <p className="text-sm text-gray-600">
              {template.isGlobal
                ? 'This is a global template available to all managers.'
                : 'This template is only available to you and your team.'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateDetail;
