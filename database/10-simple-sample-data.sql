-- Simple Sample Monthly Dashboard Data for July 2025
-- This creates realistic sample data using existing teams

USE ehrx;

-- Clean up any existing July 2025 data first
DELETE FROM monthly_dashboard_kpi_values WHERE submission_id IN (
  SELECT id FROM monthly_dashboard_submissions WHERE submission_month = 7 AND submission_year = 2025
);
DELETE FROM monthly_dashboard_submissions WHERE submission_month = 7 AND submission_year = 2025;

-- Get some existing team IDs to work with
SET @team1 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Frontend%' LIMIT 1);
SET @team2 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Backend%' LIMIT 1);
SET @team3 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%DevOps%' LIMIT 1);
SET @team4 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Quality%' LIMIT 1);
SET @team5 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Data%' LIMIT 1);
SET @team6 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Security%' LIMIT 1);
SET @team7 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Infrastructure%' LIMIT 1);
SET @team8 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Support%' LIMIT 1);
SET @team9 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Mobile%' LIMIT 1);
SET @team10 = (SELECT id FROM organizational_units WHERE type = 'team' AND name LIKE '%Cloud%' LIMIT 1);

-- Use actual user IDs from the database
SET @user1 = 5;  -- <EMAIL>
SET @user2 = 6;  -- <EMAIL>
SET @user3 = 9;  -- <EMAIL>
SET @user4 = 3;  -- <EMAIL>
SET @user5 = 8;  -- <EMAIL>

-- Create monthly dashboard submissions for July 2025
INSERT INTO monthly_dashboard_submissions (
  organizational_unit_id, 
  submitted_by_user_id, 
  submission_month, 
  submission_year, 
  status, 
  completion_date,
  notes
) VALUES
(@team1, @user1, 7, 2025, 'submitted', '2025-07-14', 'July 2025 dashboard - Frontend Team'),
(@team2, @user2, 7, 2025, 'approved', '2025-07-13', 'July 2025 dashboard - Backend Team'),
(@team3, @user3, 7, 2025, 'submitted', '2025-07-15', 'July 2025 dashboard - DevOps Team'),
(@team4, @user4, 7, 2025, 'approved', '2025-07-12', 'July 2025 dashboard - QA Team'),
(@team5, @user5, 7, 2025, 'submitted', '2025-07-16', 'July 2025 dashboard - Data Team'),
(@team6, @user1, 7, 2025, 'submitted', '2025-07-14', 'July 2025 dashboard - Security Team'),
(@team7, @user2, 7, 2025, 'approved', '2025-07-11', 'July 2025 dashboard - Infrastructure Team'),
(@team8, @user3, 7, 2025, 'submitted', '2025-07-17', 'July 2025 dashboard - Support Team'),
(@team9, @user4, 7, 2025, 'submitted', '2025-07-13', 'July 2025 dashboard - Mobile Team'),
(@team10, @user5, 7, 2025, 'approved', '2025-07-10', 'July 2025 dashboard - Cloud Team');

-- Get submission IDs
SET @sub1 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team1 AND submission_month = 7 AND submission_year = 2025);
SET @sub2 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team2 AND submission_month = 7 AND submission_year = 2025);
SET @sub3 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team3 AND submission_month = 7 AND submission_year = 2025);
SET @sub4 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team4 AND submission_month = 7 AND submission_year = 2025);
SET @sub5 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team5 AND submission_month = 7 AND submission_year = 2025);
SET @sub6 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team6 AND submission_month = 7 AND submission_year = 2025);
SET @sub7 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team7 AND submission_month = 7 AND submission_year = 2025);
SET @sub8 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team8 AND submission_month = 7 AND submission_year = 2025);
SET @sub9 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team9 AND submission_month = 7 AND submission_year = 2025);
SET @sub10 = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = @team10 AND submission_month = 7 AND submission_year = 2025);

-- Use actual KPI IDs from the database
SET @kpi_fte = 1;           -- FTE
SET @kpi_attrition = 3;     -- ATTRITION
SET @kpi_sla = 4;           -- SLA
SET @kpi_utilization = 5;   -- UTILIZATION
SET @kpi_time_reg = 6;      -- TIME_REGISTRATION
SET @kpi_compliance = 7;    -- COMPLIANCE
SET @kpi_ab_financial = 8;  -- AB (AB Financial Results)
SET @kpi_pto = 9;           -- PTO
SET @kpi_rto = 10;          -- RTO

-- Insert realistic KPI values for all teams
INSERT INTO monthly_dashboard_kpi_values (submission_id, kpi_id, value, target_value, traffic_light_status, notes) VALUES

-- Frontend Team (@sub1) - Good performance
(@sub1, @kpi_fte, 12, 12, 'green', 'Team at target size'),
(@sub1, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@sub1, @kpi_sla, 97.5, 95.0, 'green', 'Exceeded SLA target'),
(@sub1, @kpi_utilization, 85.0, 80.0, 'green', 'Good utilization'),
(@sub1, @kpi_time_reg, 96.0, 95.0, 'green', 'Good time registration'),
(@sub1, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@sub1, @kpi_ab_financial, -1200.00, 0, 'green', 'Under budget'),
(@sub1, @kpi_pto, 15, 25, 'green', '15 PTO days taken'),
(@sub1, @kpi_rto, 88.0, 80.0, 'green', 'Good office attendance'),

-- Backend Team (@sub2) - Excellent performance
(@sub2, @kpi_fte, 15, 15, 'green', 'Team at target size'),
(@sub2, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@sub2, @kpi_sla, 99.1, 95.0, 'green', 'Excellent SLA'),
(@sub2, @kpi_utilization, 92.0, 80.0, 'green', 'High utilization'),
(@sub2, @kpi_time_reg, 98.0, 95.0, 'green', 'Excellent time registration'),
(@sub2, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@sub2, @kpi_ab_financial, -2500.00, 0, 'green', 'Well under budget'),
(@sub2, @kpi_pto, 8, 25, 'green', '8 PTO days taken'),
(@sub2, @kpi_rto, 95.0, 80.0, 'green', 'Excellent attendance'),

-- DevOps Team (@sub3) - Mixed performance
(@sub3, @kpi_fte, 8, 8, 'green', 'Team at target size'),
(@sub3, @kpi_attrition, 1, 0, 'yellow', '1 resignation this month'),
(@sub3, @kpi_sla, 94.2, 95.0, 'yellow', 'Slightly below SLA'),
(@sub3, @kpi_utilization, 78.0, 80.0, 'yellow', 'Below utilization target'),
(@sub3, @kpi_time_reg, 93.0, 95.0, 'yellow', 'Below time reg target'),
(@sub3, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@sub3, @kpi_ab_financial, 800.00, 0, 'yellow', 'Slightly over budget'),
(@sub3, @kpi_pto, 20, 25, 'green', '20 PTO days taken'),
(@sub3, @kpi_rto, 75.0, 80.0, 'yellow', 'Below attendance target'),

-- QA Team (@sub4) - Some issues
(@sub4, @kpi_fte, 10, 10, 'green', 'Team at target size'),
(@sub4, @kpi_attrition, 2, 0, 'red', '2 resignations this month'),
(@sub4, @kpi_sla, 91.5, 95.0, 'red', 'Below SLA target'),
(@sub4, @kpi_utilization, 72.0, 80.0, 'red', 'Low utilization'),
(@sub4, @kpi_time_reg, 88.0, 95.0, 'red', 'Poor time registration'),
(@sub4, @kpi_compliance, 1, 0, 'red', '1 compliance issue'),
(@sub4, @kpi_ab_financial, 1500.00, 0, 'red', 'Over budget'),
(@sub4, @kpi_pto, 25, 25, 'green', '25 PTO days taken'),
(@sub4, @kpi_rto, 65.0, 80.0, 'red', 'Poor attendance'),

-- Data Team (@sub5) - Good performance
(@sub5, @kpi_fte, 14, 14, 'green', 'Team at target size'),
(@sub5, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@sub5, @kpi_sla, 96.8, 95.0, 'green', 'Good SLA performance'),
(@sub5, @kpi_utilization, 83.0, 80.0, 'green', 'Good utilization'),
(@sub5, @kpi_time_reg, 95.0, 95.0, 'green', 'Target time registration'),
(@sub5, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@sub5, @kpi_ab_financial, -900.00, 0, 'green', 'Under budget'),
(@sub5, @kpi_pto, 12, 25, 'green', '12 PTO days taken'),
(@sub5, @kpi_rto, 82.0, 80.0, 'green', 'Good attendance');

-- Display confirmation
SELECT 'Sample monthly dashboard data for July 2025 created successfully' AS status;

-- Show summary
SELECT 
    COUNT(DISTINCT s.id) as total_submissions,
    COUNT(kv.id) as total_kpi_values,
    SUM(CASE WHEN kv.traffic_light_status = 'green' THEN 1 ELSE 0 END) as green_count,
    SUM(CASE WHEN kv.traffic_light_status = 'yellow' THEN 1 ELSE 0 END) as yellow_count,
    SUM(CASE WHEN kv.traffic_light_status = 'red' THEN 1 ELSE 0 END) as red_count
FROM monthly_dashboard_submissions s
LEFT JOIN monthly_dashboard_kpi_values kv ON s.id = kv.submission_id
WHERE s.submission_month = 7 AND s.submission_year = 2025;
