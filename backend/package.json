{"name": "ehrx-backend", "version": "1.0.0", "description": "Backend API for Employee Performance Management Dashboard", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/common": "^8.0.0", "@nestjs/config": "^2.0.0", "@nestjs/core": "^8.0.0", "@nestjs/jwt": "^8.0.0", "@nestjs/passport": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/schedule": "^2.2.3", "@nestjs/swagger": "^5.2.1", "@nestjs/throttler": "^2.0.0", "@nestjs/typeorm": "^8.0.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "csv-writer": "^1.6.0", "exceljs": "^4.3.0", "helmet": "^5.0.2", "moment": "^2.29.4", "mysql2": "^2.3.3", "passport": "^0.5.2", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pdfkit": "^0.13.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.2.41"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.13", "@types/moment": "^2.11.29", "@types/node": "^16.0.0", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/pdfkit": "^0.12.12", "typescript": "^4.3.5"}}