import { IsOptional, IsEnum, IsN<PERSON>ber, IsString } from 'class-validator';
import { RuleType, ConditionOperator } from '../entities/scoring-rule.entity';

export class UpdateScoringRuleDto {
  @IsOptional()
  id?: number;

  @IsOptional()
  @IsEnum(RuleType)
  ruleType?: RuleType;

  @IsOptional()
  @IsString()
  conditionField?: string;

  @IsOptional()
  @IsEnum(ConditionOperator)
  conditionOperator?: ConditionOperator;

  @IsOptional()
  @IsString()
  conditionValue?: string;

  @IsOptional()
  @IsNumber()
  scoreAdjustment?: number;
}
