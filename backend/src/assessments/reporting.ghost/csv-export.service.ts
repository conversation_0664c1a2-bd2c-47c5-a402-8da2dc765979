import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { createObjectCsvWriter } from 'csv-writer';
import { AssessmentInstance } from '../entities/assessment-instance.entity';
import { AssessmentResponse } from '../entities/assessment-response.entity';

@Injectable()
export class CsvExportService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(AssessmentResponse)
    private responseRepository: Repository<AssessmentResponse>,
  ) {}

  // Export assessments to CSV with filters
  async exportAssessmentsCsv(exportParams: { 
    startDate?: string;
    endDate?: string;
    employeeIds?: number[];
    teamIds?: number[];
    statuses?: string[];
  }) {
    // Build query with filters
    const query = this.assessmentRepository.createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.responses', 'response');
    
    // Apply date filters if provided
    if (exportParams.startDate && exportParams.endDate) {
      query.andWhere('assessment.assessmentDate BETWEEN :startDate AND :endDate', {
        startDate: new Date(exportParams.startDate),
        endDate: new Date(exportParams.endDate),
      });
    }
    
    // Apply employee filters if provided
    if (exportParams.employeeIds && exportParams.employeeIds.length > 0) {
      query.andWhere('assessment.employeeId IN (:...employeeIds)', {
        employeeIds: exportParams.employeeIds,
      });
    }
    
    // Apply status filters if provided
    if (exportParams.statuses && exportParams.statuses.length > 0) {
      query.andWhere('assessment.status IN (:...statuses)', {
        statuses: exportParams.statuses,
      });
    }
    
    // Apply team filters if provided (would require join with users table)
    if (exportParams.teamIds && exportParams.teamIds.length > 0) {
      query.leftJoin('users', 'user', 'assessment.employeeId = user.id')
        .andWhere('user.teamId IN (:...teamIds)', {
          teamIds: exportParams.teamIds,
        });
    }
    
    // Get assessments with all filters applied
    const assessments = await query.getMany();
    
    if (!assessments || assessments.length === 0) {
      throw new Error('No assessments found matching the criteria');
    }
    
    // Format data for CSV
    const csvData = [];
    
    for (const assessment of assessments) {
      // Get all responses for this assessment
      const responses = assessment.responses || [];
      
      // Basic assessment info
      const baseRow = {
        assessment_id: assessment.id,
        employee_id: assessment.employeeId,
        evaluator_id: assessment.evaluatorId,
        assessment_date: assessment.assessmentDate.toISOString().split('T')[0],
        template_name: (assessment.templateSnapshot as any)?.name || '',
        status: assessment.status,
        total_score: assessment.totalScore,
      };
      
      // If there are no responses, add the base row only
      if (responses.length === 0) {
        csvData.push(baseRow);
      } else {
        // Add one row per response, combining with the base assessment data
        for (const response of responses) {
          csvData.push({
            ...baseRow,
            area_name: (response.areaSnapshot as any)?.name || '',
            area_weight: response.areaWeight,
            score: response.score,
            max_score: (response.areaSnapshot as any)?.maxScore || 0,
            weighted_score: response.weightedScore,
            evaluator_comments: response.evaluatorComments || '',
            employee_comments: response.employeeComments || '',
          });
        }
      }
    }
    
    // Create CSV file
    const tempDir = os.tmpdir();
    const filename = `assessments_export_${Date.now()}.csv`;
    const filePath = path.join(tempDir, filename);
    
    const csvWriter = createObjectCsvWriter({
      path: filePath,
      header: [
        { id: 'assessment_id', title: 'Assessment ID' },
        { id: 'employee_id', title: 'Employee ID' },
        { id: 'evaluator_id', title: 'Evaluator ID' },
        { id: 'assessment_date', title: 'Assessment Date' },
        { id: 'template_name', title: 'Template Name' },
        { id: 'status', title: 'Status' },
        { id: 'total_score', title: 'Total Score' },
        { id: 'area_name', title: 'Area Name' },
        { id: 'area_weight', title: 'Area Weight' },
        { id: 'score', title: 'Score' },
        { id: 'max_score', title: 'Max Score' },
        { id: 'weighted_score', title: 'Weighted Score' },
        { id: 'evaluator_comments', title: 'Evaluator Comments' },
        { id: 'employee_comments', title: 'Employee Comments' },
      ],
    });
    
    await csvWriter.writeRecords(csvData);
    
    // Read the file and return it
    const file = fs.readFileSync(filePath);
    
    // Clean up temporary file
    fs.unlinkSync(filePath);
    
    return {
      file,
      filename: `assessments_export_${new Date().toISOString().split('T')[0]}.csv`,
    };
  }
}
