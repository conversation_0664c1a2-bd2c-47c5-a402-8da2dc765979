import React, { useState } from 'react';
import {
  Box,
} from '@mui/material';
import UserStatusMenu from '../layout/UserStatusMenu';
import AccountSettingsDialog from '../account/AccountSettingsDialog';
import ChangePasswordDialog from './ChangePasswordDialog';

/**
 * UserAuthenticationModule - Modular authentication component
 * 
 * This module handles all user authentication-related UI components:
 * - User status display and menu
 * - Account settings dialog
 * - Password change functionality
 * - Logout functionality
 * 
 * This follows the modular approach where authentication logic is separated
 * from the main App.tsx and can be easily maintained and tested independently.
 */
const UserAuthenticationModule: React.FC = () => {
  const [accountSettingsOpen, setAccountSettingsOpen] = useState(false);
  const [changePasswordOpen, setChangePasswordOpen] = useState(false);

  const handleOpenAccountSettings = () => {
    setAccountSettingsOpen(true);
  };

  const handleCloseAccountSettings = () => {
    setAccountSettingsOpen(false);
  };

  const handleOpenChangePassword = () => {
    setChangePasswordOpen(true);
  };

  const handleCloseChangePassword = () => {
    setChangePasswordOpen(false);
  };

  return (
    <>
      {/* User Status Menu with Logout */}
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <UserStatusMenu
          onOpenAccountSettings={handleOpenAccountSettings}
          onOpenChangePassword={handleOpenChangePassword}
        />
      </Box>

      {/* Account Settings Dialog */}
      <AccountSettingsDialog
        open={accountSettingsOpen}
        onClose={handleCloseAccountSettings}
        onOpenChangePassword={handleOpenChangePassword}
      />

      {/* Change Password Dialog */}
      <ChangePasswordDialog
        open={changePasswordOpen}
        onClose={handleCloseChangePassword}
        onSuccess={handleCloseChangePassword}
      />
    </>
  );
};

export default UserAuthenticationModule;
