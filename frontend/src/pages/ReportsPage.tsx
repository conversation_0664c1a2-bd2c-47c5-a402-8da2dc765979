import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Tab, 
  Tabs, 
  Typography, 
  Container, 
  Paper, 
  CircularProgress,
  Alert
} from '@mui/material';
import EmployeeReportSection from '../components/reporting/EmployeeReportSection';
import TeamReportSection from '../components/reporting/TeamReportSection';
import OrganizationReportSection from '../components/reporting/OrganizationReportSection';
import { ReportingService } from '../services/reporting';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`reports-tabpanel-${index}`}
      aria-labelledby={`reports-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `reports-tab-${index}`,
    'aria-controls': `reports-tabpanel-${index}`,
  };
}

const ReportsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 3)).toISOString().split('T')[0], // 3 months ago
    endDate: new Date().toISOString().split('T')[0] // today
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Update date range for all reports
  const handleDateRangeChange = (newDateRange: { startDate: string, endDate: string }) => {
    setDateRange(newDateRange);
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Reports & Analytics
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
            <Box component="span" sx={{ ml: 2, cursor: 'pointer' }} onClick={() => setError(null)}>
              Dismiss
            </Box>
          </Alert>
        )}

        <Paper sx={{ width: '100%', mt: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              aria-label="reports tabs"
              variant="fullWidth"
            >
              <Tab label="Employee Reports" {...a11yProps(0)} />
              <Tab label="Team Reports" {...a11yProps(1)} />
              <Tab label="Organization Reports" {...a11yProps(2)} />
            </Tabs>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TabPanel value={tabValue} index={0}>
                <EmployeeReportSection 
                  dateRange={dateRange} 
                  onDateRangeChange={handleDateRangeChange} 
                  onError={setError}
                />
              </TabPanel>
              <TabPanel value={tabValue} index={1}>
                <TeamReportSection 
                  dateRange={dateRange} 
                  onDateRangeChange={handleDateRangeChange}
                  onError={setError} 
                />
              </TabPanel>
              <TabPanel value={tabValue} index={2}>
                <OrganizationReportSection 
                  dateRange={dateRange} 
                  onDateRangeChange={handleDateRangeChange}
                  onError={setError} 
                />
              </TabPanel>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default ReportsPage;
