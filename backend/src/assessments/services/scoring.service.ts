import { Injectable, BadRequestException } from '@nestjs/common';
import { AssessmentArea } from '../entities/assessment-area.entity';
import { ScoringRule, RuleType, ConditionOperator } from '../entities/scoring-rule.entity';

export interface AssessmentInput {
  areaId: number;
  baseScore: number;
  additionalData?: Record<string, any>;
}

export interface ScoredResult {
  areaId: number;
  baseScore: number;
  adjustments: {
    ruleId: number;
    ruleType: RuleType;
    adjustment: number;
    reason: string;
  }[];
  finalScore: number;
  weightedScore: number;
  weight: number;
}

export interface AssessmentCalculationResult {
  areaResults: ScoredResult[];
  totalScore: number;
  maxPossibleScore: number;
  scorePercentage: number;
}

@Injectable()
export class ScoringService {
  
  /**
   * Calculate scores for a complete assessment
   */
  async calculateAssessmentScore(
    areas: AssessmentArea[],
    inputs: AssessmentInput[]
  ): Promise<AssessmentCalculationResult> {
    const areaResults: ScoredResult[] = [];
    let totalWeightedScore = 0;
    let totalWeight = 0;
    let maxPossibleWeightedScore = 0;

    for (const area of areas) {
      const input = inputs.find(i => i.areaId === area.id);
      if (!input) {
        throw new BadRequestException(`Missing input for area: ${area.name}`);
      }

      const areaResult = await this.calculateAreaScore(area, input);
      areaResults.push(areaResult);
      
      totalWeightedScore += areaResult.weightedScore;
      totalWeight += area.weight;
      maxPossibleWeightedScore += area.maxScore * area.weight;
    }

    const totalScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    const maxPossibleScore = totalWeight > 0 ? maxPossibleWeightedScore / totalWeight : 0;
    const scorePercentage = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;

    return {
      areaResults,
      totalScore,
      maxPossibleScore,
      scorePercentage
    };
  }

  /**
   * Calculate score for a single assessment area
   */
  async calculateAreaScore(
    area: AssessmentArea,
    input: AssessmentInput
  ): Promise<ScoredResult> {
    let finalScore = input.baseScore;
    const adjustments: ScoredResult['adjustments'] = [];

    // Apply scoring rules
    if (area.scoringRules && area.scoringRules.length > 0) {
      for (const rule of area.scoringRules) {
        const adjustment = this.applyRule(rule, input.baseScore, input.additionalData || {});
        if (adjustment !== 0) {
          adjustments.push({
            ruleId: rule.id,
            ruleType: rule.ruleType,
            adjustment,
            reason: this.generateAdjustmentReason(rule, input.additionalData || {})
          });
          finalScore += adjustment;
        }
      }
    }

    // Ensure score doesn't exceed max or go below 0
    finalScore = Math.max(0, Math.min(finalScore, area.maxScore));
    
    const weightedScore = finalScore * area.weight;

    return {
      areaId: area.id,
      baseScore: input.baseScore,
      adjustments,
      finalScore,
      weightedScore,
      weight: area.weight
    };
  }

  /**
   * Apply a single scoring rule
   */
  private applyRule(
    rule: ScoringRule,
    baseScore: number,
    additionalData: Record<string, any>
  ): number {
    switch (rule.ruleType) {
      case RuleType.ADDITION:
        return this.evaluateCondition(rule, additionalData) ? rule.scoreAdjustment : 0;
      
      case RuleType.SUBTRACTION:
        return this.evaluateCondition(rule, additionalData) ? -rule.scoreAdjustment : 0;
      
      case RuleType.MULTIPLICATION:
        return this.evaluateCondition(rule, additionalData) ? 
          (baseScore * (rule.scoreAdjustment / 100)) - baseScore : 0;
      
      case RuleType.DIVISION:
        return this.evaluateCondition(rule, additionalData) ? 
          baseScore - (baseScore / (rule.scoreAdjustment / 100)) : 0;
      
      case RuleType.CONDITIONAL:
        return this.evaluateConditionalRule(rule, baseScore, additionalData);
      
      default:
        return 0;
    }
  }

  /**
   * Evaluate if a rule condition is met
   */
  private evaluateCondition(
    rule: ScoringRule,
    additionalData: Record<string, any>
  ): boolean {
    if (!rule.conditionField || !rule.conditionOperator || rule.conditionValue === undefined) {
      return true; // No condition means always apply
    }

    const fieldValue = additionalData[rule.conditionField];
    const conditionValue = this.parseConditionValue(rule.conditionValue);

    switch (rule.conditionOperator) {
      case ConditionOperator.EQUALS:
        return fieldValue == conditionValue;
      
      case ConditionOperator.NOT_EQUALS:
        return fieldValue != conditionValue;
      
      case ConditionOperator.GREATER_THAN:
        return Number(fieldValue) > Number(conditionValue);
      
      case ConditionOperator.LESS_THAN:
        return Number(fieldValue) < Number(conditionValue);
      
      case ConditionOperator.GREATER_OR_EQUAL:
        return Number(fieldValue) >= Number(conditionValue);
      
      case ConditionOperator.LESS_OR_EQUAL:
        return Number(fieldValue) <= Number(conditionValue);
      
      default:
        return false;
    }
  }

  /**
   * Handle complex conditional rules
   */
  private evaluateConditionalRule(
    rule: ScoringRule,
    baseScore: number,
    additionalData: Record<string, any>
  ): number {
    // Handle special cases like NC incidents, training completions, etc.
    if (rule.conditionField === 'nc_incidents') {
      const incidents = Number(additionalData['nc_incidents']) || 0;
      return incidents * (-rule.scoreAdjustment); // Negative adjustment per incident
    }
    
    if (rule.conditionField === 'training_completed') {
      const trainings = Number(additionalData['training_completed']) || 0;
      return trainings * rule.scoreAdjustment; // Positive adjustment per training
    }
    
    if (rule.conditionField === 'certifications') {
      const certs = Number(additionalData['certifications']) || 0;
      return certs * rule.scoreAdjustment;
    }

    // Default conditional evaluation
    return this.evaluateCondition(rule, additionalData) ? rule.scoreAdjustment : 0;
  }

  /**
   * Parse condition value to appropriate type
   */
  private parseConditionValue(value: string): any {
    // Try to parse as number
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      return numValue;
    }
    
    // Try to parse as boolean
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;
    
    // Return as string
    return value;
  }

  /**
   * Generate human-readable reason for score adjustment
   */
  private generateAdjustmentReason(
    rule: ScoringRule,
    additionalData: Record<string, any>
  ): string {
    if (rule.conditionField === 'nc_incidents') {
      const incidents = additionalData['nc_incidents'] || 0;
      return `${incidents} non-conformance incident(s): ${rule.scoreAdjustment} points each`;
    }
    
    if (rule.conditionField === 'training_completed') {
      const trainings = additionalData['training_completed'] || 0;
      return `${trainings} training(s) completed: +${rule.scoreAdjustment} points each`;
    }
    
    if (rule.conditionField === 'certifications') {
      const certs = additionalData['certifications'] || 0;
      return `${certs} certification(s): +${rule.scoreAdjustment} points each`;
    }

    return `${rule.ruleType} rule applied: ${rule.scoreAdjustment > 0 ? '+' : ''}${rule.scoreAdjustment} points`;
  }

  /**
   * Validate assessment inputs
   */
  validateInputs(areas: AssessmentArea[], inputs: AssessmentInput[]): string[] {
    const errors: string[] = [];
    
    for (const area of areas) {
      const input = inputs.find(i => i.areaId === area.id);
      if (!input) {
        errors.push(`Missing input for area: ${area.name}`);
        continue;
      }
      
      if (input.baseScore < 0 || input.baseScore > area.maxScore) {
        errors.push(`Score for ${area.name} must be between 0 and ${area.maxScore}`);
      }
    }
    
    return errors;
  }
}
