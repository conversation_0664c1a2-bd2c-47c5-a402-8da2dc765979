import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Skillset, SkillLevel } from './skillset.entity';

@Entity('user_skillsets')
@Unique(['userId', 'skillsetId'])
export class UserSkillset {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'skillset_id' })
  skillsetId: number;

  @Column({
    name: 'proficiency_level',
    type: 'enum',
    enum: SkillLevel,
    default: SkillLevel.INTERMEDIATE,
  })
  proficiencyLevel: SkillLevel;

  @Column({ name: 'years_experience', type: 'decimal', precision: 3, scale: 1, default: 0.0 })
  yearsExperience: number;

  @Column({ name: 'is_certified', default: false })
  isCertified: boolean;

  @Column({ name: 'certification_name', length: 255, nullable: true })
  certificationName: string;

  @Column({ name: 'certification_date', type: 'date', nullable: true })
  certificationDate: Date;

  @Column({ name: 'last_used_date', type: 'date', nullable: true })
  lastUsedDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.userSkillsets, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Skillset, (skillset) => skillset.userSkillsets, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'skillset_id' })
  skillset: Skillset;
}
