import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { ActionItem, ActionItemStatus } from '../action-items/entities/action-item.entity';
import { ImprovementPlan, ImprovementPlanStatus } from '../improvement-plans/entities/improvement-plan.entity';
import { User, UserRole } from '../users/entities/user.entity';

export interface PerformanceMetrics {
  averageScore: number;
  scoreDistribution: { range: string; count: number }[];
  trendData: { period: string; averageScore: number; assessmentCount: number }[];
  topPerformers: { user: User; averageScore: number; assessmentCount: number }[];
  improvementAreas: { area: string; averageScore: number; assessmentCount: number }[];
}

export interface TeamAnalytics {
  teamId: number;
  teamName: string;
  memberCount: number;
  averageScore: number;
  completedAssessments: number;
  pendingAssessments: number;
  actionItemsCompleted: number;
  actionItemsPending: number;
  improvementPlansActive: number;
  performanceTrend: { period: string; score: number }[];
}

export interface ComparisonAnalytics {
  individual: {
    userId: number;
    currentScore: number;
    previousScore: number;
    percentageChange: number;
    ranking: number;
    totalParticipants: number;
  };
  team: {
    teamScore: number;
    departmentAverage: number;
    companyAverage: number;
    percentileRank: number;
  };
  benchmarks: {
    industryAverage?: number;
    bestPracticeScore?: number;
    targetScore?: number;
  };
}

export interface TrendAnalysis {
  timeframe: 'monthly' | 'quarterly' | 'yearly';
  data: {
    period: string;
    assessmentScores: number;
    actionItemCompletion: number;
    improvementPlanProgress: number;
    userEngagement: number;
  }[];
  predictions: {
    nextPeriod: string;
    predictedScore: number;
    confidence: number;
  };
}

export interface AnalyticsFilters {
  startDate?: Date;
  endDate?: Date;
  userIds?: number[];
  teamIds?: number[];
  departmentIds?: number[];
  templateIds?: number[];
  roles?: UserRole[];
}

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(ActionItem)
    private actionItemRepository: Repository<ActionItem>,
    @InjectRepository(ImprovementPlan)
    private improvementPlanRepository: Repository<ImprovementPlan>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async getPerformanceMetrics(filters: AnalyticsFilters = {}): Promise<PerformanceMetrics> {
    const queryBuilder = this.assessmentRepository.createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.employee', 'employee')
      .leftJoinAndSelect('assessment.template', 'template')
      .leftJoinAndSelect('assessment.responses', 'responses')
      .where('assessment.status = :status', { status: 'completed' });

    this.applyFilters(queryBuilder, filters);

    const assessments = await queryBuilder.getMany();

    // Calculate average score
    const totalScore = assessments.reduce((sum, assessment) => {
      return sum + this.calculateAssessmentScore(assessment);
    }, 0);
    const averageScore = assessments.length > 0 ? totalScore / assessments.length : 0;

    // Score distribution
    const scoreDistribution = this.calculateScoreDistribution(assessments);

    // Trend data (last 12 months)
    const trendData = await this.calculateTrendData(filters);

    // Top performers
    const topPerformers = await this.getTopPerformers(filters, 10);

    // Improvement areas
    const improvementAreas = await this.getImprovementAreas(filters);

    return {
      averageScore,
      scoreDistribution,
      trendData,
      topPerformers,
      improvementAreas
    };
  }

  async getTeamAnalytics(teamId?: number, filters: AnalyticsFilters = {}): Promise<TeamAnalytics[]> {
    // This would require a teams table/entity which we haven't implemented yet
    // For now, we'll group by manager or department
    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .leftJoinAndSelect('user.managedAssessments', 'assessments')
      .leftJoinAndSelect('user.assignedActionItems', 'actionItems')
      .leftJoinAndSelect('user.improvementPlans', 'improvementPlans')
      .where('user.role = :role', { role: UserRole.MANAGER });

    if (teamId) {
      // Would filter by team if we had team structure
      queryBuilder.andWhere('user.id = :teamId', { teamId });
    }

    const managers = await queryBuilder.getMany();

    const teamAnalytics: TeamAnalytics[] = [];

    for (const manager of managers) {
      const teamMembers = await this.userRepository.find({
        where: { managerId: manager.id } // Assuming we have managerId field
      });

      const memberIds = teamMembers.map(member => member.id);

      // Get team assessments
      const teamAssessments = await this.assessmentRepository.find({
        where: { employeeId: memberIds.length > 0 ? memberIds : [manager.id] },
        relations: ['responses']
      });

      const completedAssessments = teamAssessments.filter(a => a.status === 'completed').length;
      const pendingAssessments = teamAssessments.filter(a => a.status !== 'completed').length;

      // Calculate average score
      const completedAssessmentsWithScores = teamAssessments.filter(a => a.status === 'completed');
      const averageScore = completedAssessmentsWithScores.length > 0 
        ? completedAssessmentsWithScores.reduce((sum, a) => sum + this.calculateAssessmentScore(a), 0) / completedAssessmentsWithScores.length
        : 0;

      // Get action items stats
      const actionItems = await this.actionItemRepository.find({
        where: { assignedToId: memberIds.length > 0 ? memberIds : [manager.id] }
      });

      const actionItemsCompleted = actionItems.filter(ai => ai.status === ActionItemStatus.COMPLETED).length;
      const actionItemsPending = actionItems.filter(ai => ai.status !== ActionItemStatus.COMPLETED).length;

      // Get improvement plans stats
      const improvementPlans = await this.improvementPlanRepository.find({
        where: { employeeId: memberIds.length > 0 ? memberIds : [manager.id] }
      });

      const improvementPlansActive = improvementPlans.filter(ip => ip.status === ImprovementPlanStatus.ACTIVE).length;

      // Performance trend (simplified)
      const performanceTrend = await this.calculateTeamTrend(memberIds.length > 0 ? memberIds : [manager.id]);

      teamAnalytics.push({
        teamId: manager.id,
        teamName: `${manager.firstName} ${manager.lastName}'s Team`,
        memberCount: teamMembers.length || 1,
        averageScore,
        completedAssessments,
        pendingAssessments,
        actionItemsCompleted,
        actionItemsPending,
        improvementPlansActive,
        performanceTrend
      });
    }

    return teamAnalytics;
  }

  async getComparisonAnalytics(userId: number, filters: AnalyticsFilters = {}): Promise<ComparisonAnalytics> {
    // Get user's current and previous scores
    const userAssessments = await this.assessmentRepository.find({
      where: { employeeId: userId, status: 'completed' },
      relations: ['responses'],
      order: { assessmentDate: 'DESC' },
      take: 2
    });

    const currentScore = userAssessments.length > 0 ? this.calculateAssessmentScore(userAssessments[0]) : 0;
    const previousScore = userAssessments.length > 1 ? this.calculateAssessmentScore(userAssessments[1]) : currentScore;
    const percentageChange = previousScore > 0 ? ((currentScore - previousScore) / previousScore) * 100 : 0;

    // Get user ranking
    const allUsers = await this.assessmentRepository.createQueryBuilder('assessment')
      .select('assessment.employeeId', 'employeeId')
      .addSelect('AVG(assessment.finalScore)', 'averageScore')
      .where('assessment.status = :status', { status: 'completed' })
      .groupBy('assessment.employeeId')
      .orderBy('averageScore', 'DESC')
      .getRawMany();

    const userRank = allUsers.findIndex(u => u.employeeId === userId) + 1;

    // Get team/department averages (simplified)
    const user = await this.userRepository.findOne({ where: { id: userId } });
    const teamMembers = await this.userRepository.find({
      where: { managerId: user?.managerId } // Assuming managerId field exists
    });

    const teamMemberIds = teamMembers.map(m => m.id);
    const teamAssessments = await this.assessmentRepository.find({
      where: { employeeId: teamMemberIds, status: 'completed' },
      relations: ['responses']
    });

    const teamScore = teamAssessments.length > 0
      ? teamAssessments.reduce((sum, a) => sum + this.calculateAssessmentScore(a), 0) / teamAssessments.length
      : 0;

    // Company average
    const allAssessments = await this.assessmentRepository.find({
      where: { status: 'completed' },
      relations: ['responses']
    });

    const companyAverage = allAssessments.length > 0
      ? allAssessments.reduce((sum, a) => sum + this.calculateAssessmentScore(a), 0) / allAssessments.length
      : 0;

    // Calculate percentile rank
    const scoresBelow = allUsers.filter(u => parseFloat(u.averageScore) < currentScore).length;
    const percentileRank = allUsers.length > 0 ? (scoresBelow / allUsers.length) * 100 : 0;

    return {
      individual: {
        userId,
        currentScore,
        previousScore,
        percentageChange,
        ranking: userRank,
        totalParticipants: allUsers.length
      },
      team: {
        teamScore,
        departmentAverage: teamScore, // Simplified
        companyAverage,
        percentileRank
      },
      benchmarks: {
        targetScore: 85, // Could be configurable
        bestPracticeScore: 90
      }
    };
  }

  async getTrendAnalysis(timeframe: 'monthly' | 'quarterly' | 'yearly', filters: AnalyticsFilters = {}): Promise<TrendAnalysis> {
    const periods = this.generatePeriods(timeframe, 12); // Last 12 periods
    const trendData = [];

    for (const period of periods) {
      const periodFilters = {
        ...filters,
        startDate: period.start,
        endDate: period.end
      };

      // Assessment scores
      const assessments = await this.assessmentRepository.find({
        where: { 
          status: 'completed',
          assessmentDate: period.start // Would use Between for proper date range
        },
        relations: ['responses']
      });

      const assessmentScores = assessments.length > 0
        ? assessments.reduce((sum, a) => sum + this.calculateAssessmentScore(a), 0) / assessments.length
        : 0;

      // Action item completion rate
      const actionItems = await this.actionItemRepository.find({
        where: { createdAt: period.start } // Would use Between for proper date range
      });

      const completedActionItems = actionItems.filter(ai => ai.status === ActionItemStatus.COMPLETED).length;
      const actionItemCompletion = actionItems.length > 0 ? (completedActionItems / actionItems.length) * 100 : 0;

      // Improvement plan progress
      const improvementPlans = await this.improvementPlanRepository.find({
        where: { createdAt: period.start } // Would use Between for proper date range
      });

      const improvementPlanProgress = improvementPlans.length > 0
        ? improvementPlans.reduce((sum, ip) => sum + ip.progressPercentage, 0) / improvementPlans.length
        : 0;

      // User engagement (simplified - could be based on login frequency, assessment participation, etc.)
      const userEngagement = Math.min(100, (assessments.length + actionItems.length + improvementPlans.length) * 10);

      trendData.push({
        period: period.label,
        assessmentScores,
        actionItemCompletion,
        improvementPlanProgress,
        userEngagement
      });
    }

    // Simple prediction based on linear trend
    const recentScores = trendData.slice(-3).map(d => d.assessmentScores);
    const avgChange = recentScores.length > 1 
      ? (recentScores[recentScores.length - 1] - recentScores[0]) / (recentScores.length - 1)
      : 0;
    
    const lastScore = recentScores[recentScores.length - 1] || 0;
    const predictedScore = Math.max(0, Math.min(100, lastScore + avgChange));

    return {
      timeframe,
      data: trendData,
      predictions: {
        nextPeriod: this.getNextPeriodLabel(timeframe),
        predictedScore,
        confidence: Math.min(95, Math.max(60, 80 - Math.abs(avgChange) * 5)) // Simple confidence calculation
      }
    };
  }

  private calculateAssessmentScore(assessment: AssessmentInstance): number {
    if (!assessment.responses || assessment.responses.length === 0) {
      return 0;
    }

    const totalScore = assessment.responses.reduce((sum, response) => sum + (response.score || 0), 0);
    const maxPossibleScore = assessment.responses.length * 100; // Assuming max score per response is 100
    
    return maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;
  }

  private calculateScoreDistribution(assessments: AssessmentInstance[]): { range: string; count: number }[] {
    const ranges = [
      { range: '0-20', min: 0, max: 20, count: 0 },
      { range: '21-40', min: 21, max: 40, count: 0 },
      { range: '41-60', min: 41, max: 60, count: 0 },
      { range: '61-80', min: 61, max: 80, count: 0 },
      { range: '81-100', min: 81, max: 100, count: 0 }
    ];

    assessments.forEach(assessment => {
      const score = this.calculateAssessmentScore(assessment);
      const range = ranges.find(r => score >= r.min && score <= r.max);
      if (range) {
        range.count++;
      }
    });

    return ranges.map(r => ({ range: r.range, count: r.count }));
  }

  private async calculateTrendData(filters: AnalyticsFilters): Promise<{ period: string; averageScore: number; assessmentCount: number }[]> {
    // Simplified trend calculation for last 12 months
    const months = [];
    const now = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthName = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      
      // This would need proper date range queries in a real implementation
      const assessments = await this.assessmentRepository.find({
        where: { status: 'completed' },
        relations: ['responses']
      });

      const monthAssessments = assessments.filter(a => {
        const assessmentDate = new Date(a.assessmentDate);
        return assessmentDate.getMonth() === date.getMonth() && 
               assessmentDate.getFullYear() === date.getFullYear();
      });

      const averageScore = monthAssessments.length > 0
        ? monthAssessments.reduce((sum, a) => sum + this.calculateAssessmentScore(a), 0) / monthAssessments.length
        : 0;

      months.push({
        period: monthName,
        averageScore,
        assessmentCount: monthAssessments.length
      });
    }

    return months;
  }

  private async getTopPerformers(filters: AnalyticsFilters, limit: number): Promise<{ user: User; averageScore: number; assessmentCount: number }[]> {
    const queryBuilder = this.assessmentRepository.createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.employee', 'employee')
      .select('assessment.employeeId', 'employeeId')
      .addSelect('AVG(assessment.finalScore)', 'averageScore')
      .addSelect('COUNT(assessment.id)', 'assessmentCount')
      .where('assessment.status = :status', { status: 'completed' })
      .groupBy('assessment.employeeId')
      .orderBy('averageScore', 'DESC')
      .limit(limit);

    this.applyFilters(queryBuilder, filters);

    const results = await queryBuilder.getRawMany();
    
    const topPerformers = [];
    for (const result of results) {
      const user = await this.userRepository.findOne({ where: { id: result.employeeId } });
      if (user) {
        topPerformers.push({
          user,
          averageScore: parseFloat(result.averageScore),
          assessmentCount: parseInt(result.assessmentCount)
        });
      }
    }

    return topPerformers;
  }

  private async getImprovementAreas(filters: AnalyticsFilters): Promise<{ area: string; averageScore: number; assessmentCount: number }[]> {
    // This would analyze assessment areas/categories with lowest scores
    // Simplified implementation
    return [
      { area: 'Communication', averageScore: 72, assessmentCount: 45 },
      { area: 'Technical Skills', averageScore: 78, assessmentCount: 42 },
      { area: 'Leadership', averageScore: 68, assessmentCount: 38 },
      { area: 'Problem Solving', averageScore: 75, assessmentCount: 40 }
    ];
  }

  private async calculateTeamTrend(memberIds: number[]): Promise<{ period: string; score: number }[]> {
    // Simplified team trend calculation
    const months = [];
    const now = new Date();
    
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthName = date.toLocaleDateString('en-US', { month: 'short' });
      
      // This would need proper date range queries
      const score = 70 + Math.random() * 20; // Placeholder
      
      months.push({
        period: monthName,
        score: Math.round(score)
      });
    }

    return months;
  }

  private applyFilters(queryBuilder: any, filters: AnalyticsFilters): void {
    if (filters.startDate) {
      queryBuilder.andWhere('assessment.assessmentDate >= :startDate', { startDate: filters.startDate });
    }

    if (filters.endDate) {
      queryBuilder.andWhere('assessment.assessmentDate <= :endDate', { endDate: filters.endDate });
    }

    if (filters.userIds && filters.userIds.length > 0) {
      queryBuilder.andWhere('assessment.employeeId IN (:...userIds)', { userIds: filters.userIds });
    }

    if (filters.templateIds && filters.templateIds.length > 0) {
      queryBuilder.andWhere('assessment.templateId IN (:...templateIds)', { templateIds: filters.templateIds });
    }
  }

  private generatePeriods(timeframe: string, count: number): { start: Date; end: Date; label: string }[] {
    const periods = [];
    const now = new Date();

    for (let i = count - 1; i >= 0; i--) {
      let start: Date, end: Date, label: string;

      switch (timeframe) {
        case 'monthly':
          start = new Date(now.getFullYear(), now.getMonth() - i, 1);
          end = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
          label = start.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
          break;
        case 'quarterly':
          const quarterStart = Math.floor((now.getMonth() - i * 3) / 3) * 3;
          start = new Date(now.getFullYear(), quarterStart, 1);
          end = new Date(now.getFullYear(), quarterStart + 3, 0);
          label = `Q${Math.floor(quarterStart / 3) + 1} ${start.getFullYear()}`;
          break;
        case 'yearly':
          start = new Date(now.getFullYear() - i, 0, 1);
          end = new Date(now.getFullYear() - i, 11, 31);
          label = start.getFullYear().toString();
          break;
        default:
          start = new Date(now.getFullYear(), now.getMonth() - i, 1);
          end = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
          label = start.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      }

      periods.push({ start, end, label });
    }

    return periods;
  }

  private getNextPeriodLabel(timeframe: string): string {
    const now = new Date();
    
    switch (timeframe) {
      case 'monthly':
        const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        return nextMonth.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      case 'quarterly':
        const currentQuarter = Math.floor(now.getMonth() / 3) + 1;
        const nextQuarter = currentQuarter === 4 ? 1 : currentQuarter + 1;
        const nextYear = currentQuarter === 4 ? now.getFullYear() + 1 : now.getFullYear();
        return `Q${nextQuarter} ${nextYear}`;
      case 'yearly':
        return (now.getFullYear() + 1).toString();
      default:
        return 'Next Period';
    }
  }
}
