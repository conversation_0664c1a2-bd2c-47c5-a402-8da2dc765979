import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { AssessmentInstance } from '../../assessments/entities/assessment-instance.entity';
import { ActionItem } from '../../action-items/entities/action-item.entity';

export enum ImprovementPlanStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum ImprovementPlanType {
  PERFORMANCE = 'performance',
  SKILL_DEVELOPMENT = 'skill_development',
  CAREER_DEVELOPMENT = 'career_development',
  CORRECTIVE_ACTION = 'corrective_action',
  GOAL_ACHIEVEMENT = 'goal_achievement'
}

@Entity('improvement_plans')
export class ImprovementPlan {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: ImprovementPlanStatus,
    default: ImprovementPlanStatus.DRAFT
  })
  status: ImprovementPlanStatus;

  @Column({
    type: 'enum',
    enum: ImprovementPlanType,
    default: ImprovementPlanType.PERFORMANCE
  })
  type: ImprovementPlanType;

  @Column({ name: 'employee_id' })
  employeeId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'employee_id' })
  employee: User;

  @Column({ name: 'manager_id' })
  managerId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  @Column({ name: 'assessment_id', nullable: true })
  assessmentId: number;

  @ManyToOne(() => AssessmentInstance, { nullable: true })
  @JoinColumn({ name: 'assessment_id' })
  assessment: AssessmentInstance;

  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @Column({ name: 'target_completion_date', type: 'date' })
  targetCompletionDate: Date;

  @Column({ name: 'actual_completion_date', type: 'date', nullable: true })
  actualCompletionDate: Date;

  @Column({ type: 'text', nullable: true })
  objectives: string;

  @Column({ name: 'success_criteria', type: 'text', nullable: true })
  successCriteria: string;

  @Column({ name: 'resources_needed', type: 'text', nullable: true })
  resourcesNeeded: string;

  @Column({ name: 'progress_percentage', type: 'int', default: 0 })
  progressPercentage: number;

  @Column({ name: 'review_frequency', type: 'varchar', length: 50, default: 'monthly' })
  reviewFrequency: string;

  @Column({ name: 'next_review_date', type: 'date', nullable: true })
  nextReviewDate: Date;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @OneToMany(() => ImprovementPlanGoal, goal => goal.improvementPlan)
  goals: ImprovementPlanGoal[];

  @OneToMany(() => ImprovementPlanReview, review => review.improvementPlan)
  reviews: ImprovementPlanReview[];

  @OneToMany(() => ActionItem, actionItem => actionItem.improvementPlan)
  actionItems: ActionItem[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}

@Entity('improvement_plan_goals')
export class ImprovementPlanGoal {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'improvement_plan_id' })
  improvementPlanId: number;

  @ManyToOne(() => ImprovementPlan, plan => plan.goals)
  @JoinColumn({ name: 'improvement_plan_id' })
  improvementPlan: ImprovementPlan;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'target_date', type: 'date' })
  targetDate: Date;

  @Column({ name: 'completion_date', type: 'date', nullable: true })
  completionDate: Date;

  @Column({ name: 'is_completed', type: 'boolean', default: false })
  isCompleted: boolean;

  @Column({ name: 'progress_percentage', type: 'int', default: 0 })
  progressPercentage: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'order_index', type: 'int', default: 0 })
  orderIndex: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}

@Entity('improvement_plan_reviews')
export class ImprovementPlanReview {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'improvement_plan_id' })
  improvementPlanId: number;

  @ManyToOne(() => ImprovementPlan, plan => plan.reviews)
  @JoinColumn({ name: 'improvement_plan_id' })
  improvementPlan: ImprovementPlan;

  @Column({ name: 'review_date', type: 'date' })
  reviewDate: Date;

  @Column({ name: 'reviewer_id' })
  reviewerId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'reviewer_id' })
  reviewer: User;

  @Column({ name: 'progress_summary', type: 'text' })
  progressSummary: string;

  @Column({ type: 'text', nullable: true })
  achievements: string;

  @Column({ type: 'text', nullable: true })
  challenges: string;

  @Column({ name: 'next_steps', type: 'text', nullable: true })
  nextSteps: string;

  @Column({ name: 'overall_rating', type: 'int', nullable: true })
  overallRating: number;

  @Column({ name: 'is_on_track', type: 'boolean', default: true })
  isOnTrack: boolean;

  @Column({ name: 'recommended_actions', type: 'text', nullable: true })
  recommendedActions: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
