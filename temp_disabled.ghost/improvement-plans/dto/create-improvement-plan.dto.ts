import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsNumber, IsDateString, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ImprovementPlanType } from '../entities/improvement-plan.entity';

export class CreateImprovementPlanGoalDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsDateString()
  targetDate: string;

  @IsString()
  @IsOptional()
  notes?: string;

  @IsNumber()
  @IsOptional()
  orderIndex?: number;
}

export class CreateImprovementPlanDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsEnum(ImprovementPlanType)
  @IsOptional()
  type?: ImprovementPlanType;

  @IsNumber()
  employeeId: number;

  @IsNumber()
  managerId: number;

  @IsNumber()
  @IsOptional()
  assessmentId?: number;

  @IsDateString()
  startDate: string;

  @IsDateString()
  targetCompletionDate: string;

  @IsString()
  @IsOptional()
  objectives?: string;

  @IsString()
  @IsOptional()
  successCriteria?: string;

  @IsString()
  @IsOptional()
  resourcesNeeded?: string;

  @IsString()
  @IsOptional()
  reviewFrequency?: string;

  @IsDateString()
  @IsOptional()
  nextReviewDate?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateImprovementPlanGoalDto)
  @IsOptional()
  goals?: CreateImprovementPlanGoalDto[];
}
