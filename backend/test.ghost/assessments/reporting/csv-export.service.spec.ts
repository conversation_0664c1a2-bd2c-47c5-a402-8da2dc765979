import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

import { CsvExportService } from '../../../src/assessments/reporting/csv-export.service';
import { Assessment } from '../../../src/assessments/entities/assessment.entity';
import { AssessmentResponse } from '../../../src/assessments/entities/assessment-response.entity';
import { User } from '../../../src/users/entities/user.entity';
import { Team } from '../../../src/teams/entities/team.entity';
import { AssessmentStatus } from '../../../src/assessments/enums/assessment-status.enum';
import { CsvExportDto } from '../../../src/assessments/dto/csv-export.dto';

describe('CsvExportService', () => {
  let service: CsvExportService;
  let assessmentRepository: Repository<Assessment>;
  let userRepository: Repository<User>;
  let teamRepository: Repository<Team>;
  let responseRepository: Repository<AssessmentResponse>;

  // Mock data
  const mockAssessments: Partial<Assessment>[] = [
    {
      id: 1,
      employeeId: 1,
      managerId: 2,
      templateId: 1,
      status: AssessmentStatus.APPROVED,
      score: 0.85,
      createdAt: new Date('2025-01-15'),
      updatedAt: new Date('2025-01-20'),
      submittedAt: new Date('2025-01-18'),
      dueDate: new Date('2025-01-25'),
      notes: 'Good performance'
    },
    {
      id: 2,
      employeeId: 3,
      managerId: 2,
      templateId: 1,
      status: AssessmentStatus.COMPLETED,
      score: 0.78,
      createdAt: new Date('2025-02-15'),
      updatedAt: new Date('2025-02-20'),
      submittedAt: new Date('2025-02-18'),
      dueDate: new Date('2025-02-25'),
      notes: 'Satisfactory performance'
    }
  ];

  const mockUsers: Partial<User>[] = [
    { id: 1, name: 'John Employee', email: '<EMAIL>', teamId: 1 },
    { id: 2, name: 'Sarah Manager', email: '<EMAIL>', teamId: 1 },
    { id: 3, name: 'Alex Worker', email: '<EMAIL>', teamId: 2 }
  ];

  const mockTeams: Partial<Team>[] = [
    { id: 1, name: 'Engineering', managerId: 2 },
    { id: 2, name: 'Product', managerId: 4 }
  ];

  const mockResponses: Partial<AssessmentResponse>[] = [
    {
      id: 1,
      assessmentId: 1,
      areaId: 1,
      areaName: 'Technical Skills',
      score: 0.85,
      notes: 'Strong technical skills'
    }
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CsvExportService,
        {
          provide: getRepositoryToken(Assessment),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AssessmentResponse),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Team),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<CsvExportService>(CsvExportService);
    assessmentRepository = module.get<Repository<Assessment>>(getRepositoryToken(Assessment));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    teamRepository = module.get<Repository<Team>>(getRepositoryToken(Team));
    responseRepository = module.get<Repository<AssessmentResponse>>(getRepositoryToken(AssessmentResponse));

    // Mock the fs methods
    jest.spyOn(fs, 'writeFileSync').mockImplementation(() => {});
    jest.spyOn(fs, 'readFileSync').mockImplementation(() => Buffer.from('mock,csv,content'));
    jest.spyOn(fs, 'unlinkSync').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('exportAssessmentsCSV', () => {
    it('should generate a CSV file with all assessments if no filters provided', async () => {
      // Mock the database queries
      jest.spyOn(assessmentRepository, 'find').mockResolvedValue(mockAssessments as Assessment[]);
      
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        const user = mockUsers.find(u => u.id === options.id);
        return Promise.resolve(user as User);
      });

      jest.spyOn(teamRepository, 'findOneBy').mockImplementation((options) => {
        const team = mockTeams.find(t => t.id === options.id);
        return Promise.resolve(team as Team);
      });

      // Create an empty filter DTO
      const filterDto = new CsvExportDto();
      
      // Call the service method
      const result = await service.exportAssessmentsCSV(filterDto);
      
      // Check that the file was written
      expect(fs.writeFileSync).toHaveBeenCalled();
      
      // Check that we got back a buffer
      expect(Buffer.isBuffer(result)).toBe(true);
      
      // Verify that we queried for all assessments
      expect(assessmentRepository.find).toHaveBeenCalledWith({
        order: { createdAt: 'DESC' }
      });
    });

    it('should filter assessments by date range', async () => {
      // Mock the database query with filtered results
      jest.spyOn(assessmentRepository, 'find').mockImplementation((options) => {
        const where = options.where;
        
        // Filter the mock data based on the date range
        const filtered = mockAssessments.filter(assessment => {
          if (!where) return true;
          
          const date = assessment.createdAt;
          let matches = true;
          
          if (where.createdAt && where.createdAt.gte) {
            matches = matches && date >= where.createdAt.gte;
          }
          
          if (where.createdAt && where.createdAt.lte) {
            matches = matches && date <= where.createdAt.lte;
          }
          
          return matches;
        });
        
        return Promise.resolve(filtered as Assessment[]);
      });
      
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        const user = mockUsers.find(u => u.id === options.id);
        return Promise.resolve(user as User);
      });

      jest.spyOn(teamRepository, 'findOneBy').mockImplementation((options) => {
        const team = mockTeams.find(t => t.id === options.id);
        return Promise.resolve(team as Team);
      });
      
      // Create a filter with date range
      const filterDto = new CsvExportDto();
      filterDto.startDate = '2025-02-01';
      filterDto.endDate = '2025-02-28';
      
      // Call the service method
      await service.exportAssessmentsCSV(filterDto);
      
      // Verify that the query included the date filters
      expect(assessmentRepository.find).toHaveBeenCalledWith({
        where: {
          createdAt: {
            gte: new Date('2025-02-01'),
            lte: new Date('2025-02-28')
          }
        },
        order: { createdAt: 'DESC' }
      });
    });

    it('should filter assessments by employee IDs', async () => {
      // Mock the database query
      jest.spyOn(assessmentRepository, 'find').mockImplementation((options) => {
        const where = options.where;
        
        // Filter the mock data based on employee IDs
        const filtered = mockAssessments.filter(assessment => {
          if (!where || !where.employeeId) return true;
          return where.employeeId.in.includes(assessment.employeeId);
        });
        
        return Promise.resolve(filtered as Assessment[]);
      });
      
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        const user = mockUsers.find(u => u.id === options.id);
        return Promise.resolve(user as User);
      });

      jest.spyOn(teamRepository, 'findOneBy').mockImplementation((options) => {
        const team = mockTeams.find(t => t.id === options.id);
        return Promise.resolve(team as Team);
      });
      
      // Create a filter with employee IDs
      const filterDto = new CsvExportDto();
      filterDto.employeeIds = [1];
      
      // Call the service method
      await service.exportAssessmentsCSV(filterDto);
      
      // Verify that the query included the employee ID filters
      expect(assessmentRepository.find).toHaveBeenCalledWith({
        where: {
          employeeId: { in: [1] }
        },
        order: { createdAt: 'DESC' }
      });
    });

    it('should filter assessments by team IDs', async () => {
      // Mock to return users in the specified teams
      jest.spyOn(userRepository, 'find').mockImplementation((options) => {
        const where = options.where;
        
        // Filter users based on team ID
        const filteredUsers = mockUsers.filter(user => {
          if (!where || !where.teamId) return true;
          return where.teamId.in.includes(user.teamId);
        });
        
        return Promise.resolve(filteredUsers as User[]);
      });

      // Mock to return assessments for those users
      jest.spyOn(assessmentRepository, 'find').mockImplementation((options) => {
        const where = options.where;
        
        // Filter the mock data based on employee IDs
        const filtered = mockAssessments.filter(assessment => {
          if (!where || !where.employeeId) return true;
          return where.employeeId.in.includes(assessment.employeeId);
        });
        
        return Promise.resolve(filtered as Assessment[]);
      });
      
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        const user = mockUsers.find(u => u.id === options.id);
        return Promise.resolve(user as User);
      });

      jest.spyOn(teamRepository, 'findOneBy').mockImplementation((options) => {
        const team = mockTeams.find(t => t.id === options.id);
        return Promise.resolve(team as Team);
      });
      
      // Create a filter with team IDs
      const filterDto = new CsvExportDto();
      filterDto.teamIds = [1];
      
      // Call the service method
      await service.exportAssessmentsCSV(filterDto);
      
      // Verify that we first queried for users in the team
      expect(userRepository.find).toHaveBeenCalledWith({
        where: { teamId: { in: [1] } }
      });
    });

    it('should filter assessments by status', async () => {
      // Mock the database query
      jest.spyOn(assessmentRepository, 'find').mockImplementation((options) => {
        const where = options.where;
        
        // Filter the mock data based on status
        const filtered = mockAssessments.filter(assessment => {
          if (!where || !where.status) return true;
          return where.status.in.includes(assessment.status);
        });
        
        return Promise.resolve(filtered as Assessment[]);
      });
      
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        const user = mockUsers.find(u => u.id === options.id);
        return Promise.resolve(user as User);
      });

      jest.spyOn(teamRepository, 'findOneBy').mockImplementation((options) => {
        const team = mockTeams.find(t => t.id === options.id);
        return Promise.resolve(team as Team);
      });
      
      // Create a filter with statuses
      const filterDto = new CsvExportDto();
      filterDto.statuses = [AssessmentStatus.APPROVED];
      
      // Call the service method
      await service.exportAssessmentsCSV(filterDto);
      
      // Verify that the query included the status filters
      expect(assessmentRepository.find).toHaveBeenCalledWith({
        where: {
          status: { in: [AssessmentStatus.APPROVED] }
        },
        order: { createdAt: 'DESC' }
      });
    });

    it('should properly format assessment data for CSV export', async () => {
      // Mock the database queries
      jest.spyOn(assessmentRepository, 'find').mockResolvedValue([mockAssessments[0] as Assessment]);
      
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        const user = mockUsers.find(u => u.id === options.id);
        return Promise.resolve(user as User);
      });

      jest.spyOn(teamRepository, 'findOneBy').mockImplementation((options) => {
        const team = mockTeams.find(t => t.id === options.id);
        return Promise.resolve(team as Team);
      });

      // Create an empty filter DTO
      const filterDto = new CsvExportDto();
      
      // Call the service method
      await service.exportAssessmentsCSV(filterDto);
      
      // Check that we wrote to a file
      expect(fs.writeFileSync).toHaveBeenCalledTimes(1);
      
      // Check the data format passed to writeFileSync
      const writeCall = (fs.writeFileSync as jest.Mock).mock.calls[0];
      const filename = writeCall[0];
      const content = writeCall[1];
      
      // Check that the filename is what we expect
      expect(path.basename(filename)).toMatch(/assessments_export_\d+\.csv/);
      
      // Check that the content has the expected headers and format
      expect(content).toContain('ID,Employee,Manager,Team,Status,Score,Created Date,Submitted Date,Due Date');
      expect(content).toContain('1,John Employee,Sarah Manager,Engineering,APPROVED,85%');
    });
  });

  describe('createCSVCandidates', () => {
    it('should transform assessment data into CSV rows', async () => {
      // Mock the user and team lookup
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        const user = mockUsers.find(u => u.id === options.id);
        return Promise.resolve(user as User);
      });

      jest.spyOn(teamRepository, 'findOneBy').mockImplementation((options) => {
        const team = mockTeams.find(t => t.id === options.id);
        return Promise.resolve(team as Team);
      });

      // Call the service method
      const result = await service['createCSVCandidates']([mockAssessments[0] as Assessment]);
      
      // Check that the result is formatted correctly
      expect(result.length).toBe(1);
      expect(result[0].id).toBe(1);
      expect(result[0].employee).toBe('John Employee');
      expect(result[0].manager).toBe('Sarah Manager');
      expect(result[0].team).toBe('Engineering');
      expect(result[0].status).toBe('APPROVED');
      expect(result[0].score).toBe('85%');
    });
  });
});
