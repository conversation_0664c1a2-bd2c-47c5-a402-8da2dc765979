import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { AssessmentTemplate } from '../entities/assessment-template.entity';
import { AssessmentArea } from '../entities/assessment-area.entity';
import { ScoringRule, RuleType } from '../entities/scoring-rule.entity';
import { AssessmentInstance } from '../entities/assessment-instance.entity';
import { AssessmentResponse } from '../entities/assessment-response.entity';
import { User, UserRole } from '../../users/entities/user.entity';

describe('Template Workflow E2E', () => {
  let app: INestApplication;
  let templateRepository: Repository<AssessmentTemplate>;
  let assessmentRepository: Repository<AssessmentInstance>;
  let userRepository: Repository<User>;

  const hrAdminUser = {
    userId: 1,
    role: UserRole.HR_ADMIN,
    email: '<EMAIL>'
  };

  const managerUser = {
    userId: 2,
    role: UserRole.MANAGER,
    email: '<EMAIL>'
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideGuard('JwtAuthGuard')
    .useValue({
      canActivate: (context) => {
        const request = context.switchToHttp().getRequest();
        // Default to HR admin, can be overridden in tests
        request.user = request.headers['test-user'] === 'manager' ? managerUser : hrAdminUser;
        return true;
      },
    })
    .overrideGuard('RolesGuard')
    .useValue({
      canActivate: () => true,
    })
    .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    templateRepository = moduleFixture.get('AssessmentTemplateRepository');
    assessmentRepository = moduleFixture.get('AssessmentInstanceRepository');
    userRepository = moduleFixture.get('UserRepository');

    // Create test users
    const hrAdmin = userRepository.create({
      id: 1,
      email: '<EMAIL>',
      firstName: 'HR',
      lastName: 'Admin',
      role: UserRole.HR_ADMIN,
      isActive: true
    });

    const manager = userRepository.create({
      id: 2,
      email: '<EMAIL>',
      firstName: 'Team',
      lastName: 'Manager',
      role: UserRole.MANAGER,
      isActive: true
    });

    const employee = userRepository.create({
      id: 3,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'Employee',
      role: UserRole.EMPLOYEE,
      isActive: true
    });

    await userRepository.save([hrAdmin, manager, employee]);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Complete Template Creation and Usage Workflow', () => {
    let createdTemplateId: number;

    it('should create a comprehensive template with scoring rules', async () => {
      const templateData = {
        name: 'Comprehensive Performance Review',
        description: 'A complete performance review template with advanced scoring',
        isGlobal: true,
        areas: [
          {
            name: 'Quality Performance',
            description: 'Quality of work and attention to detail',
            weight: 0.4,
            maxScore: 100,
            orderIndex: 1,
            scoringRules: [
              {
                ruleType: RuleType.CONDITIONAL,
                conditionField: 'nc_incidents',
                scoreAdjustment: 5,
                description: 'Penalty per non-conformance incident'
              },
              {
                ruleType: RuleType.CONDITIONAL,
                conditionField: 'quality_improvements',
                scoreAdjustment: 3,
                description: 'Bonus per quality improvement suggestion'
              }
            ]
          },
          {
            name: 'Professional Development',
            description: 'Learning and skill development',
            weight: 0.3,
            maxScore: 100,
            orderIndex: 2,
            scoringRules: [
              {
                ruleType: RuleType.CONDITIONAL,
                conditionField: 'training_completed',
                scoreAdjustment: 2,
                description: 'Bonus per training completed'
              },
              {
                ruleType: RuleType.CONDITIONAL,
                conditionField: 'certifications',
                scoreAdjustment: 5,
                description: 'Bonus per certification obtained'
              }
            ]
          },
          {
            name: 'Safety & Compliance',
            description: 'Adherence to safety protocols and regulations',
            weight: 0.3,
            maxScore: 100,
            orderIndex: 3,
            scoringRules: [
              {
                ruleType: RuleType.CONDITIONAL,
                conditionField: 'safety_incidents',
                scoreAdjustment: 10,
                description: 'Penalty per safety incident'
              },
              {
                ruleType: RuleType.MULTIPLICATION,
                scoreAdjustment: 110,
                description: '10% bonus for perfect safety record'
              }
            ]
          }
        ]
      };

      const response = await request(app.getHttpServer())
        .post('/templates')
        .send(templateData)
        .expect(201);

      createdTemplateId = response.body.id;

      expect(response.body.name).toBe('Comprehensive Performance Review');
      expect(response.body.isGlobal).toBe(true);
      expect(response.body.areas).toHaveLength(3);
      
      // Verify scoring rules were created
      expect(response.body.areas[0].scoringRules).toHaveLength(2);
      expect(response.body.areas[1].scoringRules).toHaveLength(2);
      expect(response.body.areas[2].scoringRules).toHaveLength(2);
    });

    it('should validate the created template', async () => {
      const response = await request(app.getHttpServer())
        .post(`/templates/${createdTemplateId}/validate`)
        .expect(201);

      expect(response.body.isValid).toBe(true);
      expect(response.body.errors).toHaveLength(0);
      expect(response.body.warnings).toHaveLength(0);
    });

    it('should clone the template with modifications', async () => {
      const response = await request(app.getHttpServer())
        .post(`/templates/${createdTemplateId}/clone`)
        .send({ name: 'Modified Performance Review' })
        .expect(201);

      expect(response.body.name).toBe('Modified Performance Review');
      expect(response.body.parentTemplateId).toBe(createdTemplateId);
      expect(response.body.areas).toHaveLength(3);
      expect(response.body.isGlobal).toBe(false); // Clones are not global by default
    });

    it('should create a new version of the template', async () => {
      const response = await request(app.getHttpServer())
        .post(`/templates/${createdTemplateId}/new-version`)
        .expect(201);

      expect(response.body.name).toBe('Comprehensive Performance Review');
      expect(response.body.version).toBe(2);
      expect(response.body.parentTemplateId).toBe(createdTemplateId);

      // Verify original template is deactivated
      const originalTemplate = await templateRepository.findOne({
        where: { id: createdTemplateId }
      });
      expect(originalTemplate.isActive).toBe(false);
    });

    it('should create an assessment using the template with advanced scoring', async () => {
      const assessmentData = {
        templateId: createdTemplateId,
        employeeId: 3,
        assessmentDate: '2024-12-15',
        notes: 'Q4 Performance Review',
        areaInputs: [
          {
            areaId: 1, // Quality Performance
            baseScore: 85,
            evaluatorComments: 'Good quality work with room for improvement',
            additionalData: {
              nc_incidents: 1,
              quality_improvements: 2
            }
          },
          {
            areaId: 2, // Professional Development
            baseScore: 90,
            evaluatorComments: 'Excellent commitment to learning',
            additionalData: {
              training_completed: 3,
              certifications: 1
            }
          },
          {
            areaId: 3, // Safety & Compliance
            baseScore: 95,
            evaluatorComments: 'Outstanding safety record',
            additionalData: {
              safety_incidents: 0
            }
          }
        ]
      };

      const response = await request(app.getHttpServer())
        .post('/assessments/create-with-scoring')
        .send(assessmentData)
        .expect(201);

      expect(response.body.templateId).toBe(createdTemplateId);
      expect(response.body.employeeId).toBe(3);
      expect(response.body.responses).toHaveLength(3);

      // Verify scoring calculations
      const qualityResponse = response.body.responses.find(r => r.areaId === 1);
      // Base: 85, -5 for NC incident, +6 for quality improvements = 86
      expect(qualityResponse.score).toBe(86);

      const developmentResponse = response.body.responses.find(r => r.areaId === 2);
      // Base: 90, +6 for training, +5 for certification = 101, capped at 100
      expect(developmentResponse.score).toBe(100);

      const safetyResponse = response.body.responses.find(r => r.areaId === 3);
      // Base: 95, no safety incidents, 10% bonus = 104.5, capped at 100
      expect(safetyResponse.score).toBe(100);

      // Verify total score calculation
      // Weighted average: (86 * 0.4 + 100 * 0.3 + 100 * 0.3) = 94.4
      expect(response.body.totalScore).toBeCloseTo(94.4, 1);
    });

    it('should retrieve template usage statistics', async () => {
      const response = await request(app.getHttpServer())
        .get(`/templates/${createdTemplateId}/usage-stats`)
        .expect(200);

      expect(response.body).toHaveProperty('totalAssessments');
      expect(response.body).toHaveProperty('activeAssessments');
      expect(response.body).toHaveProperty('averageScore');
      expect(response.body).toHaveProperty('lastUsed');
    });

    it('should handle manager permissions correctly', async () => {
      // Manager should be able to create personal templates
      const managerTemplateData = {
        name: 'Manager Team Template',
        description: 'Template for my team',
        isGlobal: false,
        areas: [
          {
            name: 'Team Collaboration',
            weight: 1.0,
            maxScore: 100,
            orderIndex: 1,
            scoringRules: []
          }
        ]
      };

      const response = await request(app.getHttpServer())
        .post('/templates')
        .set('test-user', 'manager')
        .send(managerTemplateData)
        .expect(201);

      expect(response.body.name).toBe('Manager Team Template');
      expect(response.body.isGlobal).toBe(false);

      // Manager should not be able to create global templates
      const globalTemplateData = {
        ...managerTemplateData,
        name: 'Attempted Global Template',
        isGlobal: true
      };

      const globalResponse = await request(app.getHttpServer())
        .post('/templates')
        .set('test-user', 'manager')
        .send(globalTemplateData)
        .expect(201);

      // Should ignore the isGlobal flag for managers
      expect(globalResponse.body.isGlobal).toBe(false);
    });

    it('should handle template deactivation and reactivation', async () => {
      // Deactivate template
      await request(app.getHttpServer())
        .post(`/templates/${createdTemplateId}/deactivate`)
        .expect(201);

      const deactivatedTemplate = await templateRepository.findOne({
        where: { id: createdTemplateId }
      });
      expect(deactivatedTemplate.isActive).toBe(false);

      // Reactivate template
      await request(app.getHttpServer())
        .post(`/templates/${createdTemplateId}/activate`)
        .expect(201);

      const reactivatedTemplate = await templateRepository.findOne({
        where: { id: createdTemplateId }
      });
      expect(reactivatedTemplate.isActive).toBe(true);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid template data', async () => {
      const invalidTemplateData = {
        name: '', // Empty name
        areas: [
          {
            name: 'Invalid Area',
            weight: 0.5, // Doesn't sum to 1.0
            maxScore: 0, // Invalid max score
            orderIndex: 1,
            scoringRules: []
          }
        ]
      };

      await request(app.getHttpServer())
        .post('/templates')
        .send(invalidTemplateData)
        .expect(400);
    });

    it('should handle missing assessment area inputs', async () => {
      // First create a valid template
      const templateData = {
        name: 'Test Template',
        areas: [
          {
            name: 'Area 1',
            weight: 0.5,
            maxScore: 100,
            orderIndex: 1,
            scoringRules: []
          },
          {
            name: 'Area 2',
            weight: 0.5,
            maxScore: 100,
            orderIndex: 2,
            scoringRules: []
          }
        ]
      };

      const templateResponse = await request(app.getHttpServer())
        .post('/templates')
        .send(templateData)
        .expect(201);

      // Try to create assessment with missing area input
      const assessmentData = {
        templateId: templateResponse.body.id,
        employeeId: 3,
        assessmentDate: '2024-12-15',
        areaInputs: [
          {
            areaId: templateResponse.body.areas[0].id,
            baseScore: 85,
            additionalData: {}
          }
          // Missing second area input
        ]
      };

      await request(app.getHttpServer())
        .post('/assessments/create-with-scoring')
        .send(assessmentData)
        .expect(400);
    });

    it('should handle non-existent template operations', async () => {
      const nonExistentId = 99999;

      await request(app.getHttpServer())
        .get(`/templates/${nonExistentId}`)
        .expect(404);

      await request(app.getHttpServer())
        .post(`/templates/${nonExistentId}/clone`)
        .expect(404);

      await request(app.getHttpServer())
        .post(`/templates/${nonExistentId}/validate`)
        .expect(404);
    });
  });
});
