import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MonthlyDashboardsController } from './monthly-dashboards.controller';
import { MonthlyDashboardsService } from './monthly-dashboards.service';
import { MonthlyDashboardSeederService } from './monthly-dashboard-seeder.service';
import { MonthlyDashboardSubmission } from './entities/monthly-dashboard-submission.entity';
import { MonthlyDashboardKpi } from './entities/monthly-dashboard-kpi.entity';
import { MonthlyDashboardKpiValue } from './entities/monthly-dashboard-kpi-value.entity';
import { MonthlyDashboardTeamTarget } from './entities/monthly-dashboard-team-target.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      MonthlyDashboardSubmission,
      MonthlyDashboardKpi,
      MonthlyDashboardKpiValue,
      MonthlyDashboardTeamTarget,
      OrganizationalUnit,
      User,
    ]),
  ],
  controllers: [MonthlyDashboardsController],
  providers: [MonthlyDashboardsService, MonthlyDashboardSeederService],
  exports: [MonthlyDashboardsService, MonthlyDashboardSeederService],
})
export class MonthlyDashboardsModule { }
