import React from 'react';
import { Alert, Snackbar } from '@mui/material';

// Simple toast implementation using Material-UI
let toastQueue: Array<{ message: string; type: 'success' | 'error' | 'info' | 'warning' }> = [];
let setToastState: React.Dispatch<React.SetStateAction<any>> | null = null;

export const toast = {
  success: (message: string) => {
    if (setToastState) {
      setToastState({ open: true, message, severity: 'success' });
    }
  },
  error: (message: string) => {
    if (setToastState) {
      setToastState({ open: true, message, severity: 'error' });
    }
  },
  info: (message: string) => {
    if (setToastState) {
      setToastState({ open: true, message, severity: 'info' });
    }
  },
  warning: (message: string) => {
    if (setToastState) {
      setToastState({ open: true, message, severity: 'warning' });
    }
  }
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toastState, setToast] = React.useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  React.useEffect(() => {
    setToastState = setToast;
    return () => {
      setToastState = null;
    };
  }, []);

  const handleClose = () => {
    setToast(prev => ({ ...prev, open: false }));
  };

  return (
    <>
      {children}
      <Snackbar
        open={toastState.open}
        autoHideDuration={6000}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleClose} severity={toastState.severity} sx={{ width: '100%' }}>
          {toastState.message}
        </Alert>
      </Snackbar>
    </>
  );
};
