import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { ApiService } from '../services/api';

// Simple types that match our needs
export interface OrganizationalUnit {
  id: number;
  name: string;
  type: 'organization' | 'division' | 'department' | 'team' | 'squad' | 'unit';
  description?: string;
  parentId?: number;
  level: number;
  managerId?: number;
  budget: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  title?: string;
  role: 'ceo' | 'vp' | 'director' | 'manager' | 'senior_engineer' | 'engineer' | 'junior_engineer' | 'intern' | 'hr_admin' | 'guest';
  organizationalUnitId?: number;
  managerId?: number;
  isActive: boolean;
  phone?: string;
  location?: string;
  startDate?: string;
  employmentType?: string;
  createdAt: string;
  updatedAt: string;
}

// State interface
interface OrganizationState {
  units: OrganizationalUnit[];
  users: User[];
  loading: boolean;
  error: string | null;
  selectedUnit: OrganizationalUnit | null;
}

// Action types
type OrganizationAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_UNITS'; payload: OrganizationalUnit[] }
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'ADD_UNIT'; payload: OrganizationalUnit }
  | { type: 'UPDATE_UNIT'; payload: OrganizationalUnit }
  | { type: 'DELETE_UNIT'; payload: number }
  | { type: 'ADD_USER'; payload: User }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'DELETE_USER'; payload: number }
  | { type: 'SET_SELECTED_UNIT'; payload: OrganizationalUnit | null };

// Initial state
const initialState: OrganizationState = {
  units: [
    {
      id: 1,
      name: 'EHRX Corporation',
      type: 'organization',
      description: 'Main organization',
      level: 0,
      budget: 10000000,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 2,
      name: 'Engineering Division',
      type: 'division',
      description: 'Software development and engineering',
      parentId: 1,
      level: 1,
      budget: 5000000,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 3,
      name: 'Frontend Team',
      type: 'team',
      description: 'React and UI development',
      parentId: 2,
      level: 2,
      budget: 1500000,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 4,
      name: 'Backend Team',
      type: 'team',
      description: 'API and database development',
      parentId: 2,
      level: 2,
      budget: 2000000,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  ],
  users: [
    {
      id: 1,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      title: 'Chief Technology Officer',
      role: 'ceo',
      organizationalUnitId: 1,
      isActive: true,
      phone: '******-0101',
      location: 'New York, NY',
      startDate: '2020-01-15',
      employmentType: 'Full-time',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 2,
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
      title: 'Engineering Manager',
      role: 'manager',
      organizationalUnitId: 2,
      managerId: 1,
      isActive: true,
      phone: '******-0102',
      location: 'San Francisco, CA',
      startDate: '2020-03-01',
      employmentType: 'Full-time',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 3,
      email: '<EMAIL>',
      firstName: 'Mike',
      lastName: 'Johnson',
      title: 'Senior Frontend Developer',
      role: 'senior_engineer',
      organizationalUnitId: 3,
      managerId: 2,
      isActive: true,
      phone: '******-0103',
      location: 'Austin, TX',
      startDate: '2021-06-15',
      employmentType: 'Full-time',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 4,
      email: '<EMAIL>',
      firstName: 'Sarah',
      lastName: 'Wilson',
      title: 'Backend Developer',
      role: 'engineer',
      organizationalUnitId: 4,
      managerId: 2,
      isActive: true,
      phone: '******-0104',
      location: 'Seattle, WA',
      startDate: '2021-09-01',
      employmentType: 'Full-time',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  ],
  loading: false,
  error: null,
  selectedUnit: null,
};

// Reducer
const organizationReducer = (state: OrganizationState, action: OrganizationAction): OrganizationState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_UNITS':
      return { ...state, units: action.payload, loading: false };
    case 'SET_USERS':
      return { ...state, users: action.payload, loading: false };
    case 'ADD_UNIT':
      return { ...state, units: [...state.units, action.payload] };
    case 'UPDATE_UNIT':
      return {
        ...state,
        units: state.units.map(unit =>
          unit.id === action.payload.id ? action.payload : unit
        )
      };
    case 'DELETE_UNIT':
      return {
        ...state,
        units: state.units.filter(unit => unit.id !== action.payload)
      };
    case 'ADD_USER':
      return { ...state, users: [...state.users, action.payload] };
    case 'UPDATE_USER':
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id ? action.payload : user
        )
      };
    case 'DELETE_USER':
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload)
      };
    case 'SET_SELECTED_UNIT':
      return { ...state, selectedUnit: action.payload };
    default:
      return state;
  }
};

// Context type
interface OrganizationContextType {
  state: OrganizationState;
  actions: {
    loadData: () => Promise<void>;
    createUnit: (unitData: Partial<OrganizationalUnit>) => Promise<void>;
    updateUnit: (id: number, unitData: Partial<OrganizationalUnit>) => Promise<void>;
    deleteUnit: (id: number) => Promise<void>;
    createUser: (userData: Partial<User>) => Promise<void>;
    updateUser: (id: number, userData: Partial<User>) => Promise<void>;
    deleteUser: (id: number) => Promise<void>;
    assignUserToUnit: (userId: number, unitId: number) => Promise<void>;
    setError: (error: string | null) => void;
    setSelectedUnit: (unit: OrganizationalUnit | null) => void;
  };
}

// Context
const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

// Provider Component
export const OrganizationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(organizationReducer, initialState);

  // Actions
  const actions = {
    loadData: async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        // Try to load from API, fallback to mock data if API fails
        try {
          const [units, users] = await Promise.all([
            ApiService.getOrganizationalUnits(),
            ApiService.getUsers()
          ]);

          dispatch({ type: 'SET_UNITS', payload: units });
          dispatch({ type: 'SET_USERS', payload: users });
        } catch (apiError) {
          // Fallback to mock data if API is not available
          console.warn('API not available, using mock data:', apiError);
          // Mock data is already in initialState, so no need to dispatch again
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to load data' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    createUnit: async (unitData: Partial<OrganizationalUnit>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Try API first, fallback to mock if API fails
        try {
          const newUnit = await ApiService.createOrganizationalUnit(unitData);
          dispatch({ type: 'ADD_UNIT', payload: newUnit });
        } catch (apiError) {
          // Fallback to mock creation
          console.warn('API not available, using mock creation:', apiError);
          const newUnit: OrganizationalUnit = {
            id: Math.max(...state.units.map(u => u.id)) + 1,
            name: unitData.name || 'New Unit',
            type: unitData.type || 'unit',
            description: unitData.description,
            parentId: unitData.parentId,
            level: unitData.parentId ? (state.units.find(u => u.id === unitData.parentId)?.level || 0) + 1 : 0,
            managerId: unitData.managerId,
            budget: unitData.budget || 0,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          dispatch({ type: 'ADD_UNIT', payload: newUnit });
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to create unit' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    updateUnit: async (id: number, unitData: Partial<OrganizationalUnit>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Try API first, fallback to mock if API fails
        try {
          const updatedUnit = await ApiService.updateOrganizationalUnit(id, unitData);
          dispatch({ type: 'UPDATE_UNIT', payload: updatedUnit });
        } catch (apiError) {
          // Fallback to mock update
          console.warn('API not available, using mock update:', apiError);
          const existingUnit = state.units.find(u => u.id === id);
          if (existingUnit) {
            const updatedUnit = {
              ...existingUnit,
              ...unitData,
              updatedAt: new Date().toISOString(),
            };
            dispatch({ type: 'UPDATE_UNIT', payload: updatedUnit });
          }
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to update unit' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    deleteUnit: async (id: number) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Try API first, fallback to mock if API fails
        try {
          await ApiService.deleteOrganizationalUnit(id);
          dispatch({ type: 'DELETE_UNIT', payload: id });
        } catch (apiError) {
          // Fallback to mock deletion
          console.warn('API not available, using mock deletion:', apiError);
          dispatch({ type: 'DELETE_UNIT', payload: id });
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to delete unit' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    createUser: async (userData: Partial<User>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Try API first, fallback to mock if API fails
        try {
          const newUser = await ApiService.createUser(userData);
          dispatch({ type: 'ADD_USER', payload: newUser });
        } catch (apiError) {
          // Fallback to mock creation
          console.warn('API not available, using mock user creation:', apiError);
          const newUser: User = {
            id: Math.max(...state.users.map(u => u.id)) + 1,
            email: userData.email || '',
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            title: userData.title,
            role: userData.role || 'engineer',
            organizationalUnitId: userData.organizationalUnitId,
            managerId: userData.managerId,
            isActive: true,
            phone: userData.phone,
            location: userData.location,
            startDate: userData.startDate,
            employmentType: userData.employmentType || 'Full-time',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          dispatch({ type: 'ADD_USER', payload: newUser });
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to create user' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    updateUser: async (id: number, userData: Partial<User>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Try API first, fallback to mock if API fails
        try {
          const updatedUser = await ApiService.updateUser(id, userData);
          dispatch({ type: 'UPDATE_USER', payload: updatedUser });
        } catch (apiError) {
          // Fallback to mock update
          console.warn('API not available, using mock user update:', apiError);
          const existingUser = state.users.find(u => u.id === id);
          if (existingUser) {
            const updatedUser = {
              ...existingUser,
              ...userData,
              updatedAt: new Date().toISOString(),
            };
            dispatch({ type: 'UPDATE_USER', payload: updatedUser });
          }
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to update user' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    deleteUser: async (id: number) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Try API first, fallback to mock if API fails
        try {
          await ApiService.deleteUser(id);
          dispatch({ type: 'DELETE_USER', payload: id });
        } catch (apiError) {
          // Fallback to mock deletion
          console.warn('API not available, using mock user deletion:', apiError);
          dispatch({ type: 'DELETE_USER', payload: id });
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to delete user' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    assignUserToUnit: async (userId: number, unitId: number) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Try API first, fallback to mock if API fails
        try {
          const updatedUser = await ApiService.updateUser(userId, { organizationalUnitId: unitId });
          dispatch({ type: 'UPDATE_USER', payload: updatedUser });
        } catch (apiError) {
          // Fallback to mock assignment
          console.warn('API not available, using mock user assignment:', apiError);
          const existingUser = state.users.find(u => u.id === userId);
          if (existingUser) {
            const updatedUser = {
              ...existingUser,
              organizationalUnitId: unitId,
              updatedAt: new Date().toISOString(),
            };
            dispatch({ type: 'UPDATE_USER', payload: updatedUser });
          }
        }
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to assign user to unit' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    setError: (error: string | null) => {
      dispatch({ type: 'SET_ERROR', payload: error });
    },

    setSelectedUnit: (unit: OrganizationalUnit | null) => {
      dispatch({ type: 'SET_SELECTED_UNIT', payload: unit });
    },
  };

  // Temporarily disable auto-loading to prevent API errors
  useEffect(() => {
    // actions.loadData(); // Disabled until backend is fixed
  }, []);

  return (
    <OrganizationContext.Provider value={{ state, actions }}>
      {children}
    </OrganizationContext.Provider>
  );
};

// Hook to use the context
export const useOrganization = () => {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};

export default OrganizationContext;
