import React from 'react';
import {
  Table as MuiTable,
  TableBody as Mui<PERSON>ableBody,
  TableCell as <PERSON>iTableCell,
  TableHead as MuiTableHead,
  TableRow as MuiTableRow,
  TableContainer,
  Paper,
  SxProps,
  Theme
} from '@mui/material';

interface TableRowProps {
  children: React.ReactNode;
  className?: string;
  sx?: SxProps<Theme>;
}

interface TableHeadProps {
  children: React.ReactNode;
  className?: string;
  sx?: SxProps<Theme>;
}

interface TableCellProps {
  children: React.ReactNode;
  className?: string;
  rowSpan?: number;
  sx?: SxProps<Theme>;
}

export const Table: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <TableContainer component={Paper}>
    <MuiTable>
      {children}
    </MuiTable>
  </TableContainer>
);

export const TableHeader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MuiTableHead>
    {children}
  </MuiTableHead>
);

export const TableBody: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MuiTableBody>
    {children}
  </MuiTableBody>
);

export const TableRow: React.FC<TableRowProps> = ({ children, className, sx }) => (
  <MuiTableRow className={className} sx={sx}>
    {children}
  </MuiTableRow>
);

export const TableHead: React.FC<TableHeadProps> = ({ children, className, sx }) => (
  <MuiTableCell component="th" className={className} sx={{ fontWeight: 'bold', ...sx }}>
    {children}
  </MuiTableCell>
);

export const TableCell: React.FC<TableCellProps> = ({ children, className, rowSpan, sx }) => (
  <MuiTableCell className={className} rowSpan={rowSpan} sx={sx}>
    {children}
  </MuiTableCell>
);
