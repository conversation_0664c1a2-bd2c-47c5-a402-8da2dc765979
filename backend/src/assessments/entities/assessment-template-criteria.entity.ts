import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AssessmentTemplate } from './assessment-template.entity';
import { AssessmentCriteria } from './assessment-criteria.entity';

@Entity('assessment_template_criteria')
export class AssessmentTemplateCriteria {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'template_id' })
  templateId: number;

  @ManyToOne(() => AssessmentTemplate, template => template.templateCriteria)
  @JoinColumn({ name: 'template_id' })
  template: AssessmentTemplate;

  @Column({ name: 'criteria_id' })
  criteriaId: number;

  @ManyToOne(() => AssessmentCriteria)
  @JoinColumn({ name: 'criteria_id' })
  criteria: AssessmentCriteria;

  @Column({ name: 'is_required', default: true })
  isRequired: boolean;

  @Column({ name: 'order_index', default: 1 })
  orderIndex: number;

  @Column({ 
    name: 'weight_override', 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    nullable: true 
  })
  weightOverride: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
