import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';

@Entity('career_paths')
export class CareerPath {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'from_role', length: 255 })
  fromRole: string;

  @Column({ name: 'to_role', length: 255 })
  toRole: string;

  @Column({ name: 'organizational_unit_id', nullable: true })
  organizationalUnitId: number;

  @Column({ name: 'required_skills', type: 'json', nullable: true })
  requiredSkills: object;

  @Column({ name: 'recommended_experience_years', nullable: true })
  recommendedExperienceYears: number;

  @Column({ name: 'typical_timeline_months', nullable: true })
  typicalTimelineMonths: number;

  @Column({ name: 'success_rate', type: 'decimal', precision: 5, scale: 2, nullable: true })
  successRate: number;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => OrganizationalUnit, { nullable: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;
}
