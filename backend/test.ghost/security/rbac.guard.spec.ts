import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';

import { RbacGuard } from '../../src/auth/guards/rbac.guard';
import { UserRole } from '../../src/users/enums/user-role.enum';
import { ROLES_KEY } from '../../src/auth/decorators/roles.decorator';

describe('RbacGuard', () => {
  let guard: RbacGuard;
  let reflector: Reflector;
  let jwtService: JwtService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RbacGuard,
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            verifyAsync: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<RbacGuard>(RbacGuard);
    reflector = module.get<Reflector>(Reflector);
    jwtService = module.get<JwtService>(JwtService);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    let mockContext: ExecutionContext;
    const mockRequest = {
      headers: {},
      user: null,
    };

    beforeEach(() => {
      mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
        getHandler: () => {},
        getClass: () => {},
      } as unknown as ExecutionContext;
    });

    it('should throw UnauthorizedException if no authorization header is present', async () => {
      mockRequest.headers = {};

      await expect(guard.canActivate(mockContext)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if token format is incorrect', async () => {
      mockRequest.headers = { authorization: 'Invalid-format' };

      await expect(guard.canActivate(mockContext)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if token verification fails', async () => {
      mockRequest.headers = { authorization: 'Bearer invalid-token' };
      jest.spyOn(jwtService, 'verifyAsync').mockRejectedValue(new Error('Invalid token'));

      await expect(guard.canActivate(mockContext)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw ForbiddenException if user role does not match required roles', async () => {
      mockRequest.headers = { authorization: 'Bearer valid-token' };
      
      // Mock token verification
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({
        sub: 1,
        role: UserRole.EMPLOYEE,
      });
      
      // Mock required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.HR_ADMIN]);

      await expect(guard.canActivate(mockContext)).rejects.toThrow(ForbiddenException);
    });

    it('should allow access if user role matches required roles', async () => {
      mockRequest.headers = { authorization: 'Bearer valid-token' };
      
      // Mock token verification
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({
        sub: 1,
        role: UserRole.HR_ADMIN,
      });
      
      // Mock required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.HR_ADMIN]);

      const result = await guard.canActivate(mockContext);
      expect(result).toBe(true);
      expect(mockRequest.user).toEqual({
        id: 1,
        role: UserRole.HR_ADMIN,
      });
    });

    it('should allow access if user role is in required roles array', async () => {
      mockRequest.headers = { authorization: 'Bearer valid-token' };
      
      // Mock token verification
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({
        sub: 1,
        role: UserRole.MANAGER,
      });
      
      // Mock required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.MANAGER, UserRole.HR_ADMIN]);

      const result = await guard.canActivate(mockContext);
      expect(result).toBe(true);
    });

    it('should allow access if no roles are required for the route', async () => {
      mockRequest.headers = { authorization: 'Bearer valid-token' };
      
      // Mock token verification
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({
        sub: 1,
        role: UserRole.EMPLOYEE,
      });
      
      // Mock no required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(undefined);

      const result = await guard.canActivate(mockContext);
      expect(result).toBe(true);
    });
  });
});
