// EHRX Production Server - Serves React Build
const express = require('express');
const path = require('path');
const app = express();
const PORT = process.env.PORT || 3080;

// Mock users for login testing
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    password: 'password123', // In real app, this would be hashed
    firstName: 'Admin',
    lastName: 'User',
    role: 'hr_admin',
    title: 'System Administrator',
    phone: '+45 12 34 56 78',
    location: 'Copenhagen, Denmark',
    mustChangePassword: false,
    twoFactorEnabled: false,
    lastLoginAt: new Date().toISOString(),
    accountStatus: 'active',
    organizationalUnitId: 1,
    managerId: null
  },
  {
    id: 2,
    email: '<EMAIL>',
    password: '<PERSON>senX123',
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON>',
    role: 'engineer',
    title: 'Software Engineer',
    phone: '+45 98 76 54 32',
    location: 'Aarhus, Denmark',
    mustChangePassword: true,
    twoFactorEnabled: false,
    lastLoginAt: '2024-07-11T10:30:00Z',
    accountStatus: 'active',
    organizationalUnitId: 5,
    managerId: 5
  },
  {
    id: 3,
    email: '<EMAIL>',
    password: 'manager123',
    firstName: 'Sarah',
    lastName: 'Johnson',
    role: 'manager',
    title: 'Engineering Manager',
    phone: '+45 11 22 33 44',
    location: 'Copenhagen, Denmark',
    mustChangePassword: false,
    twoFactorEnabled: true,
    lastLoginAt: new Date().toISOString(),
    accountStatus: 'active',
    organizationalUnitId: 5,
    managerId: 2
  }
];

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Authentication endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  console.log('Login attempt:', { email, password });

  const user = mockUsers.find(u => u.email === email && u.password === password);

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password'
    });
  }

  // Generate a mock JWT token
  const token = 'mock-jwt-token-' + Date.now();

  // Update last login
  user.lastLoginAt = new Date().toISOString();

  res.json({
    success: true,
    user: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      title: user.title,
      phone: user.phone,
      location: user.location,
      mustChangePassword: user.mustChangePassword,
      twoFactorEnabled: user.twoFactorEnabled,
      lastLoginAt: user.lastLoginAt,
      accountStatus: user.accountStatus,
      organizationalUnitId: user.organizationalUnitId,
      managerId: user.managerId
    },
    access_token: token,
    expires_in: 3600
  });
});

app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

app.get('/api/auth/profile', (req, res) => {
  // For demo purposes, return the admin user
  const user = mockUsers[0];
  res.json({
    success: true,
    data: user
  });
});

app.patch('/api/users/profile', (req, res) => {
  // For demo purposes, just return success
  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: { ...mockUsers[0], ...req.body }
  });
});

app.post('/api/auth/change-password', (req, res) => {
  const { currentPassword, newPassword } = req.body;

  // For demo purposes, just return success
  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

app.get('/api/auth/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

// In-memory storage for templates (simulates database)
let templatesStorage = {
  hrLevel: [
    {
      id: 1,
      name: 'HR Corporate Standards Template',
      description: 'Company-wide mandatory assessment criteria template',
      version: '2.1',
      templateLevel: 'hr_level',
      organizationalUnit: null,
      criteria: [
        { id: 1, name: 'Professional Ethics & Integrity', weight: 2.0, isRequired: true, orderIndex: 1 },
        { id: 2, name: 'Communication Skills', weight: 1.5, isRequired: true, orderIndex: 2 },
        { id: 3, name: 'Collaboration & Teamwork', weight: 1.5, isRequired: true, orderIndex: 3 },
        { id: 4, name: 'Adaptability & Learning', weight: 1.0, isRequired: true, orderIndex: 4 },
        { id: 5, name: 'Time Management & Reliability', weight: 1.0, isRequired: true, orderIndex: 5 },
        { id: 6, name: 'Customer Focus', weight: 1.0, isRequired: true, orderIndex: 6 }
      ],
      isActive: true,
      createdAt: '2024-01-15',
      createdBy: 'HR Department',
      usageCount: 45
    },
    {
      id: 2,
      name: 'Annual Performance Review Template',
      description: 'Comprehensive yearly evaluation with HR standards',
      version: '2.0',
      templateLevel: 'hr_level',
      organizationalUnit: null,
      criteria: [
        { id: 1, name: 'Professional Ethics & Integrity', weight: 2.0, isRequired: true, orderIndex: 1 },
        { id: 2, name: 'Communication Skills', weight: 1.5, isRequired: true, orderIndex: 2 },
        { id: 3, name: 'Collaboration & Teamwork', weight: 1.5, isRequired: true, orderIndex: 3 },
        { id: 4, name: 'Adaptability & Learning', weight: 1.0, isRequired: true, orderIndex: 4 },
        { id: 5, name: 'Time Management & Reliability', weight: 1.0, isRequired: true, orderIndex: 5 },
        { id: 6, name: 'Customer Focus', weight: 1.0, isRequired: true, orderIndex: 6 }
      ],
      isActive: true,
      createdAt: '2024-01-10',
      createdBy: 'HR Department',
      usageCount: 38
    }
  ],
  organizationalLevel: [
    {
      id: 4,
      name: 'Technology Division Excellence Template',
      description: 'Technical assessment criteria for technology teams',
      version: '1.8',
      templateLevel: 'organizational_level',
      organizationalUnit: { id: 2, name: 'Technology Division', type: 'division' },
      criteria: [
        { id: 7, name: 'Technical Excellence', weight: 2.0, isRequired: true, orderIndex: 1 },
        { id: 8, name: 'Code Quality & Best Practices', weight: 1.5, isRequired: true, orderIndex: 2 },
        { id: 9, name: 'System Design & Architecture', weight: 1.5, isRequired: true, orderIndex: 3 },
        { id: 10, name: 'Security Awareness', weight: 1.0, isRequired: true, orderIndex: 4 },
        { id: 11, name: 'Innovation & Problem Solving', weight: 1.0, isRequired: true, orderIndex: 5 }
      ],
      isActive: true,
      createdAt: '2024-02-01',
      createdBy: 'Technology Division',
      usageCount: 22
    },
    {
      id: 5,
      name: 'Product Division Innovation Template',
      description: 'Product-focused assessment criteria',
      version: '1.6',
      templateLevel: 'organizational_level',
      organizationalUnit: { id: 3, name: 'Product Division', type: 'division' },
      criteria: [
        { id: 12, name: 'Product Strategy & Vision', weight: 2.0, isRequired: true, orderIndex: 1 },
        { id: 13, name: 'User Experience Focus', weight: 1.5, isRequired: true, orderIndex: 2 },
        { id: 14, name: 'Data-Driven Decision Making', weight: 1.5, isRequired: true, orderIndex: 3 },
        { id: 15, name: 'Stakeholder Management', weight: 1.0, isRequired: true, orderIndex: 4 },
        { id: 16, name: 'Market Research & Analysis', weight: 1.0, isRequired: true, orderIndex: 5 }
      ],
      isActive: true,
      createdAt: '2024-02-15',
      createdBy: 'Product Division',
      usageCount: 18
    }
  ],
  teamLevel: [
    {
      id: 7,
      name: 'Frontend Engineering Assessment',
      description: 'Frontend development specific criteria',
      version: '2.2',
      templateLevel: 'team_level',
      organizationalUnit: { id: 11, name: 'Frontend Team', type: 'team' },
      criteria: [
        { id: 17, name: 'React/TypeScript Proficiency', weight: 2.0, isRequired: true, orderIndex: 1 },
        { id: 18, name: 'UI/UX Implementation', weight: 1.5, isRequired: true, orderIndex: 2 },
        { id: 19, name: 'Performance Optimization', weight: 1.5, isRequired: true, orderIndex: 3 },
        { id: 20, name: 'Testing & Quality Assurance', weight: 1.0, isRequired: true, orderIndex: 4 }
      ],
      isActive: true,
      createdAt: '2024-03-01',
      createdBy: 'Frontend Team',
      usageCount: 12
    },
    {
      id: 8,
      name: 'Backend Engineering Assessment',
      description: 'Backend development specific criteria',
      version: '2.0',
      templateLevel: 'team_level',
      organizationalUnit: { id: 12, name: 'Backend Team', type: 'team' },
      criteria: [
        { id: 21, name: 'API Design & Development', weight: 2.0, isRequired: true, orderIndex: 1 },
        { id: 22, name: 'Database Design & Optimization', weight: 1.5, isRequired: true, orderIndex: 2 },
        { id: 23, name: 'Microservices Architecture', weight: 1.5, isRequired: true, orderIndex: 3 },
        { id: 24, name: 'Performance & Scalability', weight: 1.0, isRequired: true, orderIndex: 4 }
      ],
      isActive: true,
      createdAt: '2024-03-10',
      createdBy: 'Backend Team',
      usageCount: 15
    },
    {
      id: 9,
      name: 'DevOps Engineering Assessment',
      description: 'DevOps and infrastructure specific criteria',
      version: '1.9',
      templateLevel: 'team_level',
      organizationalUnit: { id: 13, name: 'DevOps Team', type: 'team' },
      criteria: [
        { id: 25, name: 'Infrastructure as Code', weight: 2.0, isRequired: true, orderIndex: 1 },
        { id: 26, name: 'CI/CD Pipeline Management', weight: 1.5, isRequired: true, orderIndex: 2 },
        { id: 27, name: 'Monitoring & Observability', weight: 1.5, isRequired: true, orderIndex: 3 },
        { id: 28, name: 'Cloud Platform Expertise', weight: 1.0, isRequired: true, orderIndex: 4 }
      ],
      isActive: true,
      createdAt: '2024-03-15',
      createdBy: 'DevOps Team',
      usageCount: 8
    }
  ]
};

// Serve static files from the React app build directory
app.use(express.static(path.join(__dirname, 'frontend/build')));

// API routes (mock data for demo)
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'EHRX Demo Server is running' });
});

// Mock API endpoints for organizational units
app.get('/api/teams/organizational-units', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 1,
        name: 'EHRX Corporation',
        type: 'organization',
        description: 'Main corporate headquarters',
        parentId: null,
        managerId: 1,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      },
      {
        id: 2,
        name: 'Technology Division',
        type: 'division',
        description: 'Software development and IT operations',
        parentId: 1,
        managerId: 2,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      },
      {
        id: 3,
        name: 'Product Division',
        type: 'division',
        description: 'Product management and strategy',
        parentId: 1,
        managerId: 3,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      },
      {
        id: 4,
        name: 'Operations Division',
        type: 'division',
        description: 'Business operations and support',
        parentId: 1,
        managerId: 4,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      },
      {
        id: 5,
        name: 'Engineering Team',
        type: 'team',
        description: 'Core software engineering team',
        parentId: 2,
        managerId: 5,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      },
      {
        id: 6,
        name: 'DevOps Team',
        type: 'team',
        description: 'Infrastructure and deployment team',
        parentId: 2,
        managerId: 6,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      }
    ]
  });
});

// Mock API endpoints for users
app.get('/api/users', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 1,
        firstName: 'Robert',
        lastName: 'Chen',
        email: '<EMAIL>',
        title: 'Chief Executive Officer',
        role: 'ceo',
        organizationalUnitId: 1
      },
      {
        id: 2,
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        title: 'VP of Technology',
        role: 'manager',
        organizationalUnitId: 2
      },
      {
        id: 3,
        firstName: 'Michael',
        lastName: 'Davis',
        email: '<EMAIL>',
        title: 'VP of Product',
        role: 'manager',
        organizationalUnitId: 3
      },
      {
        id: 4,
        firstName: 'Lisa',
        lastName: 'Wilson',
        email: '<EMAIL>',
        title: 'VP of Operations',
        role: 'manager',
        organizationalUnitId: 4
      },
      {
        id: 5,
        firstName: 'David',
        lastName: 'Rodriguez',
        email: '<EMAIL>',
        title: 'Engineering Manager',
        role: 'manager',
        organizationalUnitId: 5
      },
      {
        id: 6,
        firstName: 'Emily',
        lastName: 'Thompson',
        email: '<EMAIL>',
        title: 'DevOps Manager',
        role: 'manager',
        organizationalUnitId: 6
      },
      {
        id: 7,
        firstName: 'James',
        lastName: 'Anderson',
        email: '<EMAIL>',
        title: 'Senior Software Engineer',
        role: 'engineer',
        organizationalUnitId: 5
      },
      {
        id: 8,
        firstName: 'Maria',
        lastName: 'Garcia',
        email: '<EMAIL>',
        title: 'Software Engineer',
        role: 'engineer',
        organizationalUnitId: 5
      },
      {
        id: 9,
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        title: 'DevOps Engineer',
        role: 'engineer',
        organizationalUnitId: 6
      },
      {
        id: 10,
        firstName: 'Anna',
        lastName: 'Brown',
        email: '<EMAIL>',
        title: 'Product Manager',
        role: 'manager',
        organizationalUnitId: 3
      }
    ]
  });
});

// Assessment Templates API endpoints
app.get('/api/assessments/templates', (req, res) => {
  res.json({
    success: true,
    data: templatesStorage
  });
});

// Template CRUD operations
app.get('/api/assessments/templates/:id', (req, res) => {
  const templateId = parseInt(req.params.id);

  // Search for template in all levels
  let foundTemplate = null;

  ['hrLevel', 'organizationalLevel', 'teamLevel'].forEach(level => {
    const template = templatesStorage[level].find(t => t.id === templateId);
    if (template) {
      foundTemplate = template;
    }
  });

  if (foundTemplate) {
    res.json({
      success: true,
      data: foundTemplate
    });
  } else {
    res.status(404).json({
      success: false,
      error: 'Template not found'
    });
  }
});

app.post('/api/assessments/templates', (req, res) => {
  res.json({
    success: true,
    data: { id: Date.now(), ...req.body },
    message: 'Template created successfully'
  });
});

app.put('/api/assessments/templates/:id', (req, res) => {
  const templateId = parseInt(req.params.id);
  const updateData = req.body;

  // Find and update the template in storage
  let templateFound = false;

  // Search in all levels
  ['hrLevel', 'organizationalLevel', 'teamLevel'].forEach(level => {
    const templateIndex = templatesStorage[level].findIndex(t => t.id === templateId);
    if (templateIndex !== -1) {
      // Update the template
      templatesStorage[level][templateIndex] = {
        ...templatesStorage[level][templateIndex],
        ...updateData,
        id: templateId // Ensure ID doesn't change
      };
      templateFound = true;
    }
  });

  if (templateFound) {
    res.json({
      success: true,
      data: { id: templateId, ...updateData },
      message: 'Template updated successfully'
    });
  } else {
    res.status(404).json({
      success: false,
      error: 'Template not found'
    });
  }
});

app.delete('/api/assessments/templates/:id', (req, res) => {
  const templateId = parseInt(req.params.id);

  // Find and delete the template from storage
  let templateDeleted = false;

  // Search in all levels
  ['hrLevel', 'organizationalLevel', 'teamLevel'].forEach(level => {
    const templateIndex = templatesStorage[level].findIndex(t => t.id === templateId);
    if (templateIndex !== -1) {
      // Remove the template
      templatesStorage[level].splice(templateIndex, 1);
      templateDeleted = true;
    }
  });

  if (templateDeleted) {
    res.json({
      success: true,
      message: 'Template deleted successfully'
    });
  } else {
    res.status(404).json({
      success: false,
      error: 'Template not found'
    });
  }
});

app.post('/api/assessments/templates/:id/duplicate', (req, res) => {
  res.json({
    success: true,
    data: { id: Date.now(), name: req.body.name },
    message: 'Template duplicated successfully'
  });
});

app.post('/api/assessments/templates/seed-mock-data', (req, res) => {
  res.json({
    success: true,
    message: 'Mock templates seeded successfully'
  });
});

// Employee Assessment API endpoints
app.get('/api/assessments/employees/team/:teamId', (req, res) => {
  const teamId = parseInt(req.params.teamId);
  res.json({
    success: true,
    data: [
      {
        employee: {
          id: 1,
          firstName: 'John',
          lastName: 'Doe',
          title: 'Senior Developer',
          email: '<EMAIL>',
          organizationalUnit: {
            id: teamId,
            name: 'Frontend Team',
            type: 'team'
          }
        },
        applicableTemplates: [
          { id: 1, name: 'HR Corporate Standards Template' },
          { id: 4, name: 'Technology Division Excellence Template' },
          { id: 7, name: 'Frontend Engineering Assessment' }
        ],
        assessmentCriteria: {
          hrLevel: [
            { id: 1, name: 'Professional Ethics & Integrity', weight: 2.0 },
            { id: 2, name: 'Communication Skills', weight: 1.5 }
          ],
          organizationalLevel: [
            { id: 7, name: 'Technical Excellence', weight: 2.0 },
            { id: 8, name: 'Code Quality & Best Practices', weight: 1.5 }
          ],
          teamLevel: [
            { id: 17, name: 'React/TypeScript Proficiency', weight: 2.0 },
            { id: 18, name: 'UI/UX Implementation', weight: 1.5 }
          ]
        },
        hasActiveAssessment: false,
        lastAssessmentDate: null,
        assessmentStatus: 'not_started'
      }
    ]
  });
});

// Database Management API endpoints
app.get('/api/database/tables/:tableName', (req, res) => {
  const tableName = req.params.tableName;

  // Mock database data based on table name
  const mockData = getDatabaseTableData(tableName);

  res.json({
    success: true,
    data: mockData
  });
});

app.post('/api/database/tables/:tableName', (req, res) => {
  const tableName = req.params.tableName;
  const recordData = req.body;

  // Simulate creating a new record
  const newRecord = {
    id: Date.now(), // Simple ID generation
    ...recordData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  res.json({
    success: true,
    data: newRecord,
    message: 'Record created successfully'
  });
});

app.put('/api/database/tables/:tableName/:id', (req, res) => {
  const tableName = req.params.tableName;
  const recordId = parseInt(req.params.id);
  const recordData = req.body;

  // Simulate updating a record
  const updatedRecord = {
    id: recordId,
    ...recordData,
    updatedAt: new Date().toISOString()
  };

  res.json({
    success: true,
    data: updatedRecord,
    message: 'Record updated successfully'
  });
});

app.delete('/api/database/tables/:tableName/:id', (req, res) => {
  const tableName = req.params.tableName;
  const recordId = parseInt(req.params.id);

  res.json({
    success: true,
    message: 'Record deleted successfully'
  });
});

app.get('/api/database/tables/:tableName/schema', (req, res) => {
  const tableName = req.params.tableName;
  const schema = getTableSchema(tableName);

  res.json({
    success: true,
    data: schema
  });
});

// Catch all handler: send back React's index.html file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend/build/index.html'));
});

// Helper function to get mock database table data
function getDatabaseTableData(tableName) {
  switch (tableName) {
    case 'users':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'firstName', label: 'First Name', type: 'text', required: true },
          { id: 'lastName', label: 'Last Name', type: 'text', required: true },
          { id: 'email', label: 'Email', type: 'email', required: true },
          { id: 'roleId', label: 'Role ID', type: 'number', required: true },
          { id: 'jobTitleId', label: 'Job Title ID', type: 'number', required: true },
          { id: 'organizationalUnitId', label: 'Team ID', type: 'number', required: true },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, firstName: 'John', lastName: 'Doe', email: '<EMAIL>', roleId: 4, jobTitleId: 3, organizationalUnitId: 11, isActive: true, createdAt: '2024-01-15T10:00:00Z' },
          { id: 2, firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', roleId: 2, jobTitleId: 5, organizationalUnitId: 3, isActive: true, createdAt: '2024-01-16T10:00:00Z' },
          { id: 3, firstName: 'Mike', lastName: 'Johnson', email: '<EMAIL>', roleId: 4, jobTitleId: 7, organizationalUnitId: 13, isActive: true, createdAt: '2024-01-17T10:00:00Z' },
          { id: 4, firstName: 'Sarah', lastName: 'Wilson', email: '<EMAIL>', roleId: 4, jobTitleId: 6, organizationalUnitId: 11, isActive: true, createdAt: '2024-01-18T10:00:00Z' },
          { id: 5, firstName: 'David', lastName: 'Brown', email: '<EMAIL>', roleId: 4, jobTitleId: 1, organizationalUnitId: 12, isActive: false, createdAt: '2024-01-19T10:00:00Z' }
        ]
      };

    case 'roles':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Role Name', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: true },
          { id: 'permissions', label: 'Permissions', type: 'textarea', required: false },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, name: 'Administrator', description: 'Full system access and administration rights', permissions: 'ALL', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 2, name: 'HR Employee', description: 'Human resources management and employee data access', permissions: 'HR_FULL,ASSESSMENTS_FULL,USERS_READ', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 3, name: 'Manager', description: 'Team management and assessment capabilities', permissions: 'TEAM_MANAGE,ASSESSMENTS_TEAM,USERS_TEAM', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 4, name: 'Employee', description: 'Standard employee access', permissions: 'PROFILE_EDIT,ASSESSMENTS_VIEW', isActive: true, createdAt: '2024-01-01T10:00:00Z' }
        ]
      };

    case 'job_titles':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'title', label: 'Job Title', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: true },
          { id: 'level', label: 'Level', type: 'select', options: ['Entry', 'Junior', 'Mid', 'Senior', 'Lead', 'Principal', 'Director', 'VP', 'C-Level'], required: true },
          { id: 'department', label: 'Department', type: 'text', required: true },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, title: 'Software Developer', description: 'Develops software applications', level: 'Mid', department: 'Technology', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 2, title: 'Junior Developer', description: 'Entry-level software development', level: 'Junior', department: 'Technology', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 3, title: 'Senior Developer', description: 'Experienced software developer', level: 'Senior', department: 'Technology', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 4, title: 'Team Lead', description: 'Technical team leadership', level: 'Lead', department: 'Technology', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 5, title: 'Product Manager', description: 'Product strategy and management', level: 'Mid', department: 'Product', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 6, title: 'UX Designer', description: 'User experience design', level: 'Mid', department: 'Design', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 7, title: 'DevOps Engineer', description: 'Infrastructure and deployment', level: 'Senior', department: 'Technology', isActive: true, createdAt: '2024-01-01T10:00:00Z' },
          { id: 8, title: 'HR Manager', description: 'Human resources management', level: 'Senior', department: 'HR', isActive: true, createdAt: '2024-01-01T10:00:00Z' }
        ]
      };

    case 'organizational_units':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Name', type: 'text', required: true },
          { id: 'type', label: 'Type', type: 'select', options: ['company', 'division', 'department', 'team'], required: true },
          { id: 'parentId', label: 'Parent ID', type: 'number', required: false },
          { id: 'managerId', label: 'Manager ID', type: 'number', required: false },
          { id: 'description', label: 'Description', type: 'textarea', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, name: 'EHRX Company', type: 'company', parentId: null, managerId: null, description: 'Main company organization', createdAt: '2024-01-01T10:00:00Z' },
          { id: 2, name: 'Technology Division', type: 'division', parentId: 1, managerId: 1, description: 'Technology and development teams', createdAt: '2024-01-02T10:00:00Z' },
          { id: 3, name: 'Product Division', type: 'division', parentId: 1, managerId: 2, description: 'Product management and strategy', createdAt: '2024-01-03T10:00:00Z' },
          { id: 11, name: 'Frontend Team', type: 'team', parentId: 2, managerId: 1, description: 'Frontend development team', createdAt: '2024-01-04T10:00:00Z' },
          { id: 12, name: 'Backend Team', type: 'team', parentId: 2, managerId: 5, description: 'Backend development team', createdAt: '2024-01-05T10:00:00Z' },
          { id: 13, name: 'DevOps Team', type: 'team', parentId: 2, managerId: 3, description: 'DevOps and infrastructure team', createdAt: '2024-01-06T10:00:00Z' }
        ]
      };

    case 'assessment_templates':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Template Name', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: true },
          { id: 'templateLevel', label: 'Level', type: 'select', options: ['hr_level', 'organizational_level', 'team_level'], required: true },
          { id: 'organizationalUnitId', label: 'Org Unit ID', type: 'number', required: false },
          { id: 'version', label: 'Version', type: 'text', required: false },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, name: 'HR Corporate Standards Template', description: 'Company-wide mandatory assessment criteria', templateLevel: 'hr_level', organizationalUnitId: null, version: '2.1', isActive: true, createdAt: '2024-01-15T10:00:00Z' },
          { id: 4, name: 'Technology Division Excellence Template', description: 'Technical assessment criteria for technology teams', templateLevel: 'organizational_level', organizationalUnitId: 2, version: '1.8', isActive: true, createdAt: '2024-02-01T10:00:00Z' },
          { id: 7, name: 'Frontend Engineering Assessment', description: 'Frontend development specific criteria', templateLevel: 'team_level', organizationalUnitId: 11, version: '2.2', isActive: true, createdAt: '2024-03-01T10:00:00Z' }
        ]
      };

    case 'assessment_criteria':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Criteria Name', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: true },
          { id: 'weight', label: 'Weight', type: 'number', required: true },
          { id: 'criteriaLevel', label: 'Level', type: 'select', options: ['hr_level', 'organizational_level', 'team_level'], required: true },
          { id: 'organizationalUnitId', label: 'Org Unit ID', type: 'number', required: false },
          { id: 'isRequired', label: 'Required', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, name: 'Professional Ethics & Integrity', description: 'Demonstrates ethical behavior and integrity', weight: 2.0, criteriaLevel: 'hr_level', organizationalUnitId: null, isRequired: true, createdAt: '2024-01-15T10:00:00Z' },
          { id: 7, name: 'Technical Excellence', description: 'Demonstrates technical skills and knowledge', weight: 2.0, criteriaLevel: 'organizational_level', organizationalUnitId: 2, isRequired: true, createdAt: '2024-02-01T10:00:00Z' },
          { id: 17, name: 'React/TypeScript Proficiency', description: 'Proficiency in React and TypeScript development', weight: 2.0, criteriaLevel: 'team_level', organizationalUnitId: 11, isRequired: true, createdAt: '2024-03-01T10:00:00Z' }
        ]
      };

    case 'assessment_instances':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'employeeId', label: 'Employee ID', type: 'number', required: true },
          { id: 'templateId', label: 'Template ID', type: 'number', required: true },
          { id: 'evaluatorId', label: 'Evaluator ID', type: 'number', required: true },
          { id: 'status', label: 'Status', type: 'select', options: ['pending', 'in_progress', 'completed', 'cancelled'], required: true },
          { id: 'totalScore', label: 'Total Score', type: 'number', required: false },
          { id: 'maxScore', label: 'Max Score', type: 'number', required: false },
          { id: 'startedAt', label: 'Started At', type: 'datetime', required: false },
          { id: 'completedAt', label: 'Completed At', type: 'datetime', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, employeeId: 1, templateId: 1, evaluatorId: 2, status: 'completed', totalScore: 87, maxScore: 100, startedAt: '2024-07-01T10:00:00Z', completedAt: '2024-07-05T15:30:00Z', createdAt: '2024-07-01T09:00:00Z' },
          { id: 2, employeeId: 3, templateId: 1, evaluatorId: 2, status: 'completed', totalScore: 92, maxScore: 100, startedAt: '2024-07-02T10:00:00Z', completedAt: '2024-07-06T14:20:00Z', createdAt: '2024-07-02T09:00:00Z' },
          { id: 3, employeeId: 4, templateId: 7, evaluatorId: 2, status: 'completed', totalScore: 85, maxScore: 100, startedAt: '2024-07-03T10:00:00Z', completedAt: '2024-07-07T16:45:00Z', createdAt: '2024-07-03T09:00:00Z' },
          { id: 4, employeeId: 1, templateId: 4, evaluatorId: 2, status: 'in_progress', totalScore: null, maxScore: 100, startedAt: '2024-07-08T10:00:00Z', completedAt: null, createdAt: '2024-07-08T09:00:00Z' },
          { id: 5, employeeId: 2, templateId: 1, evaluatorId: 2, status: 'pending', totalScore: null, maxScore: 100, startedAt: null, completedAt: null, createdAt: '2024-07-10T09:00:00Z' },
          { id: 6, employeeId: 5, templateId: 7, evaluatorId: 2, status: 'completed', totalScore: 78, maxScore: 100, startedAt: '2024-07-04T10:00:00Z', completedAt: '2024-07-08T13:15:00Z', createdAt: '2024-07-04T09:00:00Z' },
          { id: 7, employeeId: 3, templateId: 4, evaluatorId: 2, status: 'pending', totalScore: null, maxScore: 100, startedAt: null, completedAt: null, createdAt: '2024-07-11T09:00:00Z' },
          { id: 8, employeeId: 4, templateId: 1, evaluatorId: 2, status: 'completed', totalScore: 89, maxScore: 100, startedAt: '2024-07-05T10:00:00Z', completedAt: '2024-07-09T11:30:00Z', createdAt: '2024-07-05T09:00:00Z' }
        ]
      };

    case 'audit_logs':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'userId', label: 'User ID', type: 'number', required: true },
          { id: 'action', label: 'Action', type: 'text', required: true },
          { id: 'tableName', label: 'Table', type: 'text', required: true },
          { id: 'recordId', label: 'Record ID', type: 'number', required: false },
          { id: 'oldValues', label: 'Old Values', type: 'textarea', required: false },
          { id: 'newValues', label: 'New Values', type: 'textarea', required: false },
          { id: 'ipAddress', label: 'IP Address', type: 'text', required: false },
          { id: 'userAgent', label: 'User Agent', type: 'textarea', required: false },
          { id: 'createdAt', label: 'Timestamp', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, userId: 1, action: 'CREATE', tableName: 'users', recordId: 5, oldValues: null, newValues: '{"firstName":"David","lastName":"Brown"}', ipAddress: '*************', userAgent: 'Mozilla/5.0...', createdAt: '2024-07-11T10:00:00Z' },
          { id: 2, userId: 2, action: 'UPDATE', tableName: 'roles', recordId: 4, oldValues: '{"name":"Employee"}', newValues: '{"name":"Standard Employee"}', ipAddress: '*************', userAgent: 'Mozilla/5.0...', createdAt: '2024-07-11T10:15:00Z' },
          { id: 3, userId: 1, action: 'DELETE', tableName: 'assessment_templates', recordId: 2, oldValues: '{"name":"Old Template"}', newValues: null, ipAddress: '*************', userAgent: 'Mozilla/5.0...', createdAt: '2024-07-11T10:30:00Z' }
        ]
      };

    case 'security_settings':
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'settingKey', label: 'Setting Key', type: 'text', required: true },
          { id: 'settingValue', label: 'Setting Value', type: 'textarea', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: true },
          { id: 'category', label: 'Category', type: 'select', options: ['authentication', 'authorization', 'encryption', 'audit', 'compliance'], required: true },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'lastModifiedBy', label: 'Modified By', type: 'number', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: [
          { id: 1, settingKey: 'PASSWORD_MIN_LENGTH', settingValue: '12', description: 'Minimum password length requirement', category: 'authentication', isActive: true, lastModifiedBy: 1, createdAt: '2024-01-01T10:00:00Z' },
          { id: 2, settingKey: 'SESSION_TIMEOUT', settingValue: '3600', description: 'Session timeout in seconds', category: 'authentication', isActive: true, lastModifiedBy: 1, createdAt: '2024-01-01T10:00:00Z' },
          { id: 3, settingKey: 'FAILED_LOGIN_ATTEMPTS', settingValue: '5', description: 'Maximum failed login attempts before lockout', category: 'authentication', isActive: true, lastModifiedBy: 1, createdAt: '2024-01-01T10:00:00Z' },
          { id: 4, settingKey: 'AUDIT_RETENTION_DAYS', settingValue: '2555', description: 'Audit log retention period (7 years for NIS2 compliance)', category: 'audit', isActive: true, lastModifiedBy: 1, createdAt: '2024-01-01T10:00:00Z' },
          { id: 5, settingKey: 'ENCRYPTION_ALGORITHM', settingValue: 'AES-256-GCM', description: 'Data encryption algorithm', category: 'encryption', isActive: true, lastModifiedBy: 1, createdAt: '2024-01-01T10:00:00Z' }
        ]
      };

    default:
      return {
        columns: [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Name', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false }
        ],
        records: []
      };
  }
}

function getTableSchema(tableName) {
  const tableData = getDatabaseTableData(tableName);
  return {
    tableName,
    columns: tableData.columns,
    recordCount: tableData.records.length
  };
}



app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 EHRX Production Server running on port ${PORT}`);
  console.log(`📱 Access the app at: http://localhost:${PORT}`);
  console.log(`🌐 Production build served from: frontend/build`);
  console.log(`🔐 Authentication: http://localhost:${PORT}/api/auth`);
  console.log('');
  console.log('🧪 TEST ACCOUNTS FOR ENTERPRISE USER STATUS:');
  console.log('   👑 Admin: <EMAIL> / password123');
  console.log('   👨‍💼 Manager: <EMAIL> / manager123');
  console.log('   👨‍💻 Henrik: <EMAIL> / ThomsenX123');
  console.log('');
  console.log('✨ Enterprise User Status Menu available in top-right corner after login!');
}).on('error', (err) => {
  console.error('Server error:', err);
  if (err.code === 'EADDRINUSE') {
    console.log(`❌ Port ${PORT} is already in use`);
    process.exit(1);
  }
});
