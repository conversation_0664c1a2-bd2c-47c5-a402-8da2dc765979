import { IsString, IsOptional, IsBoolean, IsObject, IsEnum, IsDateString } from 'class-validator';
import { SurveyType, SurveyStatus } from '../entities/engagement-survey.entity';

export class CreateSurveyDto {
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(SurveyType)
  surveyType: SurveyType;

  @IsObject()
  questions: object;

  @IsOptional()
  @IsObject()
  targetAudience?: object;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean;

  @IsOptional()
  @IsEnum(SurveyStatus)
  status?: SurveyStatus;
}

export class UpdateSurveyDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(SurveyType)
  surveyType?: SurveyType;

  @IsOptional()
  @IsObject()
  questions?: object;

  @IsOptional()
  @IsObject()
  targetAudience?: object;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean;

  @IsOptional()
  @IsEnum(SurveyStatus)
  status?: SurveyStatus;
}
