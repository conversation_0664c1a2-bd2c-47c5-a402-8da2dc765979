import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  HttpStatus,
  HttpCode,
  BadRequestException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { MonthlyDashboardsService } from './monthly-dashboards.service';
import { MonthlyDashboardSeederService } from './monthly-dashboard-seeder.service';
import { CreateMonthlyDashboardSubmissionDto } from './dto/create-monthly-dashboard-submission.dto';
import { UpdateMonthlyDashboardSubmissionDto } from './dto/update-monthly-dashboard-submission.dto';
import { MonthlyDashboardQueryDto, DashboardOverviewQueryDto } from './dto/monthly-dashboard-query.dto';
import { CreateKpiDto } from './dto/create-kpi.dto';
import { UpdateKpiDto } from './dto/update-kpi.dto';

@Controller('monthly-dashboards')
export class MonthlyDashboardsController {
  constructor(
    private readonly monthlyDashboardsService: MonthlyDashboardsService,
    private readonly seederService: MonthlyDashboardSeederService,
  ) { }

  // Seed sample data (development only)
  @Post('seed')
  async seedSampleData() {
    try {
      await this.seederService.seedKpis();
      await this.seederService.seedSampleData();
      return {
        success: true,
        message: 'Monthly dashboard sample data seeded successfully'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to seed sample data',
        error: error.message
      };
    }
  }

  // Get all active KPIs (public for testing)
  @Get('kpis')
  async getKpis() {
    return this.monthlyDashboardsService.getKpis();
  }

  // Get all active KPIs (protected)
  @Get('kpis/protected')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.DIRECTOR, UserRole.MANAGER, UserRole.HR_ADMIN)
  async getKpisProtected() {
    return this.monthlyDashboardsService.getKpis();
  }

  // Get team-specific targets for KPIs
  @Get('teams/:organizationalUnitId/targets')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.DIRECTOR, UserRole.MANAGER, UserRole.HR_ADMIN)
  async getTeamTargets(@Param('organizationalUnitId', ParseIntPipe) organizationalUnitId: number) {
    return this.monthlyDashboardsService.getTeamTargets(organizationalUnitId);
  }

  // Get dashboard overview for leadership
  @Get('overview')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.DIRECTOR, UserRole.HR_ADMIN)
  async getDashboardOverview(@Query() query: DashboardOverviewQueryDto, @Request() req) {
    return this.monthlyDashboardsService.getDashboardOverview(query, req.user.id);
  }

  // Get dashboard submissions with filtering
  @Get('submissions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.DIRECTOR, UserRole.MANAGER, UserRole.HR_ADMIN)
  async getSubmissions(@Query() query: MonthlyDashboardQueryDto, @Request() req) {
    return this.monthlyDashboardsService.getSubmissions(query, req.user.id);
  }

  // Get a specific submission by ID
  @Get('submissions/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.DIRECTOR, UserRole.MANAGER, UserRole.HR_ADMIN)
  async getSubmissionById(@Param('id', ParseIntPipe) id: number) {
    return this.monthlyDashboardsService.getSubmissionById(id);
  }

  // Create a new dashboard submission
  @Post('submissions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.HR_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  async createSubmission(
    @Body() createDto: CreateMonthlyDashboardSubmissionDto,
    @Request() req
  ) {
    return this.monthlyDashboardsService.createSubmission(createDto, req.user.id);
  }

  // Update a dashboard submission
  @Put('submissions/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.HR_ADMIN)
  async updateSubmission(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateMonthlyDashboardSubmissionDto,
    @Request() req
  ) {
    return this.monthlyDashboardsService.updateSubmission(id, updateDto, req.user.id);
  }

  // Submit a dashboard (change status to submitted)
  @Post('submissions/:id/submit')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.HR_ADMIN)
  async submitDashboard(@Param('id', ParseIntPipe) id: number, @Request() req) {
    return this.monthlyDashboardsService.updateSubmission(
      id,
      { status: 'submitted' as any },
      req.user.id
    );
  }

  // Approve a dashboard submission
  @Post('submissions/:id/approve')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.DIRECTOR, UserRole.HR_ADMIN, UserRole.CEO)
  async approveDashboard(@Param('id', ParseIntPipe) id: number, @Request() req) {
    return this.monthlyDashboardsService.updateSubmission(
      id,
      { status: 'approved' as any, approvedByUserId: req.user.id },
      req.user.id
    );
  }

  // Reject a dashboard submission
  @Post('submissions/:id/reject')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.DIRECTOR, UserRole.HR_ADMIN, UserRole.CEO)
  async rejectDashboard(@Param('id', ParseIntPipe) id: number, @Request() req) {
    return this.monthlyDashboardsService.updateSubmission(
      id,
      { status: 'rejected' as any },
      req.user.id
    );
  }

  // Get dashboard statistics
  @Get('statistics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.DIRECTOR, UserRole.HR_ADMIN)
  async getDashboardStatistics(@Query() query: DashboardOverviewQueryDto, @Request() req) {
    try {
      const overview = await this.monthlyDashboardsService.getDashboardOverview(query, req.user.id);

      // Handle new dashboard summary format
      if (overview.divisions) {
        const allTeams = overview.divisions.flatMap(division => division.teams);
        const totalTeams = allTeams.length;
        const submittedTeams = allTeams.filter(team => team.lastUpdate !== 'N/A').length;

        return {
          totalTeams,
          submittedTeams,
          onTimeSubmissions: submittedTeams,
          submissionRate: totalTeams > 0 ? (submittedTeams / totalTeams) * 100 : 0,
          onTimeRate: 100,
          statusCounts: {
            green: 0,
            yellow: 0,
            red: 0,
            na: totalTeams - submittedTeams,
          },
          overview
        };
      }

      // Handle legacy format
      const totalTeams = Array.isArray(overview) ? overview.length : 0;
      const submittedTeams = Array.isArray(overview) ? overview.filter(team => team.submissions?.length > 0).length : 0;
      const onTimeSubmissions = Array.isArray(overview) ? overview.filter(team =>
        team.submissions?.some(sub => sub.getCompletionStatus?.() === 'on_time')
      ).length : 0;

      const statusCounts = {
        green: Array.isArray(overview) ? overview.filter(team => team.overallStatus === 'green').length : 0,
        yellow: Array.isArray(overview) ? overview.filter(team => team.overallStatus === 'yellow').length : 0,
        red: Array.isArray(overview) ? overview.filter(team => team.overallStatus === 'red').length : 0,
        na: Array.isArray(overview) ? overview.filter(team => team.overallStatus === 'na').length : 0,
      };

      return {
        totalTeams,
        submittedTeams,
        onTimeSubmissions,
        submissionRate: totalTeams > 0 ? (submittedTeams / totalTeams) * 100 : 0,
        onTimeRate: submittedTeams > 0 ? (onTimeSubmissions / submittedTeams) * 100 : 0,
        statusCounts,
        overview
      };
    } catch (error) {
      console.error('Error in getDashboardStatistics:', error);
      return {
        totalTeams: 0,
        submittedTeams: 0,
        onTimeSubmissions: 0,
        submissionRate: 0,
        onTimeRate: 0,
        statusCounts: { green: 0, yellow: 0, red: 0, na: 0 },
        overview: null
      };
    }
  }

  // Get KPI trends over time
  @Get('trends')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.DIRECTOR, UserRole.HR_ADMIN, UserRole.MANAGER)
  async getKpiTrends(
    @Query('kpiId', ParseIntPipe) kpiId: number,
    @Request() req,
    @Query('organizationalUnitId') organizationalUnitId?: string,
    @Query('months') months?: string
  ) {
    try {
      // Parse and validate parameters
      const parsedOrgUnitId = organizationalUnitId ? parseInt(organizationalUnitId, 10) : undefined;
      const parsedMonths = months ? parseInt(months, 10) : 12;

      // Validate months parameter
      if (parsedMonths < 1 || parsedMonths > 36) {
        throw new BadRequestException('Months parameter must be between 1 and 36');
      }

      // Validate organizationalUnitId if provided
      if (organizationalUnitId && isNaN(parsedOrgUnitId)) {
        throw new BadRequestException('Invalid organizational unit ID');
      }

      const trends = await this.monthlyDashboardsService.getKpiTrends(
        kpiId,
        parsedOrgUnitId,
        parsedMonths,
        req.user.id
      );

      return {
        success: true,
        data: trends
      };
    } catch (error) {
      console.error('Error in getKpiTrends:', error);
      throw error;
    }
  }

  // KPI Management Endpoints
  @Post('kpis')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  async createKpi(@Body() createKpiDto: CreateKpiDto) {
    return this.monthlyDashboardsService.createKpi(createKpiDto);
  }

  @Get('kpis')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN, UserRole.MANAGER)
  async getAllKpis(@Query('includeInactive') includeInactive?: boolean) {
    return this.monthlyDashboardsService.getAllKpis(includeInactive === true);
  }

  @Get('kpis/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN, UserRole.MANAGER)
  async getKpiById(@Param('id', ParseIntPipe) id: number) {
    return this.monthlyDashboardsService.getKpiById(id);
  }

  @Put('kpis/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  async updateKpi(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateKpiDto: UpdateKpiDto
  ) {
    return this.monthlyDashboardsService.updateKpi(id, updateKpiDto);
  }

  @Post('kpis/:id/toggle-status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  async toggleKpiStatus(@Param('id', ParseIntPipe) id: number) {
    return this.monthlyDashboardsService.toggleKpiStatus(id);
  }
}
