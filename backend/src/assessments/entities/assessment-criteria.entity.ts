import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AssessmentCriteriaLevel } from './assessment-criteria-level.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { User } from '../../users/entities/user.entity';
import { AssessmentCriteriaInheritance } from './assessment-criteria-inheritance.entity';
import { AssessmentCriteriaResponse } from './assessment-criteria-response.entity';

export enum ScoringMethod {
  NUMERIC = 'numeric',
  PERCENTAGE = 'percentage',
  SCALE_1_5 = 'scale_1_5',
  SCALE_1_10 = 'scale_1_10',
  BOOLEAN = 'boolean'
}

@Entity('assessment_criteria')
export class AssessmentCriteria {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'criteria_level_id' })
  criteriaLevelId: number;

  @ManyToOne(() => AssessmentCriteriaLevel, level => level.criteria)
  @JoinColumn({ name: 'criteria_level_id' })
  criteriaLevel: AssessmentCriteriaLevel;

  @Column({ name: 'organizational_unit_id', nullable: true })
  organizationalUnitId: number;

  @ManyToOne(() => OrganizationalUnit, { nullable: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  weight: number;

  @Column({ name: 'max_score', default: 100 })
  maxScore: number;

  @Column({
    type: 'enum',
    enum: ScoringMethod,
    name: 'scoring_method',
    default: ScoringMethod.PERCENTAGE
  })
  scoringMethod: ScoringMethod;

  @Column({ name: 'is_mandatory', default: true })
  isMandatory: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'effective_from', type: 'date' })
  effectiveFrom: Date;

  @Column({ name: 'effective_until', type: 'date', nullable: true })
  effectiveUntil: Date;

  @Column({ name: 'order_index', default: 1 })
  orderIndex: number;

  @OneToMany(() => AssessmentCriteriaInheritance, inheritance => inheritance.criteria)
  inheritances: AssessmentCriteriaInheritance[];

  @OneToMany(() => AssessmentCriteriaResponse, response => response.criteria)
  responses: AssessmentCriteriaResponse[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
