import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';
import { AssessmentTemplate } from '../assessments/entities/assessment-template.entity';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { Skillset } from '../teams/entities/skillset.entity';
import { TeamMember } from '../teams/entities/team-member.entity';
// import { ActionItem } from '../trackers/entities/action-item.entity'; // GHOST MODULE

@Injectable()
export class DatabaseService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(OrganizationalUnit)
    private orgUnitsRepository: Repository<OrganizationalUnit>,
    @InjectRepository(AssessmentTemplate)
    private templatesRepository: Repository<AssessmentTemplate>,
    @InjectRepository(AssessmentInstance)
    private assessmentInstancesRepository: Repository<AssessmentInstance>,
    @InjectRepository(Skillset)
    private skillsetsRepository: Repository<Skillset>,
    @InjectRepository(TeamMember)
    private teamMembersRepository: Repository<TeamMember>,
    // @InjectRepository(ActionItem) // GHOST MODULE
    // private actionItemsRepository: Repository<ActionItem>, // GHOST MODULE
  ) { }

  async getTableData(tableName: string, page: number = 1, limit: number = 100) {
    const repository = this.getRepository(tableName);
    const [records, total] = await repository.findAndCount({
      skip: (page - 1) * limit,
      take: limit,
      order: { id: 'DESC' },
    });

    const columns = this.getTableColumns(tableName);

    return {
      success: true,
      data: {
        records,
        columns,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };
  }

  async getTableSchema(tableName: string) {
    const columns = this.getTableColumns(tableName);
    const repository = this.getRepository(tableName);
    const recordCount = await repository.count();

    return {
      success: true,
      data: {
        tableName,
        columns,
        recordCount,
      },
    };
  }

  async createRecord(tableName: string, recordData: any) {
    const repository = this.getRepository(tableName);

    // Handle special cases for different tables
    if (tableName === 'users') {
      // Users require password hashing and validation
      throw new BadRequestException('Use the /users endpoint for creating users');
    }

    const record = repository.create(recordData);
    const savedRecord = await repository.save(record);

    return {
      success: true,
      data: savedRecord,
      message: 'Record created successfully',
    };
  }

  async updateRecord(tableName: string, id: number, recordData: any) {
    const repository = this.getRepository(tableName);

    const existingRecord = await repository.findOne({ where: { id } });
    if (!existingRecord) {
      throw new NotFoundException(`Record with ID ${id} not found in ${tableName}`);
    }

    // Remove id from update data to prevent conflicts
    const { id: _, ...updateData } = recordData;

    await repository.update(id, updateData);
    const updatedRecord = await repository.findOne({ where: { id } });

    return {
      success: true,
      data: updatedRecord,
      message: 'Record updated successfully',
    };
  }

  async deleteRecord(tableName: string, id: number) {
    const repository = this.getRepository(tableName);

    const existingRecord = await repository.findOne({ where: { id } });
    if (!existingRecord) {
      throw new NotFoundException(`Record with ID ${id} not found in ${tableName}`);
    }

    await repository.delete(id);

    return {
      success: true,
      message: 'Record deleted successfully',
    };
  }

  async getAvailableTables() {
    return {
      success: true,
      data: [
        // Core System Tables
        { id: 'users', name: 'Users', description: 'User accounts and profiles' },
        { id: 'organizational_units', name: 'Organizational Units', description: 'Teams and departments' },
        { id: 'assessment_templates', name: 'Assessment Templates', description: 'Performance assessment templates' },
        { id: 'assessment_instances', name: 'Assessment Instances', description: 'Completed assessments' },
        { id: 'skillsets', name: 'Skillsets', description: 'Available skills and competencies' },
        { id: 'team_members', name: 'Team Members', description: 'Team membership relationships' },
        { id: 'action_items', name: 'Action Items', description: 'Tasks and action items from assessments and reviews' },

        // Management Dashboard Tables
        { id: 'analytics_dashboards', name: 'Analytics Dashboards', description: 'User dashboard configurations and layouts' },
        { id: 'performance_metrics', name: 'Performance Metrics', description: 'Aggregated performance data and trends' },
        { id: 'engagement_surveys', name: 'Engagement Surveys', description: 'Employee engagement and pulse surveys' },
        { id: 'survey_responses', name: 'Survey Responses', description: 'Employee survey response data' },
        { id: 'recognition_badges', name: 'Recognition Badges', description: 'Achievement and appreciation badge definitions' },
        { id: 'recognition_instances', name: 'Recognition Instances', description: 'Given recognitions and appreciations' },
        { id: 'user_gamification', name: 'User Gamification', description: 'User points, levels, and achievement tracking' },
        { id: 'attrition_predictions', name: 'Attrition Predictions', description: 'AI-generated employee attrition risk scores' },
        { id: 'competency_frameworks', name: 'Competency Frameworks', description: 'Skills and competency framework definitions' },
        { id: 'career_paths', name: 'Career Paths', description: 'Career progression routes and requirements' },
        { id: 'micro_feedback', name: 'Micro Feedback', description: 'Quick feedback, reactions, and check-ins' },
        { id: 'ai_insights', name: 'AI Insights', description: 'AI-generated insights and recommendations' },
      ],
    };
  }

  private getRepository(tableName: string): Repository<any> {
    switch (tableName) {
      case 'users':
        return this.usersRepository;
      case 'organizational_units':
        return this.orgUnitsRepository;
      case 'assessment_templates':
        return this.templatesRepository;
      case 'assessment_instances':
        return this.assessmentInstancesRepository;
      case 'skillsets':
        return this.skillsetsRepository;
      case 'team_members':
        return this.teamMembersRepository;
      // case 'action_items': // GHOST MODULE
      //   return this.actionItemsRepository; // GHOST MODULE
      default:
        throw new BadRequestException(`Table '${tableName}' is not supported`);
    }
  }

  private getTableColumns(tableName: string) {
    switch (tableName) {
      case 'users':
        return [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'firstName', label: 'First Name', type: 'text', required: true },
          { id: 'lastName', label: 'Last Name', type: 'text', required: true },
          { id: 'email', label: 'Email', type: 'email', required: true },
          { id: 'title', label: 'Job Title', type: 'text', required: false },
          { id: 'role', label: 'Role', type: 'select', required: true, options: ['ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest', 'employee'] },
          { id: 'organizationalUnitId', label: 'Organizational Unit ID', type: 'number', required: false },
          { id: 'managerId', label: 'Manager ID', type: 'number', required: false },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'accountStatus', label: 'Account Status', type: 'select', required: false, options: ['active', 'inactive', 'locked', 'pending_activation', 'suspended'] },
          { id: 'lastLoginAt', label: 'Last Login', type: 'datetime', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false },
          { id: 'updatedAt', label: 'Updated', type: 'datetime', required: false },
        ];
      case 'organizational_units':
        return [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Name', type: 'text', required: true },
          { id: 'type', label: 'Type', type: 'select', required: true, options: ['organization', 'division', 'department', 'team', 'project'] },
          { id: 'description', label: 'Description', type: 'textarea', required: false },
          { id: 'parentId', label: 'Parent ID', type: 'number', required: false },
          { id: 'managerId', label: 'Manager ID', type: 'number', required: false },
          { id: 'level', label: 'Level', type: 'number', required: false },
          { id: 'budget', label: 'Budget', type: 'number', required: false },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false },
          { id: 'updatedAt', label: 'Updated', type: 'datetime', required: false },
        ];
      case 'assessment_templates':
        return [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Name', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: false },
          { id: 'level', label: 'Level', type: 'select', required: true, options: ['hr', 'organizational', 'team'] },
          { id: 'organizationalUnitId', label: 'Organizational Unit ID', type: 'number', required: false },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdBy', label: 'Created By', type: 'number', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false },
          { id: 'updatedAt', label: 'Updated', type: 'datetime', required: false },
        ];
      case 'assessment_instances':
        return [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'templateId', label: 'Template ID', type: 'number', required: true },
          { id: 'employeeId', label: 'Employee ID', type: 'number', required: true },
          { id: 'evaluatorId', label: 'Evaluator ID', type: 'number', required: true },
          { id: 'status', label: 'Status', type: 'select', required: true, options: ['draft', 'in_progress', 'completed', 'reviewed', 'archived'] },
          { id: 'totalScore', label: 'Total Score', type: 'number', required: false },
          { id: 'maxScore', label: 'Max Score', type: 'number', required: false },
          { id: 'startedAt', label: 'Started At', type: 'datetime', required: false },
          { id: 'completedAt', label: 'Completed At', type: 'datetime', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false },
          { id: 'updatedAt', label: 'Updated', type: 'datetime', required: false },
        ];
      case 'skillsets':
        return [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Name', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: false },
          { id: 'category', label: 'Category', type: 'text', required: false },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false },
          { id: 'updatedAt', label: 'Updated', type: 'datetime', required: false },
        ];
      case 'team_members':
        return [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'userId', label: 'User ID', type: 'number', required: true },
          { id: 'organizationalUnitId', label: 'Organizational Unit ID', type: 'number', required: true },
          { id: 'role', label: 'Role', type: 'text', required: false },
          { id: 'joinedAt', label: 'Joined At', type: 'datetime', required: false },
          { id: 'isActive', label: 'Active', type: 'boolean', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false },
          { id: 'updatedAt', label: 'Updated', type: 'datetime', required: false },
        ];
      default:
        return [
          { id: 'id', label: 'ID', type: 'number', required: false },
          { id: 'name', label: 'Name', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'textarea', required: false },
          { id: 'createdAt', label: 'Created', type: 'datetime', required: false },
        ];
    }
  }
}
