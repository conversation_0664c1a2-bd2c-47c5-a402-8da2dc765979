import { Controller, Request, Post, UseGuards, Body, Get, Ip, BadRequestException } from '@nestjs/common';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { SecurityAuditService } from './services/security-audit.service';

@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private securityAuditService: SecurityAuditService
  ) { }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  async login(@Body() loginDto: LoginDto, @Request() req, @Ip() clientIp: string) {
    // LocalAuthGuard validates the user credentials before this function is called
    return this.authService.login(req.user, clientIp);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  async logout(@Request() req) {
    await this.authService.logout(req.user.userId);
    return { message: 'Logged out successfully' };
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }

  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  async changePassword(@Body() changePasswordDto: ChangePasswordDto, @Request() req) {
    const { currentPassword, newPassword, confirmPassword } = changePasswordDto;

    if (newPassword !== confirmPassword) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    await this.authService.changePassword(req.user.userId, currentPassword, newPassword);
    return { message: 'Password changed successfully' };
  }

  @Get('health')
  healthCheck() {
    return { status: 'ok', timestamp: new Date().toISOString() };
  }

  /**
   * 🔐 NIS2-Compliant Security Audit Endpoint
   * Receives security events from frontend for centralized logging
   */
  @Post('security-audit')
  async logSecurityEvent(@Body() auditData: any, @Request() req, @Ip() clientIp: string) {
    try {
      // Extract user info if authenticated
      const userId = req.user?.userId || null;
      const email = req.user?.email || null;

      // Log the security event
      await this.securityAuditService.logSecurityEvent({
        eventType: auditData.event,
        userId,
        email,
        ipAddress: clientIp,
        userAgent: req.headers['user-agent'],
        sessionId: auditData.sessionFingerprint,
        details: auditData.details,
        severity: this.determineSeverity(auditData.event)
      });

      return { status: 'logged' };
    } catch (error) {
      // Silent fail for security audit - don't expose system details
      return { status: 'error' };
    }
  }

  /**
   * 🔐 Determine security event severity
   */
  private determineSeverity(eventType: string): any {
    const highSeverityEvents = [
      'LOGIN_FAILED',
      'TOKEN_EXPIRED',
      'SESSION_FINGERPRINT_MISMATCH',
      'TOKEN_RETRIEVAL_RATE_LIMITED'
    ];

    const criticalSeverityEvents = [
      'INVALID_TOKEN_FORMAT',
      'TOKEN_STORAGE_FAILED',
      'SESSION_HIJACK_ATTEMPT'
    ];

    if (criticalSeverityEvents.includes(eventType)) {
      return 'CRITICAL';
    } else if (highSeverityEvents.includes(eventType)) {
      return 'HIGH';
    } else {
      return 'LOW';
    }
  }
}
