# 🏢 EHRX Dynamic Organizational Management System

## 📋 Overview

EHRX is a modern, enterprise-grade Employee Performance Management System featuring **dynamic organizational hierarchy** with **infinite levels** and **drag-and-drop visual management**. Built with React, TypeScript, and Material-UI, it provides a comprehensive solution for managing complex organizational structures.

## ✨ Key Features

### 🌳 Dynamic Organizational Structure
- **Infinite hierarchy levels** - no hard-coded limitations
- **Add sub-units to any level** - teams under teams under departments
- **Visual drag-and-drop** organizational chart editing
- **Real-time restructuring** with automatic level calculation
- **Dual view system**: Tree view (visual chart) and List view (detailed cards)

### 👥 Comprehensive User Management
- **156+ employees** integrated as website users
- **Role-based hierarchy**: CEO → VPs → Directors → Managers → Employees
- **Team member assignment** to any organizational unit
- **Manager relationships** with proper reporting structure
- **Real-time member counting** and assignment tracking

### 📱 Mobile-Responsive Design
- **Hamburger menu navigation** for smartphones
- **Touch-optimized interface** with proper touch targets
- **Responsive layouts** across all device sizes
- **Mobile-first approach** with progressive enhancement
- **Professional design** maintained across all platforms

### 🎨 Modern User Interface
- **Material-UI components** with custom styling
- **Gradient backgrounds** and smooth animations
- **Color-coded organizational levels** for visual clarity
- **Interactive elements** with hover effects and transitions
- **Enterprise-grade aesthetics** with scenic visual appeal

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ and npm
- Modern web browser
- SSL certificate for HTTPS (production)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd ehrx
```

2. **Install dependencies**
```bash
# Frontend
cd frontend
npm install

# Backend (if applicable)
cd ../backend
npm install
```

3. **Start development servers**
```bash
# Frontend (React)
cd frontend
npm start

# Backend (Node.js)
cd backend
node mock-backend.js
```

4. **Access the application**
- Development: `http://localhost:3080`
- Production: `https://dev.trusthansen.dk`

## 🏗️ Architecture

### Frontend Stack
- **React 18** with TypeScript
- **Material-UI (MUI)** for components
- **React Router** for navigation
- **Responsive design** with mobile optimization

### Backend Stack
- **Node.js** with Express
- **Mock API endpoints** for development
- **JSON data structure** for organizational hierarchy
- **RESTful API design**

### Data Structure
```javascript
organizationData: {
  rootNode: {
    id: 'ORG_ROOT',
    name: 'EHRX Corporation',
    type: 'organization',
    level: 0,
    children: ['DIV001', 'DIV002'],
    isExpanded: true
  },
  nodes: {
    'DIV001': {
      id: 'DIV001',
      name: 'Technology Division',
      type: 'division',
      parentId: 'ORG_ROOT',
      level: 1,
      children: ['DEPT001', 'DEPT002'],
      managerId: 'VP001',
      budget: 18500000
    }
    // Infinite nesting possible...
  },
  users: [
    // 156+ employee records with team assignments
  ]
}
```

## 📖 User Guide

### 🌳 Tree View (Visual Organizational Chart)
1. **Navigate** to Team Management
2. **Click** "🌳 Tree View" button
3. **Expand/Collapse** nodes by clicking 📁/📂 icons
4. **Drag & Drop** nodes to restructure organization
5. **Add sub-units** by clicking ➕ on any node
6. **Edit details** by clicking ✏️ on any node

### 📋 List View (Detailed Management)
1. **Click** "📋 List View" button
2. **View** all organizational units as detailed cards
3. **Manage members** by clicking 👥 on any card
4. **Add sub-units** by clicking ➕ on any card
5. **Edit/Delete** units using action buttons

### ➕ Adding Infinite Sub-Levels
1. **Click ➕** on any organizational unit
2. **Fill form**: Name, type, description, budget, manager
3. **Submit** to create new sub-unit
4. **Repeat** to add unlimited nested levels

### 🎯 Drag & Drop Restructuring
1. **Switch to Tree View**
2. **Drag** any organizational node
3. **Drop** on target parent node
4. **Automatic** hierarchy recalculation
5. **Instant** visual updates

## 🔧 Configuration

### Environment Variables
```bash
# Frontend
REACT_APP_API_URL=http://localhost:4000
REACT_APP_ENVIRONMENT=development

# Backend
PORT=4000
NODE_ENV=development
```

### Deployment
The system is configured for deployment on:
- **Domain**: dev.trusthansen.dk
- **SSL**: HTTPS enabled
- **Server**: *************
- **Reverse Proxy**: Nginx configuration

## 🎨 Customization

### Adding New Organizational Types
```javascript
// Add to getNodeColor function
case 'custom-type': 
  return { bg: '#color', border: '#color', text: '#color' };
```

### Modifying User Roles
```javascript
// Update user role hierarchy
const userRoles = {
  'executive': { level: 0, permissions: ['all'] },
  'vp': { level: 1, permissions: ['manage-divisions'] },
  'director': { level: 2, permissions: ['manage-departments'] },
  'manager': { level: 3, permissions: ['manage-teams'] },
  'employee': { level: 4, permissions: ['view-only'] }
};
```

## 📱 Mobile Optimization

### Responsive Breakpoints
- **xs (0-599px)**: Mobile phones - Hamburger menu, full-width content
- **sm (600-899px)**: Large phones/tablets - Two-column layout
- **md (900-1199px)**: Tablets - Enhanced spacing
- **lg (1200px+)**: Desktop - Full sidebar layout

### Mobile Features
- **Hamburger menu** (☰) for navigation
- **Touch-friendly** buttons and interactions
- **Optimized typography** for mobile reading
- **Responsive modals** that use full screen width
- **Swipe-friendly** interface elements

## 🧪 Testing

### Manual Testing Checklist
- [ ] Organizational hierarchy creation/editing
- [ ] Drag & drop functionality
- [ ] Mobile responsive design
- [ ] User management integration
- [ ] Performance with large datasets
- [ ] Cross-browser compatibility

### Test Devices
- iPhone SE (375px) - Smallest modern mobile
- iPhone 12/13 (390px) - Standard iPhone
- iPad Mini (768px) - Small tablet
- Desktop (1200px+) - Full experience

## 🤝 Contributing

### Development Workflow
1. **Fork** the repository
2. **Create** feature branch
3. **Implement** changes with tests
4. **Test** on multiple devices
5. **Submit** pull request

### Code Standards
- **TypeScript** for type safety
- **ESLint** for code quality
- **Prettier** for formatting
- **Material-UI** design system
- **Mobile-first** responsive design

## 📄 License

This project is proprietary software developed for EHRX Corporation.

## 🆘 Support

### Documentation
- **README**: This file
- **YouTube Script**: `/docs/youtube-script.md`
- **API Documentation**: `/docs/api.md`
- **Deployment Guide**: `/docs/deployment.md`

### Contact
- **Development Team**: [Contact Information]
- **System Administrator**: [Contact Information]
- **Business Owner**: [Contact Information]

## 🔄 Version History

### v2.0.0 - Dynamic Organizational System
- ✅ Infinite hierarchy levels
- ✅ Drag & drop organizational chart
- ✅ Mobile-responsive design
- ✅ Visual tree and list views
- ✅ Real-time organizational restructuring

### v1.0.0 - Initial Release
- ✅ Basic team management
- ✅ User authentication
- ✅ Performance assessments
- ✅ Reporting dashboard

---

**🌟 EHRX Dynamic Organizational Management System - Infinite Hierarchy • Visual Management • Enterprise Grade**
