import { Injectable, Logger } from '@nestjs/common';
import { LogEntry, LogLevel, LogSource, LogQuery, LogStats, BrowserError } from './interfaces/log.interface';
import * as fs from 'fs/promises';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LoggingService {
  private readonly logger = new Logger(LoggingService.name);
  private readonly logDirectory = path.join(process.cwd(), 'logs');
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB
  private readonly maxFiles = 30; // Keep 30 days of logs

  constructor() {
    this.ensureLogDirectory();
  }

  private async ensureLogDirectory(): Promise<void> {
    try {
      await fs.access(this.logDirectory);
    } catch {
      await fs.mkdir(this.logDirectory, { recursive: true });
    }
  }

  async logEntry(entry: Omit<LogEntry, 'id' | 'timestamp'>): Promise<void> {
    const logEntry: LogEntry = {
      id: uuidv4(),
      timestamp: new Date(),
      ...entry,
    };

    // Write to appropriate log file
    await this.writeToFile(logEntry);

    // Also log to console for development
    this.logToConsole(logEntry);
  }

  async logBrowserError(browserError: BrowserError, userId?: string): Promise<void> {
    const logEntry: LogEntry = {
      id: uuidv4(),
      timestamp: browserError.timestamp,
      level: LogLevel.ERROR,
      source: LogSource.FRONTEND,
      message: `Browser Error: ${browserError.message}`,
      details: {
        filename: browserError.filename,
        lineno: browserError.lineno,
        colno: browserError.colno,
        error: browserError.error,
      },
      userId: userId || browserError.userId,
      sessionId: browserError.sessionId,
      userAgent: browserError.userAgent,
      url: browserError.url,
      stackTrace: browserError.error?.stack,
      component: 'browser',
      action: 'javascript_error',
    };

    await this.logEntry(logEntry);
  }

  async logApiRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    userId?: string,
    ip?: string,
    userAgent?: string,
  ): Promise<void> {
    const level = statusCode >= 500 ? LogLevel.ERROR : statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;

    await this.logEntry({
      level,
      source: LogSource.BACKEND,
      message: `${method} ${url} - ${statusCode}`,
      method,
      url,
      statusCode,
      responseTime,
      userId,
      ip,
      userAgent,
      component: 'api',
      action: 'http_request',
    });
  }

  async logDatabaseQuery(
    query: string,
    duration: number,
    success: boolean,
    error?: string,
  ): Promise<void> {
    await this.logEntry({
      level: success ? LogLevel.DEBUG : LogLevel.ERROR,
      source: LogSource.DATABASE,
      message: success ? `Database query executed in ${duration}ms` : `Database query failed: ${error}`,
      details: {
        query: query.substring(0, 500), // Truncate long queries
        duration,
        success,
        error,
      },
      component: 'database',
      action: 'query',
    });
  }

  private async writeToFile(entry: LogEntry): Promise<void> {
    const fileName = this.getLogFileName(entry.level, entry.source);
    const filePath = path.join(this.logDirectory, fileName);

    const logLine = JSON.stringify(entry) + '\n';

    try {
      await fs.appendFile(filePath, logLine);

      // Check file size and rotate if necessary
      const stats = await fs.stat(filePath);
      if (stats.size > this.maxFileSize) {
        await this.rotateLogFile(filePath);
      }
    } catch (error) {
      this.logger.error(`Failed to write log entry: ${error.message}`);
    }
  }

  private getLogFileName(level: LogLevel, source: LogSource): string {
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    return `${source}-${level}-${date}.log`;
  }

  private async rotateLogFile(filePath: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const rotatedPath = filePath.replace('.log', `-${timestamp}.log`);

    try {
      await fs.rename(filePath, rotatedPath);
      this.logger.log(`Rotated log file: ${path.basename(rotatedPath)}`);
    } catch (error) {
      this.logger.error(`Failed to rotate log file: ${error.message}`);
    }
  }

  private logToConsole(entry: LogEntry): void {
    const message = `[${entry.source.toUpperCase()}] ${entry.message}`;

    switch (entry.level) {
      case LogLevel.ERROR:
        this.logger.error(message, entry.stackTrace);
        break;
      case LogLevel.WARN:
        this.logger.warn(message);
        break;
      case LogLevel.INFO:
        this.logger.log(message);
        break;
      case LogLevel.DEBUG:
        this.logger.debug(message);
        break;
      case LogLevel.TRACE:
        this.logger.verbose(message);
        break;
    }
  }

  async queryLogs(query: LogQuery): Promise<LogEntry[]> {
    const logs: LogEntry[] = [];
    const files = await this.getLogFiles();

    for (const file of files) {
      const fileLogs = await this.readLogFile(file);
      logs.push(...fileLogs);
    }

    return this.filterLogs(logs, query);
  }

  async getLogStats(): Promise<LogStats> {
    const logs = await this.queryLogs({});

    const stats: LogStats = {
      totalLogs: logs.length,
      errorCount: logs.filter(l => l.level === LogLevel.ERROR).length,
      warnCount: logs.filter(l => l.level === LogLevel.WARN).length,
      infoCount: logs.filter(l => l.level === LogLevel.INFO).length,
      debugCount: logs.filter(l => l.level === LogLevel.DEBUG).length,
      traceCount: logs.filter(l => l.level === LogLevel.TRACE).length,
      sourceBreakdown: {} as Record<LogSource, number>,
      recentErrors: logs
        .filter(l => l.level === LogLevel.ERROR)
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 10),
      topErrors: this.getTopErrors(logs),
    };

    // Calculate source breakdown
    Object.values(LogSource).forEach(source => {
      stats.sourceBreakdown[source] = logs.filter(l => l.source === source).length;
    });

    return stats;
  }

  private async getLogFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.logDirectory);
      return files
        .filter(file => file.endsWith('.log'))
        .map(file => path.join(this.logDirectory, file));
    } catch (error) {
      this.logger.error(`Failed to read log directory: ${error.message}`);
      return [];
    }
  }

  private async readLogFile(filePath: string): Promise<LogEntry[]> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.trim().split('\n').filter(line => line.trim());

      return lines.map(line => {
        try {
          const entry = JSON.parse(line);
          entry.timestamp = new Date(entry.timestamp);
          return entry;
        } catch {
          return null;
        }
      }).filter(Boolean);
    } catch (error) {
      this.logger.error(`Failed to read log file ${filePath}: ${error.message}`);
      return [];
    }
  }

  private filterLogs(logs: LogEntry[], query: LogQuery): LogEntry[] {
    let filtered = logs;

    if (query.level) {
      filtered = filtered.filter(log => log.level === query.level);
    }

    if (query.source) {
      filtered = filtered.filter(log => log.source === query.source);
    }

    if (query.startDate) {
      filtered = filtered.filter(log => log.timestamp >= query.startDate);
    }

    if (query.endDate) {
      filtered = filtered.filter(log => log.timestamp <= query.endDate);
    }

    if (query.userId) {
      filtered = filtered.filter(log => log.userId === query.userId);
    }

    if (query.component) {
      filtered = filtered.filter(log => log.component === query.component);
    }

    if (query.search) {
      const searchLower = query.search.toLowerCase();
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(searchLower) ||
        (log.details && JSON.stringify(log.details).toLowerCase().includes(searchLower))
      );
    }

    // Sort by timestamp (newest first)
    filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply pagination
    const offset = query.offset || 0;
    const limit = query.limit || 100;

    return filtered.slice(offset, offset + limit);
  }

  private getTopErrors(logs: LogEntry[]): Array<{ message: string; count: number; lastOccurrence: Date }> {
    const errorCounts = new Map<string, { count: number; lastOccurrence: Date }>();

    logs
      .filter(log => log.level === LogLevel.ERROR)
      .forEach(log => {
        const key = log.message;
        const existing = errorCounts.get(key);

        if (existing) {
          existing.count++;
          if (log.timestamp > existing.lastOccurrence) {
            existing.lastOccurrence = log.timestamp;
          }
        } else {
          errorCounts.set(key, { count: 1, lastOccurrence: log.timestamp });
        }
      });

    return Array.from(errorCounts.entries())
      .map(([message, data]) => ({ message, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }
}
