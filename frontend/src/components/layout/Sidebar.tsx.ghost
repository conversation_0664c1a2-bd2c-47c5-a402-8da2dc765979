import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { UserRole } from '../../types';

interface SidebarProps {
  userRole?: UserRole;
}

const Sidebar: React.FC<SidebarProps> = ({ userRole = UserRole.EMPLOYEE }) => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;

  return (
    <aside className="bg-gray-800 text-white w-64 min-h-screen p-4">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">EHRX</h1>
        <p className="text-sm text-gray-400">Performance Dashboard</p>
      </div>

      <nav>
        <ul className="space-y-2">
          <li>
            <Link 
              to="/dashboard" 
              className={`flex items-center p-2 rounded-lg ${
                isActive('/dashboard') ? 'bg-gray-700' : 'hover:bg-gray-700'
              }`}
            >
              <span className="material-icons mr-3">dashboard</span>
              <span>Dashboard</span>
            </Link>
          </li>

          <li>
            <Link 
              to="/assessments" 
              className={`flex items-center p-2 rounded-lg ${
                isActive('/assessments') ? 'bg-gray-700' : 'hover:bg-gray-700'
              }`}
            >
              <span className="material-icons mr-3">assignment</span>
              <span>Assessments</span>
            </Link>
          </li>

          {/* Show team management for managers and HR admins */}
          {(userRole === UserRole.MANAGER || userRole === UserRole.HR_ADMIN) && (
            <li>
              <Link 
                to="/teams" 
                className={`flex items-center p-2 rounded-lg ${
                  isActive('/teams') ? 'bg-gray-700' : 'hover:bg-gray-700'
                }`}
              >
                <span className="material-icons mr-3">groups</span>
                <span>Teams</span>
              </Link>
            </li>
          )}

          {/* Show template management for managers and HR admins */}
          {(userRole === UserRole.MANAGER || userRole === UserRole.HR_ADMIN) && (
            <li>
              <Link 
                to="/templates" 
                className={`flex items-center p-2 rounded-lg ${
                  isActive('/templates') ? 'bg-gray-700' : 'hover:bg-gray-700'
                }`}
              >
                <span className="material-icons mr-3">content_paste</span>
                <span>Templates</span>
              </Link>
            </li>
          )}

          {/* Show user management only for HR admins */}
          {userRole === UserRole.HR_ADMIN && (
            <li>
              <Link 
                to="/users" 
                className={`flex items-center p-2 rounded-lg ${
                  isActive('/users') ? 'bg-gray-700' : 'hover:bg-gray-700'
                }`}
              >
                <span className="material-icons mr-3">people</span>
                <span>Users</span>
              </Link>
            </li>
          )}

          {/* Show reports for managers and HR */}
          {(userRole === UserRole.MANAGER || userRole === UserRole.HR_ADMIN) && (
            <li>
              <Link 
                to="/reports" 
                className={`flex items-center p-2 rounded-lg ${
                  isActive('/reports') ? 'bg-gray-700' : 'hover:bg-gray-700'
                }`}
              >
                <span className="material-icons mr-3">analytics</span>
                <span>Reports</span>
              </Link>
            </li>
          )}

          <li>
            <Link 
              to="/profile" 
              className={`flex items-center p-2 rounded-lg ${
                isActive('/profile') ? 'bg-gray-700' : 'hover:bg-gray-700'
              }`}
            >
              <span className="material-icons mr-3">person</span>
              <span>My Profile</span>
            </Link>
          </li>
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;
