-- Management Dashboard Schema Extension
-- This file adds new tables for the advanced management dashboard features
-- without modifying existing tables

USE ehrx;

-- Analytics dashboards configuration
CREATE TABLE IF NOT EXISTS analytics_dashboards (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  layout_config <PERSON><PERSON><PERSON>,
  widget_config <PERSON><PERSON><PERSON>,
  is_default BOOLEAN DEFAULT FALSE,
  is_shared BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_shared (is_shared)
) ENGINE=InnoDB;

-- Performance metrics aggregation
CREATE TABLE IF NOT EXISTS performance_metrics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT,
  organizational_unit_id INT,
  metric_type ENUM('individual', 'team', 'department', 'organization') NOT NULL,
  metric_name VARCHAR(255) NOT NULL,
  metric_value DECIMAL(10,4),
  period_start DATE,
  period_end DATE,
  calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  metadata JSON,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
  INDEX idx_metric_type_date (metric_type, calculation_date),
  INDEX idx_user_period (user_id, period_start, period_end),
  INDEX idx_org_unit_period (organizational_unit_id, period_start, period_end)
) ENGINE=InnoDB;

-- Engagement surveys
CREATE TABLE IF NOT EXISTS engagement_surveys (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  survey_type ENUM('pulse', 'annual', 'onboarding', 'exit', 'custom') NOT NULL,
  questions JSON NOT NULL,
  target_audience JSON, -- roles, departments, etc.
  start_date DATE,
  end_date DATE,
  is_anonymous BOOLEAN DEFAULT TRUE,
  created_by_id INT NOT NULL,
  status ENUM('draft', 'active', 'completed', 'archived') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by_id) REFERENCES users(id),
  INDEX idx_status_dates (status, start_date, end_date),
  INDEX idx_survey_type (survey_type)
) ENGINE=InnoDB;

-- Survey responses
CREATE TABLE IF NOT EXISTS survey_responses (
  id INT AUTO_INCREMENT PRIMARY KEY,
  survey_id INT NOT NULL,
  respondent_id INT,
  responses JSON NOT NULL,
  completion_time INT, -- seconds
  submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ip_address VARCHAR(45),
  user_agent TEXT,
  FOREIGN KEY (survey_id) REFERENCES engagement_surveys(id) ON DELETE CASCADE,
  FOREIGN KEY (respondent_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_survey_submitted (survey_id, submitted_at),
  INDEX idx_respondent (respondent_id)
) ENGINE=InnoDB;

-- Recognition badge definitions
CREATE TABLE IF NOT EXISTS recognition_badges (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  icon_url VARCHAR(500),
  badge_type ENUM('achievement', 'appreciation', 'milestone', 'skill') NOT NULL,
  point_value INT DEFAULT 0,
  criteria JSON,
  is_active BOOLEAN DEFAULT TRUE,
  created_by_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by_id) REFERENCES users(id),
  INDEX idx_badge_type (badge_type),
  INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- Recognition instances
CREATE TABLE IF NOT EXISTS recognition_instances (
  id INT AUTO_INCREMENT PRIMARY KEY,
  badge_id INT,
  giver_id INT NOT NULL,
  receiver_id INT NOT NULL,
  message TEXT,
  points_awarded INT DEFAULT 0,
  is_public BOOLEAN DEFAULT TRUE,
  given_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (badge_id) REFERENCES recognition_badges(id),
  FOREIGN KEY (giver_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_receiver_date (receiver_id, given_at),
  INDEX idx_giver_date (giver_id, given_at),
  INDEX idx_public_date (is_public, given_at)
) ENGINE=InnoDB;

-- User points and levels
CREATE TABLE IF NOT EXISTS user_gamification (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL UNIQUE,
  total_points INT DEFAULT 0,
  current_level INT DEFAULT 1,
  badges_earned INT DEFAULT 0,
  recognitions_given INT DEFAULT 0,
  recognitions_received INT DEFAULT 0,
  last_activity_date DATE,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_points (total_points),
  INDEX idx_level (current_level)
) ENGINE=InnoDB;

-- Attrition prediction results
CREATE TABLE IF NOT EXISTS attrition_predictions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  risk_score DECIMAL(5,4) NOT NULL, -- 0.0000 to 1.0000
  risk_level ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  contributing_factors JSON,
  prediction_date DATE NOT NULL,
  model_version VARCHAR(50),
  confidence_score DECIMAL(5,4),
  recommended_actions JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_date (user_id, prediction_date),
  INDEX idx_risk_level (risk_level),
  INDEX idx_prediction_date (prediction_date)
) ENGINE=InnoDB;

-- Competency frameworks
CREATE TABLE IF NOT EXISTS competency_frameworks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  organizational_unit_id INT,
  competencies JSON NOT NULL,
  version VARCHAR(50) DEFAULT '1.0',
  is_active BOOLEAN DEFAULT TRUE,
  created_by_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id),
  FOREIGN KEY (created_by_id) REFERENCES users(id),
  INDEX idx_org_unit (organizational_unit_id),
  INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- Career path definitions
CREATE TABLE IF NOT EXISTS career_paths (
  id INT AUTO_INCREMENT PRIMARY KEY,
  from_role VARCHAR(255) NOT NULL,
  to_role VARCHAR(255) NOT NULL,
  organizational_unit_id INT,
  required_skills JSON,
  recommended_experience_years INT,
  typical_timeline_months INT,
  success_rate DECIMAL(5,2),
  created_by_id INT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id),
  FOREIGN KEY (created_by_id) REFERENCES users(id),
  INDEX idx_from_role (from_role),
  INDEX idx_to_role (to_role),
  INDEX idx_org_unit (organizational_unit_id)
) ENGINE=InnoDB;

-- Micro feedback instances
CREATE TABLE IF NOT EXISTS micro_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY,
  giver_id INT NOT NULL,
  receiver_id INT,
  feedback_type ENUM('emoji', 'quick_poll', 'check_in', 'thumbs_up') NOT NULL,
  feedback_value VARCHAR(255),
  context_type ENUM('task', 'meeting', 'project', 'general'),
  context_id INT,
  given_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (giver_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_receiver_date (receiver_id, given_at),
  INDEX idx_context (context_type, context_id),
  INDEX idx_feedback_type (feedback_type)
) ENGINE=InnoDB;

-- AI insights and recommendations
CREATE TABLE IF NOT EXISTS ai_insights (
  id INT AUTO_INCREMENT PRIMARY KEY,
  insight_type ENUM('attrition_risk', 'engagement_trend', 'skill_gap', 'performance_anomaly') NOT NULL,
  target_type ENUM('individual', 'team', 'department', 'organization') NOT NULL,
  target_id INT,
  insight_data JSON NOT NULL,
  confidence_score DECIMAL(5,4),
  priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  status ENUM('new', 'acknowledged', 'acted_upon', 'dismissed') DEFAULT 'new',
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP,
  INDEX idx_target (target_type, target_id),
  INDEX idx_type_priority (insight_type, priority),
  INDEX idx_status (status)
) ENGINE=InnoDB;
