import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  Grid, 
  Paper, 
  CircularProgress,
  Autocomplete,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { ReportingService } from '../../services/reporting';
import { EmployeeReport } from '../../types/reporting';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface EmployeeReportSectionProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateRangeChange: (newDateRange: { startDate: string; endDate: string }) => void;
  onError: (error: string | null) => void;
}

interface Employee {
  id: number;
  name: string;
}

const EmployeeReportSection: React.FC<EmployeeReportSectionProps> = ({
  dateRange,
  onDateRangeChange,
  onError
}) => {
  const [employees, setEmployees] = useState<Employee[]>([
    { id: 1, name: 'John Doe' },
    { id: 2, name: 'Jane Smith' },
    { id: 3, name: 'Mike Johnson' },
    // Mocked employees data - would come from API in real implementation
  ]);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [employeeReport, setEmployeeReport] = useState<EmployeeReport | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchEmployeeReport = async () => {
    if (!selectedEmployee) return;
    
    setLoading(true);
    try {
      const report = await ReportingService.getEmployeeReport(
        selectedEmployee.id,
        dateRange.startDate,
        dateRange.endDate
      );
      setEmployeeReport(report);
    } catch (error) {
      console.error('Error fetching employee report:', error);
      onError('Failed to fetch employee report. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      onDateRangeChange({
        ...dateRange,
        startDate: date.toISOString().split('T')[0]
      });
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      onDateRangeChange({
        ...dateRange,
        endDate: date.toISOString().split('T')[0]
      });
    }
  };

  const handleEmployeeChange = (_event: React.SyntheticEvent, value: Employee | null) => {
    setSelectedEmployee(value);
    setEmployeeReport(null);
  };

  const renderScoreHistory = () => {
    if (!employeeReport?.scoreHistory || employeeReport.scoreHistory.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 2 }}>
          <Typography variant="body1">No assessment history available</Typography>
        </Box>
      );
    }

    const chartData = {
      labels: employeeReport.scoreHistory.map(item => 
        new Date(item.date).toLocaleDateString()
      ),
      datasets: [
        {
          label: 'Performance Score',
          data: employeeReport.scoreHistory.map(item => item.score * 100), // Convert to percentage
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
        },
      ],
    };

    const chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: 'Performance Score History',
        },
      },
      scales: {
        y: {
          min: 0,
          max: 100,
          title: {
            display: true,
            text: 'Score (%)'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Assessment Date'
          }
        }
      }
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Line data={chartData} options={chartOptions} />
      </Box>
    );
  };

  const renderEmployeeReport = () => {
    if (!employeeReport) return null;

    return (
      <Box sx={{ mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h5" component="h2" gutterBottom>
              Performance Report: {selectedEmployee?.name}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" gutterBottom>
              Period: {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
            </Typography>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Summary" />
              <Divider />
              <CardContent>
                <Typography variant="body1" gutterBottom>
                  <strong>Assessments:</strong> {employeeReport.assessmentCount}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Average Score:</strong> {(employeeReport.averageScore * 100).toFixed(2)}%
                </Typography>
                {employeeReport.latestAssessment && (
                  <Typography variant="body1" gutterBottom>
                    <strong>Latest Assessment:</strong> {new Date(employeeReport.latestAssessment.date).toLocaleDateString()} 
                    ({(employeeReport.latestAssessment.score * 100).toFixed(2)}%)
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader title="Performance Trend" />
              <Divider />
              <CardContent>
                {renderScoreHistory()}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="flex-end" mt={2}>
              <Button 
                variant="contained"
                onClick={() => {
                  if (selectedEmployee) {
                    window.open(
                      `${window.location.origin}/api/reports/employee/${selectedEmployee.id}/export/pdf?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`,
                      '_blank'
                    );
                  }
                }}
                disabled={!selectedEmployee}
                sx={{ mr: 2 }}
              >
                Export PDF
              </Button>
              <Button 
                variant="outlined"
                onClick={() => {
                  if (selectedEmployee) {
                    const csvFilters = {
                      startDate: dateRange.startDate,
                      endDate: dateRange.endDate,
                      employeeIds: [selectedEmployee.id]
                    };
                    ReportingService.exportAssessmentsCsv(csvFilters);
                  }
                }}
                disabled={!selectedEmployee}
              >
                Export CSV
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    );
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Employee Performance Report
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={5}>
            <Autocomplete
              options={employees}
              getOptionLabel={(option) => option.name}
              value={selectedEmployee}
              onChange={handleEmployeeChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Employee"
                  variant="outlined"
                  fullWidth
                />
              )}
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Start Date"
                value={new Date(dateRange.startDate)}
                onChange={handleStartDateChange}
                slotProps={{ textField: { variant: 'outlined', fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="End Date"
                value={new Date(dateRange.endDate)}
                onChange={handleEndDateChange}
                slotProps={{ textField: { variant: 'outlined', fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} md={1}>
            <Button
              variant="contained"
              fullWidth
              onClick={fetchEmployeeReport}
              disabled={!selectedEmployee || loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Run'}
            </Button>
          </Grid>
        </Grid>
      </Paper>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : renderEmployeeReport()}
    </Box>
  );
};

export default EmployeeReportSection;
