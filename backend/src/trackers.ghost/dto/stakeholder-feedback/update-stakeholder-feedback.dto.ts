import { IsString, IsOptional, <PERSON><PERSON><PERSON>, IsISO8601, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { FeedbackType, FeedbackStatus, FeedbackSource } from '../../entities/stakeholder-feedback.entity';

export class UpdateStakeholderFeedbackDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  content?: string;

  @IsEnum(FeedbackType)
  @IsOptional()
  type?: FeedbackType;

  @IsEnum(FeedbackStatus)
  @IsOptional()
  status?: FeedbackStatus;

  @IsEnum(FeedbackSource)
  @IsOptional()
  source?: FeedbackSource;

  @IsString()
  @IsOptional()
  stakeholderName?: string;

  @IsString()
  @IsOptional()
  stakeholderRole?: string;

  @IsString()
  @IsOptional()
  stakeholderContact?: string;

  @IsString()
  @IsOptional()
  response?: string;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;

  @IsISO8601()
  @IsOptional()
  resolvedAt?: string;
}
