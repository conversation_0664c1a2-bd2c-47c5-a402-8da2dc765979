import {
  Controller,
  Get,
  Query,
  UseGuards,
  Request,
  Param,
} from '@nestjs/common';
import { AnalyticsService, AnalyticsFilters } from './analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('performance-metrics')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getPerformanceMetrics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userIds') userIds?: string,
    @Query('teamIds') teamIds?: string,
    @Query('departmentIds') departmentIds?: string,
    @Query('templateIds') templateIds?: string,
    @Query('roles') roles?: string,
    @Request() req
  ) {
    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userIds: userIds ? userIds.split(',').map(id => parseInt(id)) : undefined,
      teamIds: teamIds ? teamIds.split(',').map(id => parseInt(id)) : undefined,
      departmentIds: departmentIds ? departmentIds.split(',').map(id => parseInt(id)) : undefined,
      templateIds: templateIds ? templateIds.split(',').map(id => parseInt(id)) : undefined,
      roles: roles ? roles.split(',') as UserRole[] : undefined,
    };

    // Apply role-based filtering
    if (req.user.role === UserRole.MANAGER) {
      // Managers can only see their team's data
      // This would need proper team structure implementation
      filters.userIds = filters.userIds || [req.user.userId];
    }

    return this.analyticsService.getPerformanceMetrics(filters);
  }

  @Get('team-analytics')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getTeamAnalytics(
    @Query('teamId') teamId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userIds') userIds?: string,
    @Request() req
  ) {
    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userIds: userIds ? userIds.split(',').map(id => parseInt(id)) : undefined,
    };

    // Apply role-based filtering
    if (req.user.role === UserRole.MANAGER) {
      teamId = teamId || req.user.userId; // Manager can only see their own team
    }

    return this.analyticsService.getTeamAnalytics(teamId ? +teamId : undefined, filters);
  }

  @Get('comparison/:userId')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getComparisonAnalytics(
    @Param('userId') userId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Request() req
  ) {
    const targetUserId = parseInt(userId);

    // Permission check: employees can only see their own data
    if (req.user.role === UserRole.EMPLOYEE && req.user.userId !== targetUserId) {
      throw new Error('Unauthorized access to user data');
    }

    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    };

    return this.analyticsService.getComparisonAnalytics(targetUserId, filters);
  }

  @Get('trends')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getTrendAnalysis(
    @Query('timeframe') timeframe: 'monthly' | 'quarterly' | 'yearly' = 'monthly',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userIds') userIds?: string,
    @Query('teamIds') teamIds?: string,
    @Request() req
  ) {
    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userIds: userIds ? userIds.split(',').map(id => parseInt(id)) : undefined,
      teamIds: teamIds ? teamIds.split(',').map(id => parseInt(id)) : undefined,
    };

    // Apply role-based filtering
    if (req.user.role === UserRole.MANAGER) {
      // Managers can only see their team's trends
      filters.userIds = filters.userIds || [req.user.userId];
    }

    return this.analyticsService.getTrendAnalysis(timeframe, filters);
  }

  @Get('dashboard-summary')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async getDashboardSummary(@Request() req) {
    const filters: AnalyticsFilters = {};

    // Apply role-based filtering
    if (req.user.role === UserRole.EMPLOYEE) {
      filters.userIds = [req.user.userId];
    } else if (req.user.role === UserRole.MANAGER) {
      // Would filter by team members
      filters.userIds = [req.user.userId]; // Simplified
    }

    const [performanceMetrics, teamAnalytics, comparisonAnalytics, trendAnalysis] = await Promise.all([
      this.analyticsService.getPerformanceMetrics(filters),
      req.user.role !== UserRole.EMPLOYEE ? this.analyticsService.getTeamAnalytics(undefined, filters) : Promise.resolve([]),
      this.analyticsService.getComparisonAnalytics(req.user.userId, filters),
      req.user.role !== UserRole.EMPLOYEE ? this.analyticsService.getTrendAnalysis('monthly', filters) : Promise.resolve(null)
    ]);

    return {
      performanceMetrics,
      teamAnalytics,
      comparisonAnalytics,
      trendAnalysis,
      userRole: req.user.role,
      userId: req.user.userId
    };
  }

  @Get('performance-ranking')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getPerformanceRanking(
    @Query('limit') limit = 50,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('teamIds') teamIds?: string,
    @Request() req
  ) {
    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      teamIds: teamIds ? teamIds.split(',').map(id => parseInt(id)) : undefined,
    };

    const performanceMetrics = await this.analyticsService.getPerformanceMetrics(filters);
    
    return {
      topPerformers: performanceMetrics.topPerformers.slice(0, +limit),
      averageScore: performanceMetrics.averageScore,
      totalParticipants: performanceMetrics.topPerformers.length
    };
  }

  @Get('improvement-opportunities')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getImprovementOpportunities(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userIds') userIds?: string,
    @Request() req
  ) {
    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userIds: userIds ? userIds.split(',').map(id => parseInt(id)) : undefined,
    };

    const performanceMetrics = await this.analyticsService.getPerformanceMetrics(filters);
    
    return {
      improvementAreas: performanceMetrics.improvementAreas,
      scoreDistribution: performanceMetrics.scoreDistribution,
      recommendations: this.generateImprovementRecommendations(performanceMetrics)
    };
  }

  @Get('engagement-metrics')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getEngagementMetrics(
    @Query('timeframe') timeframe: 'monthly' | 'quarterly' | 'yearly' = 'monthly',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Request() req
  ) {
    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    };

    const trendAnalysis = await this.analyticsService.getTrendAnalysis(timeframe, filters);
    
    return {
      engagementTrend: trendAnalysis.data.map(d => ({
        period: d.period,
        engagement: d.userEngagement,
        assessmentParticipation: d.assessmentScores > 0 ? 100 : 0, // Simplified
        actionItemCompletion: d.actionItemCompletion
      })),
      averageEngagement: trendAnalysis.data.reduce((sum, d) => sum + d.userEngagement, 0) / trendAnalysis.data.length,
      predictions: trendAnalysis.predictions
    };
  }

  @Get('export-data')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async exportAnalyticsData(
    @Query('format') format: 'json' | 'csv' = 'json',
    @Query('type') type: 'performance' | 'trends' | 'teams' | 'all' = 'all',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Request() req
  ) {
    const filters: AnalyticsFilters = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    };

    let data: any = {};

    switch (type) {
      case 'performance':
        data = await this.analyticsService.getPerformanceMetrics(filters);
        break;
      case 'trends':
        data = await this.analyticsService.getTrendAnalysis('monthly', filters);
        break;
      case 'teams':
        data = await this.analyticsService.getTeamAnalytics(undefined, filters);
        break;
      case 'all':
        data = {
          performance: await this.analyticsService.getPerformanceMetrics(filters),
          trends: await this.analyticsService.getTrendAnalysis('monthly', filters),
          teams: await this.analyticsService.getTeamAnalytics(undefined, filters)
        };
        break;
    }

    if (format === 'csv') {
      // Would implement CSV conversion here
      return { message: 'CSV export not implemented yet', data };
    }

    return {
      format,
      type,
      exportDate: new Date().toISOString(),
      data
    };
  }

  private generateImprovementRecommendations(metrics: any): string[] {
    const recommendations = [];

    if (metrics.averageScore < 70) {
      recommendations.push('Consider implementing additional training programs to improve overall performance');
    }

    if (metrics.improvementAreas.some((area: any) => area.averageScore < 65)) {
      recommendations.push('Focus on targeted skill development in low-scoring areas');
    }

    const lowScoreCount = metrics.scoreDistribution
      .filter((dist: any) => dist.range.startsWith('0-') || dist.range.startsWith('21-'))
      .reduce((sum: number, dist: any) => sum + dist.count, 0);

    if (lowScoreCount > 0) {
      recommendations.push('Implement individual improvement plans for underperforming employees');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance levels are satisfactory. Continue current practices and monitor trends.');
    }

    return recommendations;
  }
}
