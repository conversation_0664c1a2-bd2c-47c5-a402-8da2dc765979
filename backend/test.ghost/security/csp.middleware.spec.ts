import { Test, TestingModule } from '@nestjs/testing';
import { NextFunction, Request, Response } from 'express';

import { CspMiddleware } from '../../src/security/middleware/csp.middleware';

describe('CspMiddleware', () => {
  let middleware: CspMiddleware;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNextFunction: NextFunction;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CspMiddleware],
    }).compile();

    middleware = module.get<CspMiddleware>(CspMiddleware);

    mockRequest = {};
    mockResponse = {
      setHeader: jest.fn(),
    };
    mockNextFunction = jest.fn();
  });

  it('should be defined', () => {
    expect(middleware).toBeDefined();
  });

  describe('use', () => {
    it('should set Content-Security-Policy header', () => {
      middleware.use(mockRequest as Request, mockResponse as Response, mockNextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'Content-Security-Policy',
        expect.any(String)
      );

      // Verify that the header contains essential CSP directives
      const cspHeader = (mockResponse.setHeader as jest.Mock).mock.calls[0][1];
      expect(cspHeader).toContain("default-src 'self'");
      expect(cspHeader).toContain("script-src 'self'");
      expect(cspHeader).toContain("style-src 'self'");
      expect(cspHeader).toContain("connect-src 'self'");
      expect(cspHeader).toContain("img-src 'self'");
      expect(cspHeader).toContain('frame-ancestors');
    });

    it('should set X-Content-Type-Options header', () => {
      middleware.use(mockRequest as Request, mockResponse as Response, mockNextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-Content-Type-Options',
        'nosniff'
      );
    });

    it('should set X-Frame-Options header', () => {
      middleware.use(mockRequest as Request, mockResponse as Response, mockNextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-Frame-Options',
        'DENY'
      );
    });

    it('should set X-XSS-Protection header', () => {
      middleware.use(mockRequest as Request, mockResponse as Response, mockNextFunction);

      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-XSS-Protection',
        '1; mode=block'
      );
    });

    it('should call next function', () => {
      middleware.use(mockRequest as Request, mockResponse as Response, mockNextFunction);

      expect(mockNextFunction).toHaveBeenCalledTimes(1);
    });
  });
});
