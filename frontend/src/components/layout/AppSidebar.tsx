import React, { useState } from 'react';
import {
  Drawer,
  Toolbar,
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
} from '@mui/material';
import { ExpandLess, ExpandMore } from '@mui/icons-material';

interface MenuItem {
  id: string;
  text: string;
  icon: React.ReactNode;
  submenu?: MenuItem[];
}

interface AppSidebarProps {
  menuItems: MenuItem[];
  selectedPage: string;
  onPageSelect: (pageId: string) => void;
  width?: number;
  mobileOpen?: boolean;
  onMobileToggle?: () => void;
  isMobile?: boolean;
}

const AppSidebar: React.FC<AppSidebarProps> = ({
  menuItems,
  selectedPage,
  onPageSelect,
  width = 280,
  mobileOpen = false,
  onMobileToggle,
  isMobile = false
}) => {
  console.log('🔍 AppSidebar rendering, menuItems:', menuItems.length);

  // State for managing expanded submenus
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());

  const toggleSubmenu = (menuId: string) => {
    const newExpanded = new Set(expandedMenus);
    if (newExpanded.has(menuId)) {
      newExpanded.delete(menuId);
    } else {
      newExpanded.add(menuId);
    }
    setExpandedMenus(newExpanded);
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const hasSubmenu = item.submenu && item.submenu.length > 0;
    const isExpanded = expandedMenus.has(item.id);
    const isSelected = selectedPage === item.id;

    return (
      <React.Fragment key={item.id}>
        <ListItem
          onClick={() => {
            if (hasSubmenu) {
              toggleSubmenu(item.id);
            } else {
              onPageSelect(item.id);
              if (isMobile && onMobileToggle) {
                onMobileToggle();
              }
            }
          }}
          sx={{
            cursor: 'pointer',
            borderRadius: 2,
            mb: 1,
            ml: level * 2, // Indent submenu items
            backgroundColor: isSelected ? 'primary.light' : 'transparent',
            color: isSelected ? 'primary.contrastText' : 'inherit',
            '&:hover': {
              backgroundColor: isSelected ? 'primary.main' : 'grey.100',
            },
          }}
        >
          <ListItemIcon sx={{
            color: isSelected ? 'primary.contrastText' : 'primary.main',
            minWidth: level > 0 ? 32 : 56 // Smaller icon space for submenu items
          }}>
            {item.icon}
          </ListItemIcon>
          <ListItemText
            primary={item.text}
            sx={{
              '& .MuiListItemText-primary': {
                fontWeight: isSelected ? 'bold' : 'normal',
                fontSize: level > 0 ? '0.875rem' : '1rem' // Smaller text for submenu items
              }
            }}
          />
          {hasSubmenu && (
            <ListItemIcon sx={{
              color: isSelected ? 'primary.contrastText' : 'primary.main',
              minWidth: 'auto'
            }}>
              {isExpanded ? <ExpandLess /> : <ExpandMore />}
            </ListItemIcon>
          )}
        </ListItem>

        {hasSubmenu && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.submenu!.map((subItem) => renderMenuItem(subItem, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };
  const drawerContent = (
    <Box sx={{ overflow: 'auto', p: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: 'primary.main' }}>
        Navigation
      </Typography>
      <List>
        {menuItems.map((item) => renderMenuItem(item))}
      </List>
    </Box>
  );

  return (
    <Box component="nav" sx={{ width: { sm: width }, flexShrink: { sm: 0 } }}>
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={onMobileToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: width },
        }}
      >
        <Toolbar />
        {drawerContent}
      </Drawer>

      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: width },
        }}
        open
      >
        <Toolbar />
        {drawerContent}
      </Drawer>
    </Box>
  );
};

export default AppSidebar;
