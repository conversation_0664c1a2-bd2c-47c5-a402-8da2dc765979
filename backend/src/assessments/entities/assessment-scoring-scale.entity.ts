import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, <PERSON>in<PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { User } from '../../users/entities/user.entity';
import { AssessmentScoringScaleLevel } from './assessment-scoring-scale-level.entity';
import { AssessmentCriteriaScoring } from './assessment-criteria-scoring.entity';

@Entity('assessment_scoring_scales')
export class AssessmentScoringScale {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'organizational_unit_id', nullable: true })
  organizationalUnitId: number;

  @ManyToOne(() => OrganizationalUnit, { nullable: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @OneToMany(() => AssessmentScoringScaleLevel, level => level.scoringScale)
  levels: AssessmentScoringScaleLevel[];

  @OneToMany(() => AssessmentCriteriaScoring, criteriaScoring => criteriaScoring.scoringScale)
  criteriaScorings: AssessmentCriteriaScoring[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
