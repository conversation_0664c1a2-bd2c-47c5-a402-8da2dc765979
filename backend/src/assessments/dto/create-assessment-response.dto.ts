import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsString, Min, Max } from 'class-validator';

export class CreateAssessmentResponseDto {
  @IsNotEmpty()
  @IsNumber()
  areaId: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100)
  score: number;

  @IsOptional()
  @IsString()
  evaluatorComments?: string;

  @IsOptional()
  @IsString()
  employeeComments?: string;
}
