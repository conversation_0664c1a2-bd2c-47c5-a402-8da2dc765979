import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as request from 'supertest';
import { Templates<PERSON>ontroller } from './templates.controller';
import { TemplatesService } from './templates.service';
import { ScoringService } from '../services/scoring.service';
import { AssessmentTemplate } from '../entities/assessment-template.entity';
import { AssessmentArea } from '../entities/assessment-area.entity';
import { ScoringRule, RuleType } from '../entities/scoring-rule.entity';
import { UserRole } from '../../users/entities/user.entity';

describe('Templates Integration', () => {
  let app: INestApplication;
  let templateRepository: Repository<AssessmentTemplate>;
  let areaRepository: Repository<AssessmentArea>;
  let ruleRepository: Repository<ScoringRule>;

  const mockUser = {
    userId: 1,
    role: UserRole.HR_ADMIN,
    email: '<EMAIL>'
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [AssessmentTemplate, AssessmentArea, ScoringRule],
          synchronize: true,
        }),
        TypeOrmModule.forFeature([AssessmentTemplate, AssessmentArea, ScoringRule]),
      ],
      controllers: [TemplatesController],
      providers: [TemplatesService, ScoringService],
    })
    .overrideGuard('JwtAuthGuard')
    .useValue({
      canActivate: (context) => {
        const request = context.switchToHttp().getRequest();
        request.user = mockUser;
        return true;
      },
    })
    .overrideGuard('RolesGuard')
    .useValue({
      canActivate: () => true,
    })
    .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    templateRepository = moduleFixture.get('AssessmentTemplateRepository');
    areaRepository = moduleFixture.get('AssessmentAreaRepository');
    ruleRepository = moduleFixture.get('ScoringRuleRepository');
  });

  afterEach(async () => {
    await app.close();
  });

  describe('POST /templates', () => {
    it('should create a new template with areas and scoring rules', async () => {
      const createTemplateDto = {
        name: 'Test Template',
        description: 'A test template',
        isGlobal: true,
        areas: [
          {
            name: 'Communication',
            description: 'Communication skills',
            weight: 0.4,
            maxScore: 100,
            orderIndex: 1,
            scoringRules: [
              {
                ruleType: RuleType.ADDITION,
                scoreAdjustment: 5,
                description: 'Training bonus'
              }
            ]
          },
          {
            name: 'Technical Skills',
            description: 'Technical proficiency',
            weight: 0.6,
            maxScore: 100,
            orderIndex: 2,
            scoringRules: [
              {
                ruleType: RuleType.CONDITIONAL,
                conditionField: 'nc_incidents',
                scoreAdjustment: 2,
                description: 'NC penalty'
              }
            ]
          }
        ]
      };

      const response = await request(app.getHttpServer())
        .post('/templates')
        .send(createTemplateDto)
        .expect(201);

      expect(response.body.name).toBe('Test Template');
      expect(response.body.isGlobal).toBe(true);
      expect(response.body.areas).toHaveLength(2);
      expect(response.body.areas[0].scoringRules).toHaveLength(1);
      expect(response.body.areas[1].scoringRules).toHaveLength(1);

      // Verify in database
      const savedTemplate = await templateRepository.findOne({
        where: { id: response.body.id },
        relations: ['areas', 'areas.scoringRules']
      });

      expect(savedTemplate).toBeDefined();
      expect(savedTemplate.areas).toHaveLength(2);
    });

    it('should validate area weights sum to 1', async () => {
      const createTemplateDto = {
        name: 'Invalid Template',
        description: 'Template with invalid weights',
        isGlobal: false,
        areas: [
          {
            name: 'Area 1',
            weight: 0.3,
            maxScore: 100,
            orderIndex: 1,
            scoringRules: []
          },
          {
            name: 'Area 2',
            weight: 0.3, // Total weight = 0.6, should be 1.0
            maxScore: 100,
            orderIndex: 2,
            scoringRules: []
          }
        ]
      };

      await request(app.getHttpServer())
        .post('/templates')
        .send(createTemplateDto)
        .expect(400);
    });
  });

  describe('GET /templates', () => {
    beforeEach(async () => {
      // Create test templates
      const template1 = templateRepository.create({
        name: 'Global Template',
        description: 'A global template',
        isGlobal: true,
        isActive: true,
        version: 1,
        createdById: 1,
        areas: []
      });

      const template2 = templateRepository.create({
        name: 'Personal Template',
        description: 'A personal template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 1,
        areas: []
      });

      await templateRepository.save([template1, template2]);
    });

    it('should return all templates', async () => {
      const response = await request(app.getHttpServer())
        .get('/templates')
        .expect(200);

      expect(response.body).toHaveLength(2);
      expect(response.body.some(t => t.name === 'Global Template')).toBe(true);
      expect(response.body.some(t => t.name === 'Personal Template')).toBe(true);
    });
  });

  describe('POST /templates/:id/clone', () => {
    let sourceTemplate: AssessmentTemplate;

    beforeEach(async () => {
      // Create a source template with areas and rules
      const area = areaRepository.create({
        name: 'Test Area',
        weight: 1.0,
        maxScore: 100,
        orderIndex: 1
      });

      const rule = ruleRepository.create({
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Test rule'
      });

      area.scoringRules = [rule];

      sourceTemplate = templateRepository.create({
        name: 'Source Template',
        description: 'Template to clone',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 1,
        areas: [area]
      });

      await templateRepository.save(sourceTemplate);
    });

    it('should clone a template with custom name', async () => {
      const response = await request(app.getHttpServer())
        .post(`/templates/${sourceTemplate.id}/clone`)
        .send({ name: 'Cloned Template' })
        .expect(201);

      expect(response.body.name).toBe('Cloned Template');
      expect(response.body.parentTemplateId).toBe(sourceTemplate.id);
      expect(response.body.areas).toHaveLength(1);
      expect(response.body.areas[0].scoringRules).toHaveLength(1);
    });

    it('should clone with default name if none provided', async () => {
      const response = await request(app.getHttpServer())
        .post(`/templates/${sourceTemplate.id}/clone`)
        .send({})
        .expect(201);

      expect(response.body.name).toBe('Source Template (Copy)');
    });
  });

  describe('POST /templates/:id/new-version', () => {
    let sourceTemplate: AssessmentTemplate;

    beforeEach(async () => {
      sourceTemplate = templateRepository.create({
        name: 'Versioned Template',
        description: 'Template for versioning',
        isGlobal: true,
        isActive: true,
        version: 1,
        createdById: 1,
        areas: []
      });

      await templateRepository.save(sourceTemplate);
    });

    it('should create a new version of template', async () => {
      const response = await request(app.getHttpServer())
        .post(`/templates/${sourceTemplate.id}/new-version`)
        .expect(201);

      expect(response.body.name).toBe('Versioned Template');
      expect(response.body.version).toBe(2);
      expect(response.body.parentTemplateId).toBe(sourceTemplate.id);

      // Verify original template is deactivated
      const originalTemplate = await templateRepository.findOne({
        where: { id: sourceTemplate.id }
      });
      expect(originalTemplate.isActive).toBe(false);
    });
  });

  describe('POST /templates/:id/validate', () => {
    it('should validate a correct template', async () => {
      const template = templateRepository.create({
        name: 'Valid Template',
        description: 'A valid template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 1,
        areas: [
          areaRepository.create({
            name: 'Test Area',
            weight: 1.0,
            maxScore: 100,
            orderIndex: 1,
            scoringRules: []
          })
        ]
      });

      await templateRepository.save(template);

      const response = await request(app.getHttpServer())
        .post(`/templates/${template.id}/validate`)
        .expect(201);

      expect(response.body.isValid).toBe(true);
      expect(response.body.errors).toHaveLength(0);
    });

    it('should detect validation errors', async () => {
      const template = templateRepository.create({
        name: '', // Invalid: empty name
        description: 'Invalid template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 1,
        areas: [
          areaRepository.create({
            name: 'Test Area',
            weight: 0.5, // Invalid: doesn't sum to 1.0
            maxScore: 100,
            orderIndex: 1,
            scoringRules: []
          })
        ]
      });

      await templateRepository.save(template);

      const response = await request(app.getHttpServer())
        .post(`/templates/${template.id}/validate`)
        .expect(201);

      expect(response.body.isValid).toBe(false);
      expect(response.body.errors.length).toBeGreaterThan(0);
      expect(response.body.errors).toContain('Template name is required');
    });
  });

  describe('Template Scoring Integration', () => {
    it('should create template and use it for assessment scoring', async () => {
      // Create template with scoring rules
      const createTemplateDto = {
        name: 'Scoring Test Template',
        description: 'Template for testing scoring',
        isGlobal: false,
        areas: [
          {
            name: 'Quality',
            weight: 1.0,
            maxScore: 100,
            orderIndex: 1,
            scoringRules: [
              {
                ruleType: RuleType.CONDITIONAL,
                conditionField: 'nc_incidents',
                scoreAdjustment: 5,
                description: 'Penalty per NC incident'
              },
              {
                ruleType: RuleType.ADDITION,
                scoreAdjustment: 10,
                description: 'Training completion bonus'
              }
            ]
          }
        ]
      };

      const templateResponse = await request(app.getHttpServer())
        .post('/templates')
        .send(createTemplateDto)
        .expect(201);

      // Verify template was created with scoring rules
      expect(templateResponse.body.areas[0].scoringRules).toHaveLength(2);

      // Test that the template can be retrieved with all relations
      const getResponse = await request(app.getHttpServer())
        .get(`/templates/${templateResponse.body.id}`)
        .expect(200);

      expect(getResponse.body.areas[0].scoringRules).toHaveLength(2);
      expect(getResponse.body.areas[0].scoringRules[0].ruleType).toBe(RuleType.CONDITIONAL);
      expect(getResponse.body.areas[0].scoringRules[1].ruleType).toBe(RuleType.ADDITION);
    });
  });
});
