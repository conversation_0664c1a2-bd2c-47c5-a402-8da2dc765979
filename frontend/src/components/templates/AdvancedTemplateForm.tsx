import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AssessmentTemplate,
  AssessmentArea,
  ScoringRule,
  RuleType,
  ConditionOperator,
  UserRole
} from '../../types';
import api from '../../services/api';

interface AdvancedTemplateFormProps {
  template?: AssessmentTemplate;
  userRole: UserRole;
  onSave?: (template: AssessmentTemplate) => void;
  onCancel?: () => void;
}

const AdvancedTemplateForm: React.FC<AdvancedTemplateFormProps> = ({
  template,
  userRole,
  onSave,
  onCancel
}) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'basic' | 'areas' | 'preview'>('basic');

  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isGlobal, setIsGlobal] = useState(false);
  const [areas, setAreas] = useState<AssessmentArea[]>([]);

  // Initialize form if editing an existing template
  useEffect(() => {
    if (template) {
      setName(template.name);
      setDescription(template.description || '');
      setIsGlobal(template.isGlobal || false);
      setAreas(template.areas || []);
    } else {
      // Initialize with one empty area for new templates
      setAreas([createEmptyArea(0)]);
    }
  }, [template]);

  const createEmptyArea = (orderIndex: number): AssessmentArea => ({
    name: '',
    description: '',
    weight: 0.25,
    maxScore: 100,
    orderIndex,
    scoringRules: []
  });

  const createEmptyScoringRule = (): ScoringRule => ({
    ruleType: RuleType.ADDITION,
    scoreAdjustment: 0,
    description: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!name.trim()) {
      setFormError('Template name is required');
      return;
    }

    if (areas.length === 0) {
      setFormError('Template must have at least one assessment area');
      return;
    }

    // Check if all areas have names
    const invalidArea = areas.find(area => !area.name.trim());
    if (invalidArea) {
      setFormError('All assessment areas must have names');
      return;
    }

    // Check if weights sum to 1
    const totalWeight = areas.reduce((sum, area) => sum + area.weight, 0);
    if (Math.abs(totalWeight - 1) > 0.01) {
      setFormError(`Area weights must sum to 1.0. Current sum: ${totalWeight.toFixed(2)}`);
      return;
    }

    try {
      setIsSubmitting(true);
      setFormError(null);

      const templateData = {
        name,
        description,
        isGlobal: userRole === UserRole.HR_ADMIN ? isGlobal : false,
        areas: areas.map(area => ({
          ...area,
          scoringRules: area.scoringRules?.map(rule => ({
            ...rule,
            // Ensure proper enum values
            ruleType: rule.ruleType,
            conditionOperator: rule.conditionOperator || undefined
          }))
        }))
      };

      let response;
      if (template?.id) {
        response = await api.put(`/templates/${template.id}`, templateData);
      } else {
        response = await api.post('/templates', templateData);
      }

      if (onSave) {
        onSave(response.data);
      } else {
        navigate(`/templates/${response.data.id}`);
      }
    } catch (err: any) {
      console.error('Error saving template:', err);
      setFormError(err.response?.data?.message || 'Failed to save template. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate('/templates');
    }
  };

  const addArea = () => {
    const newArea = createEmptyArea(areas.length);
    setAreas([...areas, newArea]);
  };

  const updateArea = (index: number, field: keyof AssessmentArea, value: any) => {
    const updatedAreas = [...areas];
    updatedAreas[index] = {
      ...updatedAreas[index],
      [field]: value
    };
    setAreas(updatedAreas);
  };

  const removeArea = (index: number) => {
    if (areas.length <= 1) {
      setFormError('Template must have at least one assessment area');
      return;
    }

    const updatedAreas = areas.filter((_, i) => i !== index);
    const reorderedAreas = updatedAreas.map((area, i) => ({
      ...area,
      orderIndex: i
    }));

    setAreas(reorderedAreas);
  };

  const addScoringRule = (areaIndex: number) => {
    const updatedAreas = [...areas];
    const area = updatedAreas[areaIndex];

    updatedAreas[areaIndex] = {
      ...area,
      scoringRules: [...(area.scoringRules || []), createEmptyScoringRule()]
    };

    setAreas(updatedAreas);
  };

  const updateScoringRule = (areaIndex: number, ruleIndex: number, field: keyof ScoringRule, value: any) => {
    const updatedAreas = [...areas];
    const area = updatedAreas[areaIndex];
    const updatedRules = [...(area.scoringRules || [])];

    updatedRules[ruleIndex] = {
      ...updatedRules[ruleIndex],
      [field]: value
    };

    updatedAreas[areaIndex] = {
      ...area,
      scoringRules: updatedRules
    };

    setAreas(updatedAreas);
  };

  const removeScoringRule = (areaIndex: number, ruleIndex: number) => {
    const updatedAreas = [...areas];
    const area = updatedAreas[areaIndex];
    const updatedRules = (area.scoringRules || []).filter((_, i) => i !== ruleIndex);

    updatedAreas[areaIndex] = {
      ...area,
      scoringRules: updatedRules
    };

    setAreas(updatedAreas);
  };

  // Calculate total weight
  const totalWeight = areas.reduce((sum, area) => sum + area.weight, 0);
  const isWeightValid = Math.abs(totalWeight - 1) <= 0.01;

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">
          {template?.id ? 'Edit Assessment Template' : 'Create Assessment Template'}
        </h2>

        {/* Tab Navigation */}
        <div className="mt-4">
          <nav className="flex space-x-8">
            <button
              type="button"
              onClick={() => setActiveTab('basic')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'basic'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              Basic Information
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('areas')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'areas'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              Assessment Areas ({areas.length})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('preview')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'preview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              Preview
            </button>
          </nav>
        </div>
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit}>
        <div className="px-6 py-6">
          {formError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              {formError}
            </div>
          )}

          {/* Basic Information Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter template name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={4}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Describe the purpose and scope of this assessment template"
                />
              </div>

              {userRole === UserRole.HR_ADMIN && (
                <div>
                  <div className="flex items-center">
                    <input
                      id="isGlobal"
                      type="checkbox"
                      checked={isGlobal}
                      onChange={(e) => setIsGlobal(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isGlobal" className="ml-2 block text-sm text-gray-900">
                      Make this template available to all teams (global template)
                    </label>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    Global templates can be used by any manager in the organization.
                  </p>
                </div>
              )}

              {!isWeightValid && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Weight Validation Warning
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>Area weights don't sum to 1.0. Current sum: {totalWeight.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Assessment Areas Tab */}
          {activeTab === 'areas' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Assessment Areas</h3>
                <button
                  type="button"
                  onClick={addArea}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Area
                </button>
              </div>

              {areas.map((area, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6 bg-gray-50">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="text-lg font-medium text-gray-900">Area {index + 1}</h4>
                    <button
                      type="button"
                      onClick={() => removeArea(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Area Name *
                      </label>
                      <input
                        type="text"
                        value={area.name}
                        onChange={(e) => updateArea(index, 'name', e.target.value)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="e.g., Communication Skills"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Weight *
                        </label>
                        <input
                          type="number"
                          step="0.05"
                          min="0.05"
                          max="1"
                          value={area.weight}
                          onChange={(e) => updateArea(index, 'weight', parseFloat(e.target.value))}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Max Score
                        </label>
                        <input
                          type="number"
                          step="1"
                          min="1"
                          value={area.maxScore}
                          onChange={(e) => updateArea(index, 'maxScore', parseInt(e.target.value))}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={area.description}
                      onChange={(e) => updateArea(index, 'description', e.target.value)}
                      rows={2}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="Describe what this area evaluates..."
                    />
                  </div>

                  {/* Scoring Rules Section */}
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="text-md font-medium text-gray-900">Scoring Rules</h5>
                      <button
                        type="button"
                        onClick={() => addScoringRule(index)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <svg className="-ml-1 mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Rule
                      </button>
                    </div>

                    {(!area.scoringRules || area.scoringRules.length === 0) && (
                      <p className="text-sm text-gray-500 italic">No scoring rules defined. Scores will be used as-is.</p>
                    )}

                    {area.scoringRules && area.scoringRules.map((rule, ruleIndex) => (
                      <div key={ruleIndex} className="bg-white border border-gray-200 rounded-md p-4 mb-3">
                        <div className="flex justify-between items-start mb-3">
                          <h6 className="text-sm font-medium text-gray-900">Rule {ruleIndex + 1}</h6>
                          <button
                            type="button"
                            onClick={() => removeScoringRule(index, ruleIndex)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Rule Type *
                            </label>
                            <select
                              value={rule.ruleType}
                              onChange={(e) => updateScoringRule(index, ruleIndex, 'ruleType', e.target.value as RuleType)}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            >
                              <option value={RuleType.ADDITION}>Addition</option>
                              <option value={RuleType.SUBTRACTION}>Subtraction</option>
                              <option value={RuleType.MULTIPLICATION}>Multiplication (%)</option>
                              <option value={RuleType.DIVISION}>Division (%)</option>
                              <option value={RuleType.CONDITIONAL}>Conditional</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Score Adjustment *
                            </label>
                            <input
                              type="number"
                              step="0.1"
                              value={rule.scoreAdjustment}
                              onChange={(e) => updateScoringRule(index, ruleIndex, 'scoreAdjustment', parseFloat(e.target.value))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              placeholder="0"
                            />
                          </div>

                          {rule.ruleType === RuleType.CONDITIONAL && (
                            <>
                              <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                  Condition Field
                                </label>
                                <select
                                  value={rule.conditionField || ''}
                                  onChange={(e) => updateScoringRule(index, ruleIndex, 'conditionField', e.target.value)}
                                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                                >
                                  <option value="">Select field...</option>
                                  <option value="nc_incidents">NC Incidents</option>
                                  <option value="training_completed">Training Completed</option>
                                  <option value="certifications">Certifications</option>
                                  <option value="customer_complaints">Customer Complaints</option>
                                  <option value="safety_incidents">Safety Incidents</option>
                                  <option value="attendance_score">Attendance Score</option>
                                </select>
                              </div>

                              <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                  Operator
                                </label>
                                <select
                                  value={rule.conditionOperator || ''}
                                  onChange={(e) => updateScoringRule(index, ruleIndex, 'conditionOperator', e.target.value as ConditionOperator)}
                                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                                >
                                  <option value="">Select operator...</option>
                                  <option value={ConditionOperator.EQUALS}>Equals</option>
                                  <option value={ConditionOperator.GREATER_THAN}>Greater Than</option>
                                  <option value={ConditionOperator.LESS_THAN}>Less Than</option>
                                  <option value={ConditionOperator.GREATER_THAN_OR_EQUAL}>Greater or Equal</option>
                                  <option value={ConditionOperator.LESS_THAN_OR_EQUAL}>Less or Equal</option>
                                </select>
                              </div>

                              <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                  Value
                                </label>
                                <input
                                  type="text"
                                  value={rule.conditionValue || ''}
                                  onChange={(e) => updateScoringRule(index, ruleIndex, 'conditionValue', e.target.value)}
                                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                                  placeholder="Condition value"
                                />
                              </div>
                            </>
                          )}
                        </div>

                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Description
                          </label>
                          <input
                            type="text"
                            value={rule.description || ''}
                            onChange={(e) => updateScoringRule(index, ruleIndex, 'description', e.target.value)}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            placeholder="Describe when this rule applies..."
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Preview Tab */}
          {activeTab === 'preview' && (
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Template Preview</h3>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900">{name || 'Untitled Template'}</h4>
                    <p className="text-sm text-gray-600 mt-1">{description || 'No description provided'}</p>
                    {isGlobal && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                        Global Template
                      </span>
                    )}
                  </div>

                  <div className="border-t border-gray-200 pt-4">
                    <h5 className="font-medium text-gray-900 mb-3">Assessment Areas ({areas.length})</h5>
                    <div className="space-y-3">
                      {areas.map((area, index) => (
                        <div key={index} className="bg-white border border-gray-200 rounded-md p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h6 className="font-medium text-gray-900">{area.name || `Area ${index + 1}`}</h6>
                              {area.description && (
                                <p className="text-sm text-gray-600 mt-1">{area.description}</p>
                              )}
                            </div>
                            <div className="text-right ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                Weight: {(area.weight * 100).toFixed(1)}%
                              </div>
                              <div className="text-sm text-gray-600">
                                Max Score: {area.maxScore}
                              </div>
                            </div>
                          </div>

                          {area.scoringRules && area.scoringRules.length > 0 && (
                            <div className="mt-3 pt-3 border-t border-gray-100">
                              <h6 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                                Scoring Rules ({area.scoringRules.length})
                              </h6>
                              <div className="mt-2 space-y-1">
                                {area.scoringRules.map((rule, ruleIndex) => (
                                  <div key={ruleIndex} className="text-xs text-gray-600">
                                    <span className="font-medium">{rule.ruleType}</span>
                                    {rule.scoreAdjustment !== 0 && (
                                      <span className="ml-1">
                                        ({rule.scoreAdjustment > 0 ? '+' : ''}{rule.scoreAdjustment})
                                      </span>
                                    )}
                                    {rule.description && (
                                      <span className="ml-1">- {rule.description}</span>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-900">Total Weight:</span>
                      <span className={`text-sm font-medium ${isWeightValid ? 'text-green-600' : 'text-red-600'
                        }`}>
                        {(totalWeight * 100).toFixed(1)}%
                        {isWeightValid ? ' ✓' : ' ⚠️'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>

          <div className="flex space-x-3">
            {activeTab !== 'preview' && (
              <button
                type="button"
                onClick={() => {
                  if (activeTab === 'basic') setActiveTab('areas');
                  else if (activeTab === 'areas') setActiveTab('preview');
                }}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Next
              </button>
            )}

            <button
              type="submit"
              disabled={isSubmitting || !isWeightValid}
              className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${isSubmitting || !isWeightValid
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
                }`}
            >
              {isSubmitting ? 'Saving...' : (template?.id ? 'Update Template' : 'Create Template')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AdvancedTemplateForm;
