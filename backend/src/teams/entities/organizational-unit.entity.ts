import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum OrganizationalUnitType {
  ORGANIZATION = 'organization',
  DIVISION = 'division',
  DEPARTMENT = 'department',
  TEAM = 'team',
  SQUAD = 'squad',
  UNIT = 'unit',
}

@Entity('organizational_units')
export class OrganizationalUnit {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: OrganizationalUnitType,
    default: OrganizationalUnitType.TEAM,
  })
  type: OrganizationalUnitType;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'parent_id', nullable: true })
  parentId: number;

  @Column({ default: 0 })
  level: number;

  @Column({ name: 'manager_id', nullable: true })
  managerId: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0.00 })
  budget: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrganizationalUnit, (unit) => unit.children, { nullable: true })
  @JoinColumn({ name: 'parent_id' })
  parent: OrganizationalUnit;

  @OneToMany(() => OrganizationalUnit, (unit) => unit.parent)
  children: OrganizationalUnit[];

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  @OneToMany(() => User, (user) => user.organizationalUnit)
  members: User[];
}
