import React, { useState } from 'react';
import {
  <PERSON>,
  Button,
  Typo<PERSON>,
  Card,
  CardContent,
  Alert,
} from '@mui/material';
import {
  BugReport as BugIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import loggingService from '../../services/logging.service';

const ErrorTestComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testJavaScriptError = () => {
    try {
      addResult('Testing JavaScript error...');
      // This will cause a ReferenceError
      (window as any).nonExistentFunction();
    } catch (error) {
      addResult('JavaScript error caught and should be logged automatically');
    }
  };

  const testUnhandledPromiseRejection = () => {
    addResult('Testing unhandled promise rejection...');
    // This will trigger an unhandled promise rejection
    Promise.reject(new Error('Test unhandled promise rejection'));
    addResult('Unhandled promise rejection triggered');
  };

  const testManualErrorLogging = async () => {
    addResult('Testing manual error logging...');
    await loggingService.logError('Manual test error', {
      testType: 'manual_error',
      timestamp: new Date(),
      userAgent: navigator.userAgent,
    }, 'error-test-component');
    addResult('Manual error logged successfully');
  };

  const testManualWarningLogging = async () => {
    addResult('Testing manual warning logging...');
    await loggingService.logWarning('Manual test warning', {
      testType: 'manual_warning',
      timestamp: new Date(),
    }, 'error-test-component');
    addResult('Manual warning logged successfully');
  };

  const testManualInfoLogging = async () => {
    addResult('Testing manual info logging...');
    await loggingService.logInfo('Manual test info', {
      testType: 'manual_info',
      timestamp: new Date(),
    }, 'error-test-component');
    addResult('Manual info logged successfully');
  };

  const testUserActionLogging = async () => {
    addResult('Testing user action logging...');
    await loggingService.logUserAction('test_button_click', {
      buttonId: 'user-action-test',
      testType: 'user_action',
    }, 'error-test-component');
    addResult('User action logged successfully');
  };

  const testReactError = () => {
    addResult('Testing React component error...');
    // This will cause a React error that should be caught by ErrorBoundary
    throw new Error('Test React component error');
  };

  const getSessionInfo = () => {
    const sessionInfo = loggingService.getSessionInfo();
    addResult(`Session Info: ${JSON.stringify(sessionInfo)}`);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🧪 Logging System Test Dashboard
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Use these buttons to test different aspects of the logging system. 
        Check the browser console and network tab to see the logging in action.
      </Typography>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
        <Button
          variant="contained"
          color="error"
          startIcon={<BugIcon />}
          onClick={testJavaScriptError}
        >
          Test JS Error
        </Button>

        <Button
          variant="contained"
          color="error"
          onClick={testUnhandledPromiseRejection}
        >
          Test Promise Rejection
        </Button>

        <Button
          variant="contained"
          color="error"
          onClick={testReactError}
        >
          Test React Error
        </Button>

        <Button
          variant="contained"
          color="warning"
          startIcon={<WarningIcon />}
          onClick={testManualErrorLogging}
        >
          Log Manual Error
        </Button>

        <Button
          variant="contained"
          color="warning"
          onClick={testManualWarningLogging}
        >
          Log Manual Warning
        </Button>

        <Button
          variant="contained"
          color="info"
          startIcon={<InfoIcon />}
          onClick={testManualInfoLogging}
        >
          Log Manual Info
        </Button>

        <Button
          variant="contained"
          color="primary"
          onClick={testUserActionLogging}
        >
          Log User Action
        </Button>

        <Button
          variant="outlined"
          onClick={getSessionInfo}
        >
          Get Session Info
        </Button>

        <Button
          variant="outlined"
          color="secondary"
          onClick={clearResults}
        >
          Clear Results
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Test Results
          </Typography>
          
          {testResults.length === 0 ? (
            <Typography variant="body2" color="textSecondary">
              No tests run yet. Click the buttons above to test the logging system.
            </Typography>
          ) : (
            <Box>
              {testResults.map((result, index) => (
                <Alert 
                  key={index} 
                  severity="info" 
                  sx={{ mb: 1 }}
                >
                  {result}
                </Alert>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>

      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          📝 Instructions
        </Typography>
        <Typography variant="body2" component="div">
          <ul>
            <li><strong>JS Error:</strong> Triggers a JavaScript error that should be automatically captured</li>
            <li><strong>Promise Rejection:</strong> Creates an unhandled promise rejection</li>
            <li><strong>React Error:</strong> Causes a React component error (will show error boundary)</li>
            <li><strong>Manual Logs:</strong> Test manual logging at different levels</li>
            <li><strong>User Action:</strong> Test user action tracking</li>
            <li><strong>Session Info:</strong> Display current session information</li>
          </ul>
        </Typography>
      </Box>
    </Box>
  );
};

export default ErrorTestComponent;
