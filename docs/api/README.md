# EHRX Backend API Documentation

This documentation provides detailed information about the EHRX Employee Performance Management Dashboard's backend APIs, including endpoints, request/response formats, and authentication requirements.

## Table of Contents

1. [Authentication](#authentication)
2. [Assessment Templates API](#assessment-templates-api)
3. [Assessments API](#assessments-api)
4. [Reporting API](#reporting-api)
5. [Error Handling](#error-handling)

## Authentication

All API endpoints require authentication using JSON Web Tokens (JWT).

### Request Headers

```
Authorization: Bearer <token>
```

### User Roles

Access to endpoints is controlled by role-based permissions:

- `EMPLOYEE`: Regular users who can view and complete their own assessments
- `MANAGER`: Team managers who can create and manage assessments for their team members
- `HR_ADMIN`: Administrators who have full access to all features and data

## Assessment Templates API

API endpoints for managing assessment templates.

| Endpoint | Method | Description | Access |
|---------|--------|-------------|--------|
| `/api/templates` | GET | List all templates | MANAGER, HR_ADMIN |
| `/api/templates/:id` | GET | Get template details | MANAGER, HR_ADMIN |
| `/api/templates` | POST | Create a new template | HR_ADMIN |
| `/api/templates/:id` | PATCH | Update a template | HR_ADMIN |
| `/api/templates/:id` | DELETE | Delete a template | HR_ADMIN |
| `/api/templates/:id/clone` | POST | Clone an existing template | HR_ADMIN |
| `/api/templates/:id/versions` | GET | List all versions of a template | MANAGER, HR_ADMIN |
| `/api/templates/:id/versions/:versionId` | GET | Get a specific template version | MANAGER, HR_ADMIN |

### Template Object Structure

```json
{
  "id": 1,
  "name": "Quarterly Performance Review",
  "description": "Standard quarterly performance assessment",
  "version": 1,
  "isActive": true,
  "createdAt": "2025-01-01T00:00:00Z",
  "updatedAt": "2025-01-01T00:00:00Z",
  "createdBy": 5,
  "assessmentAreas": [
    {
      "id": 1,
      "name": "Technical Skills",
      "description": "Evaluation of technical capabilities and knowledge",
      "weight": 0.4,
      "questions": [
        {
          "id": 1,
          "text": "How well does the employee apply technical knowledge to solve problems?",
          "type": "SCALE",
          "required": true,
          "options": ["1", "2", "3", "4", "5"]
        }
      ]
    }
  ]
}
```

## Assessments API

API endpoints for managing performance assessments.

| Endpoint | Method | Description | Access |
|---------|--------|-------------|--------|
| `/api/assessments` | GET | List assessments (filtered by user role) | ALL |
| `/api/assessments/:id` | GET | Get assessment details | ALL (with access restrictions) |
| `/api/assessments` | POST | Create a new assessment | MANAGER, HR_ADMIN |
| `/api/assessments/:id` | PATCH | Update an assessment | ALL (with access restrictions) |
| `/api/assessments/:id` | DELETE | Delete an assessment | MANAGER, HR_ADMIN |
| `/api/assessments/:id/submit` | POST | Submit an assessment | EMPLOYEE |
| `/api/assessments/:id/approve` | POST | Approve an assessment | MANAGER, HR_ADMIN |
| `/api/assessments/:id/reject` | POST | Reject an assessment | MANAGER, HR_ADMIN |

### Assessment Object Structure

```json
{
  "id": 1,
  "employeeId": 101,
  "employeeName": "John Doe",
  "managerId": 201,
  "managerName": "Sarah Manager",
  "templateId": 1,
  "templateName": "Quarterly Performance Review",
  "templateVersion": 1,
  "status": "COMPLETED",
  "score": 85.4,
  "createdAt": "2025-01-10T09:30:00Z",
  "updatedAt": "2025-01-15T14:45:00Z",
  "submittedAt": "2025-01-15T14:45:00Z",
  "dueDate": "2025-01-16T23:59:59Z",
  "responses": [
    {
      "areaId": 1,
      "areaName": "Technical Skills",
      "score": 85,
      "notes": "Demonstrated strong technical knowledge..."
    }
  ],
  "notes": "Overall performance notes..."
}
```

## Reporting API

API endpoints for generating reports and exporting assessment data.

| Endpoint | Method | Description | Access |
|---------|--------|-------------|--------|
| `/api/reports/employee/:id` | GET | Get employee performance report | MANAGER, HR_ADMIN, Self |
| `/api/reports/team/:id` | GET | Get team performance report | MANAGER, HR_ADMIN |
| `/api/reports/organization` | GET | Get organization-wide performance report | HR_ADMIN |
| `/api/reports/dashboard` | GET | Get dashboard metrics | MANAGER, HR_ADMIN |
| `/api/reports/trends` | GET | Get performance trends over time | MANAGER, HR_ADMIN |
| `/api/reports/benchmarks` | GET | Get team benchmarks for comparison | MANAGER, HR_ADMIN |
| `/api/reports/assessment/:id/export/pdf` | GET | Export assessment as PDF | MANAGER, HR_ADMIN, Self |
| `/api/reports/assessment/:id/export/json` | GET | Export assessment as JSON | MANAGER, HR_ADMIN, Self |
| `/api/reports/export/csv` | POST | Export filtered assessments as CSV | MANAGER, HR_ADMIN |

### Employee Report

**Request:**
```
GET /api/reports/employee/{employeeId}?startDate=2025-01-01&endDate=2025-03-31
```

**Response:**
```json
{
  "employeeId": 101,
  "assessmentCount": 4,
  "averageScore": 0.854,
  "scoreHistory": [
    {
      "id": 1,
      "date": "2025-01-15T00:00:00Z",
      "score": 0.82,
      "status": "APPROVED"
    },
    {
      "id": 2,
      "date": "2025-02-15T00:00:00Z",
      "score": 0.85,
      "status": "APPROVED"
    }
  ],
  "latestAssessment": {
    "id": 4,
    "date": "2025-03-15T00:00:00Z",
    "score": 0.87,
    "status": "APPROVED"
  }
}
```

### Team Report

**Request:**
```
GET /api/reports/team/{teamId}?startDate=2025-01-01&endDate=2025-03-31
```

**Response:**
```json
{
  "teamId": 5,
  "assessmentCount": 12,
  "averageScore": 0.83,
  "employeeScores": [
    {
      "employeeId": 101,
      "averageScore": 0.85,
      "assessmentCount": 3
    },
    {
      "employeeId": 102,
      "averageScore": 0.81,
      "assessmentCount": 3
    }
  ],
  "performanceDistribution": {
    "excellent": 3,
    "good": 5,
    "satisfactory": 2,
    "needsImprovement": 1,
    "poor": 0
  }
}
```

### Organization Report

**Request:**
```
GET /api/reports/organization?startDate=2025-01-01&endDate=2025-03-31
```

**Response:**
```json
{
  "assessmentCount": 87,
  "averageScore": 0.79,
  "completionRate": 0.92,
  "statusBreakdown": {
    "draft": 3,
    "inProgress": 4,
    "completed": 5,
    "approved": 72,
    "rejected": 3
  },
  "performanceDistribution": {
    "excellent": 15,
    "good": 42,
    "satisfactory": 20,
    "needsImprovement": 8,
    "poor": 2
  }
}
```

### Dashboard Metrics

**Request:**
```
GET /api/reports/dashboard?startDate=2025-01-01&endDate=2025-03-31&teamId=5
```

**Response:**
```json
{
  "totalAssessments": 45,
  "pendingAssessments": 7,
  "completedAssessments": 38,
  "averageScore": 0.81,
  "recentAssessments": [
    {
      "id": 87,
      "employeeId": 105,
      "status": "COMPLETED",
      "score": 0.76,
      "date": "2025-03-28T14:30:00Z"
    }
  ],
  "performanceDistribution": {
    "excellent": 8,
    "good": 20,
    "satisfactory": 12,
    "needsImprovement": 4,
    "poor": 1
  },
  "statusBreakdown": {
    "draft": 2,
    "inProgress": 5,
    "completed": 4,
    "approved": 33,
    "rejected": 1
  }
}
```

### Performance Trends

**Request:**
```
GET /api/reports/trends?period=monthly&count=6&teamId=5
```

**Response:**
```json
[
  {
    "period": "2025-03",
    "averageScore": 0.82,
    "completionRate": 0.95,
    "assessmentCount": 19
  },
  {
    "period": "2025-02",
    "averageScore": 0.80,
    "completionRate": 0.92,
    "assessmentCount": 18
  },
  {
    "period": "2025-01",
    "averageScore": 0.78,
    "completionRate": 0.90,
    "assessmentCount": 20
  }
]
```

### Team Benchmarks

**Request:**
```
GET /api/reports/benchmarks?teamIds=1,2,3,5&startDate=2025-01-01&endDate=2025-03-31
```

**Response:**
```json
[
  {
    "teamId": 1,
    "teamName": "Engineering",
    "teamSize": 12,
    "averageScore": 0.84,
    "completionRate": 0.96,
    "assessmentCount": 35
  },
  {
    "teamId": 2,
    "teamName": "Product",
    "teamSize": 8,
    "averageScore": 0.83,
    "completionRate": 0.94,
    "assessmentCount": 23
  }
]
```

### Export Assessment PDF

**Request:**
```
GET /api/reports/assessment/{assessmentId}/export/pdf
```

**Response:**
Binary PDF file with appropriate Content-Type and Content-Disposition headers.

### Export Assessment JSON

**Request:**
```
GET /api/reports/assessment/{assessmentId}/export/json
```

**Response:**
JSON file with detailed assessment information.

### Export Assessments CSV

**Request:**
```
POST /api/reports/export/csv
Content-Type: application/json

{
  "startDate": "2025-01-01",
  "endDate": "2025-03-31",
  "employeeIds": [101, 102, 103],
  "teamIds": [5],
  "statuses": ["APPROVED", "REJECTED"]
}
```

**Response:**
CSV file with appropriate Content-Type and Content-Disposition headers.

## Error Handling

All API endpoints follow a consistent error handling pattern:

### Error Response Format

```json
{
  "statusCode": 400,
  "message": "Error message describing what went wrong",
  "error": "Error type"
}
```

### Common HTTP Status Codes

- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Authentication valid but insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error
