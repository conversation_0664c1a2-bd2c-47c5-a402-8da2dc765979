import React, { useState, useEffect } from 'react';
import {
  Card,
  CardHeader,
  CardContent,
  Box,
  Typography,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface AttritionHeatmapWidgetProps {
  filters: any;
  detailed?: boolean;
}

interface HeatmapData {
  name: string;
  avgRiskScore: number;
  employeeCount: number;
  riskLevel: string;
}

const AttritionHeatmapWidget: React.FC<AttritionHeatmapWidgetProps> = ({ filters, detailed = false }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [heatmapData, setHeatmapData] = useState<HeatmapData[]>([]);
  const [groupBy, setGroupBy] = useState('department');
  const [riskAnalysis, setRiskAnalysis] = useState<any>(null);

  useEffect(() => {
    loadHeatmapData();
  }, [filters, groupBy]);

  const loadHeatmapData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load heatmap data
      const heatmapResponse = await ApiService.get(`/ai/attrition/heatmap?groupBy=${groupBy}`);
      if (heatmapResponse.data) {
        setHeatmapData(heatmapResponse.data.heatmapData || []);
      }

      // Load risk analysis
      const riskResponse = await ApiService.get('/ai/attrition/risk-analysis', {
        params: filters.department !== 'all' ? { departmentId: filters.department } : {},
      });
      if (riskResponse.data) {
        setRiskAnalysis(riskResponse.data);
      }
    } catch (err) {
      console.error('Error loading attrition heatmap data:', err);
      setError('Failed to load attrition data');
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high':
        return '#f44336';
      case 'medium':
        return '#ff9800';
      case 'low':
        return '#4caf50';
      default:
        return '#9e9e9e';
    }
  };

  const getRiskIntensity = (riskScore: number) => {
    return Math.min(1, riskScore / 100);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader title="Attrition Risk Heatmap" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Attrition Risk Heatmap"
        subheader="Visual representation of attrition risk across organizational units"
        action={
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Group By</InputLabel>
            <Select
              value={groupBy}
              label="Group By"
              onChange={(e) => setGroupBy(e.target.value)}
            >
              <MenuItem value="department">Department</MenuItem>
              <MenuItem value="role">Role</MenuItem>
              <MenuItem value="team">Team</MenuItem>
            </Select>
          </FormControl>
        }
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Risk Summary */}
        {riskAnalysis && (
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center" p={2} bgcolor="background.paper" borderRadius={1}>
                <Typography variant="h4" color="error">
                  {riskAnalysis.atRiskCount || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  High Risk Employees
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center" p={2} bgcolor="background.paper" borderRadius={1}>
                <Typography variant="h4" color="primary">
                  {riskAnalysis.riskScore?.toFixed(1) || '0.0'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Average Risk Score
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center" p={2} bgcolor="background.paper" borderRadius={1}>
                <Typography variant="h4" color="info.main">
                  {riskAnalysis.totalEmployees || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Employees
                </Typography>
              </Box>
            </Grid>
          </Grid>
        )}

        {/* Heatmap Visualization */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Risk Distribution by {groupBy.charAt(0).toUpperCase() + groupBy.slice(1)}
          </Typography>
          <Grid container spacing={1}>
            {heatmapData.map((item, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Tooltip
                  title={`${item.name}: ${item.avgRiskScore.toFixed(1)} risk score, ${item.employeeCount} employees`}
                >
                  <Paper
                    sx={{
                      p: 2,
                      textAlign: 'center',
                      backgroundColor: getRiskColor(item.riskLevel),
                      color: 'white',
                      opacity: 0.7 + (getRiskIntensity(item.avgRiskScore) * 0.3),
                      cursor: 'pointer',
                      '&:hover': {
                        opacity: 1,
                        transform: 'scale(1.02)',
                      },
                      transition: 'all 0.2s ease-in-out',
                    }}
                  >
                    <Typography variant="subtitle2" noWrap>
                      {item.name}
                    </Typography>
                    <Typography variant="h6">
                      {item.avgRiskScore.toFixed(1)}
                    </Typography>
                    <Typography variant="caption">
                      {item.employeeCount} employees
                    </Typography>
                  </Paper>
                </Tooltip>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Risk Legend */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Risk Level Legend
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            <Chip
              label="High Risk (70-100)"
              sx={{ backgroundColor: '#f44336', color: 'white' }}
              size="small"
            />
            <Chip
              label="Medium Risk (40-69)"
              sx={{ backgroundColor: '#ff9800', color: 'white' }}
              size="small"
            />
            <Chip
              label="Low Risk (0-39)"
              sx={{ backgroundColor: '#4caf50', color: 'white' }}
              size="small"
            />
          </Box>
        </Box>

        {/* Detailed Table View */}
        {detailed && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Detailed Risk Analysis
            </Typography>
            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>{groupBy.charAt(0).toUpperCase() + groupBy.slice(1)}</TableCell>
                    <TableCell align="right">Employees</TableCell>
                    <TableCell align="right">Risk Score</TableCell>
                    <TableCell align="center">Risk Level</TableCell>
                    <TableCell align="right">High Risk Count</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {heatmapData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell component="th" scope="row">
                        {item.name}
                      </TableCell>
                      <TableCell align="right">{item.employeeCount}</TableCell>
                      <TableCell align="right">{item.avgRiskScore.toFixed(1)}</TableCell>
                      <TableCell align="center">
                        <Chip
                          label={item.riskLevel}
                          color={
                            item.riskLevel === 'high' ? 'error' :
                              item.riskLevel === 'medium' ? 'warning' : 'success'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        {Math.round(item.employeeCount * (item.riskLevel === 'high' ? 1 : item.riskLevel === 'medium' ? 0.3 : 0.1))}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {/* Top Risk Factors */}
        {riskAnalysis?.topRiskFactors && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Top Risk Factors
            </Typography>
            <Grid container spacing={1}>
              {riskAnalysis.topRiskFactors.slice(0, 6).map((factor: any, index: number) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      {factor.factor}
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1}>
                      <WarningIcon color="warning" fontSize="small" />
                      <Typography variant="body2">
                        {factor.count} employees ({factor.percentage.toFixed(1)}%)
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Recommendations */}
        {riskAnalysis?.recommendations && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              AI Recommendations
            </Typography>
            {riskAnalysis.recommendations.map((recommendation: string, index: number) => (
              <Alert key={index} severity="info" sx={{ mb: 1 }}>
                {recommendation}
              </Alert>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default AttritionHeatmapWidget;
