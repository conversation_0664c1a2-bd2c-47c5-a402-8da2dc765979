import api from './api';

/**
 * Service for reporting and export functionality
 */
export const ReportingService = {
  // Employee Reports
  getEmployeeReport: async (employeeId: number, startDate?: string, endDate?: string) => {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const response = await api.get(`/reports/employee/${employeeId}?${params.toString()}`);
    return response.data;
  },

  // Team Reports
  getTeamReport: async (teamId: number, startDate?: string, endDate?: string) => {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const response = await api.get(`/reports/team/${teamId}?${params.toString()}`);
    return response.data;
  },

  // Organization Reports
  getOrganizationReport: async (startDate?: string, endDate?: string) => {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const response = await api.get(`/reports/organization?${params.toString()}`);
    return response.data;
  },

  // Dashboard Metrics
  getDashboardMetrics: async (startDate?: string, endDate?: string, teamId?: number) => {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    if (teamId) params.append('teamId', teamId.toString());
    
    const response = await api.get(`/reports/dashboard?${params.toString()}`);
    return response.data;
  },

  // Performance Trends
  getPerformanceTrends: async (
    period: 'monthly' | 'quarterly' | 'yearly' = 'monthly',
    count: number = 12,
    teamId?: number
  ) => {
    const params = new URLSearchParams();
    params.append('period', period);
    params.append('count', count.toString());
    if (teamId) params.append('teamId', teamId.toString());
    
    const response = await api.get(`/reports/trends?${params.toString()}`);
    return response.data;
  },

  // Team Benchmarks
  getTeamBenchmarks: async (
    teamIds: number[] = [],
    startDate?: string,
    endDate?: string
  ) => {
    const params = new URLSearchParams();
    if (teamIds.length > 0) params.append('teamIds', teamIds.join(','));
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const response = await api.get(`/reports/benchmarks?${params.toString()}`);
    return response.data;
  },

  // Export PDF
  exportAssessmentPdf: async (assessmentId: number) => {
    // Use browser download instead of axios for file download
    window.open(`${api.defaults.baseURL}/reports/assessment/${assessmentId}/export/pdf`, '_blank');
  },

  // Export JSON
  exportAssessmentJson: async (assessmentId: number) => {
    // Use browser download instead of axios for file download
    window.open(`${api.defaults.baseURL}/reports/assessment/${assessmentId}/export/json`, '_blank');
  },

  // Export CSV (with filters)
  exportAssessmentsCsv: async (filters: {
    startDate?: string;
    endDate?: string;
    employeeIds?: number[];
    teamIds?: number[];
    statuses?: string[];
  }) => {
    const response = await api.post('/reports/export/csv', filters, {
      responseType: 'blob'
    });
    
    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    const filename = response.headers['content-disposition']?.split('filename=')[1]?.replace(/"/g, '') || 
      `assessments_export_${new Date().toISOString().split('T')[0]}.csv`;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
};
