import { IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString, IsDateString, IsObject, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export class AssessmentAreaInputDto {
  @IsNotEmpty()
  @IsNumber()
  areaId: number;

  @IsNotEmpty()
  @IsNumber()
  baseScore: number;

  @IsOptional()
  @IsString()
  evaluatorComments?: string;

  @IsOptional()
  @IsString()
  employeeComments?: string;

  @IsOptional()
  @IsObject()
  additionalData?: Record<string, any>;
}

export class CreateAssessmentWithScoringDto {
  @IsNotEmpty()
  @IsNumber()
  templateId: number;

  @IsNotEmpty()
  @IsNumber()
  employeeId: number;

  @IsOptional()
  @IsNumber()
  evaluatorId?: number;

  @IsNotEmpty()
  @IsDateString()
  assessmentDate: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AssessmentAreaInputDto)
  areaInputs: AssessmentAreaInputDto[];
}

export class UpdateAssessmentScoreDto {
  @IsNotEmpty()
  @IsNumber()
  areaId: number;

  @IsNotEmpty()
  @IsNumber()
  baseScore: number;

  @IsOptional()
  @IsString()
  evaluatorComments?: string;

  @IsOptional()
  @IsString()
  employeeComments?: string;

  @IsOptional()
  @IsObject()
  additionalData?: Record<string, any>;
}

export class BulkUpdateAssessmentDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateAssessmentScoreDto)
  areaUpdates: UpdateAssessmentScoreDto[];

  @IsOptional()
  @IsString()
  notes?: string;
}

export class AssessmentScoringDataDto {
  // Common scoring fields that can be used across different templates
  
  @IsOptional()
  @IsNumber()
  nc_incidents?: number; // Non-conformance incidents

  @IsOptional()
  @IsNumber()
  training_completed?: number; // Number of trainings completed

  @IsOptional()
  @IsNumber()
  certifications?: number; // Number of certifications obtained

  @IsOptional()
  @IsNumber()
  projects_completed?: number; // Number of projects completed

  @IsOptional()
  @IsNumber()
  overtime_hours?: number; // Overtime hours worked

  @IsOptional()
  @IsNumber()
  customer_complaints?: number; // Customer complaints received

  @IsOptional()
  @IsNumber()
  customer_compliments?: number; // Customer compliments received

  @IsOptional()
  @IsNumber()
  team_collaboration_score?: number; // Team collaboration rating

  @IsOptional()
  @IsNumber()
  innovation_initiatives?: number; // Innovation initiatives led

  @IsOptional()
  @IsNumber()
  mentoring_hours?: number; // Hours spent mentoring others

  @IsOptional()
  @IsNumber()
  safety_incidents?: number; // Safety incidents

  @IsOptional()
  @IsNumber()
  quality_improvements?: number; // Quality improvement suggestions

  @IsOptional()
  @IsNumber()
  attendance_score?: number; // Attendance score (percentage)

  @IsOptional()
  @IsNumber()
  punctuality_score?: number; // Punctuality score (percentage)

  // Allow for custom fields
  @IsOptional()
  @IsObject()
  customFields?: Record<string, any>;
}
