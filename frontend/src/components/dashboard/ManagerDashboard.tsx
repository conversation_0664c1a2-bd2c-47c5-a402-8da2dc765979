import React, { useState, useEffect } from 'react';
import { 
  OverviewStats, 
  EmployeePerformance, 
  AssessmentAreaStats,
  TrendData,
  UserRole
} from '../../types';
import api from '../../services/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  ArcElement
} from 'chart.js';
import { Line, Bar, Radar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  ArcElement
);

interface ManagerDashboardProps {
  teamId: number;
  userRole: UserRole;
}

const ManagerDashboard: React.FC<ManagerDashboardProps> = ({ teamId, userRole }) => {
  const [stats, setStats] = useState<OverviewStats | null>(null);
  const [employeePerformance, setEmployeePerformance] = useState<EmployeePerformance[]>([]);
  const [areaStats, setAreaStats] = useState<AssessmentAreaStats[]>([]);
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, these would be separate API calls
        // For now, we'll mock this data
        
        // Mock stats data
        setStats({
          totalAssessments: 24,
          statusCounts: [
            { status: 'completed', count: 18 },
            { status: 'in_progress', count: 4 },
            { status: 'draft', count: 2 }
          ],
          averageScore: 78.5
        });
        
        // Mock employee performance data
        setEmployeePerformance([
          { employeeId: 1, firstName: 'John', lastName: 'Doe', averageScore: 85.3, assessmentCount: 3 },
          { employeeId: 2, firstName: 'Jane', lastName: 'Smith', averageScore: 92.1, assessmentCount: 3 },
          { employeeId: 3, firstName: 'Robert', lastName: 'Johnson', averageScore: 71.8, assessmentCount: 3 },
          { employeeId: 4, firstName: 'Emily', lastName: 'Williams', averageScore: 76.5, assessmentCount: 3 },
          { employeeId: 5, firstName: 'Michael', lastName: 'Brown', averageScore: 68.9, assessmentCount: 3 }
        ]);
        
        // Mock area stats data
        setAreaStats([
          { areaId: 1, areaName: 'Communication', averageScore: 82.3, averageWeightedScore: 20.6 },
          { areaId: 2, areaName: 'Technical Skills', averageScore: 79.5, averageWeightedScore: 23.9 },
          { areaId: 3, areaName: 'Leadership', averageScore: 74.1, averageWeightedScore: 18.5 },
          { areaId: 4, areaName: 'Teamwork', averageScore: 86.7, averageWeightedScore: 17.3 },
          { areaId: 5, areaName: 'Innovation', averageScore: 68.9, averageWeightedScore: 13.8 }
        ]);
        
        // Mock trend data (12 months)
        const mockTrendData = [];
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth();
        
        for (let i = 11; i >= 0; i--) {
          let month = currentMonth - i;
          let year = currentYear;
          if (month < 0) {
            month = 12 + month;
            year--;
          }
          
          mockTrendData.push({
            year,
            month,
            count: Math.floor(Math.random() * 10) + 1,
            averageScore: Math.floor(Math.random() * 20) + 70 // Random score between 70-90
          });
        }
        
        setTrendData(mockTrendData);
        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [teamId]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading dashboard data...</p>
      </div>
    </div>;
  }

  if (error) {
    return <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <p>{error}</p>
      <button 
        className="mt-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
        onClick={() => window.location.reload()}
      >
        Retry
      </button>
    </div>;
  }

  // Prepare chart data
  const trendLabels = trendData.map(d => {
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    return `${monthNames[d.month]} ${d.year}`;
  });
  
  const trendScoreData = {
    labels: trendLabels,
    datasets: [
      {
        label: 'Average Score',
        data: trendData.map(d => d.averageScore),
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      }
    ]
  };

  const employeeBenchmarkData = {
    labels: employeePerformance.map(e => `${e.firstName} ${e.lastName}`),
    datasets: [
      {
        label: 'Score',
        data: employeePerformance.map(e => e.averageScore),
        backgroundColor: employeePerformance.map(e => 
          e.averageScore >= 80 ? 'rgba(53, 162, 235, 0.5)' : 
          e.averageScore >= 70 ? 'rgba(255, 205, 86, 0.5)' : 
          'rgba(255, 99, 132, 0.5)'
        ),
      }
    ]
  };

  const assessmentAreaData = {
    labels: areaStats.map(a => a.areaName),
    datasets: [
      {
        label: 'Average Score',
        data: areaStats.map(a => a.averageScore),
        backgroundColor: 'rgba(53, 162, 235, 0.2)',
        borderColor: 'rgb(53, 162, 235)',
        pointBackgroundColor: 'rgb(53, 162, 235)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(53, 162, 235)'
      }
    ]
  };

  return (
    <div className="p-6">
      {/* Top stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Total Assessments</h3>
          <p className="text-3xl font-bold text-blue-600">{stats?.totalAssessments}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Average Score</h3>
          <p className="text-3xl font-bold text-blue-600">{stats?.averageScore.toFixed(1)}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Pending Assessments</h3>
          <p className="text-3xl font-bold text-orange-500">
            {stats?.statusCounts.find(s => s.status === 'draft' || s.status === 'in_progress')?.count || 0}
          </p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Performance Trend Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Performance Trend</h3>
          <div className="h-64">
            <Line 
              data={trendScoreData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    min: 0,
                    max: 100
                  }
                }
              }}
            />
          </div>
        </div>
        
        {/* Employee Benchmark Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Team Benchmarks</h3>
          <div className="h-64">
            <Bar 
              data={employeeBenchmarkData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    min: 0,
                    max: 100
                  }
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* Additional section with assessmnet areas radar chart */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Assessment Areas Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Assessment Areas</h3>
          <div className="h-80">
            <Radar 
              data={assessmentAreaData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  r: {
                    min: 0,
                    max: 100,
                    beginAtZero: true
                  }
                }
              }}
            />
          </div>
        </div>
        
        {/* Team Members Performance Table */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Team Performance</h3>
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Average Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {employeePerformance.map((employee) => (
                <tr key={employee.employeeId}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {employee.firstName} {employee.lastName}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{employee.averageScore.toFixed(1)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                      ${employee.averageScore >= 80 ? 'bg-green-100 text-green-800' : 
                        employee.averageScore >= 70 ? 'bg-yellow-100 text-yellow-800' : 
                        'bg-red-100 text-red-800'}`}>
                      {employee.averageScore >= 80 ? 'Excellent' : 
                       employee.averageScore >= 70 ? 'Good' : 
                       'Needs Improvement'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      View Details
                    </button>
                    <button className="text-blue-600 hover:text-blue-900">
                      New Assessment
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

    </div>
  );
};

export default ManagerDashboard;
