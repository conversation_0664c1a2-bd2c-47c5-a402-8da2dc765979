const mysql = require('mysql2/promise');

async function populateMonthlyDashboardData() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'debian-sys-maint',
      password: 'a500cd12558364e3f91966f73b1fe8ea009531ff669bf74c',
      database: 'ehrx'
    });

    console.log('🔧 Connected to database');

    // 1. Create KPIs with detailed help text and BI report URLs
    const kpis = [
      {
        name: 'FTE',
        displayName: 'Full-Time Equivalents',
        description: 'Number of full-time equivalent employees in the team',
        targetValue: null,
        unit: 'count',
        calculationMethod: 'auto_fte',
        helpText: 'This field is automatically calculated based on the number of active team members. No manual input required.',
        isActive: true,
        sortOrder: 1,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: null
      },
      {
        name: 'ATTRITION',
        displayName: 'Attrition Rate',
        description: 'Monthly resignations and annual attrition rate',
        targetValue: 5.0,
        unit: '%',
        calculationMethod: 'manual',
        helpText: 'Enter the number of resignations this month. The system will calculate the annual attrition rate. Mark employees as "resigned" in the team management system.',
        isActive: true,
        sortOrder: 2,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: null
      },
      {
        name: 'SLA',
        displayName: 'Service Level Agreement',
        description: 'Team SLA performance percentage',
        targetValue: 100.0,
        unit: '%',
        calculationMethod: 'manual',
        helpText: 'Enter the SLA percentage for your team. Target is 100%. If no value is available, leave blank to display "N/A". Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Csla%20evidence%20-%20aeven%20-%20newco.qvw',
        isActive: true,
        sortOrder: 3,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: 'http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Csla%20evidence%20-%20aeven%20-%20newco.qvw&lang=en-US&host=QVS%40QvCluster'
      },
      {
        name: 'UTILIZATION',
        displayName: 'Billable Utilization',
        description: 'Team billable utilization percentage',
        targetValue: 85.0,
        unit: '%',
        calculationMethod: 'manual',
        helpText: 'Enter the team\'s billable utilization percentage. Target varies by team but typically 85%. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cbillable%20utilization%20-%20newco.qvw',
        isActive: true,
        sortOrder: 4,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: 'http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cbillable%20utilization%20-%20newco.qvw&lang=en-US&host=QVS%40QvCluster'
      },
      {
        name: 'TIME_REGISTRATION',
        displayName: 'Time Registration',
        description: 'Timely time registration compliance percentage',
        targetValue: 100.0,
        unit: '%',
        calculationMethod: 'manual',
        helpText: 'Enter the percentage of timely time registrations for your team. Target is 100%. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cax%20time%20registration%20overview.qvw',
        isActive: true,
        sortOrder: 5,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: 'http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cax%20time%20registration%20overview.qvw&lang=en-US&host=QVS%40QvCluster'
      },
      {
        name: 'COMPLIANCE',
        displayName: 'Compliance',
        description: 'Number of non-compliance incidents',
        targetValue: 0.0,
        unit: 'count',
        calculationMethod: 'manual',
        helpText: 'Enter the number of non-compliance incidents for your team. Target is 0. Even 1 incident results in red status. Data received via email from compliance team.',
        isActive: true,
        sortOrder: 6,
        trafficLightGreenMin: 0.0,
        trafficLightGreenMax: 0.0,
        trafficLightYellowMin: 0.0,
        trafficLightYellowMax: 0.0,
        specialRules: JSON.stringify({ red_threshold: 1 }),
        biReportUrl: null
      },
      {
        name: 'AB',
        displayName: 'Annual Budget vs Actual',
        description: 'Financial performance: Full Year 2025 vs Annual Budget',
        targetValue: 0.0,
        unit: 'currency',
        calculationMethod: 'manual',
        helpText: 'Enter the financial variance (Full Year 2025 vs AB) for your team. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Ccost%20-%20newco.qvw',
        isActive: true,
        sortOrder: 7,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: 'http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Ccost%20-%20newco.qvw&lang=en-US&host=QVS%40QvCluster'
      },
      {
        name: 'PTO',
        displayName: 'Paid Time Off',
        description: 'Vacation and sick leave utilization vs expected',
        targetValue: 75.0,
        unit: '%',
        calculationMethod: 'manual',
        helpText: 'Enter the number of PTO days taken this month. System calculates progress toward 75% of expected annual PTO by this point in the year. Data received via email from Finance.',
        isActive: true,
        sortOrder: 8,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: null
      },
      {
        name: 'RTO',
        displayName: 'Return to Office',
        description: 'Office attendance compliance percentage',
        targetValue: 100.0,
        unit: '%',
        calculationMethod: 'manual',
        helpText: 'Enter raw office attendance percentage from Facility email. Also enter number of leave days to adjust for legitimate absences. Expected: employees 1x/week, managers 2x/week.',
        isActive: true,
        sortOrder: 9,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
        biReportUrl: null
      }
    ];

    console.log('📊 Creating KPIs...');
    for (const kpi of kpis) {
      await connection.execute(`
        INSERT INTO monthly_dashboard_kpis (
          name, display_name, description, target_value, unit, calculation_method,
          help_text, is_active, sort_order, traffic_light_green_min, traffic_light_green_max,
          traffic_light_yellow_min, traffic_light_yellow_max, special_rules, bi_report_url,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
          display_name = VALUES(display_name),
          description = VALUES(description),
          help_text = VALUES(help_text),
          bi_report_url = VALUES(bi_report_url),
          updated_at = NOW()
      `, [
        kpi.name, kpi.displayName, kpi.description, kpi.targetValue, kpi.unit,
        kpi.calculationMethod, kpi.helpText, kpi.isActive, kpi.sortOrder,
        kpi.trafficLightGreenMin, kpi.trafficLightGreenMax, kpi.trafficLightYellowMin,
        kpi.trafficLightYellowMax, kpi.specialRules, kpi.biReportUrl
      ]);
    }

    console.log('✅ KPIs created successfully');

    // 2. Get organizational units (teams) and users for sample data
    const [teams] = await connection.execute('SELECT id, name FROM organizational_units WHERE type = "team" LIMIT 5');
    const [managers] = await connection.execute('SELECT id, email FROM users WHERE role IN ("hr_admin", "manager") LIMIT 3');

    if (teams.length === 0 || managers.length === 0) {
      console.log('⚠️ No teams or managers found. Creating sample organizational structure...');

      // Create sample divisions and teams
      const divisions = [
        { name: 'Technology Division', description: 'IT and Development teams' },
        { name: 'Operations Division', description: 'Operations and Support teams' },
        { name: 'Business Division', description: 'Sales and Marketing teams' }
      ];

      for (const division of divisions) {
        const [divResult] = await connection.execute(`
          INSERT INTO organizational_units (name, description, type, created_at, updated_at)
          VALUES (?, ?, 'division', NOW(), NOW())
          ON DUPLICATE KEY UPDATE updated_at = NOW()
        `, [division.name, division.description]);

        const divisionId = divResult.insertId || (await connection.execute(
          'SELECT id FROM organizational_units WHERE name = ? AND type = "division"',
          [division.name]
        ))[0][0].id;

        // Create teams under each division
        const teamNames = division.name.includes('Technology')
          ? ['Frontend Development', 'Backend Development', 'DevOps']
          : division.name.includes('Operations')
            ? ['Customer Support', 'Quality Assurance', 'Infrastructure']
            : ['Sales Team', 'Marketing Team', 'Business Development'];

        for (const teamName of teamNames) {
          await connection.execute(`
            INSERT INTO organizational_units (name, description, type, parent_id, created_at, updated_at)
            VALUES (?, ?, 'team', ?, NOW(), NOW())
            ON DUPLICATE KEY UPDATE updated_at = NOW()
          `, [teamName, `${teamName} under ${division.name}`, divisionId]);
        }
      }

      // Refresh teams list
      const [newTeams] = await connection.execute('SELECT id, name FROM organizational_units WHERE type = "team" LIMIT 5');
      teams.push(...newTeams);
    }

    console.log(`📋 Found ${teams.length} teams and ${managers.length} managers`);

    // 3. Create team-specific targets for some KPIs
    console.log('🎯 Creating team-specific targets...');
    const teamTargets = [
      { teamId: teams[0]?.id, kpiName: 'UTILIZATION', targetValue: 90.0 },
      { teamId: teams[1]?.id, kpiName: 'UTILIZATION', targetValue: 80.0 },
      { teamId: teams[2]?.id, kpiName: 'UTILIZATION', targetValue: 85.0 },
      { teamId: teams[0]?.id, kpiName: 'PTO', targetValue: 80.0 },
      { teamId: teams[1]?.id, kpiName: 'PTO', targetValue: 70.0 }
    ];

    for (const target of teamTargets) {
      if (target.teamId) {
        const [kpiResult] = await connection.execute('SELECT id FROM monthly_dashboard_kpis WHERE name = ?', [target.kpiName]);
        if (kpiResult.length > 0) {
          await connection.execute(`
            INSERT INTO monthly_dashboard_team_targets (
              organizational_unit_id, kpi_id, target_value, effective_from, created_at, updated_at
            ) VALUES (?, ?, ?, '2024-01-01', NOW(), NOW())
            ON DUPLICATE KEY UPDATE target_value = VALUES(target_value), updated_at = NOW()
          `, [target.teamId, kpiResult[0].id, target.targetValue]);
        }
      }
    }

    console.log('✅ Team targets created successfully');

    await connection.end();
    console.log('🔧 Database connection closed');

  } catch (error) {
    console.error('❌ Error populating monthly dashboard data:', error.message);
    process.exit(1);
  }
}

populateMonthlyDashboardData();
