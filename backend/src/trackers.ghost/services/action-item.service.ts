import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ActionItem, ActionItemStatus } from '../entities/action-item.entity';
import { CreateActionItemDto } from '../dto/action-item/create-action-item.dto';
import { UpdateActionItemDto } from '../dto/action-item/update-action-item.dto';
import { UserRole } from '../../users/entities/user.entity';

@Injectable()
export class ActionItemService {
  constructor(
    @InjectRepository(ActionItem)
    private actionItemRepository: Repository<ActionItem>,
  ) {}

  async create(createActionItemDto: CreateActionItemDto, userId: number): Promise<ActionItem> {
    const actionItem = this.actionItemRepository.create({
      ...createActionItemDto,
      createdById: userId,
    });

    return this.actionItemRepository.save(actionItem);
  }

  async findAll(userId: number, userRole: string, filterOptions?: any): Promise<ActionItem[]> {
    const query = this.actionItemRepository.createQueryBuilder('actionItem')
      .leftJoinAndSelect('actionItem.assignedTo', 'assignedTo')
      .leftJoinAndSelect('actionItem.createdBy', 'createdBy');

    // Apply role-based filtering
    if (userRole !== UserRole.HR_ADMIN) {
      query.where('actionItem.createdById = :userId OR actionItem.assignedToId = :userId', { userId });
    }

    // Apply additional filters if provided
    if (filterOptions) {
      if (filterOptions.status) {
        query.andWhere('actionItem.status = :status', { status: filterOptions.status });
      }
      
      if (filterOptions.priority) {
        query.andWhere('actionItem.priority = :priority', { priority: filterOptions.priority });
      }
      
      if (filterOptions.dueDateFrom) {
        query.andWhere('actionItem.dueDate >= :dueDateFrom', { dueDateFrom: filterOptions.dueDateFrom });
      }
      
      if (filterOptions.dueDateTo) {
        query.andWhere('actionItem.dueDate <= :dueDateTo', { dueDateTo: filterOptions.dueDateTo });
      }
      
      if (filterOptions.assignedToId) {
        query.andWhere('actionItem.assignedToId = :assignedToId', { assignedToId: filterOptions.assignedToId });
      }

      if (filterOptions.assessmentId) {
        query.andWhere('actionItem.assessmentId = :assessmentId', { assessmentId: filterOptions.assessmentId });
      }
    }

    return query.orderBy('actionItem.createdAt', 'DESC').getMany();
  }

  async findOne(id: number, userId: number, userRole: string): Promise<ActionItem> {
    const actionItem = await this.actionItemRepository.findOne({
      where: { id },
      relations: ['assignedTo', 'createdBy'],
    });

    if (!actionItem) {
      throw new NotFoundException(`Action item with ID ${id} not found`);
    }

    // Check permissions based on role
    if (userRole !== UserRole.HR_ADMIN && 
        actionItem.createdById !== userId && 
        actionItem.assignedToId !== userId) {
      throw new ForbiddenException('You do not have permission to view this action item');
    }

    return actionItem;
  }

  async update(id: number, updateActionItemDto: UpdateActionItemDto, userId: number, userRole: string): Promise<ActionItem> {
    const actionItem = await this.findOne(id, userId, userRole);

    // Check if user has permission to update
    if (userRole !== UserRole.HR_ADMIN && 
        actionItem.createdById !== userId && 
        actionItem.assignedToId !== userId) {
      throw new ForbiddenException('You do not have permission to update this action item');
    }

    // Handle status transitions and completion date
    if (updateActionItemDto.status && 
        updateActionItemDto.status === ActionItemStatus.COMPLETED && 
        actionItem.status !== ActionItemStatus.COMPLETED) {
      actionItem.completedAt = new Date();
    } else if (updateActionItemDto.status && 
               updateActionItemDto.status !== ActionItemStatus.COMPLETED && 
               actionItem.status === ActionItemStatus.COMPLETED) {
      actionItem.completedAt = null;
    }

    // Update all provided fields
    Object.assign(actionItem, updateActionItemDto);

    return this.actionItemRepository.save(actionItem);
  }

  async remove(id: number, userId: number, userRole: string): Promise<void> {
    const actionItem = await this.findOne(id, userId, userRole);

    // Only creator or HR admin can delete
    if (userRole !== UserRole.HR_ADMIN && actionItem.createdById !== userId) {
      throw new ForbiddenException('You do not have permission to delete this action item');
    }

    await this.actionItemRepository.remove(actionItem);
  }

  async getActionItemsByAssessment(assessmentId: number, userId: number, userRole: string): Promise<ActionItem[]> {
    const query = this.actionItemRepository.createQueryBuilder('actionItem')
      .leftJoinAndSelect('actionItem.assignedTo', 'assignedTo')
      .leftJoinAndSelect('actionItem.createdBy', 'createdBy')
      .where('actionItem.assessmentId = :assessmentId', { assessmentId });

    // Apply role-based filtering
    if (userRole !== UserRole.HR_ADMIN) {
      query.andWhere('actionItem.createdById = :userId OR actionItem.assignedToId = :userId', { userId });
    }

    return query.orderBy('actionItem.createdAt', 'DESC').getMany();
  }
}
