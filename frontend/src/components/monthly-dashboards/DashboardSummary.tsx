import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Download, FilterList as Filter, Refresh as RefreshCw } from '@mui/icons-material';
import { monthlyDashboardsApi } from '../../services/api';
import { toast } from '../ui/toast';

interface KpiValue {
  kpiId: number;
  kpiName: string;
  value: number;
  targetValue?: number;
  variance?: number;
  trafficLight: 'green' | 'yellow' | 'red';
  unit?: string;
}

interface TeamSummary {
  teamId: number;
  teamName: string;
  managerName: string;
  lastUpdate: string;
  kpiValues: KpiValue[];
}

interface DivisionSummary {
  divisionId: number;
  divisionName: string;
  teams: TeamSummary[];
}

interface DashboardSummaryData {
  month: string;
  year: number;
  divisions: DivisionSummary[];
  kpiHeaders: Array<{
    id: number;
    name: string;
    displayName: string;
    unit?: string;
  }>;
}

const TrafficLightIndicator: React.FC<{ status: 'green' | 'yellow' | 'red' }> = ({ status }) => {
  const colors = {
    green: '#4caf50',
    yellow: '#ff9800',
    red: '#f44336'
  };

  return (
    <div
      style={{
        width: 12,
        height: 12,
        borderRadius: '50%',
        backgroundColor: colors[status],
        display: 'inline-block',
        marginRight: 4,
        boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
      }}
    />
  );
};

const KpiCell: React.FC<{ kpiValue: KpiValue }> = ({ kpiValue }) => {
  return (
    <TableCell sx={{ textAlign: 'center', py: 1.5 }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 4 }}>
        <TrafficLightIndicator status={kpiValue.trafficLight} />
        <span style={{ fontSize: '0.875rem', fontWeight: 500 }}>
          {kpiValue.value}
          {kpiValue.unit && (
            <span style={{ fontSize: '0.75rem', color: '#666', marginLeft: 4 }}>
              {kpiValue.unit}
            </span>
          )}
        </span>
      </div>
    </TableCell>
  );
};

export const DashboardSummary: React.FC = () => {
  const [data, setData] = useState<DashboardSummaryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

  const months = [
    { value: '01', label: 'January' },
    { value: '02', label: 'February' },
    { value: '03', label: 'March' },
    { value: '04', label: 'April' },
    { value: '05', label: 'May' },
    { value: '06', label: 'June' },
    { value: '07', label: 'July' },
    { value: '08', label: 'August' },
    { value: '09', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' }
  ];

  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i);

  useEffect(() => {
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
    setSelectedMonth(currentMonth);
  }, []);

  useEffect(() => {
    if (selectedMonth && selectedYear) {
      fetchDashboardSummary();
    }
  }, [selectedMonth, selectedYear]);

  const fetchDashboardSummary = async () => {
    if (!selectedMonth || !selectedYear) return;

    try {
      setLoading(true);
      const response = await monthlyDashboardsApi.getDashboardOverview({
        month: parseInt(selectedMonth),
        year: selectedYear
      });
      setData(response.data || response);
    } catch (error) {
      console.error('Error fetching dashboard summary:', error);
      toast.error('Failed to load dashboard summary');
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  const exportToExcel = async () => {
    try {
      toast.info('Exporting dashboard summary...');
      // Implementation for Excel export would go here
      toast.success('Dashboard summary exported successfully');
    } catch (error) {
      console.error('Error exporting dashboard:', error);
      toast.error('Failed to export dashboard summary');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)', color: 'white' }}>
        <CardContent sx={{ p: 3 }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <CardTitle sx={{ fontSize: '1.75rem', fontWeight: 700, color: 'white', mb: 1 }}>
                SEVEN MANAGER DASHBOARD PH
              </CardTitle>
              <div style={{ fontSize: '0.875rem', opacity: 0.9 }}>
                Performance Overview • {new Date(0, parseInt(selectedMonth) - 1).toLocaleString('default', { month: 'long' })} {selectedYear}
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
              <Select
                value={selectedMonth}
                onValueChange={setSelectedMonth}
                sx={{ minWidth: 120, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 1 }}
              >
                {months.map(month => (
                  <SelectItem key={month.value} value={month.value}>
                    {month.label}
                  </SelectItem>
                ))}
              </Select>

              <Select
                value={selectedYear.toString()}
                onValueChange={(value) => setSelectedYear(parseInt(value))}
                sx={{ minWidth: 100, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 1 }}
              >
                {years.map(year => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </Select>

              <Button
                onClick={fetchDashboardSummary}
                variant="outline"
                size="sm"
                sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', '&:hover': { borderColor: 'white' } }}
              >
                <RefreshCw sx={{ fontSize: 16, mr: 1 }} />
                Refresh
              </Button>

              <Button
                onClick={exportToExcel}
                variant="outline"
                size="sm"
                sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', '&:hover': { borderColor: 'white' } }}
              >
                <Download sx={{ fontSize: 16, mr: 1 }} />
                Export
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card sx={{ mb: 3, border: '1px solid #e0e0e0' }}>
        <CardContent sx={{ py: 2 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
            <span style={{ fontWeight: 600, color: '#333' }}>Performance Indicators:</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <TrafficLightIndicator status="green" />
                <span style={{ fontSize: '0.875rem', color: '#333' }}>Green = ±5% (On Target)</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <TrafficLightIndicator status="yellow" />
                <span style={{ fontSize: '0.875rem', color: '#333' }}>Yellow = ±5% to 10% (Caution)</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <TrafficLightIndicator status="red" />
                <span style={{ fontSize: '0.875rem', color: '#333' }}>Red = &gt;10% (Action Required)</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Table */}
      {data && (
        <Card sx={{ boxShadow: '0 4px 12px rgba(0,0,0,0.1)', border: '1px solid #e0e0e0' }}>
          <CardContent sx={{ p: 0 }}>
            <div style={{ overflowX: 'auto' }}>
              <Table>
                <TableHeader>
                  <TableRow sx={{ background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)' }}>
                    <TableHead sx={{ color: 'white', fontWeight: 700, py: 2, borderRight: '1px solid rgba(255,255,255,0.2)' }}>
                      Director
                    </TableHead>
                    <TableHead sx={{ color: 'white', fontWeight: 700, py: 2, borderRight: '1px solid rgba(255,255,255,0.2)' }}>
                      Sub Area
                    </TableHead>
                    <TableHead sx={{ color: 'white', fontWeight: 700, py: 2, borderRight: '1px solid rgba(255,255,255,0.2)' }}>
                      Manager
                    </TableHead>
                    <TableHead sx={{ color: 'white', fontWeight: 700, py: 2, borderRight: '1px solid rgba(255,255,255,0.2)' }}>
                      Last Update
                    </TableHead>
                    {data.kpiHeaders.map(kpi => (
                      <TableHead
                        key={kpi.id}
                        sx={{
                          color: 'white',
                          fontWeight: 700,
                          textAlign: 'center',
                          py: 2,
                          borderRight: '1px solid rgba(255,255,255,0.2)',
                          minWidth: 80
                        }}
                      >
                        {kpi.displayName}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.divisions.map(division => (
                    <React.Fragment key={division.divisionId}>
                      {division.teams.map((team, teamIndex) => (
                        <TableRow
                          key={team.teamId}
                          sx={{
                            borderTop: teamIndex === 0 ? '2px solid #e0e0e0' : 'none',
                            '&:hover': { backgroundColor: '#f8f9fa' },
                            '&:nth-of-type(even)': { backgroundColor: '#fafafa' }
                          }}
                        >
                          {teamIndex === 0 && (
                            <TableCell
                              rowSpan={division.teams.length}
                              sx={{
                                fontWeight: 700,
                                backgroundColor: '#f5f5f5',
                                borderRight: '2px solid #e0e0e0',
                                verticalAlign: 'top',
                                py: 2,
                                fontSize: '0.875rem',
                                color: '#1565c0'
                              }}
                            >
                              {division.divisionName}
                            </TableCell>
                          )}
                          <TableCell sx={{ fontWeight: 600, py: 1.5, fontSize: '0.875rem' }}>
                            {team.teamName}
                          </TableCell>
                          <TableCell sx={{ py: 1.5, fontSize: '0.875rem' }}>
                            {team.managerName}
                          </TableCell>
                          <TableCell sx={{ py: 1.5, fontSize: '0.75rem', color: '#666' }}>
                            {team.lastUpdate}
                          </TableCell>
                          {data.kpiHeaders.map(kpiHeader => {
                            const kpiValue = team.kpiValues.find(kv => kv.kpiId === kpiHeader.id);
                            return kpiValue ? (
                              <KpiCell key={kpiHeader.id} kpiValue={kpiValue} />
                            ) : (
                              <TableCell
                                key={kpiHeader.id}
                                sx={{
                                  textAlign: 'center',
                                  color: '#999',
                                  py: 1.5,
                                  fontSize: '0.875rem',
                                  fontStyle: 'italic'
                                }}
                              >
                                N/A
                              </TableCell>
                            );
                          })}
                        </TableRow>
                      ))}
                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {!data && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <p className="text-gray-500">No dashboard data available for the selected period.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
