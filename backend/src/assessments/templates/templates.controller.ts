import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { CreateTemplateDto } from '../dto/create-template.dto';
import { UpdateTemplateDto } from '../dto/update-template.dto';
import { TeamTemplateAssignmentDto, CustomizeTemplateForTeamDto } from '../dto/team-template-assignment.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

@Controller('templates')
@UseGuards(JwtAuthGuard)
export class TemplatesController {
  constructor(private readonly templatesService: TemplatesService) { }

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() createTemplateDto: CreateTemplateDto, @Request() req) {
    return this.templatesService.create(createTemplateDto, req.user.userId);
  }

  @Get()
  findAll(@Request() req) {
    // Only HR_ADMIN and MANAGERS can see inactive templates
    const includeInactive = [UserRole.HR_ADMIN, UserRole.MANAGER].includes(req.user.role);
    return this.templatesService.findAll(includeInactive);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.templatesService.findOne(+id);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  update(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
    @Request() req,
  ) {
    return this.templatesService.update(+id, updateTemplateDto, req.user.userId);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  remove(@Param('id') id: string) {
    return this.templatesService.remove(+id);
  }

  @Post(':id/clone')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  clone(@Param('id') id: string, @Request() req, @Body() body?: { name?: string }) {
    return this.templatesService.clone(+id, req.user.userId, body?.name);
  }

  @Post(':id/new-version')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  createNewVersion(@Param('id') id: string, @Request() req) {
    return this.templatesService.createNewVersion(+id, req.user.userId, req.user.role);
  }

  @Get(':id/versions')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getVersions(@Param('id') id: string) {
    return this.templatesService.getTemplateVersions(+id);
  }

  @Get('team/:teamId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  findByTeam(@Param('teamId') teamId: string) {
    return this.templatesService.findByTeam(+teamId);
  }

  @Post('assign-to-teams')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  assignToTeams(@Body() assignmentDto: TeamTemplateAssignmentDto, @Request() req) {
    return this.templatesService.assignToTeams(assignmentDto, req.user.userId, req.user.role);
  }

  @Post('customize-for-team')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  customizeForTeam(@Body() customizeDto: CustomizeTemplateForTeamDto, @Request() req) {
    return this.templatesService.customizeForTeam(customizeDto, req.user.userId, req.user.role);
  }

  @Post(':id/activate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  activate(@Param('id') id: string, @Request() req) {
    return this.templatesService.activate(+id, req.user.userId, req.user.role);
  }

  @Post(':id/deactivate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  deactivate(@Param('id') id: string, @Request() req) {
    return this.templatesService.deactivate(+id, req.user.userId, req.user.role);
  }

  @Get(':id/usage-stats')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getUsageStats(@Param('id') id: string) {
    return this.templatesService.getUsageStats(+id);
  }

  @Post(':id/validate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  validateTemplate(@Param('id') id: string) {
    return this.templatesService.validateTemplate(+id);
  }
}
