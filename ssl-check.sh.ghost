#!/bin/bash

# SSL Certificate Check Script for dev.trusthansen.dk
# This script checks the SSL certificate status and expiration

DOMAIN="dev.trusthansen.dk"

echo "=== SSL Certificate Status for $DOMAIN ==="
echo

# Check certificate expiration
echo "Certificate Validity:"
echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates

echo
echo "Certificate Details:"
echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -subject -issuer

echo
echo "Days until expiration:"
EXPIRY_DATE=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -enddate | cut -d= -f2)
EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
CURRENT_EPOCH=$(date +%s)
DAYS_LEFT=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))
echo "$DAYS_LEFT days"

echo
echo "HTTPS Test:"
curl -s -I https://$DOMAIN | head -1

echo
echo "Security Headers:"
curl -s -I https://$DOMAIN | grep -E "(Strict-Transport-Security|X-Frame-Options|X-Content-Type-Options)"

echo
echo "=== SSL Check Complete ==="
