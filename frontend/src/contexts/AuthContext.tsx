import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ApiService } from '../services/api';
import { User } from '../types';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated on app load
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      // 🔐 NIS2-COMPLIANT: Use centralized secure authentication validation
      if (ApiService.isAuthenticated()) {
        const userData = ApiService.getCurrentUser();
        if (userData) {
          setUser(userData);
        } else {
          // User data corrupted or missing, clear auth
          console.warn('🔐 [AUTH] User data missing, clearing authentication');
          await logout();
        }
      }
    } catch (error) {
      console.error('🔐 [AUTH] Initialization error:', error);
      // Clear invalid auth data
      await logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<void> => {
    try {
      // 🔐 NIS2-COMPLIANT: Use centralized secure authentication
      const response = await ApiService.login(email, password);

      if (response.user) {
        setUser(response.user);
        // Token and user data are now securely managed by ApiService
      }
    } catch (error) {
      console.error('🔐 [AUTH] Login failed:', error);
      throw error; // Re-throw to let the login component handle the error
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // 🔐 NIS2-COMPLIANT: Secure logout with centralized token management
      if (user) {
        await ApiService.logout();
      }
    } catch (error) {
      console.error('🔐 [AUTH] Logout error:', error);
    } finally {
      setUser(null);
      // Token clearing is handled by ApiService.logout()
      // Redirect to login page
      window.location.href = '/login';
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      // 🔐 NIS2-COMPLIANT: User data updates should go through secure API
      // For now, we'll update local state only. In production, this should
      // trigger an API call to update user data server-side
      console.warn('🔐 [AUTH] User data updated locally. Consider implementing server-side update.');
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user && ApiService.isAuthenticated(),
    isLoading,
    login,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protecting routes
export const withAuth = <P extends object>(Component: React.ComponentType<P>) => {
  return (props: P) => {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh'
        }}>
          <div>Loading...</div>
        </div>
      );
    }

    if (!isAuthenticated) {
      window.location.href = '/login';
      return null;
    }

    return <Component {...props} />;
  };
};

export default AuthContext;
