import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { UserSkillset } from './user-skillset.entity';

export enum SkillsetCategory {
  PROGRAMMING = 'programming',
  INFRASTRUCTURE = 'infrastructure',
  DATABASE = 'database',
  CLOUD = 'cloud',
  SECURITY = 'security',
  DEVOPS = 'devops',
  NETWORKING = 'networking',
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  MOBILE = 'mobile',
  DATA = 'data',
  AI_ML = 'ai_ml',
  PROJECT_MANAGEMENT = 'project_management',
  SOFT_SKILLS = 'soft_skills',
}

export enum SkillLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert',
}

@Entity('skillsets')
export class Skillset {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255, unique: true })
  name: string;

  @Column({
    type: 'enum',
    enum: SkillsetCategory,
  })
  category: SkillsetCategory;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    name: 'level_required',
    type: 'enum',
    enum: SkillLevel,
    default: SkillLevel.INTERMEDIATE,
  })
  levelRequired: SkillLevel;

  @Column({ name: 'is_core_skill', default: false })
  isCoreSkill: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToMany(() => UserSkillset, (userSkillset) => userSkillset.skillset)
  userSkillsets: UserSkillset[];
}
