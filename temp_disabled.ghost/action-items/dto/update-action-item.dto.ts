import { PartialType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsString, IsNumber, IsDateString, Min, Max } from 'class-validator';
import { CreateActionItemDto } from './create-action-item.dto';
import { ActionItemStatus } from '../entities/action-item.entity';

export class UpdateActionItemDto extends PartialType(CreateActionItemDto) {
  @IsEnum(ActionItemStatus)
  @IsOptional()
  status?: ActionItemStatus;

  @IsDateString()
  @IsOptional()
  completedDate?: string;

  @IsNumber()
  @IsOptional()
  actualHours?: number;
}

export class UpdateActionItemProgressDto {
  @IsNumber()
  @Min(0)
  @Max(100)
  progressPercentage: number;

  @IsString()
  @IsOptional()
  comments?: string;

  @IsNumber()
  @IsOptional()
  hoursLogged?: number;
}

export class BulkUpdateActionItemsDto {
  @IsNumber({}, { each: true })
  actionItemIds: number[];

  @IsEnum(ActionItemStatus)
  @IsOptional()
  status?: ActionItemStatus;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;

  @IsDateString()
  @IsOptional()
  dueDate?: string;

  @IsString()
  @IsOptional()
  comments?: string;
}
