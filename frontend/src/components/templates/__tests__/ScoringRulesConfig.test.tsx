import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import ScoringRulesConfig from '../ScoringRulesConfig';
import { RuleType, ConditionOperator } from '../../../types';

describe('ScoringRulesConfig', () => {
  const mockOnAddRule = jest.fn();
  const mockOnUpdateRule = jest.fn();
  const mockOnRemoveRule = jest.fn();

  const defaultProps = {
    rules: [],
    onAddRule: mockOnAddRule,
    onUpdateRule: mockOnUpdateRule,
    onRemoveRule: mockOnRemoveRule,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders empty state when no rules', () => {
    render(<ScoringRulesConfig {...defaultProps} />);
    
    expect(screen.getByText('Scoring Rules')).toBeInTheDocument();
    expect(screen.getByText('No scoring rules defined')).toBeInTheDocument();
    expect(screen.getByText('Scores will be used as entered without adjustments')).toBeInTheDocument();
  });

  it('renders add rule button', () => {
    render(<ScoringRulesConfig {...defaultProps} />);
    
    const addButton = screen.getByText('Add Rule');
    expect(addButton).toBeInTheDocument();
    
    fireEvent.click(addButton);
    expect(mockOnAddRule).toHaveBeenCalled();
  });

  it('renders existing rules', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Training bonus'
      },
      {
        id: 2,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        conditionOperator: ConditionOperator.GREATER_THAN,
        conditionValue: '0',
        scoreAdjustment: 5,
        description: 'NC penalty'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    expect(screen.getByText('Rule 1')).toBeInTheDocument();
    expect(screen.getByText('Rule 2')).toBeInTheDocument();
    expect(screen.getByText('Add a fixed number of points')).toBeInTheDocument();
    expect(screen.getByText('Apply adjustment based on condition')).toBeInTheDocument();
  });

  it('handles rule type changes', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const ruleTypeSelect = screen.getByDisplayValue('addition');
    fireEvent.change(ruleTypeSelect, { target: { value: 'subtraction' } });
    
    expect(mockOnUpdateRule).toHaveBeenCalledWith(0, 'ruleType', 'subtraction');
  });

  it('handles score adjustment changes', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const scoreInput = screen.getByDisplayValue('10');
    fireEvent.change(scoreInput, { target: { value: '15' } });
    
    expect(mockOnUpdateRule).toHaveBeenCalledWith(0, 'scoreAdjustment', 15);
  });

  it('shows conditional fields for conditional rules', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        conditionOperator: ConditionOperator.GREATER_THAN,
        conditionValue: '0',
        scoreAdjustment: 5,
        description: 'NC penalty'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    expect(screen.getByText('Condition Field')).toBeInTheDocument();
    expect(screen.getByText('Operator')).toBeInTheDocument();
    expect(screen.getByText('Value')).toBeInTheDocument();
    expect(screen.getByDisplayValue('nc_incidents')).toBeInTheDocument();
    expect(screen.getByDisplayValue('greater_than')).toBeInTheDocument();
    expect(screen.getByDisplayValue('0')).toBeInTheDocument();
  });

  it('hides conditional fields for non-conditional rules', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Addition rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    expect(screen.queryByText('Condition Field')).not.toBeInTheDocument();
    expect(screen.queryByText('Operator')).not.toBeInTheDocument();
    expect(screen.queryByText('Value')).not.toBeInTheDocument();
  });

  it('handles condition field changes', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        scoreAdjustment: 5,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const conditionFieldSelect = screen.getByDisplayValue('nc_incidents');
    fireEvent.change(conditionFieldSelect, { target: { value: 'training_completed' } });
    
    expect(mockOnUpdateRule).toHaveBeenCalledWith(0, 'conditionField', 'training_completed');
  });

  it('handles condition operator changes', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        conditionOperator: ConditionOperator.GREATER_THAN,
        scoreAdjustment: 5,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const operatorSelect = screen.getByDisplayValue('greater_than');
    fireEvent.change(operatorSelect, { target: { value: 'equals' } });
    
    expect(mockOnUpdateRule).toHaveBeenCalledWith(0, 'conditionOperator', 'equals');
  });

  it('handles condition value changes', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        conditionOperator: ConditionOperator.GREATER_THAN,
        conditionValue: '0',
        scoreAdjustment: 5,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const valueInput = screen.getByDisplayValue('0');
    fireEvent.change(valueInput, { target: { value: '1' } });
    
    expect(mockOnUpdateRule).toHaveBeenCalledWith(0, 'conditionValue', '1');
  });

  it('handles description changes', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Original description'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const descriptionInput = screen.getByDisplayValue('Original description');
    fireEvent.change(descriptionInput, { target: { value: 'Updated description' } });
    
    expect(mockOnUpdateRule).toHaveBeenCalledWith(0, 'description', 'Updated description');
  });

  it('handles rule removal', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const removeButton = screen.getByTitle('Remove rule');
    fireEvent.click(removeButton);
    
    expect(mockOnRemoveRule).toHaveBeenCalledWith(0);
  });

  it('shows correct score adjustment label for different rule types', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.MULTIPLICATION,
        scoreAdjustment: 10,
        description: 'Multiplication rule'
      },
      {
        id: 2,
        ruleType: RuleType.CONDITIONAL,
        scoreAdjustment: 5,
        description: 'Conditional rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    expect(screen.getByText('Percentage (%)')).toBeInTheDocument();
    expect(screen.getByText('Points per occurrence')).toBeInTheDocument();
  });

  it('shows rule preview for conditional rules', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        conditionOperator: ConditionOperator.GREATER_THAN,
        conditionValue: '0',
        scoreAdjustment: 5,
        description: 'NC penalty'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    expect(screen.getByText('Rule Preview:')).toBeInTheDocument();
    expect(screen.getByText(/When nc incidents greater than 0/)).toBeInTheDocument();
    expect(screen.getByText(/add 5 points per occurrence/)).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <ScoringRulesConfig {...defaultProps} className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('shows all available condition fields', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        scoreAdjustment: 5,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const conditionFieldSelect = screen.getByText('Select field...');
    fireEvent.click(conditionFieldSelect);
    
    expect(screen.getByText('Non-Conformance Incidents')).toBeInTheDocument();
    expect(screen.getByText('Training Completed')).toBeInTheDocument();
    expect(screen.getByText('Certifications')).toBeInTheDocument();
    expect(screen.getByText('Customer Complaints')).toBeInTheDocument();
    expect(screen.getByText('Safety Incidents')).toBeInTheDocument();
  });

  it('shows all available condition operators', () => {
    const rules = [
      {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        scoreAdjustment: 5,
        description: 'Test rule'
      }
    ];

    render(<ScoringRulesConfig {...defaultProps} rules={rules} />);
    
    const operatorSelect = screen.getByText('Select operator...');
    fireEvent.click(operatorSelect);
    
    expect(screen.getByText('Equals (=)')).toBeInTheDocument();
    expect(screen.getByText('Greater Than (>)')).toBeInTheDocument();
    expect(screen.getByText('Less Than (<)')).toBeInTheDocument();
    expect(screen.getByText('Greater or Equal (≥)')).toBeInTheDocument();
    expect(screen.getByText('Less or Equal (≤)')).toBeInTheDocument();
  });
});
