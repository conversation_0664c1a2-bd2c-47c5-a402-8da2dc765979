import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StakeholderFeedback, FeedbackStatus } from '../entities/stakeholder-feedback.entity';
import { CreateStakeholderFeedbackDto } from '../dto/stakeholder-feedback/create-stakeholder-feedback.dto';
import { UpdateStakeholderFeedbackDto } from '../dto/stakeholder-feedback/update-stakeholder-feedback.dto';
import { UserRole } from '../../users/entities/user.entity';

@Injectable()
export class StakeholderFeedbackService {
  constructor(
    @InjectRepository(StakeholderFeedback)
    private stakeholderFeedbackRepository: Repository<StakeholderFeedback>,
  ) {}

  async create(createStakeholderFeedbackDto: CreateStakeholderFeedbackDto, userId: number): Promise<StakeholderFeedback> {
    const stakeholderFeedback = this.stakeholderFeedbackRepository.create({
      ...createStakeholderFeedbackDto,
      createdById: userId,
    });

    return this.stakeholderFeedbackRepository.save(stakeholderFeedback);
  }

  async findAll(userId: number, userRole: string, filterOptions?: any): Promise<StakeholderFeedback[]> {
    const query = this.stakeholderFeedbackRepository.createQueryBuilder('stakeholderFeedback')
      .leftJoinAndSelect('stakeholderFeedback.assignedTo', 'assignedTo')
      .leftJoinAndSelect('stakeholderFeedback.createdBy', 'createdBy');

    // Apply role-based filtering
    if (userRole !== UserRole.HR_ADMIN) {
      query.where('stakeholderFeedback.createdById = :userId OR stakeholderFeedback.assignedToId = :userId', { userId });
    }

    // Apply additional filters if provided
    if (filterOptions) {
      if (filterOptions.status) {
        query.andWhere('stakeholderFeedback.status = :status', { status: filterOptions.status });
      }
      
      if (filterOptions.type) {
        query.andWhere('stakeholderFeedback.type = :type', { type: filterOptions.type });
      }
      
      if (filterOptions.source) {
        query.andWhere('stakeholderFeedback.source = :source', { source: filterOptions.source });
      }
      
      if (filterOptions.createdAtFrom) {
        query.andWhere('stakeholderFeedback.createdAt >= :createdAtFrom', { createdAtFrom: filterOptions.createdAtFrom });
      }
      
      if (filterOptions.createdAtTo) {
        query.andWhere('stakeholderFeedback.createdAt <= :createdAtTo', { createdAtTo: filterOptions.createdAtTo });
      }
      
      if (filterOptions.assignedToId) {
        query.andWhere('stakeholderFeedback.assignedToId = :assignedToId', { assignedToId: filterOptions.assignedToId });
      }

      if (filterOptions.assessmentId) {
        query.andWhere('stakeholderFeedback.assessmentId = :assessmentId', { assessmentId: filterOptions.assessmentId });
      }
    }

    return query.orderBy('stakeholderFeedback.createdAt', 'DESC').getMany();
  }

  async findOne(id: number, userId: number, userRole: string): Promise<StakeholderFeedback> {
    const stakeholderFeedback = await this.stakeholderFeedbackRepository.findOne({
      where: { id },
      relations: ['assignedTo', 'createdBy'],
    });

    if (!stakeholderFeedback) {
      throw new NotFoundException(`Stakeholder feedback with ID ${id} not found`);
    }

    // Check permissions based on role
    if (userRole !== UserRole.HR_ADMIN && 
        stakeholderFeedback.createdById !== userId && 
        stakeholderFeedback.assignedToId !== userId) {
      throw new ForbiddenException('You do not have permission to view this stakeholder feedback');
    }

    return stakeholderFeedback;
  }

  async update(id: number, updateStakeholderFeedbackDto: UpdateStakeholderFeedbackDto, userId: number, userRole: string): Promise<StakeholderFeedback> {
    const stakeholderFeedback = await this.findOne(id, userId, userRole);

    // Check if user has permission to update
    if (userRole !== UserRole.HR_ADMIN && 
        stakeholderFeedback.createdById !== userId && 
        stakeholderFeedback.assignedToId !== userId) {
      throw new ForbiddenException('You do not have permission to update this stakeholder feedback');
    }

    // Handle status transitions and resolved date
    if (updateStakeholderFeedbackDto.status && 
        updateStakeholderFeedbackDto.status === FeedbackStatus.RESOLVED && 
        stakeholderFeedback.status !== FeedbackStatus.RESOLVED) {
      
      // If transitioning to RESOLVED and no resolved date is provided, set it to current date
      if (!updateStakeholderFeedbackDto.resolvedAt) {
        stakeholderFeedback.resolvedAt = new Date();
      }
    }

    // Update all provided fields
    Object.assign(stakeholderFeedback, updateStakeholderFeedbackDto);

    return this.stakeholderFeedbackRepository.save(stakeholderFeedback);
  }

  async remove(id: number, userId: number, userRole: string): Promise<void> {
    const stakeholderFeedback = await this.findOne(id, userId, userRole);

    // Only creator or HR admin can delete
    if (userRole !== UserRole.HR_ADMIN && stakeholderFeedback.createdById !== userId) {
      throw new ForbiddenException('You do not have permission to delete this stakeholder feedback');
    }

    await this.stakeholderFeedbackRepository.remove(stakeholderFeedback);
  }

  async getStakeholderFeedbackByAssessment(assessmentId: number, userId: number, userRole: string): Promise<StakeholderFeedback[]> {
    const query = this.stakeholderFeedbackRepository.createQueryBuilder('stakeholderFeedback')
      .leftJoinAndSelect('stakeholderFeedback.assignedTo', 'assignedTo')
      .leftJoinAndSelect('stakeholderFeedback.createdBy', 'createdBy')
      .where('stakeholderFeedback.assessmentId = :assessmentId', { assessmentId });

    // Apply role-based filtering
    if (userRole !== UserRole.HR_ADMIN) {
      query.andWhere('stakeholderFeedback.createdById = :userId OR stakeholderFeedback.assignedToId = :userId', { userId });
    }

    return query.orderBy('stakeholderFeedback.createdAt', 'DESC').getMany();
  }
}
