import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MonthlyDashboardKpi, CalculationMethod } from './entities/monthly-dashboard-kpi.entity';
import { MonthlyDashboardSubmission, SubmissionStatus } from './entities/monthly-dashboard-submission.entity';
import { MonthlyDashboardKpiValue, TrafficLightStatus } from './entities/monthly-dashboard-kpi-value.entity';
import { MonthlyDashboardTeamTarget } from './entities/monthly-dashboard-team-target.entity';
import { OrganizationalUnit, OrganizationalUnitType } from '../teams/entities/organizational-unit.entity';
import { User, UserRole } from '../users/entities/user.entity';

@Injectable()
export class MonthlyDashboardSeederService {
  constructor(
    @InjectRepository(MonthlyDashboardKpi)
    private kpiRepository: Repository<MonthlyDashboardKpi>,
    @InjectRepository(MonthlyDashboardSubmission)
    private submissionRepository: Repository<MonthlyDashboardSubmission>,
    @InjectRepository(MonthlyDashboardKpiValue)
    private kpiValueRepository: Repository<MonthlyDashboardKpiValue>,
    @InjectRepository(MonthlyDashboardTeamTarget)
    private teamTargetRepository: Repository<MonthlyDashboardTeamTarget>,
    @InjectRepository(OrganizationalUnit)
    private orgUnitRepository: Repository<OrganizationalUnit>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) { }

  async seedKpis(): Promise<void> {
    console.log('📊 Seeding Monthly Dashboard KPIs...');

    const kpis = [
      {
        name: 'FTE',
        displayName: 'Full-Time Equivalents',
        description: 'Number of full-time equivalent employees in the team',
        targetValue: null,
        unit: 'count',
        calculationMethod: CalculationMethod.AUTO_FTE,
        helpText: 'This field is automatically calculated based on the number of active team members. No manual input required.',
        isActive: true,
        sortOrder: 1,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      },
      {
        name: 'ATTRITION',
        displayName: 'Attrition Rate',
        description: 'Monthly resignations and annual attrition rate',
        targetValue: 5.0,
        unit: '%',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter the number of resignations this month. The system will calculate the annual attrition rate. Mark employees as "resigned" in the team management system.',
        isActive: true,
        sortOrder: 2,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      },
      {
        name: 'SLA',
        displayName: 'Service Level Agreement',
        description: 'Team SLA performance percentage',
        targetValue: 100.0,
        unit: '%',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter the SLA percentage for your team. Target is 100%. If no value is available, leave blank to display "N/A". Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Csla%20evidence%20-%20aeven%20-%20newco.qvw',
        isActive: true,
        sortOrder: 3,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      },
      {
        name: 'UTILIZATION',
        displayName: 'Billable Utilization',
        description: 'Team billable utilization percentage',
        targetValue: 85.0,
        unit: '%',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter the team\'s billable utilization percentage. Target varies by team but typically 85%. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cbillable%20utilization%20-%20newco.qvw',
        isActive: true,
        sortOrder: 4,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      },
      {
        name: 'TIME_REGISTRATION',
        displayName: 'Time Registration',
        description: 'Timely time registration compliance percentage',
        targetValue: 100.0,
        unit: '%',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter the percentage of timely time registrations for your team. Target is 100%. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cax%20time%20registration%20overview.qvw',
        isActive: true,
        sortOrder: 5,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      },
      {
        name: 'COMPLIANCE',
        displayName: 'Compliance',
        description: 'Number of non-compliance incidents',
        targetValue: 0.0,
        unit: 'count',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter the number of non-compliance incidents for your team. Target is 0. Even 1 incident results in red status. Data received via email from compliance team.',
        isActive: true,
        sortOrder: 6,
        trafficLightGreenMin: 0.0,
        trafficLightGreenMax: 0.0,
        trafficLightYellowMin: 0.0,
        trafficLightYellowMax: 0.0,
        specialRules: { red_threshold: 1 },
      },
      {
        name: 'AB',
        displayName: 'Annual Budget vs Actual',
        description: 'Financial performance: Full Year 2025 vs Annual Budget',
        targetValue: 0.0,
        unit: 'currency',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter the financial variance (Full Year 2025 vs AB) for your team. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Ccost%20-%20newco.qvw',
        isActive: true,
        sortOrder: 7,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      },
      {
        name: 'PTO',
        displayName: 'Paid Time Off',
        description: 'Vacation and sick leave utilization vs expected',
        targetValue: 75.0,
        unit: '%',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter the number of PTO days taken this month. System calculates progress toward 75% of expected annual PTO by this point in the year. Data received via email from Finance.',
        isActive: true,
        sortOrder: 8,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      },
      {
        name: 'RTO',
        displayName: 'Return to Office',
        description: 'Office attendance compliance percentage',
        targetValue: 100.0,
        unit: '%',
        calculationMethod: CalculationMethod.MANUAL,
        helpText: 'Enter raw office attendance percentage from Facility email. Also enter number of leave days to adjust for legitimate absences. Expected: employees 1x/week, managers 2x/week.',
        isActive: true,
        sortOrder: 9,
        trafficLightGreenMin: -5.0,
        trafficLightGreenMax: 5.0,
        trafficLightYellowMin: -10.0,
        trafficLightYellowMax: 10.0,
        specialRules: null,
      }
    ];

    for (const kpiData of kpis) {
      const existingKpi = await this.kpiRepository.findOne({ where: { name: kpiData.name } });

      if (!existingKpi) {
        const kpi = this.kpiRepository.create(kpiData);
        await this.kpiRepository.save(kpi);
        console.log(`✅ Created KPI: ${kpiData.displayName}`);
      } else {
        // Update existing KPI with new help text and other fields
        await this.kpiRepository.update(existingKpi.id, {
          displayName: kpiData.displayName,
          description: kpiData.description,
          helpText: kpiData.helpText,
          specialRules: kpiData.specialRules,
        });
        console.log(`🔄 Updated KPI: ${kpiData.displayName}`);
      }
    }

    console.log('✅ KPI seeding completed');
  }

  async seedSampleData(): Promise<void> {
    console.log('📋 Seeding sample organizational structure and submissions...');

    // Create sample divisions and teams if they don't exist
    await this.createSampleOrganizationalStructure();

    // Create sample submissions for the last 12 months
    await this.createSampleSubmissions();

    console.log('✅ Sample data seeding completed');
  }

  private async createSampleOrganizationalStructure(): Promise<void> {
    const divisions = [
      { name: 'Technology Division', description: 'IT and Development teams' },
      { name: 'Operations Division', description: 'Operations and Support teams' },
      { name: 'Business Division', description: 'Sales and Marketing teams' }
    ];

    for (const divisionData of divisions) {
      let division = await this.orgUnitRepository.findOne({
        where: { name: divisionData.name, type: OrganizationalUnitType.DIVISION }
      });

      if (!division) {
        division = this.orgUnitRepository.create({
          ...divisionData,
          type: OrganizationalUnitType.DIVISION
        });
        division = await this.orgUnitRepository.save(division);
        console.log(`✅ Created division: ${divisionData.name}`);
      }

      // Create teams under each division
      const teamNames = divisionData.name.includes('Technology')
        ? ['Frontend Development', 'Backend Development', 'DevOps']
        : divisionData.name.includes('Operations')
          ? ['Customer Support', 'Quality Assurance', 'Infrastructure']
          : ['Sales Team', 'Marketing Team', 'Business Development'];

      for (const teamName of teamNames) {
        const existingTeam = await this.orgUnitRepository.findOne({
          where: { name: teamName, type: OrganizationalUnitType.TEAM }
        });

        if (!existingTeam) {
          const team = this.orgUnitRepository.create({
            name: teamName,
            description: `${teamName} under ${divisionData.name}`,
            type: OrganizationalUnitType.TEAM,
            parentId: division.id
          });
          await this.orgUnitRepository.save(team);
          console.log(`✅ Created team: ${teamName}`);
        }
      }
    }
  }

  private async createSampleSubmissions(): Promise<void> {
    console.log('📊 Creating sample submissions for last 12 months...');

    // Get teams, managers, and KPIs
    const teams = await this.orgUnitRepository.find({
      where: { type: OrganizationalUnitType.TEAM },
      take: 5
    });

    const managers = await this.userRepository.find({
      where: { role: UserRole.MANAGER },
      take: 3
    });

    const kpis = await this.kpiRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC' }
    });

    if (teams.length === 0 || managers.length === 0 || kpis.length === 0) {
      console.log('⚠️ Missing required data for sample submissions');
      return;
    }

    // Generate data for last 12 months
    const currentDate = new Date();
    const months = [];

    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      months.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
      });
    }

    // Create submissions for each team and month
    for (const team of teams) {
      for (const monthData of months) {
        const managerId = managers[Math.floor(Math.random() * managers.length)].id;

        // Check if submission already exists
        const existingSubmission = await this.submissionRepository.findOne({
          where: {
            organizationalUnitId: team.id,
            submissionMonth: monthData.month,
            submissionYear: monthData.year
          }
        });

        if (existingSubmission) {
          console.log(`⏭️ Submission already exists for ${team.name} - ${monthData.month}/${monthData.year}`);
          continue;
        }

        // Create submission
        const submission = this.submissionRepository.create({
          organizationalUnitId: team.id,
          submittedByUserId: managerId,
          submissionMonth: monthData.month,
          submissionYear: monthData.year,
          submissionDate: new Date(monthData.year, monthData.month - 1, Math.floor(Math.random() * 5) + 12),
          completionDate: new Date(monthData.year, monthData.month - 1, Math.floor(Math.random() * 3) + 14),
          status: SubmissionStatus.SUBMITTED,
          notes: `Sample submission for ${team.name} - ${monthData.month}/${monthData.year}`
        });

        const savedSubmission = await this.submissionRepository.save(submission);

        // Create KPI values for this submission
        for (const kpi of kpis) {
          let value: number, targetValue: number, trafficLightStatus: TrafficLightStatus;

          // Generate realistic sample data based on KPI type
          switch (kpi.name) {
            case 'FTE':
              value = Math.floor(Math.random() * 15) + 5; // 5-20 people
              targetValue = value; // Auto-calculated, so target = actual
              trafficLightStatus = TrafficLightStatus.GREEN;
              break;

            case 'ATTRITION':
              value = Math.random() * 12; // 0-12% annual rate
              targetValue = kpi.targetValue || 5.0;
              break;

            case 'SLA':
              value = 85 + Math.random() * 15; // 85-100%
              targetValue = kpi.targetValue || 100.0;
              break;

            case 'UTILIZATION':
              value = 70 + Math.random() * 25; // 70-95%
              targetValue = kpi.targetValue || 85.0;
              break;

            case 'TIME_REGISTRATION':
              value = 80 + Math.random() * 20; // 80-100%
              targetValue = kpi.targetValue || 100.0;
              break;

            case 'COMPLIANCE':
              value = Math.random() < 0.8 ? 0 : Math.floor(Math.random() * 3); // Usually 0, sometimes 1-2
              targetValue = kpi.targetValue || 0.0;
              break;

            case 'AB':
              value = (Math.random() - 0.5) * 200000; // -100k to +100k variance
              targetValue = kpi.targetValue || 0.0;
              break;

            case 'PTO':
              value = 60 + Math.random() * 30; // 60-90% of expected
              targetValue = kpi.targetValue || 75.0;
              break;

            case 'RTO':
              value = 70 + Math.random() * 30; // 70-100%
              targetValue = kpi.targetValue || 100.0;
              break;

            default:
              value = Math.random() * 100;
              targetValue = kpi.targetValue || 50.0;
          }

          // Calculate traffic light status if not set
          if (!trafficLightStatus) {
            if (kpi.name === 'COMPLIANCE') {
              trafficLightStatus = value >= 1 ? TrafficLightStatus.RED : TrafficLightStatus.GREEN;
            } else {
              const deviation = targetValue === 0 ? 0 : Math.abs((value - targetValue) / targetValue) * 100;
              if (deviation <= 5) trafficLightStatus = TrafficLightStatus.GREEN;
              else if (deviation <= 10) trafficLightStatus = TrafficLightStatus.YELLOW;
              else trafficLightStatus = TrafficLightStatus.RED;
            }
          }

          // Additional data for complex KPIs
          let additionalData = null;
          if (kpi.name === 'ATTRITION') {
            additionalData = {
              resignations_this_month: Math.floor(Math.random() * 3),
              annual_rate: value
            };
          } else if (kpi.name === 'PTO') {
            additionalData = {
              days_taken_this_month: Math.floor(Math.random() * 10) + 1,
              ytd_percentage: value
            };
          } else if (kpi.name === 'RTO') {
            additionalData = {
              raw_attendance_percentage: value - 5,
              leave_days_adjustment: Math.floor(Math.random() * 5),
              adjusted_percentage: value
            };
          }

          const kpiValue = this.kpiValueRepository.create({
            submissionId: savedSubmission.id,
            kpiId: kpi.id,
            value: value,
            targetValue: targetValue,
            trafficLightStatus: trafficLightStatus,
            additionalData: additionalData
          });

          await this.kpiValueRepository.save(kpiValue);
        }

        console.log(`✅ Created submission for ${team.name} - ${monthData.month}/${monthData.year}`);
      }
    }

    console.log('🎉 Sample submissions created successfully!');
  }
}
