import { IsString, IsOptional, <PERSON><PERSON><PERSON>, IsISO8601, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ProcessImprovementStatus, ImprovementImpact, ImprovementEffort } from '../../entities/process-improvement.entity';

export class UpdateProcessImprovementDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  currentProcess?: string;

  @IsString()
  @IsOptional()
  proposedProcess?: string;

  @IsEnum(ProcessImprovementStatus)
  @IsOptional()
  status?: ProcessImprovementStatus;

  @IsEnum(ImprovementImpact)
  @IsOptional()
  impact?: ImprovementImpact;

  @IsEnum(ImprovementEffort)
  @IsOptional()
  effort?: ImprovementEffort;

  @IsISO8601()
  @IsOptional()
  targetCompletionDate?: string;

  @IsISO8601()
  @IsOptional()
  actualCompletionDate?: string;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;
}
