import { Controller, Get, Post, Param, Body } from '@nestjs/common';
import { EmployeeAssessmentService } from '../services/employee-assessment.service';

@Controller('assessments/employees')
export class EmployeeAssessmentController {
  constructor(
    private readonly employeeAssessmentService: EmployeeAssessmentService,
  ) {}

  /**
   * Get assessment data for a specific employee
   */
  @Get(':employeeId')
  async getEmployeeAssessmentData(@Param('employeeId') employeeId: number) {
    try {
      const data = await this.employeeAssessmentService.getEmployeeAssessmentData(employeeId);
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get assessment data for all employees in a team
   */
  @Get('team/:teamId')
  async getTeamAssessmentData(@Param('teamId') teamId: number) {
    try {
      const data = await this.employeeAssessmentService.getTeamAssessmentData(teamId);
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get assessment data for all employees
   */
  @Get()
  async getAllEmployeesAssessmentData() {
    try {
      const data = await this.employeeAssessmentService.getAllEmployeesAssessmentData();
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create assessment from template
   */
  @Post(':employeeId/assessments')
  async createAssessmentFromTemplate(
    @Param('employeeId') employeeId: number,
    @Body() createData: { templateId: number; evaluatorId: number }
  ) {
    try {
      const assessment = await this.employeeAssessmentService.createAssessmentFromTemplate(
        employeeId,
        createData.templateId,
        createData.evaluatorId
      );
      return {
        success: true,
        data: assessment,
        message: 'Assessment created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
