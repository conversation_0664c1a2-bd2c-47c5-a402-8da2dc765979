import React from 'react';
import {
  Box,
  Toolbar,
} from '@mui/material';

interface MainContentProps {
  children?: React.ReactNode;
  renderContent: () => React.ReactNode;
}

const MainContent: React.FC<MainContentProps> = ({ children, renderContent }) => {
  return (
    <Box
      component="main"
      sx={{
        flexGrow: 1,
        bgcolor: 'background.default',
        overflow: 'auto',
        p: 3,
      }}
    >
      <Toolbar />
      {renderContent()}
      {children}
    </Box>
  );
};

export default MainContent;
