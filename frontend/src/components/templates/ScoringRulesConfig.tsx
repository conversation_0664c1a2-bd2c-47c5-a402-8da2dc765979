import React from 'react';
import { ScoringRule, RuleType, ConditionOperator } from '../../types';

interface ScoringRulesConfigProps {
  rules: ScoringRule[];
  onAddRule: () => void;
  onUpdateRule: (index: number, field: keyof ScoringRule, value: any) => void;
  onRemoveRule: (index: number) => void;
  className?: string;
}

const ScoringRulesConfig: React.FC<ScoringRulesConfigProps> = ({
  rules,
  onAddRule,
  onUpdateRule,
  onRemoveRule,
  className = ''
}) => {
  const commonConditionFields = [
    { value: 'nc_incidents', label: 'Non-Conformance Incidents' },
    { value: 'training_completed', label: 'Training Completed' },
    { value: 'certifications', label: 'Certifications' },
    { value: 'projects_completed', label: 'Projects Completed' },
    { value: 'customer_complaints', label: 'Customer Complaints' },
    { value: 'customer_compliments', label: 'Customer Compliments' },
    { value: 'safety_incidents', label: 'Safety Incidents' },
    { value: 'quality_improvements', label: 'Quality Improvements' },
    { value: 'attendance_score', label: 'Attendance Score (%)' },
    { value: 'punctuality_score', label: 'Punctuality Score (%)' },
    { value: 'team_collaboration_score', label: 'Team Collaboration Score' },
    { value: 'innovation_initiatives', label: 'Innovation Initiatives' },
    { value: 'mentoring_hours', label: 'Mentoring Hours' },
    { value: 'overtime_hours', label: 'Overtime Hours' }
  ];

  const getRuleTypeDescription = (ruleType: RuleType) => {
    switch (ruleType) {
      case RuleType.ADDITION:
        return 'Add a fixed number of points';
      case RuleType.SUBTRACTION:
        return 'Subtract a fixed number of points';
      case RuleType.MULTIPLICATION:
        return 'Multiply score by percentage';
      case RuleType.DIVISION:
        return 'Divide score by percentage';
      case RuleType.CONDITIONAL:
        return 'Apply adjustment based on condition';
      default:
        return '';
    }
  };

  const getScoreAdjustmentLabel = (ruleType: RuleType) => {
    switch (ruleType) {
      case RuleType.MULTIPLICATION:
      case RuleType.DIVISION:
        return 'Percentage (%)';
      case RuleType.CONDITIONAL:
        return 'Points per occurrence';
      default:
        return 'Points';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Scoring Rules</h4>
          <p className="text-xs text-gray-500 mt-1">
            Define how scores are calculated and adjusted based on additional criteria
          </p>
        </div>
        <button
          type="button"
          onClick={onAddRule}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg className="-ml-0.5 mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Rule
        </button>
      </div>

      {rules.length === 0 && (
        <div className="text-center py-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="mt-2 text-sm text-gray-500">No scoring rules defined</p>
          <p className="text-xs text-gray-400">Scores will be used as entered without adjustments</p>
        </div>
      )}

      {rules.map((rule, index) => (
        <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h5 className="text-sm font-medium text-gray-900">Rule {index + 1}</h5>
              <p className="text-xs text-gray-500 mt-1">
                {getRuleTypeDescription(rule.ruleType)}
              </p>
            </div>
            <button
              type="button"
              onClick={() => onRemoveRule(index)}
              className="text-red-600 hover:text-red-800 p-1"
              title="Remove rule"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Rule Type */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Rule Type *
              </label>
              <select
                value={rule.ruleType}
                onChange={(e) => onUpdateRule(index, 'ruleType', e.target.value as RuleType)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
              >
                <option value={RuleType.ADDITION}>Addition</option>
                <option value={RuleType.SUBTRACTION}>Subtraction</option>
                <option value={RuleType.MULTIPLICATION}>Multiplication (%)</option>
                <option value={RuleType.DIVISION}>Division (%)</option>
                <option value={RuleType.CONDITIONAL}>Conditional</option>
              </select>
            </div>

            {/* Score Adjustment */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                {getScoreAdjustmentLabel(rule.ruleType)} *
              </label>
              <input
                type="number"
                step="0.1"
                value={rule.scoreAdjustment}
                onChange={(e) => onUpdateRule(index, 'scoreAdjustment', parseFloat(e.target.value) || 0)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="0"
              />
            </div>
          </div>

          {/* Conditional Rule Fields */}
          {rule.ruleType === RuleType.CONDITIONAL && (
            <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4 p-3 bg-blue-50 rounded-md">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Condition Field
                </label>
                <select
                  value={rule.conditionField || ''}
                  onChange={(e) => onUpdateRule(index, 'conditionField', e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">Select field...</option>
                  {commonConditionFields.map(field => (
                    <option key={field.value} value={field.value}>
                      {field.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Operator
                </label>
                <select
                  value={rule.conditionOperator || ''}
                  onChange={(e) => onUpdateRule(index, 'conditionOperator', e.target.value as ConditionOperator)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">Select operator...</option>
                  <option value={ConditionOperator.EQUALS}>Equals (=)</option>
                  <option value={ConditionOperator.NOT_EQUALS}>Not Equals (≠)</option>
                  <option value={ConditionOperator.GREATER_THAN}>Greater Than (&gt;)</option>
                  <option value={ConditionOperator.LESS_THAN}>Less Than (&lt;)</option>
                  <option value={ConditionOperator.GREATER_THAN_OR_EQUAL}>Greater or Equal (≥)</option>
                  <option value={ConditionOperator.LESS_THAN_OR_EQUAL}>Less or Equal (≤)</option>
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Value
                </label>
                <input
                  type="text"
                  value={rule.conditionValue || ''}
                  onChange={(e) => onUpdateRule(index, 'conditionValue', e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                  placeholder="Condition value"
                />
              </div>
            </div>
          )}

          {/* Description */}
          <div className="mt-4">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Description
            </label>
            <input
              type="text"
              value={rule.description || ''}
              onChange={(e) => onUpdateRule(index, 'description', e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
              placeholder="Describe when and how this rule applies..."
            />
          </div>

          {/* Rule Preview */}
          {rule.ruleType === RuleType.CONDITIONAL && rule.conditionField && rule.conditionOperator && rule.conditionValue && (
            <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
              <strong>Rule Preview:</strong> When {rule.conditionField.replace(/_/g, ' ')} {rule.conditionOperator.replace(/_/g, ' ')} {rule.conditionValue},
              {rule.scoreAdjustment >= 0 ? ' add ' : ' subtract '}{Math.abs(rule.scoreAdjustment)} points per occurrence.
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ScoringRulesConfig;
