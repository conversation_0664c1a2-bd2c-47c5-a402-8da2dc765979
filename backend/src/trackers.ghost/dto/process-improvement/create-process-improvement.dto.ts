import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, IsISO8601, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ProcessImprovementStatus, ImprovementImpact, ImprovementEffort } from '../../entities/process-improvement.entity';

export class CreateProcessImprovementDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsString()
  currentProcess: string;

  @IsString()
  proposedProcess: string;

  @IsEnum(ProcessImprovementStatus)
  @IsOptional()
  status?: ProcessImprovementStatus;

  @IsEnum(ImprovementImpact)
  @IsOptional()
  impact?: ImprovementImpact;

  @IsEnum(ImprovementEffort)
  @IsOptional()
  effort?: ImprovementEffort;

  @IsISO8601()
  @IsOptional()
  targetCompletionDate?: string;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;

  @IsNumber()
  @IsOptional()
  assessmentId?: number;
}
