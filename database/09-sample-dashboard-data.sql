-- Sample Monthly Dashboard Data for July 2025
-- This creates realistic sample data for all teams based on the Excel dashboard structure

USE ehrx;

-- First, let's ensure we have the organizational structure
-- Insert sample divisions first
INSERT IGNORE INTO organizational_units (name, type, parent_id, description, is_active) VALUES
('CHOI', 'division', NULL, 'CHOI Division', 1),
('THNQ', 'division', NULL, 'THNQ Division', 1),
('ALDW', 'division', NULL, 'ALDW Division', 1);

-- Get division IDs
SET @choi_id = (SELECT id FROM organizational_units WHERE name = 'CHOI' AND type = 'division' LIMIT 1);
SET @thnq_id = (SELECT id FROM organizational_units WHERE name = 'THNQ' AND type = 'division' LIMIT 1);
SET @aldw_id = (SELECT id FROM organizational_units WHERE name = 'ALDW' AND type = 'division' LIMIT 1);

-- Insert teams under divisions
INSERT IGNORE INTO organizational_units (name, type, parent_id, description, is_active) VALUES
-- CHOI Division Teams
('2nd Line Global', 'team', @choi_id, '2nd Line Global Support Team', 1),
('Patch Planning with Server Order', 'team', @choi_id, 'Patch Planning and Server Order Team', 1),
('Change Management - SLM', 'team', @choi_id, 'Change Management and SLM Team', 1),
('Automation & AI', 'team', @choi_id, 'Automation and AI Team', 1),

-- THNQ Division Teams
('IAM Operations', 'team', @thnq_id, 'Identity and Access Management Operations', 1),
('Security Operations', 'team', @thnq_id, 'Security Operations Team', 1),
('Network Operations & NetScaler', 'team', @thnq_id, 'Network Operations and NetScaler Team', 1),

-- ALDW Division Teams
('AIS Operations', 'team', @aldw_id, 'AIS Operations Team', 1),
('Windows Services', 'team', @aldw_id, 'Windows Services Team', 1),
('Unix/Linux Services', 'team', @aldw_id, 'Unix/Linux Services Team', 1);

-- Create sample users for team managers if they don't exist
INSERT IGNORE INTO users (email, first_name, last_name, role, is_active, password) VALUES
('<EMAIL>', 'John', 'Smith', 'manager', 1, '$2b$10$dummy.hash.for.john.smith'),
('<EMAIL>', 'Sarah', 'Johnson', 'manager', 1, '$2b$10$dummy.hash.for.sarah.johnson'),
('<EMAIL>', 'Mike', 'Wilson', 'manager', 1, '$2b$10$dummy.hash.for.mike.wilson'),
('<EMAIL>', 'Lisa', 'Chen', 'manager', 1, '$2b$10$dummy.hash.for.lisa.chen'),
('<EMAIL>', 'David', 'Brown', 'manager', 1, '$2b$10$dummy.hash.for.david.brown'),
('<EMAIL>', 'Emma', 'Davis', 'manager', 1, '$2b$10$dummy.hash.for.emma.davis'),
('<EMAIL>', 'Tom', 'Anderson', 'manager', 1, '$2b$10$dummy.hash.for.tom.anderson'),
('<EMAIL>', 'Alex', 'Martinez', 'manager', 1, '$2b$10$dummy.hash.for.alex.martinez'),
('<EMAIL>', 'Rachel', 'Green', 'manager', 1, '$2b$10$dummy.hash.for.rachel.green'),
('<EMAIL>', 'Chris', 'Taylor', 'manager', 1, '$2b$10$dummy.hash.for.chris.taylor');

-- Create monthly dashboard submissions for July 2025 for all teams
INSERT INTO monthly_dashboard_submissions (
  organizational_unit_id, 
  submitted_by_user_id, 
  submission_month, 
  submission_year, 
  status, 
  completion_date,
  notes
) VALUES
-- CHOI Division Teams
((SELECT id FROM organizational_units WHERE name = '2nd Line Global'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'submitted', '2025-07-14', 'July 2025 dashboard - 2nd Line Global'),
((SELECT id FROM organizational_units WHERE name = 'Patch Planning with Server Order'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'submitted', '2025-07-13', 'July 2025 dashboard - Patch Planning'),
((SELECT id FROM organizational_units WHERE name = 'Change Management - SLM'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'approved', '2025-07-12', 'July 2025 dashboard - Change Management'),
((SELECT id FROM organizational_units WHERE name = 'Automation & AI'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'submitted', '2025-07-15', 'July 2025 dashboard - Automation & AI'),

-- THNQ Division Teams  
((SELECT id FROM organizational_units WHERE name = 'IAM Operations'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'submitted', '2025-07-14', 'July 2025 dashboard - IAM Operations'),
((SELECT id FROM organizational_units WHERE name = 'Security Operations'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'approved', '2025-07-11', 'July 2025 dashboard - Security Operations'),
((SELECT id FROM organizational_units WHERE name = 'Network Operations & NetScaler'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'submitted', '2025-07-16', 'July 2025 dashboard - Network Operations'),

-- ALDW Division Teams
((SELECT id FROM organizational_units WHERE name = 'AIS Operations'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'submitted', '2025-07-13', 'July 2025 dashboard - AIS Operations'),
((SELECT id FROM organizational_units WHERE name = 'Windows Services'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'approved', '2025-07-10', 'July 2025 dashboard - Windows Services'),
((SELECT id FROM organizational_units WHERE name = 'Unix/Linux Services'), (SELECT id FROM users WHERE email = '<EMAIL>'), 7, 2025, 'submitted', '2025-07-15', 'July 2025 dashboard - Unix/Linux Services');

-- Now insert realistic KPI values for each team
-- We'll use the submission IDs that were just created

-- Helper variables for submission IDs (we'll get these dynamically)
SET @submission_2nd_line = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = '2nd Line Global') AND submission_month = 7 AND submission_year = 2025);
SET @submission_patch = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'Patch Planning with Server Order') AND submission_month = 7 AND submission_year = 2025);
SET @submission_change = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'Change Management - SLM') AND submission_month = 7 AND submission_year = 2025);
SET @submission_automation = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'Automation & AI') AND submission_month = 7 AND submission_year = 2025);
SET @submission_iam = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'IAM Operations') AND submission_month = 7 AND submission_year = 2025);
SET @submission_security = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'Security Operations') AND submission_month = 7 AND submission_year = 2025);
SET @submission_network = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'Network Operations & NetScaler') AND submission_month = 7 AND submission_year = 2025);
SET @submission_ais = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'AIS Operations') AND submission_month = 7 AND submission_year = 2025);
SET @submission_windows = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'Windows Services') AND submission_month = 7 AND submission_year = 2025);
SET @submission_unix = (SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = (SELECT id FROM organizational_units WHERE name = 'Unix/Linux Services') AND submission_month = 7 AND submission_year = 2025);

-- Get KPI IDs
SET @kpi_fte = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'FTE');
SET @kpi_attrition = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'ATTRITION');
SET @kpi_sla = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'SLA');
SET @kpi_utilization = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION');
SET @kpi_time_reg = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'TIME_REGISTRATION');
SET @kpi_compliance = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'COMPLIANCE');
SET @kpi_ab_financial = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'AB_FINANCIAL_RESULTS');
SET @kpi_pto = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO');
SET @kpi_rto = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO');

-- Insert KPI values for all teams with realistic data
INSERT INTO monthly_dashboard_kpi_values (submission_id, kpi_id, value, target_value, traffic_light_status, notes) VALUES

-- 2nd Line Global Team
(@submission_2nd_line, @kpi_fte, 15, 15, 'green', 'Team at target size'),
(@submission_2nd_line, @kpi_attrition, 1, 0, 'yellow', '1 resignation this month'),
(@submission_2nd_line, @kpi_sla, 97.5, 95.0, 'green', 'Exceeded SLA target'),
(@submission_2nd_line, @kpi_utilization, 82.0, 80.0, 'green', 'Good utilization rate'),
(@submission_2nd_line, @kpi_time_reg, 94.0, 95.0, 'yellow', 'Slightly below target'),
(@submission_2nd_line, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_2nd_line, @kpi_ab_financial, -1500.00, 0, 'green', 'Under budget by 1.5K'),
(@submission_2nd_line, @kpi_pto, 12, 25, 'green', '12 PTO days taken'),
(@submission_2nd_line, @kpi_rto, 85.0, 80.0, 'green', 'Good office attendance'),

-- Patch Planning Team
(@submission_patch, @kpi_fte, 8, 8, 'green', 'Team at target size'),
(@submission_patch, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@submission_patch, @kpi_sla, 99.2, 95.0, 'green', 'Excellent SLA performance'),
(@submission_patch, @kpi_utilization, 88.0, 80.0, 'green', 'High utilization'),
(@submission_patch, @kpi_time_reg, 98.0, 95.0, 'green', 'Excellent time registration'),
(@submission_patch, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_patch, @kpi_ab_financial, 500.00, 0, 'yellow', 'Slightly over budget'),
(@submission_patch, @kpi_pto, 8, 25, 'green', '8 PTO days taken'),
(@submission_patch, @kpi_rto, 92.0, 80.0, 'green', 'Excellent office attendance'),

-- Change Management Team  
(@submission_change, @kpi_fte, 12, 12, 'green', 'Team at target size'),
(@submission_change, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@submission_change, @kpi_sla, 96.8, 95.0, 'green', 'Good SLA performance'),
(@submission_change, @kpi_utilization, 79.0, 80.0, 'yellow', 'Slightly below target'),
(@submission_change, @kpi_time_reg, 96.0, 95.0, 'green', 'Good time registration'),
(@submission_change, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_change, @kpi_ab_financial, -800.00, 0, 'green', 'Under budget'),
(@submission_change, @kpi_pto, 15, 25, 'green', '15 PTO days taken'),
(@submission_change, @kpi_rto, 78.0, 80.0, 'yellow', 'Below target attendance');

-- Automation & AI Team
(@submission_automation, @kpi_fte, 10, 10, 'green', 'Team at target size'),
(@submission_automation, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@submission_automation, @kpi_sla, 98.5, 95.0, 'green', 'Excellent SLA performance'),
(@submission_automation, @kpi_utilization, 91.0, 80.0, 'green', 'Very high utilization'),
(@submission_automation, @kpi_time_reg, 97.0, 95.0, 'green', 'Excellent time registration'),
(@submission_automation, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_automation, @kpi_ab_financial, -2200.00, 0, 'green', 'Well under budget'),
(@submission_automation, @kpi_pto, 6, 25, 'green', '6 PTO days taken'),
(@submission_automation, @kpi_rto, 88.0, 80.0, 'green', 'Good office attendance'),

-- IAM Operations Team
(@submission_iam, @kpi_fte, 14, 14, 'green', 'Team at target size'),
(@submission_iam, @kpi_attrition, 1, 0, 'yellow', '1 resignation this month'),
(@submission_iam, @kpi_sla, 94.2, 95.0, 'yellow', 'Slightly below SLA target'),
(@submission_iam, @kpi_utilization, 76.0, 80.0, 'yellow', 'Below utilization target'),
(@submission_iam, @kpi_time_reg, 92.0, 95.0, 'yellow', 'Below time registration target'),
(@submission_iam, @kpi_compliance, 1, 0, 'red', '1 compliance issue identified'),
(@submission_iam, @kpi_ab_financial, 1200.00, 0, 'red', 'Over budget'),
(@submission_iam, @kpi_pto, 18, 25, 'green', '18 PTO days taken'),
(@submission_iam, @kpi_rto, 72.0, 80.0, 'yellow', 'Below attendance target'),

-- Security Operations Team
(@submission_security, @kpi_fte, 16, 16, 'green', 'Team at target size'),
(@submission_security, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@submission_security, @kpi_sla, 99.1, 95.0, 'green', 'Excellent SLA performance'),
(@submission_security, @kpi_utilization, 84.0, 80.0, 'green', 'Good utilization'),
(@submission_security, @kpi_time_reg, 99.0, 95.0, 'green', 'Excellent time registration'),
(@submission_security, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_security, @kpi_ab_financial, -3100.00, 0, 'green', 'Well under budget'),
(@submission_security, @kpi_pto, 10, 25, 'green', '10 PTO days taken'),
(@submission_security, @kpi_rto, 95.0, 80.0, 'green', 'Excellent office attendance'),

-- Network Operations Team
(@submission_network, @kpi_fte, 11, 11, 'green', 'Team at target size'),
(@submission_network, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@submission_network, @kpi_sla, 96.5, 95.0, 'green', 'Good SLA performance'),
(@submission_network, @kpi_utilization, 81.0, 80.0, 'green', 'Good utilization'),
(@submission_network, @kpi_time_reg, 93.0, 95.0, 'yellow', 'Slightly below target'),
(@submission_network, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_network, @kpi_ab_financial, 300.00, 0, 'yellow', 'Slightly over budget'),
(@submission_network, @kpi_pto, 14, 25, 'green', '14 PTO days taken'),
(@submission_network, @kpi_rto, 83.0, 80.0, 'green', 'Good office attendance'),

-- AIS Operations Team
(@submission_ais, @kpi_fte, 13, 13, 'green', 'Team at target size'),
(@submission_ais, @kpi_attrition, 2, 0, 'red', '2 resignations this month'),
(@submission_ais, @kpi_sla, 92.8, 95.0, 'yellow', 'Below SLA target'),
(@submission_ais, @kpi_utilization, 74.0, 80.0, 'yellow', 'Below utilization target'),
(@submission_ais, @kpi_time_reg, 89.0, 95.0, 'red', 'Well below time registration target'),
(@submission_ais, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_ais, @kpi_ab_financial, 2500.00, 0, 'red', 'Significantly over budget'),
(@submission_ais, @kpi_pto, 22, 25, 'green', '22 PTO days taken'),
(@submission_ais, @kpi_rto, 68.0, 80.0, 'red', 'Well below attendance target'),

-- Windows Services Team
(@submission_windows, @kpi_fte, 18, 18, 'green', 'Team at target size'),
(@submission_windows, @kpi_attrition, 0, 0, 'green', 'No attrition'),
(@submission_windows, @kpi_sla, 98.2, 95.0, 'green', 'Excellent SLA performance'),
(@submission_windows, @kpi_utilization, 86.0, 80.0, 'green', 'High utilization'),
(@submission_windows, @kpi_time_reg, 96.0, 95.0, 'green', 'Good time registration'),
(@submission_windows, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_windows, @kpi_ab_financial, -1800.00, 0, 'green', 'Under budget'),
(@submission_windows, @kpi_pto, 16, 25, 'green', '16 PTO days taken'),
(@submission_windows, @kpi_rto, 89.0, 80.0, 'green', 'Good office attendance'),

-- Unix/Linux Services Team
(@submission_unix, @kpi_fte, 14, 14, 'green', 'Team at target size'),
(@submission_unix, @kpi_attrition, 1, 0, 'yellow', '1 resignation this month'),
(@submission_unix, @kpi_sla, 97.8, 95.0, 'green', 'Good SLA performance'),
(@submission_unix, @kpi_utilization, 83.0, 80.0, 'green', 'Good utilization'),
(@submission_unix, @kpi_time_reg, 94.0, 95.0, 'yellow', 'Slightly below target'),
(@submission_unix, @kpi_compliance, 0, 0, 'green', 'No compliance issues'),
(@submission_unix, @kpi_ab_financial, -950.00, 0, 'green', 'Under budget'),
(@submission_unix, @kpi_pto, 11, 25, 'green', '11 PTO days taken'),
(@submission_unix, @kpi_rto, 81.0, 80.0, 'green', 'Good office attendance');

-- Display confirmation
SELECT 'Sample monthly dashboard data for July 2025 created successfully' AS status;

-- Show summary of created data
SELECT
    ou.name AS team_name,
    s.status,
    COUNT(kv.id) AS kpi_count,
    SUM(CASE WHEN kv.traffic_light_status = 'green' THEN 1 ELSE 0 END) AS green_kpis,
    SUM(CASE WHEN kv.traffic_light_status = 'yellow' THEN 1 ELSE 0 END) AS yellow_kpis,
    SUM(CASE WHEN kv.traffic_light_status = 'red' THEN 1 ELSE 0 END) AS red_kpis
FROM monthly_dashboard_submissions s
JOIN organizational_units ou ON s.organizational_unit_id = ou.id
LEFT JOIN monthly_dashboard_kpi_values kv ON s.id = kv.submission_id
WHERE s.submission_month = 7 AND s.submission_year = 2025
GROUP BY ou.name, s.status
ORDER BY ou.name;
