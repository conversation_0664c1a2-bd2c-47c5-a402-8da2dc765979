import {
  Controller,
  Get,
  UseGuards,
  Request,
} from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('dashboard')
@UseGuards(JwtAuthGuard)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('overview')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getOverviewStats(@Request() req) {
    return this.dashboardService.getOverviewStats(req.user.userId, req.user.role);
  }

  @Get('employee-performance')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getEmployeePerformanceStats(@Request() req) {
    return this.dashboardService.getEmployeePerformanceStats(req.user.userId, req.user.role);
  }

  @Get('area-stats')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getAssessmentAreaStats() {
    return this.dashboardService.getAssessmentAreaStats();
  }

  @Get('trends')
  getTrendsByMonth(@Request() req) {
    return this.dashboardService.getAssessmentTrendsByMonth(req.user.userId, req.user.role);
  }

  @Get('user-dashboard')
  getUserDashboardData(@Request() req) {
    return this.dashboardService.getUserDashboardData(req.user.userId, req.user.role);
  }
}
