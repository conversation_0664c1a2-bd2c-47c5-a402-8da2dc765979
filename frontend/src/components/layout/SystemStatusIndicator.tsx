import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Chip,
  Tooltip,
  IconButton,
  Popover,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
} from '@mui/material';
import {
  Circle as CircleIcon,
  Wifi as WifiIcon,
  Storage as StorageIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

interface SystemStatus {
  overall: 'healthy' | 'warning' | 'error';
  database: 'connected' | 'disconnected' | 'slow';
  api: 'online' | 'offline' | 'degraded';
  security: 'secure' | 'warning' | 'alert';
  lastUpdate: Date;
}

const SystemStatusIndicator: React.FC = () => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    overall: 'healthy',
    database: 'connected',
    api: 'online',
    security: 'secure',
    lastUpdate: new Date(),
  });

  const open = Boolean(anchorEl);

  useEffect(() => {
    // Simulate system status checks
    const checkSystemStatus = () => {
      // In a real application, this would make actual API calls to check system health
      setSystemStatus(prev => ({
        ...prev,
        lastUpdate: new Date(),
      }));
    };

    const interval = setInterval(checkSystemStatus, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'online':
      case 'secure':
        return theme.palette.success.main;
      case 'warning':
      case 'slow':
      case 'degraded':
        return theme.palette.warning.main;
      case 'error':
      case 'disconnected':
      case 'offline':
      case 'alert':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'online':
      case 'secure':
        return <CheckCircleIcon fontSize="small" />;
      case 'warning':
      case 'slow':
      case 'degraded':
        return <WarningIcon fontSize="small" />;
      case 'error':
      case 'disconnected':
      case 'offline':
      case 'alert':
        return <ErrorIcon fontSize="small" />;
      default:
        return <InfoIcon fontSize="small" />;
    }
  };

  const getStatusText = (component: string, status: string) => {
    const statusMap: Record<string, Record<string, string>> = {
      database: {
        connected: 'Database Connected',
        disconnected: 'Database Offline',
        slow: 'Database Slow Response',
      },
      api: {
        online: 'API Services Online',
        offline: 'API Services Offline',
        degraded: 'API Performance Degraded',
      },
      security: {
        secure: 'Security Systems Active',
        warning: 'Security Warning',
        alert: 'Security Alert',
      },
    };

    return statusMap[component]?.[status] || `${component}: ${status}`;
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Tooltip title="System Status">
        <IconButton
          onClick={handleClick}
          size="small"
          sx={{
            p: 0.5,
            border: `1px solid ${getStatusColor(systemStatus.overall)}`,
            borderRadius: 1,
          }}
        >
          <CircleIcon
            sx={{
              fontSize: 12,
              color: getStatusColor(systemStatus.overall),
            }}
          />
        </IconButton>
      </Tooltip>

      <Chip
        label="EHRX Online"
        size="small"
        sx={{
          fontSize: '0.7rem',
          height: 20,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          color: 'inherit',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          display: { xs: 'none', md: 'flex' },
        }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 280,
            maxWidth: 320,
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            System Status
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Last updated: {systemStatus.lastUpdate.toLocaleTimeString()}
          </Typography>
        </Box>

        <Divider />

        <List dense>
          <ListItem>
            <ListItemIcon sx={{ color: getStatusColor(systemStatus.database) }}>
              <StorageIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary={getStatusText('database', systemStatus.database)}
              secondary="Database connectivity and performance"
            />
            {getStatusIcon(systemStatus.database)}
          </ListItem>

          <ListItem>
            <ListItemIcon sx={{ color: getStatusColor(systemStatus.api) }}>
              <WifiIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary={getStatusText('api', systemStatus.api)}
              secondary="API services and endpoints"
            />
            {getStatusIcon(systemStatus.api)}
          </ListItem>

          <ListItem>
            <ListItemIcon sx={{ color: getStatusColor(systemStatus.security) }}>
              <SecurityIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary={getStatusText('security', systemStatus.security)}
              secondary="Authentication and security systems"
            />
            {getStatusIcon(systemStatus.security)}
          </ListItem>

          <ListItem>
            <ListItemIcon sx={{ color: theme.palette.info.main }}>
              <SpeedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary="Performance Monitoring"
              secondary="System response times within normal range"
            />
            <CheckCircleIcon fontSize="small" color="success" />
          </ListItem>
        </List>

        <Divider />

        <Box sx={{ p: 2 }}>
          <Typography variant="caption" color="text.secondary">
            EHRX Employee Performance Management System
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Version 1.0.0 • NIS2 Compliant
          </Typography>
        </Box>
      </Popover>
    </Box>
  );
};

export default SystemStatusIndicator;
