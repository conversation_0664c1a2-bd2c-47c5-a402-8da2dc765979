import { IsString, IsEnum, IsOptional, IsNumber, IsArray, IsBoolean, IsDateString, Min, Max } from 'class-validator';
import { ActionItemPriority, ActionItemCategory } from '../entities/action-item.entity';

export class CreateActionItemDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsEnum(ActionItemPriority)
  @IsOptional()
  priority?: ActionItemPriority;

  @IsEnum(ActionItemCategory)
  @IsOptional()
  category?: ActionItemCategory;

  @IsDateString()
  @IsOptional()
  dueDate?: string;

  @IsNumber()
  assignedToId: number;

  @IsNumber()
  @IsOptional()
  assessmentId?: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  progressPercentage?: number;

  @IsString()
  @IsOptional()
  notes?: string;

  @IsNumber()
  @IsOptional()
  estimatedHours?: number;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @IsBoolean()
  @IsOptional()
  isRecurring?: boolean;

  @IsString()
  @IsOptional()
  recurrencePattern?: string;

  @IsNumber()
  @IsOptional()
  parentActionItemId?: number;
}
