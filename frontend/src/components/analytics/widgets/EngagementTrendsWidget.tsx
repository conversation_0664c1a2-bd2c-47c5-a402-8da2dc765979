import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Avatar,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
// import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { ApiService } from '../../../services/api';

interface EngagementData {
  currentScore: number;
  previousScore: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  chartData: Array<{
    period: string;
    score: number;
    date: string;
  }>;
}

interface EngagementTrendsWidgetProps {
  filters?: {
    department?: string;
    timeRange?: string;
    team?: string;
  };
  compact?: boolean;
}

const EngagementTrendsWidget: React.FC<EngagementTrendsWidgetProps> = ({ filters, compact = false }) => {
  const [data, setData] = useState<EngagementData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState('3months');

  useEffect(() => {
    loadEngagementData();
  }, [period]);

  const loadEngagementData = async () => {
    try {
      setLoading(true);
      const [trendsData, summaryData] = await Promise.all([
        ApiService.getEngagementTrends(period),
        ApiService.getEngagementSummary(),
      ]);

      // Process trends data for chart
      const chartData = trendsData.slice(0, 12).reverse().map((metric: any, index: number) => ({
        period: `Period ${index + 1}`,
        score: metric.metricValue || 0,
        date: metric.calculationDate,
      }));

      // Calculate trend
      const currentScore = summaryData.userScore || 0;
      const previousScore = chartData.length > 1 ? chartData[chartData.length - 2].score : currentScore;
      const trendPercentage = previousScore > 0 ? ((currentScore - previousScore) / previousScore) * 100 : 0;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (Math.abs(trendPercentage) > 2) {
        trend = trendPercentage > 0 ? 'up' : 'down';
      }

      const processedData: EngagementData = {
        currentScore,
        previousScore,
        trend,
        trendPercentage: Math.abs(trendPercentage),
        chartData,
      };

      setData(processedData);
    } catch (err) {
      setError('Failed to load engagement data');
      console.error('Error loading engagement data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'success';
      case 'down':
        return 'error';
      default:
        return 'primary';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon color="success" fontSize="small" />;
      case 'down':
        return <TrendingDownIcon color="error" fontSize="small" />;
      default:
        return null;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8.0) return '#4caf50'; // Green
    if (score >= 6.0) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  if (loading) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Typography variant="body2" color="textSecondary">
            No engagement data available
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: 400 }}>
      <CardContent>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
              <FavoriteIcon />
            </Avatar>
            <Typography variant="h6">Engagement Trends</Typography>
          </Box>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Period</InputLabel>
            <Select
              value={period}
              label="Period"
              onChange={(e) => setPeriod(e.target.value)}
            >
              <MenuItem value="1month">1 Month</MenuItem>
              <MenuItem value="3months">3 Months</MenuItem>
              <MenuItem value="6months">6 Months</MenuItem>
              <MenuItem value="1year">1 Year</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Current Score and Trend */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Box>
            <Typography variant="h3" sx={{ color: getScoreColor(data.currentScore) }}>
              {data.currentScore.toFixed(1)}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Current Engagement Score
            </Typography>
          </Box>

          <Box display="flex" alignItems="center">
            {getTrendIcon(data.trend)}
            <Chip
              label={`${data.trend === 'up' ? '+' : data.trend === 'down' ? '-' : ''}${data.trendPercentage.toFixed(1)}%`}
              color={getTrendColor(data.trend) as any}
              size="small"
              sx={{ ml: 1 }}
            />
          </Box>
        </Box>

        {/* Chart Placeholder */}
        <Box
          height={200}
          display="flex"
          alignItems="center"
          justifyContent="center"
          sx={{
            bgcolor: 'grey.50',
            borderRadius: 1,
            border: '1px dashed',
            borderColor: 'grey.300'
          }}
        >
          <Typography variant="body2" color="textSecondary">
            📈 Engagement Trends Chart
            <br />
            <Typography variant="caption" component="span">
              {data.chartData.length} data points available
            </Typography>
          </Typography>
        </Box>

        {/* Summary */}
        <Box mt={2}>
          <Typography variant="caption" color="textSecondary">
            {data.trend === 'up' && 'Engagement is trending upward - great progress!'}
            {data.trend === 'down' && 'Engagement needs attention - consider action items.'}
            {data.trend === 'stable' && 'Engagement is stable - maintain current efforts.'}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default EngagementTrendsWidget;
