# Remote Access Guide for EHRX Application

This guide provides instructions for accessing the EHRX application from your local browser when the application is running on a remote server.

## Port Configuration

The EHRX application uses the following ports:

| Service | Port | Description |
|---------|------|-------------|
| Frontend | 3080 | React application UI |
| Backend | 4000 | NestJS API server |
| Status Page | 3030 | Application status monitor |

## Accessing the Application

### Step 1: Start the Application on the Server

SSH into your server and run:

```bash
cd /var/www/ehrx
./remote-launch-updated.sh
```

This will start the application in a tmux session, keeping it running even if your SSH connection drops.

### Step 2: Access from Your Local Browser

Once the application is running on the server, you can access it from your local browser by entering the server's IP address followed by the port number:

```
http://<server-ip-address>:3080
```

Replace `<server-ip-address>` with your server's actual IP address.

The status page is also available at:

```
http://<server-ip-address>:3030
```

### Checking Server IP Address

If you don't know your server's IP address, you can find it by running this command on the server:

```bash
hostname -I | awk '{print $1}'
```

## Troubleshooting Remote Access

### Firewall Configuration

Ensure that the required ports are open in your server's firewall:

```bash
# Check if UFW is enabled
sudo ufw status

# If enabled, allow the necessary ports
sudo ufw allow 3080/tcp
sudo ufw allow 4000/tcp
sudo ufw allow 3030/tcp
```

### Checking if Services are Running

You can verify that the services are running and binding to the correct ports:

```bash
# Check for listening ports
netstat -tulpn | grep -E ':(3080|4000|3030)'
```

### Viewing Application Logs

To view application logs:

```bash
# If using tmux
tmux attach -t ehrx

# If using nohup
tail -f /var/www/ehrx/app.log
tail -f /var/www/ehrx/frontend-logs.txt
tail -f /var/www/ehrx/backend-logs.txt
```

## Stopping the Application

To stop the application:

```bash
# If using tmux
tmux kill-session -t ehrx

# Kill all Node.js processes
pkill -f node
```

## Security Considerations

When exposing your application to remote access, consider the following:

1. Use HTTPS for secure communication
2. Implement proper authentication
3. Restrict access to authorized IPs if possible
4. Regularly update and patch your server

For production environments, consider using a reverse proxy like Nginx with SSL/TLS certificates from Let's Encrypt.
