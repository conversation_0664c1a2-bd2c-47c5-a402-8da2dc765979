# 🚀 EHRX Service Management Scripts

This directory contains comprehensive scripts for managing all EHRX application services.

## 📋 Available Scripts

### 🟢 **Start All Services**
```bash
./start-all-services.sh
```
- Starts both frontend (React) and backend (NestJS) services
- Checks for port conflicts and offers to kill existing processes
- Waits for services to be ready before completing
- Installs dependencies if needed
- Shows service URLs and process IDs

### 🔴 **Stop All Services**
```bash
./stop-all-services.sh
```
- Gracefully stops all EHRX services
- Kills processes on ports 3080 (frontend) and 4000 (backend)
- Force kills if graceful shutdown fails
- Verifies all services are stopped

### 🔄 **Restart All Services**
```bash
./restart-all-services.sh
```
- Stops all services, waits, then starts them again
- Useful for applying configuration changes
- Combines stop and start scripts

### 📊 **Check Service Status**
```bash
./check-services-status.sh
```
- Comprehensive health check of all services
- Shows port status, process information, and HTTP responses
- Tests API endpoints and public URL accessibility
- Provides quick action suggestions

## 🎯 Quick Reference

| Action | Command |
|--------|---------|
| **Start everything** | `./start-all-services.sh` |
| **Stop everything** | `./stop-all-services.sh` |
| **Restart everything** | `./restart-all-services.sh` |
| **Check status** | `./check-services-status.sh` |

## 🔧 Service Configuration

- **Frontend (React)**: Port 3080, Development server
- **Backend (NestJS)**: Port 4000, Development mode with watch
- **Public URL**: https://dev.trusthansen.dk/
- **API Documentation**: http://localhost:4000/api/docs

## 🚨 Troubleshooting

### Port Already in Use
The start script will detect port conflicts and offer to kill existing processes.

### Services Won't Start
1. Check if dependencies are installed: `npm install` in both `/frontend` and `/backend`
2. Verify you're in the correct directory: `/var/www/ehrx`
3. Check logs in the terminal output

### Authentication Issues
After restarting services, you may need to:
1. Clear browser cache and localStorage
2. Log in again to get fresh authentication tokens

## 🔍 Monitoring

The scripts provide colored output:
- 🟢 **Green**: Success/Running
- 🔴 **Red**: Error/Stopped
- 🟡 **Yellow**: Warning/In Progress
- 🔵 **Blue**: Information
- 🟣 **Purple**: Section Headers

## 📝 Notes

- All scripts include comprehensive error handling
- Process IDs are displayed for manual management if needed
- Scripts are designed to be run from the project root (`/var/www/ehrx`)
- Logs include timestamps and detailed status information

## 🛠️ Manual Commands

If you need to manage services manually:

```bash
# Kill specific ports
lsof -ti:3080 | xargs kill -9  # Frontend
lsof -ti:4000 | xargs kill -9  # Backend

# Start services manually
cd /var/www/ehrx/backend && npm run start:dev &
cd /var/www/ehrx/frontend && PORT=3080 npm start &

# Check what's running
lsof -i :3080  # Frontend
lsof -i :4000  # Backend
```
