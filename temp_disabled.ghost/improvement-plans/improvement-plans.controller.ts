import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ImprovementPlansService, ImprovementPlanFilters } from './improvement-plans.service';
import { CreateImprovementPlanDto } from './dto/create-improvement-plan.dto';
import { 
  UpdateImprovementPlanDto, 
  UpdateImprovementPlanGoalDto, 
  CreateImprovementPlanReviewDto 
} from './dto/update-improvement-plan.dto';
import { ImprovementPlanStatus, ImprovementPlanType } from './entities/improvement-plan.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('improvement-plans')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ImprovementPlansController {
  constructor(private readonly improvementPlansService: ImprovementPlansService) {}

  @Post()
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() createImprovementPlanDto: CreateImprovementPlanDto, @Request() req) {
    return this.improvementPlansService.create(createImprovementPlanDto, req.user.userId);
  }

  @Get()
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  findAll(
    @Query('status') status?: ImprovementPlanStatus,
    @Query('type') type?: ImprovementPlanType,
    @Query('employeeId') employeeId?: number,
    @Query('managerId') managerId?: number,
    @Query('assessmentId') assessmentId?: number,
    @Query('startDateFrom') startDateFrom?: string,
    @Query('startDateTo') startDateTo?: string,
    @Query('isOverdue') isOverdue?: boolean,
    @Query('search') search?: string,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Request() req
  ) {
    const filters: ImprovementPlanFilters = {
      status,
      type,
      employeeId: employeeId ? +employeeId : undefined,
      managerId: managerId ? +managerId : undefined,
      assessmentId: assessmentId ? +assessmentId : undefined,
      startDateFrom: startDateFrom ? new Date(startDateFrom) : undefined,
      startDateTo: startDateTo ? new Date(startDateTo) : undefined,
      isOverdue,
      search,
    };

    return this.improvementPlansService.findAll(
      filters,
      +page,
      +limit,
      req.user.role,
      req.user.userId
    );
  }

  @Get('my-plans')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getMyPlans(
    @Query('status') status?: ImprovementPlanStatus,
    @Request() req
  ) {
    return this.improvementPlansService.getMyPlans(req.user.userId, status);
  }

  @Get('statistics')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getStatistics(@Request() req) {
    return this.improvementPlansService.getStatistics(req.user.userId, req.user.role);
  }

  @Get('assessment/:assessmentId')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getPlansByAssessment(@Param('assessmentId') assessmentId: string) {
    return this.improvementPlansService.getPlansByAssessment(+assessmentId);
  }

  @Get(':id')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  findOne(@Param('id') id: string) {
    return this.improvementPlansService.findOne(+id);
  }

  @Patch(':id')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  update(
    @Param('id') id: string,
    @Body() updateImprovementPlanDto: UpdateImprovementPlanDto,
    @Request() req
  ) {
    return this.improvementPlansService.update(
      +id,
      updateImprovementPlanDto,
      req.user.userId,
      req.user.role
    );
  }

  @Delete(':id')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  remove(@Param('id') id: string, @Request() req) {
    return this.improvementPlansService.remove(+id, req.user.userId, req.user.role);
  }

  // Goal Management
  @Post(':id/goals')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  addGoal(
    @Param('id') id: string,
    @Body() goalData: any,
    @Request() req
  ) {
    return this.improvementPlansService.addGoal(+id, goalData, req.user.userId, req.user.role);
  }

  @Patch('goals/:goalId')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  updateGoal(
    @Param('goalId') goalId: string,
    @Body() updateGoalDto: UpdateImprovementPlanGoalDto,
    @Request() req
  ) {
    return this.improvementPlansService.updateGoal(
      +goalId,
      updateGoalDto,
      req.user.userId,
      req.user.role
    );
  }

  @Delete('goals/:goalId')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  removeGoal(@Param('goalId') goalId: string, @Request() req) {
    return this.improvementPlansService.removeGoal(+goalId, req.user.userId, req.user.role);
  }

  // Review Management
  @Post(':id/reviews')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  addReview(
    @Param('id') id: string,
    @Body() reviewData: CreateImprovementPlanReviewDto,
    @Request() req
  ) {
    return this.improvementPlansService.addReview(
      +id,
      reviewData,
      req.user.userId,
      req.user.role
    );
  }
}
