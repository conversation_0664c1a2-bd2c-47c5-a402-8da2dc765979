import React from 'react';
import { Switch as MuiSwitch, SwitchProps } from '@mui/material';

interface CustomSwitchProps extends Omit<SwitchProps, 'onChange'> {
  onCheckedChange?: (checked: boolean) => void;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export const Switch: React.FC<CustomSwitchProps> = ({ onCheckedChange, onChange, ...props }) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onCheckedChange) {
      onCheckedChange(event.target.checked);
    }
    if (onChange) {
      onChange(event);
    }
  };

  return <MuiSwitch {...props} onChange={handleChange} />;
};
