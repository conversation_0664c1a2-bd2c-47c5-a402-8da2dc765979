# Enterprise Logging System Documentation

## Overview

The EHRX application features a comprehensive enterprise-grade logging system that captures, stores, and manages logs from multiple sources including browser errors, API requests, database queries, and system events.

## Architecture

### Components

1. **Backend Logging Service** (`backend/src/modules/logging/`)
   - Centralized log collection and storage
   - File-based logging with rotation and compression
   - Automatic cleanup and retention policies
   - RESTful API for log querying and management

2. **Frontend Logging Service** (`frontend/src/services/logging.service.ts`)
   - Browser error capture (JavaScript errors, unhandled promises)
   - API request/response logging
   - Performance monitoring
   - Offline queue with retry mechanism

3. **Error Boundary** (`frontend/src/components/common/ErrorBoundary.tsx`)
   - React error boundary for component error capture
   - User-friendly error display
   - Automatic error reporting

4. **HTTP Middleware** (`backend/src/middleware/logging.middleware.ts`)
   - Request/response logging
   - Performance metrics
   - User activity tracking

5. **Log Management Dashboard** (`frontend/src/components/admin/LogManagementDashboard.tsx`)
   - Real-time log viewing and filtering
   - Log statistics and analytics
   - Manual cleanup controls

## Features

### 🔍 **Comprehensive Error Capture**
- JavaScript errors and exceptions
- Unhandled promise rejections
- React component errors
- API request/response errors
- Database query errors
- System-level errors

### 📊 **Performance Monitoring**
- API response times
- Page load performance
- First paint and contentful paint metrics
- Database query performance

### 🗂️ **Intelligent Log Management**
- Automatic file rotation (10MB per file)
- Compression of old logs
- Retention policies (30 days default)
- Storage limit enforcement (100MB total)
- Daily cleanup scheduling

### 🔒 **Security & Privacy**
- User data sanitization
- Configurable log levels
- IP address logging
- Session tracking
- User activity correlation

### 📈 **Analytics & Reporting**
- Error frequency analysis
- Top error identification
- Source breakdown (frontend/backend/database)
- User activity patterns
- Performance trends

## Configuration

### Environment Variables

```bash
# Log file settings
LOG_MAX_FILE_SIZE=10485760    # 10MB
LOG_MAX_FILES=30              # Keep 30 files
LOG_MAX_AGE=30                # 30 days retention
LOG_MAX_TOTAL_SIZE=104857600  # 100MB total

# Compression
LOG_COMPRESSION_ENABLED=true

# Log levels (comma-separated)
LOG_LEVELS=error,warn,info,debug,trace

# Cleanup schedule (cron format)
LOG_CLEANUP_CRON="0 2 * * *"  # Daily at 2 AM
```

### Log Levels

- **ERROR**: Critical errors requiring immediate attention
- **WARN**: Warning conditions that should be monitored
- **INFO**: General information about system operation
- **DEBUG**: Detailed information for debugging (dev only)
- **TRACE**: Very detailed tracing information (dev only)

### Log Sources

- **FRONTEND**: Browser-side errors and events
- **BACKEND**: Server-side application logs
- **DATABASE**: Database query logs and errors
- **EXTERNAL_API**: Third-party API interaction logs
- **SYSTEM**: System-level events and errors

## Usage

### Frontend Logging

```typescript
import loggingService from './services/logging.service';

// Manual error logging
loggingService.logError('Something went wrong', { details: 'error details' }, 'component-name');

// Warning logging
loggingService.logWarning('Potential issue detected', { data: 'warning data' });

// Info logging
loggingService.logInfo('Operation completed', { result: 'success' });

// User action logging
loggingService.logUserAction('button_click', { buttonId: 'submit-form' }, 'form-component');

// Set user ID for correlation
loggingService.setUserId('user-123');
```

### Backend Logging

```typescript
import { LoggingService } from './modules/logging/logging.service';

// Inject the service
constructor(private readonly loggingService: LoggingService) {}

// Log custom entries
await this.loggingService.logEntry({
  level: LogLevel.ERROR,
  source: LogSource.BACKEND,
  message: 'Database connection failed',
  details: { error: 'Connection timeout' },
  component: 'database',
  action: 'connection_attempt',
});

// Log database queries
await this.loggingService.logDatabaseQuery(
  'SELECT * FROM users WHERE id = ?',
  150, // duration in ms
  true, // success
);
```

### Error Boundary Usage

```tsx
import ErrorBoundary from './components/common/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary>
      <YourAppComponents />
    </ErrorBoundary>
  );
}
```

## API Endpoints

### Log Collection
- `POST /api/logs/browser-error` - Submit browser errors
- `POST /api/logs/custom` - Submit custom log entries

### Log Querying
- `GET /api/logs/query` - Query logs with filters
- `GET /api/logs/stats` - Get log statistics
- `GET /api/logs/health` - Get logging system health

### Log Management
- `POST /api/logs/cleanup` - Trigger manual cleanup
- `DELETE /api/logs/old` - Delete old logs
- `GET /api/logs/directory-stats` - Get storage statistics

## File Structure

```
logs/
├── frontend-error-2024-01-15.log
├── frontend-info-2024-01-15.log
├── backend-error-2024-01-15.log
├── backend-info-2024-01-15.log
├── database-error-2024-01-15.log
└── archived/
    ├── frontend-error-2024-01-14.log.gz
    └── backend-error-2024-01-14.log.gz
```

## Monitoring & Alerts

### Key Metrics to Monitor
- Error rate trends
- Log file sizes
- Storage usage
- Cleanup job success
- API response times

### Recommended Alerts
- Error rate > 5% of total requests
- Log storage > 80% of limit
- Cleanup job failures
- Critical errors in production

## Best Practices

### Development
- Use appropriate log levels
- Include relevant context in log messages
- Avoid logging sensitive information
- Test error scenarios thoroughly

### Production
- Monitor log storage usage
- Set up automated alerts
- Regular log analysis
- Backup critical error logs

### Security
- Sanitize user input in logs
- Limit log retention period
- Secure log file access
- Regular security audits

## Troubleshooting

### Common Issues

1. **High Storage Usage**
   - Check cleanup job execution
   - Verify retention policies
   - Consider increasing cleanup frequency

2. **Missing Logs**
   - Verify log level configuration
   - Check file permissions
   - Ensure logging service is running

3. **Performance Impact**
   - Adjust batch sizes
   - Increase flush intervals
   - Consider async logging

### Log Analysis

Use the Log Management Dashboard to:
- Filter logs by level, source, or time range
- Search for specific error messages
- Analyze error patterns and trends
- Monitor system performance metrics

## Maintenance

### Daily Tasks
- Automatic cleanup runs at 2 AM
- Log rotation when files exceed 10MB
- Compression of logs older than 1 day

### Weekly Tasks
- Review error trends
- Check storage usage
- Verify cleanup job execution

### Monthly Tasks
- Analyze performance trends
- Review retention policies
- Update log level configurations

## Integration with External Systems

The logging system can be extended to integrate with:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Splunk
- DataDog
- New Relic
- Custom monitoring solutions

## Support

For issues or questions about the logging system:
1. Check the logs in `/logs` directory
2. Review the Log Management Dashboard
3. Check system health at `/api/logs/health`
4. Contact the development team
