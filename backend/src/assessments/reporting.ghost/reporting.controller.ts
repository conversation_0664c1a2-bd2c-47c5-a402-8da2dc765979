import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  Res,
  StreamableFile,
} from '@nestjs/common';
import { Response } from 'express';
import { ReportingService } from './reporting.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

@Controller('reports')
@UseGuards(JwtAuthGuard)
export class ReportingController {
  constructor(private readonly reportingService: ReportingService) {}

  @Get('employee/:employeeId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getEmployeeReport(
    @Param('employeeId') employeeId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    return this.reportingService.generateEmployeeReport(
      +employeeId,
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );
  }

  @Get('team/:teamId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getTeamReport(
    @Param('teamId') teamId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    return this.reportingService.generateTeamReport(
      +teamId,
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );
  }

  @Get('organization')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async getOrganizationReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    return this.reportingService.generateOrganizationReport(
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );
  }

  @Get('assessment/:id/export/pdf')
  async exportAssessmentPdf(
    @Param('id') id: string,
    @Res({ passthrough: true }) res: Response,
    @Request() req,
  ): Promise<StreamableFile> {
    const { file, filename } = await this.reportingService.generateAssessmentPdf(
      +id,
      req.user.userId,
      req.user.role,
    );

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    return new StreamableFile(file);
  }

  @Get('assessment/:id/export/json')
  async exportAssessmentJson(
    @Param('id') id: string,
    @Res({ passthrough: true }) res: Response,
    @Request() req,
  ) {
    const { data, filename } = await this.reportingService.exportAssessmentJson(
      +id,
      req.user.userId,
      req.user.role,
    );

    res.set({
      'Content-Type': 'application/json',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    return data;
  }

  @Post('export/csv')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async exportAssessmentsCsv(
    @Body() exportParams: { 
      startDate?: string;
      endDate?: string;
      employeeIds?: number[];
      teamIds?: number[];
      statuses?: string[];
    },
    @Res({ passthrough: true }) res: Response,
    @Request() req,
  ): Promise<StreamableFile> {
    const { file, filename } = await this.reportingService.exportAssessmentsCsv(
      exportParams,
      req.user.userId,
      req.user.role,
    );

    res.set({
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    return new StreamableFile(file);
  }

  @Get('dashboard')
  async getDashboardMetrics(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('teamId') teamId: string,
    @Request() req,
  ) {
    return this.reportingService.getDashboardMetrics(
      { startDate, endDate, teamId: teamId ? +teamId : undefined },
      req.user.userId,
      req.user.role,
    );
  }

  @Get('trends')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getPerformanceTrends(
    @Query('period') period: 'monthly' | 'quarterly' | 'yearly',
    @Query('count') count: string,
    @Query('teamId') teamId: string,
    @Request() req,
  ) {
    return this.reportingService.getPerformanceTrends(
      { period: period || 'monthly', count: count ? +count : 12, teamId: teamId ? +teamId : undefined },
      req.user.userId,
      req.user.role,
    );
  }

  @Get('benchmarks')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getTeamBenchmarks(
    @Query('teamIds') teamIdsStr: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    const teamIds = teamIdsStr ? teamIdsStr.split(',').map(id => +id) : [];
    
    return this.reportingService.getTeamBenchmarks(
      { teamIds, startDate, endDate },
      req.user.userId,
      req.user.role,
    );
  }
}
