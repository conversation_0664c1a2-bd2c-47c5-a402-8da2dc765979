import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { SurveyResponse } from './survey-response.entity';

export enum SurveyType {
  PULSE = 'pulse',
  ANNUAL = 'annual',
  ONBOARDING = 'onboarding',
  EXIT = 'exit',
  CUSTOM = 'custom',
}

export enum SurveyStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

@Entity('engagement_surveys')
export class EngagementSurvey {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    name: 'survey_type',
    type: 'enum',
    enum: SurveyType,
  })
  surveyType: SurveyType;

  @Column({ type: 'json' })
  questions: object;

  @Column({ name: 'target_audience', type: 'json', nullable: true })
  targetAudience: object;

  @Column({ name: 'start_date', type: 'date', nullable: true })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: true })
  endDate: Date;

  @Column({ name: 'is_anonymous', default: true })
  isAnonymous: boolean;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @Column({
    type: 'enum',
    enum: SurveyStatus,
    default: SurveyStatus.DRAFT,
  })
  status: SurveyStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @OneToMany(() => SurveyResponse, response => response.survey)
  responses: SurveyResponse[];
}
