# Quick Start: Management Dashboard Implementation

## Immediate Next Steps (Phase 1)

### 1. Database Setup (COMPLETED ✅)

The database migration scripts have been created:
- `database/03-management-dashboard-schema.sql` - New table definitions
- `database/04-management-dashboard-sample-data.sql` - Sample data population

**To apply these changes:**

```bash
# Connect to your MySQL database
mysql -u your_username -p ehrx

# Run the schema migration
source database/03-management-dashboard-schema.sql

# Populate sample data
source database/04-management-dashboard-sample-data.sql
```

### 2. Next Immediate Tasks

#### Task 1: Update Database Service (Priority: HIGH)

**File:** `backend/src/database/database.service.ts`

Add new tables to the `getAvailableTables()` method:

```typescript
async getAvailableTables() {
  return {
    success: true,
    data: [
      // Existing tables
      { id: 'users', name: 'Users', description: 'User accounts and profiles' },
      { id: 'organizational_units', name: 'Organizational Units', description: 'Teams and departments' },
      { id: 'assessment_templates', name: 'Assessment Templates', description: 'Performance assessment templates' },
      { id: 'assessment_instances', name: 'Assessment Instances', description: 'Completed assessments' },
      { id: 'skillsets', name: 'Skillsets', description: 'Available skills and competencies' },
      { id: 'team_members', name: 'Team Members', description: 'Team membership relationships' },
      
      // NEW TABLES - Management Dashboard
      { id: 'analytics_dashboards', name: 'Analytics Dashboards', description: 'User dashboard configurations' },
      { id: 'performance_metrics', name: 'Performance Metrics', description: 'Aggregated performance data' },
      { id: 'engagement_surveys', name: 'Engagement Surveys', description: 'Employee engagement surveys' },
      { id: 'survey_responses', name: 'Survey Responses', description: 'Survey response data' },
      { id: 'recognition_badges', name: 'Recognition Badges', description: 'Achievement and appreciation badges' },
      { id: 'recognition_instances', name: 'Recognition Instances', description: 'Given recognitions and appreciations' },
      { id: 'user_gamification', name: 'User Gamification', description: 'User points, levels, and achievements' },
      { id: 'attrition_predictions', name: 'Attrition Predictions', description: 'AI-generated attrition risk scores' },
      { id: 'competency_frameworks', name: 'Competency Frameworks', description: 'Skills and competency definitions' },
      { id: 'career_paths', name: 'Career Paths', description: 'Career progression routes' },
      { id: 'micro_feedback', name: 'Micro Feedback', description: 'Quick feedback and reactions' },
      { id: 'ai_insights', name: 'AI Insights', description: 'AI-generated insights and recommendations' },
    ],
  };
}
```

#### Task 2: Create TypeScript Entities (Priority: HIGH)

Create the following directory structure and entity files:

```
backend/src/
├── analytics/
│   └── entities/
│       ├── analytics-dashboard.entity.ts
│       ├── performance-metric.entity.ts
│       ├── engagement-survey.entity.ts
│       └── survey-response.entity.ts
├── recognition/
│   └── entities/
│       ├── recognition-badge.entity.ts
│       ├── recognition-instance.entity.ts
│       ├── user-gamification.entity.ts
│       └── micro-feedback.entity.ts
└── ai/
    └── entities/
        ├── attrition-prediction.entity.ts
        ├── competency-framework.entity.ts
        ├── career-path.entity.ts
        └── ai-insight.entity.ts
```

**Example Entity Template:**

```typescript
// backend/src/analytics/entities/analytics-dashboard.entity.ts
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('analytics_dashboards')
export class AnalyticsDashboard {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'layout_config', type: 'json', nullable: true })
  layoutConfig: object;

  @Column({ name: 'widget_config', type: 'json', nullable: true })
  widgetConfig: object;

  @Column({ name: 'is_default', default: false })
  isDefault: boolean;

  @Column({ name: 'is_shared', default: false })
  isShared: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
```

#### Task 3: Create Basic Modules (Priority: MEDIUM)

Create module files to organize the new functionality:

```typescript
// backend/src/analytics/analytics.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsDashboard } from './entities/analytics-dashboard.entity';
import { PerformanceMetric } from './entities/performance-metric.entity';
import { EngagementSurvey } from './entities/engagement-survey.entity';
import { SurveyResponse } from './entities/survey-response.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AnalyticsDashboard,
      PerformanceMetric,
      EngagementSurvey,
      SurveyResponse,
    ]),
  ],
  controllers: [],
  providers: [],
  exports: [],
})
export class AnalyticsModule {}
```

#### Task 4: Update App Module (Priority: HIGH)

Add new modules to the main application module:

```typescript
// backend/src/app.module.ts
import { AnalyticsModule } from './analytics/analytics.module';
import { RecognitionModule } from './recognition/recognition.module';
import { AiModule } from './ai/ai.module';

@Module({
  imports: [
    // ... existing imports
    AnalyticsModule,
    RecognitionModule,
    AiModule,
  ],
  // ... rest of module configuration
})
export class AppModule {}
```

### 3. Testing the Implementation

#### Verify Database Tables

```sql
-- Check if all new tables were created
SHOW TABLES LIKE '%analytics%';
SHOW TABLES LIKE '%recognition%';
SHOW TABLES LIKE '%survey%';
SHOW TABLES LIKE '%attrition%';

-- Verify sample data
SELECT COUNT(*) FROM analytics_dashboards;
SELECT COUNT(*) FROM performance_metrics;
SELECT COUNT(*) FROM recognition_badges;
SELECT COUNT(*) FROM engagement_surveys;
```

#### Test API Access

```bash
# Start the backend server
cd backend
npm run start:dev

# Test database endpoint (should include new tables)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:3000/api/database/tables
```

### 4. Sample Data Overview

The implementation includes comprehensive sample data:

**Analytics Dashboards (3 records):**
- HR Executive Dashboard
- Team Manager Dashboard  
- Individual Performance Dashboard

**Performance Metrics (15+ records):**
- Individual performance scores
- Team averages
- Department engagement metrics

**Recognition System (7 badges, 8 instances):**
- Team Player, Innovation Champion, Mentor badges
- Real recognition instances between users
- Gamification data with points and levels

**Engagement Surveys (3 surveys, 7 responses):**
- Q1 Pulse Survey
- Annual Culture Survey
- Onboarding Survey

**AI Insights (4 records):**
- Attrition risk predictions
- Engagement trends
- Skill gap analysis
- Performance anomalies

### 5. Next Phase Preview

Once Phase 1 is complete, Phase 2 will focus on:
- Creating REST API endpoints for all new entities
- Implementing business logic services
- Adding proper authentication and authorization
- Building the first dashboard components

### 6. Troubleshooting

**Common Issues:**

1. **Database Connection Errors:**
   - Verify MySQL credentials in environment variables
   - Check database exists and user has proper permissions

2. **TypeORM Entity Errors:**
   - Ensure all entity files are properly imported in modules
   - Check foreign key relationships match database schema

3. **Module Import Errors:**
   - Verify all new modules are added to app.module.ts
   - Check circular dependency issues

**Getting Help:**
- Review existing entity patterns in `backend/src/users/entities/`
- Check existing module structure in `backend/src/teams/`
- Refer to TypeORM documentation for entity relationships

This quick start guide provides the immediate next steps to begin implementing the management dashboard features while maintaining the existing system integrity.
