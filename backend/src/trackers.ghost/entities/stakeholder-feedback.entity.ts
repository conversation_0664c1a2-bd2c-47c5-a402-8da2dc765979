import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum FeedbackType {
  PRAISE = 'praise',
  IMPROVEMENT = 'improvement',
  ISSUE = 'issue',
  SUGGESTION = 'suggestion',
  QUESTION = 'question'
}

export enum FeedbackStatus {
  NEW = 'new',
  ACKNOWLEDGED = 'acknowledged',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  CLOSED = 'closed'
}

export enum FeedbackSource {
  DIRECT = 'direct',
  EMAIL = 'email',
  MEETING = 'meeting',
  SURVEY = 'survey',
  ASSESSMENT = 'assessment',
  OTHER = 'other'
}

@Entity('stakeholder_feedback')
export class StakeholderFeedback {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: FeedbackType,
    default: FeedbackType.SUGGESTION
  })
  type: FeedbackType;

  @Column({
    type: 'enum',
    enum: FeedbackStatus,
    default: FeedbackStatus.NEW
  })
  status: FeedbackStatus;

  @Column({
    type: 'enum',
    enum: FeedbackSource,
    default: FeedbackSource.DIRECT
  })
  source: FeedbackSource;

  @Column({ name: 'stakeholder_name', nullable: true })
  stakeholderName: string;

  @Column({ name: 'stakeholder_role', nullable: true })
  stakeholderRole: string;

  @Column({ name: 'stakeholder_contact', nullable: true })
  stakeholderContact: string;

  @Column({ name: 'response', type: 'text', nullable: true })
  response: string;

  @Column({ name: 'created_by' })
  createdById: number;

  @Column({ name: 'assigned_to', nullable: true })
  assignedToId: number;

  @Column({ name: 'assessment_id', nullable: true })
  assessmentId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_to' })
  assignedTo: User;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'resolved_at', nullable: true })
  resolvedAt: Date;
}
