import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from './../../src/app.module';

describe('Authentication (e2e)', () => {
  let app: INestApplication;
  let jwtToken: string;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('should login a user with valid credentials', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(200)
      .expect((res) => {
        expect(res.body.access_token).toBeDefined();
        jwtToken = res.body.access_token;
      });
  });

  it('should reject login with invalid credentials', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'wrongpassword' })
      .expect(401);
  });

  it('should access protected profile endpoint with valid JWT', () => {
    return request(app.getHttpServer())
      .get('/auth/profile')
      .set('Authorization', Bearer )
      .expect(200)
      .expect((res) => {
        expect(res.body.userId).toBeDefined();
        expect(res.body.email).toBe('<EMAIL>');
      });
  });

  it('should reject access to protected endpoint without JWT', () => {
    return request(app.getHttpServer())
      .get('/auth/profile')
      .expect(401);
  });

  afterAll(async () => {
    await app.close();
  });
});
