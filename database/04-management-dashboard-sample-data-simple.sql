-- Management Dashboard Sample Data (Simple Version)
-- This file populates the new management dashboard tables with minimal sample data

USE ehrx;

-- Insert sample analytics dashboards
INSERT INTO analytics_dashboards (user_id, name, description, layout_config, widget_config, is_default, is_shared) VALUES
(1, 'HR Executive Dashboard', 'Comprehensive HR analytics for executive leadership', 
 '{"layout": "grid", "columns": 3, "rows": 4}',
 '{"widgets": [{"type": "performance_overview", "position": {"x": 0, "y": 0, "w": 2, "h": 1}}, {"type": "attrition_risk", "position": {"x": 2, "y": 0, "w": 1, "h": 1}}]}',
 TRUE, TRUE),
(2, 'Manager Dashboard', 'Team performance and engagement tracking',
 '{"layout": "grid", "columns": 2, "rows": 3}',
 '{"widgets": [{"type": "team_performance", "position": {"x": 0, "y": 0, "w": 1, "h": 1}}, {"type": "recognition_feed", "position": {"x": 1, "y": 0, "w": 1, "h": 2}}]}',
 TRUE, FALSE);

-- Insert sample performance metrics
INSERT INTO performance_metrics (user_id, organizational_unit_id, metric_type, metric_name, metric_value, period_start, period_end, metadata) VALUES
-- Individual metrics
(1, NULL, 'individual', 'overall_performance_score', 8.5, '2024-01-01', '2024-03-31', '{"assessment_id": 1, "areas": ["technical", "communication", "leadership"]}'),
(2, NULL, 'individual', 'overall_performance_score', 7.8, '2024-01-01', '2024-03-31', '{"assessment_id": 2, "areas": ["technical", "communication", "teamwork"]}'),
(1, NULL, 'individual', 'engagement_score', 8.2, '2024-01-01', '2024-03-31', '{"survey_id": 1, "response_rate": 100}'),
(2, NULL, 'individual', 'engagement_score', 7.5, '2024-01-01', '2024-03-31', '{"survey_id": 1, "response_rate": 100}'),

-- Organization metrics
(NULL, NULL, 'organization', 'average_engagement_score', 8.3, '2024-01-01', '2024-03-31', '{"response_rate": 95}'),
(NULL, NULL, 'organization', 'average_performance_score', 8.15, '2024-01-01', '2024-03-31', '{"completion_rate": 100}');

-- Insert sample engagement surveys
INSERT INTO engagement_surveys (title, description, survey_type, questions, target_audience, start_date, end_date, created_by_id, status) VALUES
('Q1 2024 Pulse Survey', 'Quarterly engagement and satisfaction survey', 'pulse',
 '{"questions": [{"id": 1, "type": "rating", "question": "How satisfied are you with your current role?", "scale": 10}, {"id": 2, "type": "rating", "question": "How likely are you to recommend this company as a great place to work?", "scale": 10}]}',
 '{"roles": ["all"], "departments": ["all"]}',
 '2024-01-15', '2024-01-29', 1, 'completed'),

('Annual Culture Survey 2024', 'Comprehensive annual culture and engagement assessment', 'annual',
 '{"questions": [{"id": 1, "type": "rating", "question": "I feel valued for my contributions", "scale": 5}, {"id": 2, "type": "rating", "question": "My manager provides clear direction", "scale": 5}]}',
 '{"roles": ["all"], "departments": ["all"]}',
 '2024-02-01', '2024-02-15', 1, 'completed');

-- Insert sample survey responses
INSERT INTO survey_responses (survey_id, respondent_id, responses, completion_time, submitted_at) VALUES
(1, 1, '{"1": 8, "2": 9}', 180, '2024-01-20 10:30:00'),
(1, 2, '{"1": 7, "2": 8}', 165, '2024-01-21 14:15:00'),
(2, 1, '{"1": 4, "2": 4}', 320, '2024-02-05 11:00:00'),
(2, 2, '{"1": 3, "2": 4}', 285, '2024-02-06 15:30:00');

-- Insert sample recognition badges
INSERT INTO recognition_badges (name, description, icon_url, badge_type, point_value, criteria, created_by_id) VALUES
('Team Player', 'Awarded for exceptional collaboration and teamwork', '/icons/team-player.svg', 'appreciation', 50, '{"criteria": "Demonstrates outstanding collaboration skills"}', 1),
('Innovation Champion', 'Recognizes creative problem-solving and innovative thinking', '/icons/innovation.svg', 'achievement', 100, '{"criteria": "Introduces innovative solutions or processes"}', 1),
('Mentor', 'Acknowledges dedication to helping others grow and learn', '/icons/mentor.svg', 'skill', 75, '{"criteria": "Actively mentors colleagues and shares knowledge"}', 1),
('Problem Solver', 'Awarded for tackling complex challenges with creative solutions', '/icons/problem-solver.svg', 'achievement', 70, '{"criteria": "Solves complex technical or business problems"}', 1);

-- Insert sample recognition instances
INSERT INTO recognition_instances (badge_id, giver_id, receiver_id, message, points_awarded, given_at) VALUES
(1, 2, 1, 'Outstanding collaboration on the Q1 project delivery. Your teamwork made all the difference!', 50, '2024-01-15 14:30:00'),
(2, 1, 2, 'Your innovative approach to the API optimization saved us weeks of development time. Brilliant work!', 100, '2024-01-20 16:45:00'),
(3, 1, 2, 'Thank you for taking the time to mentor our new team members. Your guidance is invaluable.', 75, '2024-02-01 10:15:00'),
(4, 2, 1, 'Incredible problem-solving on the database performance issue. You saved the day!', 70, '2024-02-20 15:10:00');

-- Insert sample user gamification data
INSERT INTO user_gamification (user_id, total_points, current_level, badges_earned, recognitions_given, recognitions_received, last_activity_date) VALUES
(1, 195, 3, 2, 2, 2, '2024-03-01'),
(2, 175, 3, 2, 2, 2, '2024-03-01');

-- Insert sample attrition predictions
INSERT INTO attrition_predictions (user_id, risk_score, risk_level, contributing_factors, prediction_date, model_version, confidence_score, recommended_actions) VALUES
(1, 0.2500, 'low', '{"factors": ["high_performance", "recent_recognition", "good_engagement"], "weights": [0.4, 0.3, 0.3]}', '2024-03-01', 'v1.2', 0.8500, '{"actions": ["continue_current_engagement", "consider_stretch_projects"]}'),
(2, 0.6500, 'medium', '{"factors": ["average_performance", "limited_growth_opportunities", "workload_concerns"], "weights": [0.3, 0.4, 0.3]}', '2024-03-01', 'v1.2', 0.7800, '{"actions": ["career_development_discussion", "workload_review", "skill_development_opportunities"]}');

-- Insert sample competency frameworks
INSERT INTO competency_frameworks (name, description, organizational_unit_id, competencies, version, is_active, created_by_id) VALUES
('Software Engineering Competencies', 'Core competencies for software engineering roles', NULL, 
 '{"technical": ["programming", "system_design", "testing"], "soft_skills": ["communication", "teamwork", "problem_solving"]}', 
 '1.0', TRUE, 1),
('Leadership Competencies', 'Leadership and management competencies', NULL,
 '{"leadership": ["team_management", "strategic_thinking", "decision_making"], "communication": ["presentation", "negotiation", "coaching"]}',
 '1.0', TRUE, 1);

-- Insert sample career paths
INSERT INTO career_paths (from_role, to_role, organizational_unit_id, required_skills, recommended_experience_years, typical_timeline_months, success_rate, created_by_id, is_active) VALUES
('Junior Developer', 'Senior Developer', NULL, '{"technical": ["advanced_programming", "system_design"], "soft_skills": ["mentoring", "leadership"]}', 3, 24, 85.5, 1, TRUE),
('Senior Developer', 'Tech Lead', NULL, '{"technical": ["architecture", "team_leadership"], "soft_skills": ["project_management", "communication"]}', 5, 18, 75.2, 1, TRUE);

-- Insert sample micro feedback
INSERT INTO micro_feedback (giver_id, receiver_id, feedback_type, feedback_value, context_type, context_id, given_at) VALUES
(2, 1, 'thumbs_up', 'great_job', 'task', 1001, '2024-02-28 14:30:00'),
(1, 2, 'emoji', '🚀', 'project', 2001, '2024-02-28 16:45:00'),
(1, 2, 'quick_poll', 'helpful', 'meeting', 3001, '2024-03-01 10:15:00'),
(2, 1, 'check_in', 'positive', 'general', NULL, '2024-03-01 17:00:00');

-- Insert sample AI insights
INSERT INTO ai_insights (insight_type, target_type, target_id, insight_data, confidence_score, priority, status, generated_at, expires_at) VALUES
('engagement_trend', 'organization', 1, '{"message": "Organization engagement trending upward (+12% vs last quarter)", "details": {"trend": "positive", "key_drivers": ["recognition_program", "flexible_work"]}}', 0.9200, 'low', 'acknowledged', '2024-03-01 08:30:00', '2024-03-31 23:59:59'),
('skill_gap', 'organization', 1, '{"message": "Critical skill gap identified in Cloud Security", "details": {"skill": "cloud_security", "gap_percentage": 65}}', 0.7800, 'high', 'new', '2024-03-01 09:00:00', '2024-06-30 23:59:59'),
('performance_anomaly', 'individual', 2, '{"message": "Performance decline detected over last 2 months", "details": {"trend": "declining", "confidence": 0.75, "suggested_action": "manager_check_in"}}', 0.7500, 'medium', 'new', '2024-03-01 09:30:00', '2024-04-01 23:59:59');
