import { Injectable } from '@nestjs/common';

export interface SecurityEvent {
  timestamp: Date;
  eventType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ip?: string;
  userAgent?: string;
  details: any;
  source: 'authentication' | 'authorization' | 'data_access' | 'system' | 'network';
}

/**
 * 🔐 NIS2-Compliant Security Audit Service
 * 
 * Implements comprehensive security event logging required by NIS2:
 * - Authentication events
 * - Authorization failures
 * - Data access logging
 * - System security events
 * - Incident detection and alerting
 */
@Injectable()
export class SecurityAuditService {
  private readonly securityEvents: SecurityEvent[] = [];
  private readonly maxEvents = 10000; // Keep last 10k events in memory

  /**
   * 🔐 Log security event with NIS2-compliant audit trail
   */
  logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    // Add to in-memory store
    this.securityEvents.push(securityEvent);

    // Maintain size limit
    if (this.securityEvents.length > this.maxEvents) {
      this.securityEvents.shift();
    }

    // Log to console for development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 [SECURITY-AUDIT]', securityEvent);
    }

    // In production, send to SIEM/logging service
    this.sendToSecurityMonitoring(securityEvent);

    // Check for critical events that need immediate attention
    if (securityEvent.severity === 'critical') {
      this.handleCriticalSecurityEvent(securityEvent);
    }
  }

  /**
   * 🔐 Log authentication events
   */
  logAuthenticationEvent(
    eventType: 'login_success' | 'login_failure' | 'logout' | 'password_change' | 'account_locked',
    userId?: string,
    ip?: string,
    userAgent?: string,
    details?: any
  ): void {
    const severity = eventType === 'login_failure' || eventType === 'account_locked' ? 'medium' : 'low';
    
    this.logSecurityEvent({
      eventType,
      severity,
      userId,
      ip,
      userAgent,
      details,
      source: 'authentication',
    });
  }

  /**
   * 🔐 Log authorization events
   */
  logAuthorizationEvent(
    eventType: 'access_granted' | 'access_denied' | 'privilege_escalation',
    userId: string,
    resource: string,
    action: string,
    ip?: string,
    details?: any
  ): void {
    const severity = eventType === 'access_denied' || eventType === 'privilege_escalation' ? 'high' : 'low';
    
    this.logSecurityEvent({
      eventType,
      severity,
      userId,
      ip,
      details: {
        resource,
        action,
        ...details,
      },
      source: 'authorization',
    });
  }

  /**
   * 🔐 Log data access events
   */
  logDataAccessEvent(
    eventType: 'data_read' | 'data_write' | 'data_delete' | 'data_export',
    userId: string,
    dataType: string,
    recordId?: string,
    ip?: string,
    details?: any
  ): void {
    const severity = eventType === 'data_delete' || eventType === 'data_export' ? 'medium' : 'low';
    
    this.logSecurityEvent({
      eventType,
      severity,
      userId,
      ip,
      details: {
        dataType,
        recordId,
        ...details,
      },
      source: 'data_access',
    });
  }

  /**
   * 🔐 Get security events for analysis
   */
  getSecurityEvents(
    startDate?: Date,
    endDate?: Date,
    severity?: string,
    source?: string
  ): SecurityEvent[] {
    let events = this.securityEvents;

    if (startDate) {
      events = events.filter(event => event.timestamp >= startDate);
    }

    if (endDate) {
      events = events.filter(event => event.timestamp <= endDate);
    }

    if (severity) {
      events = events.filter(event => event.severity === severity);
    }

    if (source) {
      events = events.filter(event => event.source === source);
    }

    return events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 🔐 Get security statistics
   */
  getSecurityStatistics(): any {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const recentEvents = this.securityEvents.filter(event => event.timestamp >= last24Hours);

    return {
      totalEvents: this.securityEvents.length,
      last24Hours: recentEvents.length,
      bySeverity: {
        low: recentEvents.filter(e => e.severity === 'low').length,
        medium: recentEvents.filter(e => e.severity === 'medium').length,
        high: recentEvents.filter(e => e.severity === 'high').length,
        critical: recentEvents.filter(e => e.severity === 'critical').length,
      },
      bySource: {
        authentication: recentEvents.filter(e => e.source === 'authentication').length,
        authorization: recentEvents.filter(e => e.source === 'authorization').length,
        data_access: recentEvents.filter(e => e.source === 'data_access').length,
        system: recentEvents.filter(e => e.source === 'system').length,
        network: recentEvents.filter(e => e.source === 'network').length,
      },
    };
  }

  /**
   * 🔐 Send to external security monitoring system
   */
  private sendToSecurityMonitoring(event: SecurityEvent): void {
    // In production, implement integration with SIEM systems like:
    // - Splunk
    // - ELK Stack
    // - Azure Sentinel
    // - AWS CloudTrail
    // - Custom security monitoring service
    
    // For now, just log to file in production
    if (process.env.NODE_ENV === 'production') {
      // TODO: Implement secure logging to external system
      console.log('🔐 [SECURITY-MONITORING]', JSON.stringify(event));
    }
  }

  /**
   * 🔐 Handle critical security events
   */
  private handleCriticalSecurityEvent(event: SecurityEvent): void {
    // In production, implement immediate alerting:
    // - Send email/SMS to security team
    // - Create incident ticket
    // - Trigger automated response
    // - Notify compliance team
    
    console.error('🚨 [CRITICAL-SECURITY-EVENT]', event);
    
    // TODO: Implement alerting system
    // AlertingService.sendCriticalAlert(event);
  }
}
