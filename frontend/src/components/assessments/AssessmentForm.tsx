import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  PerformanceAssessment,
  AssessmentTemplate,
  AssessmentStatus,
  User,
  UserRole,
  AssessmentArea,
  AssessmentResponse
} from '../../types';
import api from '../../services/api';

interface AssessmentFormProps {
  assessmentId?: number;
  userRole: UserRole;
  userId: number;
  onSave?: (assessment: PerformanceAssessment) => void;
  onCancel?: () => void;
}

const AssessmentForm: React.FC<AssessmentFormProps> = ({
  assessmentId,
  userRole,
  userId,
  onSave,
  onCancel
}) => {
  const navigate = useNavigate();
  const isCreating = !assessmentId;

  // Form states
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Data states
  const [templates, setTemplates] = useState<AssessmentTemplate[]>([]);
  const [employees, setEmployees] = useState<User[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<AssessmentTemplate | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<User | null>(null);
  const [dueDate, setDueDate] = useState<string>('');
  const [responses, setResponses] = useState<AssessmentResponse[]>([]);
  const [notes, setNotes] = useState<string>('');

  // Load data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // In a real implementation, this would make actual API calls
        // For now, we'll use mock data

        // Mock templates
        const mockTemplates: AssessmentTemplate[] = [
          {
            id: 1,
            name: 'Quarterly Performance Review',
            description: 'Standard quarterly review to assess employee performance across key areas',
            isActive: true,
            isGlobal: true,
            areas: [
              {
                name: 'Technical Skills',
                description: 'Assessment of technical capabilities and knowledge',
                weight: 0.4,
                maxScore: 100,
                orderIndex: 0,
                scoringRules: []
              },
              {
                name: 'Communication',
                description: 'Ability to communicate effectively with team members and clients',
                weight: 0.3,
                maxScore: 100,
                orderIndex: 1,
                scoringRules: []
              },
              {
                name: 'Team Collaboration',
                description: 'How well the employee works with others and contributes to team goals',
                weight: 0.3,
                maxScore: 100,
                orderIndex: 2,
                scoringRules: []
              }
            ],
            version: 1,
            createdById: 1,
            createdAt: '2025-01-01T00:00:00Z',
            updatedAt: '2025-01-01T00:00:00Z'
          },
          {
            id: 2,
            name: 'Monthly Check-in',
            description: 'Brief monthly check-in to track progress on goals and provide feedback',
            isActive: true,
            isGlobal: true,
            areas: [
              {
                name: 'Goal Progress',
                description: 'Progress on defined goals and objectives',
                weight: 0.6,
                maxScore: 100,
                orderIndex: 0,
                scoringRules: []
              },
              {
                name: 'Challenges',
                description: 'Assessment of challenges faced and solutions implemented',
                weight: 0.4,
                maxScore: 100,
                orderIndex: 1,
                scoringRules: []
              }
            ],
            version: 1,
            createdById: 1,
            createdAt: '2025-01-01T00:00:00Z',
            updatedAt: '2025-01-01T00:00:00Z'
          }
        ];

        // Mock employees
        const mockEmployees: User[] = [
          {
            id: 101,
            firstName: 'John',
            lastName: 'Doe',
            name: 'John Doe',
            email: '<EMAIL>',
            role: UserRole.EMPLOYEE,
            teamId: 1,
            managerId: 201,
            profileImage: '',
            isActive: true
          },
          {
            id: 102,
            firstName: 'Jane',
            lastName: 'Smith',
            name: 'Jane Smith',
            email: '<EMAIL>',
            role: UserRole.EMPLOYEE,
            teamId: 1,
            managerId: 201,
            profileImage: '',
            isActive: true
          },
          {
            id: 103,
            firstName: 'Michael',
            lastName: 'Brown',
            name: 'Michael Brown',
            email: '<EMAIL>',
            role: UserRole.EMPLOYEE,
            teamId: 1,
            managerId: 201,
            profileImage: '',
            isActive: true
          },
          {
            id: 104,
            firstName: 'Emily',
            lastName: 'Wilson',
            name: 'Emily Wilson',
            email: '<EMAIL>',
            role: UserRole.EMPLOYEE,
            teamId: 1,
            managerId: 201,
            profileImage: '',
            isActive: true
          }
        ];

        setTemplates(mockTemplates);
        setEmployees(mockEmployees);

        // If editing an existing assessment, fetch its details
        if (assessmentId) {
          // In a real implementation, this would be an API call
          // const response = await api.get(`/assessments/${assessmentId}`);
          // const assessment = response.data;

          // Mock assessment data
          const mockAssessment: PerformanceAssessment = {
            id: assessmentId,
            employeeId: 101,
            employeeName: 'John Doe',
            managerId: userId,
            managerName: 'Sarah Manager',
            templateId: 1,
            templateName: 'Quarterly Performance Review',
            status: AssessmentStatus.IN_PROGRESS,
            score: null,
            assessmentDate: '2025-01-13T16:20:00Z',
            createdAt: '2025-01-12T10:15:00Z',
            updatedAt: '2025-01-13T16:20:00Z',
            submittedAt: null,
            dueDate: '2025-01-18T23:59:59Z',
            responses: [
              {
                areaId: 1,
                areaName: 'Technical Skills',
                score: 85,
                notes: 'Good technical skills, but could improve in system design'
              },
              {
                areaId: 2,
                areaName: 'Communication',
                score: 90,
                notes: 'Excellent communication with team and stakeholders'
              },
              {
                areaId: 3,
                areaName: 'Team Collaboration',
                score: 80,
                notes: 'Works well with the team, could take more initiative'
              }
            ],
            notes: 'Overall good performance this quarter.'
          };

          const foundTemplate = mockTemplates.find(t => t.id === mockAssessment.templateId) || null;
          const foundEmployee = mockEmployees.find(e => e.id === mockAssessment.employeeId) || null;

          setSelectedTemplate(foundTemplate);
          setSelectedEmployee(foundEmployee);
          setDueDate(mockAssessment.dueDate.split('T')[0]); // Format as YYYY-MM-DD
          setResponses(mockAssessment.responses || []);
          setNotes(mockAssessment.notes || '');
        } else {
          // Set default due date (14 days from today)
          const defaultDueDate = new Date();
          defaultDueDate.setDate(defaultDueDate.getDate() + 14);
          setDueDate(defaultDueDate.toISOString().split('T')[0]);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error loading form data:', err);
        setError('Failed to load form data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchData();
  }, [assessmentId, userId]);

  // When template changes, reset responses
  useEffect(() => {
    if (selectedTemplate && isCreating) {
      // Initialize responses based on template areas
      const initialResponses: AssessmentResponse[] = selectedTemplate.areas.map(area => ({
        areaId: area.orderIndex,
        areaName: area.name,
        score: null,
        notes: ''
      }));

      setResponses(initialResponses);
    }
  }, [selectedTemplate, isCreating]);

  const handleScoreChange = (index: number, value: string) => {
    const score = parseInt(value);

    if (isNaN(score) || score < 0 || score > 100) {
      return; // Invalid score
    }

    const updatedResponses = [...responses];
    updatedResponses[index] = {
      ...updatedResponses[index],
      score
    };

    setResponses(updatedResponses);
  };

  const handleNotesChange = (index: number, value: string) => {
    const updatedResponses = [...responses];
    updatedResponses[index] = {
      ...updatedResponses[index],
      notes: value
    };

    setResponses(updatedResponses);
  };

  const calculateTotalScore = (): number | null => {
    if (!selectedTemplate || responses.length === 0) {
      return null;
    }

    let totalScore = 0;
    let totalWeight = 0;

    responses.forEach((response, index) => {
      if (response.score !== null && selectedTemplate.areas[index]) {
        const area = selectedTemplate.areas[index];
        totalScore += response.score * area.weight;
        totalWeight += area.weight;
      }
    });

    if (totalWeight === 0) {
      return null;
    }

    return parseFloat((totalScore / totalWeight).toFixed(1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedTemplate || !selectedEmployee || !dueDate) {
      setError('Please fill in all required fields.');
      return;
    }

    // Check if all areas have scores
    const missingScores = responses.some(res => res.score === null);

    try {
      setIsSaving(true);
      setError(null);

      // Determine status based on completeness and submission action
      const status = missingScores ? AssessmentStatus.IN_PROGRESS : AssessmentStatus.COMPLETED;

      // Create assessment object
      const assessment: PerformanceAssessment = {
        id: assessmentId,
        employeeId: selectedEmployee.id,
        employeeName: selectedEmployee.name,
        managerId: userId,
        managerName: 'Current Manager', // Would be filled by backend
        templateId: selectedTemplate.id!,
        templateName: selectedTemplate.name,
        status,
        score: calculateTotalScore(),
        assessmentDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        submittedAt: status === AssessmentStatus.COMPLETED ? new Date().toISOString() : null,
        dueDate: new Date(dueDate).toISOString(),
        responses,
        notes
      };

      // In a real implementation, this would be an API call
      // if (assessmentId) {
      //   await api.put(`/assessments/${assessmentId}`, assessment);
      // } else {
      //   await api.post('/assessments', assessment);
      // }

      // Mock successful save
      setTimeout(() => {
        if (onSave) {
          onSave(assessment);
        } else {
          // Navigate to the assessment list page
          navigate('/assessments');
        }
        setIsSaving(false);
      }, 500);
    } catch (err) {
      console.error('Error saving assessment:', err);
      setError('Failed to save assessment. Please try again.');
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate('/assessments');
    }
  };

  const handleSaveAsDraft = async () => {
    try {
      setIsSaving(true);
      setError(null);

      // Create assessment object with DRAFT status
      const assessment: PerformanceAssessment = {
        id: assessmentId,
        employeeId: selectedEmployee?.id || 0,
        employeeName: selectedEmployee?.name || '',
        managerId: userId,
        managerName: 'Current Manager', // Would be filled by backend
        templateId: selectedTemplate?.id || 0,
        templateName: selectedTemplate?.name || '',
        status: AssessmentStatus.DRAFT,
        score: null,
        assessmentDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        submittedAt: null,
        dueDate: new Date(dueDate).toISOString(),
        responses,
        notes
      };

      // In a real implementation, this would be an API call
      // if (assessmentId) {
      //   await api.put(`/assessments/${assessmentId}`, assessment);
      // } else {
      //   await api.post('/assessments', assessment);
      // }

      // Mock successful save
      setTimeout(() => {
        if (onSave) {
          onSave(assessment);
        } else {
          // Navigate to the assessment list page
          navigate('/assessments');
        }
        setIsSaving(false);
      }, 500);
    } catch (err) {
      console.error('Error saving assessment draft:', err);
      setError('Failed to save assessment draft. Please try again.');
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading assessment form...</p>
      </div>
    </div>;
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-6">
        {assessmentId ? 'Edit Performance Assessment' : 'Create Performance Assessment'}
      </h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Assessment Setup Section */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-700 mb-4">Assessment Setup</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Employee Selection - Only editable during creation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Employee*
              </label>
              <select
                value={selectedEmployee?.id || ''}
                onChange={(e) => {
                  const employeeId = parseInt(e.target.value);
                  const employee = employees.find(emp => emp.id === employeeId) || null;
                  setSelectedEmployee(employee);
                }}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                disabled={!isCreating}
                required
              >
                <option value="">Select an employee</option>
                {employees.map(employee => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name} ({employee.email})
                  </option>
                ))}
              </select>
            </div>

            {/* Template Selection - Only editable during creation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assessment Template*
              </label>
              <select
                value={selectedTemplate?.id || ''}
                onChange={(e) => {
                  const templateId = parseInt(e.target.value);
                  const template = templates.find(t => t.id === templateId) || null;
                  setSelectedTemplate(template);
                }}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                disabled={!isCreating}
                required
              >
                <option value="">Select a template</option>
                {templates.map(template => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
              {selectedTemplate && (
                <p className="mt-1 text-sm text-gray-500">
                  {selectedTemplate.description}
                </p>
              )}
            </div>

            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Due Date*
              </label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
          </div>
        </div>

        {/* Assessment Areas Section */}
        {selectedTemplate && responses.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Performance Assessment</h3>

            {selectedTemplate.areas.map((area, index) => (
              <div key={index} className="mb-6 p-4 border border-gray-200 rounded-md">
                <div className="mb-3 flex justify-between items-center">
                  <h4 className="font-medium text-gray-800">
                    {area.name} <span className="text-sm text-gray-500">(Weight: {area.weight * 100}%)</span>
                  </h4>
                  <div className="flex items-center">
                    <span className="text-sm font-medium mr-2">Score:</span>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={responses[index]?.score ?? ''}
                      onChange={(e) => handleScoreChange(index, e.target.value)}
                      className="w-16 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 text-center"
                    />
                  </div>
                </div>

                {area.description && (
                  <p className="text-sm text-gray-600 mb-2">
                    {area.description}
                  </p>
                )}

                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes / Feedback
                </label>
                <textarea
                  value={responses[index]?.notes || ''}
                  onChange={(e) => handleNotesChange(index, e.target.value)}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  rows={3}
                />
              </div>
            ))}

            {/* Overall Score */}
            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <div className="flex justify-between items-center">
                <h4 className="font-medium text-gray-800">Overall Score</h4>
                <div className="text-xl font-bold text-blue-600">
                  {calculateTotalScore() !== null ? `${calculateTotalScore()}%` : 'N/A'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Overall Notes */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-700 mb-4">Overall Notes</h3>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
            rows={4}
            placeholder="Enter overall assessment notes, feedback, or action items..."
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>

          <button
            type="button"
            onClick={handleSaveAsDraft}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSaving}
          >
            Save as Draft
          </button>

          <button
            type="submit"
            className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${isSaving ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : (
              assessmentId ? 'Update Assessment' : 'Create Assessment'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AssessmentForm;
