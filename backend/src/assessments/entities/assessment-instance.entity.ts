import { <PERSON>tity, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { AssessmentTemplate } from './assessment-template.entity';
import { AssessmentResponse } from './assessment-response.entity';
import { AssessmentCriteriaResponse } from './assessment-criteria-response.entity';

export enum AssessmentStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity('assessment_instances')
export class AssessmentInstance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'template_id' })
  templateId: number;

  @ManyToOne(() => AssessmentTemplate)
  @JoinColumn({ name: 'template_id' })
  template: AssessmentTemplate;

  @Column({ name: 'employee_id' })
  employeeId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'employee_id' })
  employee: User;

  @Column({ name: 'evaluator_id' })
  evaluatorId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'evaluator_id' })
  evaluator: User;

  @Column({
    type: 'enum',
    enum: AssessmentStatus,
    default: AssessmentStatus.DRAFT
  })
  status: AssessmentStatus;

  @Column({ name: 'template_snapshot', type: 'json', nullable: true })
  templateSnapshot: object;

  @Column({ type: 'date', name: 'assessment_date' })
  assessmentDate: Date;

  @Column({ nullable: true })
  notes: string;

  @Column({ name: 'total_score', type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalScore: number;

  @Column({ name: 'score_percentage', type: 'decimal', precision: 5, scale: 2, nullable: true })
  scorePercentage: number;

  @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ name: 'approved_by_id', nullable: true })
  approvedById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'approved_by_id' })
  approvedBy: User;

  @Column({ name: 'rejected_at', type: 'timestamp', nullable: true })
  rejectedAt: Date;

  @Column({ name: 'rejected_by_id', nullable: true })
  rejectedById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'rejected_by_id' })
  rejectedBy: User;

  @Column({ name: 'rejection_reason', type: 'text', nullable: true })
  rejectionReason: string;

  @Column({ name: 'workflow_history', type: 'json', nullable: true })
  workflowHistory: string;

  @OneToMany(() => AssessmentResponse, response => response.assessment)
  responses: AssessmentResponse[];

  @OneToMany(() => AssessmentCriteriaResponse, response => response.assessment)
  criteriaResponses: AssessmentCriteriaResponse[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
