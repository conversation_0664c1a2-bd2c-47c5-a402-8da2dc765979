-- Additional tables to replace mock data with real database functionality
-- NIS2-Compliant database structure for enterprise-grade HR system

-- ===================================================================
-- DASHBOARD AND ACTIVITY TRACKING TABLES
-- ===================================================================

-- Table for tracking recent activities across the system
CREATE TABLE IF NOT EXISTS recent_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activity_type ENUM('assessment', 'template', 'user', 'team', 'report') NOT NULL,
    activity_action ENUM('created', 'updated', 'deleted', 'completed', 'submitted', 'approved', 'rejected') NOT NULL,
    entity_id INT NOT NULL,
    entity_name VARCHAR(255) NOT NULL,
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_activities (user_id, created_at),
    INDEX idx_activity_type (activity_type, created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for storing dashboard statistics and metrics
CREATE TABLE IF NOT EXISTS dashboard_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_type ENUM('user_count', 'assessment_stats', 'performance_avg', 'completion_rate') NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    calculation_date DATE NOT NULL,
    organizational_unit_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_metric_type_date (metric_type, calculation_date),
    INDEX idx_org_unit_metrics (organizational_unit_id, metric_type),
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE SET NULL
);

-- ===================================================================
-- TEAM PERFORMANCE AND ANALYTICS TABLES
-- ===================================================================

-- Table for storing team performance snapshots
CREATE TABLE IF NOT EXISTS team_performance_snapshots (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organizational_unit_id INT NOT NULL,
    snapshot_date DATE NOT NULL,
    member_count INT NOT NULL DEFAULT 0,
    completed_assessments INT NOT NULL DEFAULT 0,
    pending_assessments INT NOT NULL DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT NULL,
    top_performer_id INT NULL,
    needs_attention_count INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_team_snapshot (organizational_unit_id, snapshot_date),
    INDEX idx_snapshot_date (snapshot_date),
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    FOREIGN KEY (top_performer_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Table for tracking individual performance trends
CREATE TABLE IF NOT EXISTS user_performance_trends (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    assessment_id INT NOT NULL,
    performance_score DECIMAL(5,2) NOT NULL,
    trend_direction ENUM('up', 'down', 'stable') NOT NULL,
    trend_percentage DECIMAL(5,2) DEFAULT NULL,
    assessment_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_trends (user_id, assessment_date),
    INDEX idx_performance_score (performance_score),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_id) REFERENCES assessment_instances(id) ON DELETE CASCADE
);

-- ===================================================================
-- ENGAGEMENT AND ANALYTICS TABLES
-- ===================================================================

-- Table for storing engagement survey responses
CREATE TABLE IF NOT EXISTS engagement_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    survey_id INT NOT NULL,
    question_id INT NOT NULL,
    response_value INT NOT NULL,
    response_text TEXT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_responses (user_id, submitted_at),
    INDEX idx_survey_responses (survey_id, submitted_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (survey_id) REFERENCES engagement_surveys(id) ON DELETE CASCADE
);

-- Table for storing calculated engagement metrics
CREATE TABLE IF NOT EXISTS engagement_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    metric_type ENUM('overall_engagement', 'job_satisfaction', 'team_collaboration', 'growth_opportunities') NOT NULL,
    metric_value DECIMAL(5,2) NOT NULL,
    calculation_date DATE NOT NULL,
    organizational_unit_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_metrics (user_id, calculation_date),
    INDEX idx_metric_type (metric_type, calculation_date),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE SET NULL
);

-- ===================================================================
-- TEMPLATE USAGE AND ANALYTICS TABLES
-- ===================================================================

-- Table for tracking template usage statistics
CREATE TABLE IF NOT EXISTS template_usage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    usage_date DATE NOT NULL,
    usage_count INT NOT NULL DEFAULT 1,
    completion_rate DECIMAL(5,2) DEFAULT NULL,
    average_completion_time INT DEFAULT NULL, -- in minutes
    average_score DECIMAL(5,2) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_template_date (template_id, usage_date),
    INDEX idx_usage_date (usage_date),
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE
);

-- ===================================================================
-- SYSTEM CONFIGURATION AND SETTINGS TABLES
-- ===================================================================

-- Table for storing system-wide configuration settings
CREATE TABLE IF NOT EXISTS system_configurations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string',
    description TEXT NULL,
    is_sensitive BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);

-- Insert default system configurations
INSERT IGNORE INTO system_configurations (config_key, config_value, config_type, description) VALUES
('assessment_reminder_days', '7', 'number', 'Days before assessment due date to send reminders'),
('max_assessment_duration_days', '30', 'number', 'Maximum days allowed to complete an assessment'),
('performance_threshold_excellent', '90', 'number', 'Score threshold for excellent performance'),
('performance_threshold_good', '75', 'number', 'Score threshold for good performance'),
('performance_threshold_satisfactory', '60', 'number', 'Score threshold for satisfactory performance'),
('dashboard_refresh_interval', '300', 'number', 'Dashboard auto-refresh interval in seconds'),
('enable_email_notifications', 'true', 'boolean', 'Enable email notifications for assessments'),
('company_name', 'EHRX Corporation', 'string', 'Company name for reports and communications');

-- ===================================================================
-- AUDIT AND COMPLIANCE TABLES
-- ===================================================================

-- Table for comprehensive audit logging (NIS2 compliance)
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action_type ENUM('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT') NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    session_id VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_audit (user_id, created_at),
    INDEX idx_action_type (action_type, created_at),
    INDEX idx_entity_audit (entity_type, entity_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- ===================================================================
-- SAMPLE DATA FOR TESTING
-- ===================================================================

-- Insert sample dashboard metrics
INSERT IGNORE INTO dashboard_metrics (metric_type, metric_value, calculation_date) VALUES
('user_count', 156, CURDATE()),
('assessment_stats', 89, CURDATE()),
('performance_avg', 82.5, CURDATE()),
('completion_rate', 94.2, CURDATE());

-- Insert sample recent activities
INSERT IGNORE INTO recent_activities (user_id, activity_type, activity_action, entity_id, entity_name, description) VALUES
(1, 'assessment', 'completed', 1, 'Q4 Performance Review', 'Assessment completed successfully'),
(2, 'template', 'created', 1, 'New Team Template', 'Created new assessment template for engineering team'),
(3, 'user', 'updated', 4, 'John Doe', 'Updated user profile information'),
(1, 'assessment', 'approved', 2, 'Monthly Check-in', 'Assessment approved by manager');

-- Insert sample team performance snapshots
INSERT IGNORE INTO team_performance_snapshots (organizational_unit_id, snapshot_date, member_count, completed_assessments, pending_assessments, average_score, top_performer_id) VALUES
(4, CURDATE(), 8, 15, 3, 87.5, 4),
(5, CURDATE(), 6, 12, 2, 84.2, 5),
(6, CURDATE(), 4, 8, 1, 91.3, 6);

-- Insert sample engagement metrics
INSERT IGNORE INTO engagement_metrics (user_id, metric_type, metric_value, calculation_date, organizational_unit_id) VALUES
(4, 'overall_engagement', 8.5, CURDATE(), 4),
(5, 'overall_engagement', 7.8, CURDATE(), 5),
(6, 'overall_engagement', 9.2, CURDATE(), 6),
(7, 'job_satisfaction', 8.0, CURDATE(), 4),
(8, 'team_collaboration', 8.7, CURDATE(), 5);

-- Insert sample template usage stats
INSERT IGNORE INTO template_usage_stats (template_id, usage_date, usage_count, completion_rate, average_completion_time, average_score) VALUES
(1, CURDATE(), 15, 94.5, 35, 85.2),
(2, CURDATE(), 8, 87.5, 42, 82.1),
(3, CURDATE(), 12, 91.7, 28, 88.4);
