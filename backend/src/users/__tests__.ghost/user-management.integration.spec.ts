import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as request from 'supertest';
import { UsersModule } from '../users.module';
import { User, UserRole } from '../entities/user.entity';

describe('Enhanced User Management Integration', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;

  const hrAdmin = {
    userId: 1,
    role: UserRole.HR_ADMIN,
    email: '<EMAIL>'
  };

  const manager = {
    userId: 2,
    role: UserRole.MANAGER,
    email: '<EMAIL>'
  };

  const employee = {
    userId: 3,
    role: UserRole.EMPLOYEE,
    email: '<EMAIL>'
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [User],
          synchronize: true,
        }),
        UsersModule,
      ],
    })
    .overrideGuard('JwtAuthGuard')
    .useValue({
      canActivate: (context) => {
        const request = context.switchToHttp().getRequest();
        request.user = request.headers['test-user'] === 'manager' ? manager : 
                      request.headers['test-user'] === 'employee' ? employee : hrAdmin;
        return true;
      },
    })
    .overrideGuard('RolesGuard')
    .useValue({
      canActivate: () => true,
    })
    .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    userRepository = moduleFixture.get('UserRepository');

    // Create test users
    await userRepository.save([
      { 
        id: 1, 
        email: '<EMAIL>', 
        firstName: 'HR', 
        lastName: 'Admin', 
        role: UserRole.HR_ADMIN, 
        is_active: true, 
        password_hash: 'hash',
        created_at: new Date(),
        updated_at: new Date()
      },
      { 
        id: 2, 
        email: '<EMAIL>', 
        firstName: 'Team', 
        lastName: 'Manager', 
        role: UserRole.MANAGER, 
        is_active: true, 
        password_hash: 'hash',
        created_at: new Date(),
        updated_at: new Date()
      },
      { 
        id: 3, 
        email: '<EMAIL>', 
        firstName: 'Test', 
        lastName: 'Employee', 
        role: UserRole.EMPLOYEE, 
        is_active: true, 
        password_hash: 'hash',
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Advanced User Search', () => {
    beforeEach(async () => {
      // Add more test users for search testing
      await userRepository.save([
        {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: UserRole.EMPLOYEE,
          is_active: true,
          password_hash: 'hash',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: UserRole.MANAGER,
          is_active: false,
          password_hash: 'hash',
          created_at: new Date(),
          updated_at: new Date()
        }
      ]);
    });

    it('should search users with text query', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/search/advanced?search=john')
        .expect(200);

      expect(response.body.users).toBeDefined();
      expect(response.body.total).toBeGreaterThan(0);
      expect(response.body.users.some(u => u.firstName.toLowerCase().includes('john'))).toBe(true);
    });

    it('should filter users by role', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/search/advanced?role=${UserRole.MANAGER}`)
        .expect(200);

      expect(response.body.users).toBeDefined();
      response.body.users.forEach(user => {
        expect(user.role).toBe(UserRole.MANAGER);
      });
    });

    it('should filter users by active status', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/search/advanced?isActive=false')
        .expect(200);

      expect(response.body.users).toBeDefined();
      response.body.users.forEach(user => {
        expect(user.is_active).toBe(false);
      });
    });

    it('should support pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/search/advanced?page=1&limit=2')
        .expect(200);

      expect(response.body.users).toBeDefined();
      expect(response.body.users.length).toBeLessThanOrEqual(2);
      expect(response.body.page).toBe(1);
      expect(response.body.totalPages).toBeGreaterThanOrEqual(1);
    });

    it('should combine multiple filters', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/search/advanced?role=${UserRole.EMPLOYEE}&isActive=true&search=test`)
        .expect(200);

      expect(response.body.users).toBeDefined();
      response.body.users.forEach(user => {
        expect(user.role).toBe(UserRole.EMPLOYEE);
        expect(user.is_active).toBe(true);
      });
    });
  });

  describe('Bulk User Operations', () => {
    it('should bulk create users', async () => {
      const bulkCreateData = {
        users: [
          {
            email: '<EMAIL>',
            firstName: 'Bulk',
            lastName: 'User1',
            role: UserRole.EMPLOYEE,
            password: 'password123'
          },
          {
            email: '<EMAIL>',
            firstName: 'Bulk',
            lastName: 'User2',
            role: UserRole.EMPLOYEE,
            password: 'password123'
          }
        ],
        sendInvitations: false
      };

      const response = await request(app.getHttpServer())
        .post('/users/bulk/create')
        .send(bulkCreateData)
        .expect(201);

      expect(response.body.created).toBeDefined();
      expect(response.body.created.length).toBe(2);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors.length).toBe(0);
    });

    it('should handle duplicate emails in bulk create', async () => {
      const bulkCreateData = {
        users: [
          {
            email: '<EMAIL>', // Duplicate email
            firstName: 'Duplicate',
            lastName: 'User',
            role: UserRole.EMPLOYEE,
            password: 'password123'
          }
        ],
        sendInvitations: false
      };

      const response = await request(app.getHttpServer())
        .post('/users/bulk/create')
        .send(bulkCreateData)
        .expect(201);

      expect(response.body.created.length).toBe(0);
      expect(response.body.errors.length).toBe(1);
      expect(response.body.errors[0].error).toContain('already exists');
    });

    it('should bulk update users', async () => {
      // First create users to update
      const users = await userRepository.save([
        {
          email: '<EMAIL>',
          firstName: 'Update',
          lastName: 'User1',
          role: UserRole.EMPLOYEE,
          is_active: true,
          password_hash: 'hash',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          email: '<EMAIL>',
          firstName: 'Update',
          lastName: 'User2',
          role: UserRole.EMPLOYEE,
          is_active: true,
          password_hash: 'hash',
          created_at: new Date(),
          updated_at: new Date()
        }
      ]);

      const bulkUpdateData = {
        userIds: users.map(u => u.id),
        updates: {
          role: UserRole.MANAGER
        }
      };

      const response = await request(app.getHttpServer())
        .patch('/users/bulk/update')
        .send(bulkUpdateData)
        .expect(200);

      expect(response.body.updated).toBeDefined();
      expect(response.body.updated.length).toBe(2);
      response.body.updated.forEach(user => {
        expect(user.role).toBe(UserRole.MANAGER);
      });
    });

    it('should bulk change roles', async () => {
      const users = await userRepository.save([
        {
          email: '<EMAIL>',
          firstName: 'Role',
          lastName: 'User1',
          role: UserRole.EMPLOYEE,
          is_active: true,
          password_hash: 'hash',
          created_at: new Date(),
          updated_at: new Date()
        }
      ]);

      const bulkRoleChangeData = {
        userIds: users.map(u => u.id),
        newRole: UserRole.MANAGER,
        reason: 'Promotion'
      };

      const response = await request(app.getHttpServer())
        .patch('/users/bulk/change-roles')
        .send(bulkRoleChangeData)
        .expect(200);

      expect(response.body.updated).toBeDefined();
      expect(response.body.updated.length).toBe(1);
      expect(response.body.updated[0].role).toBe(UserRole.MANAGER);
    });

    it('should bulk deactivate users', async () => {
      const users = await userRepository.save([
        {
          email: '<EMAIL>',
          firstName: 'Deactivate',
          lastName: 'User',
          role: UserRole.EMPLOYEE,
          is_active: true,
          password_hash: 'hash',
          created_at: new Date(),
          updated_at: new Date()
        }
      ]);

      const response = await request(app.getHttpServer())
        .post('/users/bulk/deactivate')
        .send({ userIds: users.map(u => u.id) })
        .expect(201);

      expect(response.body.deactivated).toBeDefined();
      expect(response.body.deactivated.length).toBe(1);
      expect(response.body.deactivated[0].is_active).toBe(false);
    });

    it('should bulk reactivate users', async () => {
      const users = await userRepository.save([
        {
          email: '<EMAIL>',
          firstName: 'Reactivate',
          lastName: 'User',
          role: UserRole.EMPLOYEE,
          is_active: false,
          password_hash: 'hash',
          created_at: new Date(),
          updated_at: new Date()
        }
      ]);

      const response = await request(app.getHttpServer())
        .post('/users/bulk/reactivate')
        .send({ userIds: users.map(u => u.id) })
        .expect(201);

      expect(response.body.reactivated).toBeDefined();
      expect(response.body.reactivated.length).toBe(1);
      expect(response.body.reactivated[0].is_active).toBe(true);
    });
  });

  describe('User Profile Management', () => {
    it('should update user profile', async () => {
      const profileUpdate = {
        firstName: 'Updated',
        lastName: 'Name',
        email: '<EMAIL>'
      };

      const response = await request(app.getHttpServer())
        .patch('/users/profile')
        .send(profileUpdate)
        .expect(200);

      expect(response.body.firstName).toBe('Updated');
      expect(response.body.lastName).toBe('Name');
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should change password with current password verification', async () => {
      const passwordChange = {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword123'
      };

      // This will fail because we don't have the actual old password
      await request(app.getHttpServer())
        .patch('/users/profile')
        .send(passwordChange)
        .expect(400);
    });

    it('should prevent email conflicts in profile update', async () => {
      const profileUpdate = {
        email: '<EMAIL>' // Already exists
      };

      await request(app.getHttpServer())
        .patch('/users/profile')
        .send(profileUpdate)
        .expect(409);
    });
  });

  describe('User Statistics', () => {
    it('should return user statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/statistics/overview')
        .expect(200);

      expect(response.body).toHaveProperty('totalUsers');
      expect(response.body).toHaveProperty('activeUsers');
      expect(response.body).toHaveProperty('inactiveUsers');
      expect(response.body).toHaveProperty('byRole');
      expect(response.body).toHaveProperty('recentlyCreated');
      expect(response.body).toHaveProperty('recentlyUpdated');

      expect(typeof response.body.totalUsers).toBe('number');
      expect(typeof response.body.activeUsers).toBe('number');
      expect(typeof response.body.inactiveUsers).toBe('number');
      expect(typeof response.body.byRole).toBe('object');
    });

    it('should include role breakdown in statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/statistics/overview')
        .expect(200);

      expect(response.body.byRole).toHaveProperty(UserRole.HR_ADMIN);
      expect(response.body.byRole).toHaveProperty(UserRole.MANAGER);
      expect(response.body.byRole).toHaveProperty(UserRole.EMPLOYEE);
    });
  });

  describe('Role-based Queries', () => {
    it('should get users by role', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/role/${UserRole.EMPLOYEE}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(user => {
        expect(user.role).toBe(UserRole.EMPLOYEE);
      });
    });

    it('should include inactive users when requested', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/role/${UserRole.EMPLOYEE}?includeInactive=true`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Permission Validation', () => {
    it('should validate role change permissions', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/validate/role-change/${UserRole.HR_ADMIN}`)
        .expect(200);

      expect(response.body).toHaveProperty('hasPermission');
      expect(typeof response.body.hasPermission).toBe('boolean');
    });

    it('should prevent managers from creating HR admins', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/validate/role-change/${UserRole.HR_ADMIN}`)
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body.hasPermission).toBe(false);
    });

    it('should allow HR admins to change any role', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/validate/role-change/${UserRole.MANAGER}`)
        .expect(200);

      expect(response.body.hasPermission).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent user operations', async () => {
      await request(app.getHttpServer())
        .get('/users/99999')
        .expect(404);

      await request(app.getHttpServer())
        .patch('/users/99999')
        .send({ firstName: 'Test' })
        .expect(404);
    });

    it('should validate required fields in bulk operations', async () => {
      const invalidBulkCreate = {
        users: [
          {
            // Missing required fields
            email: '',
            firstName: '',
            lastName: '',
            password: ''
          }
        ]
      };

      const response = await request(app.getHttpServer())
        .post('/users/bulk/create')
        .send(invalidBulkCreate)
        .expect(201);

      expect(response.body.errors.length).toBeGreaterThan(0);
    });

    it('should handle empty bulk operations gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/users/bulk/deactivate')
        .send({ userIds: [] })
        .expect(201);

      expect(response.body.deactivated).toBeDefined();
      expect(response.body.deactivated.length).toBe(0);
    });
  });

  describe('Access Control', () => {
    it('should restrict user management to authorized roles', async () => {
      // Employee should not be able to access user management endpoints
      await request(app.getHttpServer())
        .get('/users/search/advanced')
        .set('test-user', 'employee')
        .expect(403);

      await request(app.getHttpServer())
        .post('/users/bulk/create')
        .set('test-user', 'employee')
        .send({ users: [] })
        .expect(403);
    });

    it('should allow managers limited access', async () => {
      // Managers should be able to search users
      await request(app.getHttpServer())
        .get('/users/search/advanced')
        .set('test-user', 'manager')
        .expect(200);

      // But not create users
      await request(app.getHttpServer())
        .post('/users/bulk/create')
        .set('test-user', 'manager')
        .send({ users: [] })
        .expect(403);
    });

    it('should allow HR admins full access', async () => {
      await request(app.getHttpServer())
        .get('/users/search/advanced')
        .expect(200);

      await request(app.getHttpServer())
        .get('/users/statistics/overview')
        .expect(200);
    });
  });
});
