#!/bin/bash

# Enhanced EHRX Website Test Script
# Tests all enhanced mockup data and functionality

DOMAIN="dev.trusthansen.dk"
URL="https://$DOMAIN"
API_URL="http://localhost:4000"

echo "=== Enhanced EHRX Website Test ==="
echo

# Test 1: Website Functionality
echo "1. Testing Website Functionality:"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $URL)
if [ "$HTTP_STATUS" = "200" ]; then
    echo "   ✅ Website accessible (Status: $HTTP_STATUS)"
else
    echo "   ❌ Website not accessible (Status: $HTTP_STATUS)"
fi

# Test 2: Enhanced Backend API
echo
echo "2. Testing Enhanced Backend API:"

# Test assessments endpoint
ASSESSMENTS=$(curl -s $API_URL/api/assessments | jq -r '.total // "error"')
if [ "$ASSESSMENTS" != "error" ] && [ "$ASSESSMENTS" -gt 0 ]; then
    echo "   ✅ Assessments API: $ASSESSMENTS assessments available"
else
    echo "   ❌ Assessments API failed"
fi

# Test teams endpoint
TEAMS=$(curl -s $API_URL/api/teams | jq -r '.total // "error"')
if [ "$TEAMS" != "error" ] && [ "$TEAMS" -gt 0 ]; then
    echo "   ✅ Teams API: $TEAMS teams available"
else
    echo "   ❌ Teams API failed"
fi

# Test reports endpoint
REPORTS=$(curl -s $API_URL/api/reports | jq -r '.data.assessmentStats.totalAssessments // "error"')
if [ "$REPORTS" != "error" ] && [ "$REPORTS" -gt 0 ]; then
    echo "   ✅ Reports API: $REPORTS total assessments tracked"
else
    echo "   ❌ Reports API failed"
fi

# Test users endpoint
USERS=$(curl -s $API_URL/api/users | jq -r '.total // "error"')
if [ "$USERS" != "error" ] && [ "$USERS" -gt 0 ]; then
    echo "   ✅ Users API: $USERS users in system"
else
    echo "   ❌ Users API failed"
fi

# Test dashboard endpoint
DASHBOARD=$(curl -s $API_URL/api/dashboard | jq -r '.data.overview.totalEmployees // "error"')
if [ "$DASHBOARD" != "error" ] && [ "$DASHBOARD" -gt 0 ]; then
    echo "   ✅ Dashboard API: $DASHBOARD employees tracked"
else
    echo "   ❌ Dashboard API failed"
fi

# Test templates endpoint
TEMPLATES=$(curl -s $API_URL/api/templates | jq -r '.total // "error"')
if [ "$TEMPLATES" != "error" ] && [ "$TEMPLATES" -gt 0 ]; then
    echo "   ✅ Templates API: $TEMPLATES templates available"
else
    echo "   ❌ Templates API failed"
fi

# Test 3: Data Quality
echo
echo "3. Testing Data Quality:"

# Check assessment data richness
ASSESSMENT_DETAILS=$(curl -s $API_URL/api/assessments | jq -r '.data[0].employeeId // "error"')
if [ "$ASSESSMENT_DETAILS" != "error" ]; then
    echo "   ✅ Assessment data includes employee IDs, departments, managers"
else
    echo "   ❌ Assessment data incomplete"
fi

# Check team data richness
TEAM_BUDGET=$(curl -s $API_URL/api/teams | jq -r '.data[0].budget // "error"')
if [ "$TEAM_BUDGET" != "error" ] && [ "$TEAM_BUDGET" -gt 0 ]; then
    echo "   ✅ Team data includes budgets, locations, technologies"
else
    echo "   ❌ Team data incomplete"
fi

# Check user data richness
USER_SKILLS=$(curl -s $API_URL/api/users | jq -r '.data[0].skills[0] // "error"')
if [ "$USER_SKILLS" != "error" ]; then
    echo "   ✅ User data includes skills, certifications, goals"
else
    echo "   ❌ User data incomplete"
fi

# Test 4: Performance Metrics
echo
echo "4. Testing Performance Metrics:"

TOTAL_BUDGET=$(curl -s $API_URL/api/teams | jq -r '.summary.totalBudget // "error"')
if [ "$TOTAL_BUDGET" != "error" ] && [ "$TOTAL_BUDGET" -gt 1000000 ]; then
    echo "   ✅ Total team budget: \$$(echo $TOTAL_BUDGET | sed 's/\(.*\)\(...\)\(...\)/\1.\2M/')"
else
    echo "   ❌ Budget calculation failed"
fi

AVG_SCORE=$(curl -s $API_URL/api/teams | jq -r '.summary.avgPerformanceScore // "error"')
if [ "$AVG_SCORE" != "error" ]; then
    echo "   ✅ Average performance score: $AVG_SCORE"
else
    echo "   ❌ Performance score calculation failed"
fi

ACTIVE_PROJECTS=$(curl -s $API_URL/api/teams | jq -r '.summary.totalActiveProjects // "error"')
if [ "$ACTIVE_PROJECTS" != "error" ] && [ "$ACTIVE_PROJECTS" -gt 0 ]; then
    echo "   ✅ Total active projects: $ACTIVE_PROJECTS"
else
    echo "   ❌ Project tracking failed"
fi

echo
echo "=== Enhanced Test Complete ==="
echo
echo "🌐 Website: $URL"
echo "🔗 API Base: $API_URL"
echo "📊 Dashboard: $URL (click Assessments, Teams, Reports, Settings)"
echo
echo "📈 Key Metrics:"
echo "   • $USERS total users across $TEAMS teams"
echo "   • $ASSESSMENTS assessments with $REPORTS total tracked"
echo "   • $TEMPLATES assessment templates available"
echo "   • \$$(echo $TOTAL_BUDGET | sed 's/\(.*\)\(...\)\(...\)/\1.\2M/') total budget managed"
