import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { MonthlyDashboardKpiValue } from './monthly-dashboard-kpi-value.entity';
import { MonthlyDashboardTeamTarget } from './monthly-dashboard-team-target.entity';

export enum CalculationMethod {
  MANUAL = 'manual',
  AUTO_COUNT = 'auto_count',
  AUTO_PERCENTAGE = 'auto_percentage',
  AUTO_FTE = 'auto_fte',
}

@Entity('monthly_dashboard_kpis')
export class MonthlyDashboardKpi {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ name: 'display_name', length: 150 })
  displayName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'target_value', type: 'decimal', precision: 10, scale: 4, nullable: true })
  targetValue: number;

  @Column({ length: 50, nullable: true })
  unit: string;

  @Column({
    name: 'calculation_method',
    type: 'enum',
    enum: CalculationMethod,
    default: CalculationMethod.MANUAL
  })
  calculationMethod: CalculationMethod;

  @Column({ name: 'help_text', type: 'text' })
  helpText: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'sort_order', default: 0 })
  sortOrder: number;

  // Traffic light configuration
  @Column({ name: 'traffic_light_green_min', type: 'decimal', precision: 5, scale: 2, default: -5.0 })
  trafficLightGreenMin: number;

  @Column({ name: 'traffic_light_green_max', type: 'decimal', precision: 5, scale: 2, default: 5.0 })
  trafficLightGreenMax: number;

  @Column({ name: 'traffic_light_yellow_min', type: 'decimal', precision: 5, scale: 2, default: -10.0 })
  trafficLightYellowMin: number;

  @Column({ name: 'traffic_light_yellow_max', type: 'decimal', precision: 5, scale: 2, default: 10.0 })
  trafficLightYellowMax: number;

  @Column({ name: 'special_rules', type: 'json', nullable: true })
  specialRules: object;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToMany(() => MonthlyDashboardKpiValue, (kpiValue) => kpiValue.kpi)
  kpiValues: MonthlyDashboardKpiValue[];

  @OneToMany(() => MonthlyDashboardTeamTarget, (teamTarget) => teamTarget.kpi)
  teamTargets: MonthlyDashboardTeamTarget[];

  // Helper methods
  calculateTrafficLightStatus(value: number, targetValue: number): 'green' | 'yellow' | 'red' | 'na' {
    if (value === null || value === undefined || targetValue === null || targetValue === undefined) {
      return 'na';
    }

    // Special handling for compliance KPI
    if (this.name === 'COMPLIANCE' && this.specialRules?.['red_threshold']) {
      return value >= this.specialRules['red_threshold'] ? 'red' : 'green';
    }

    // Calculate percentage deviation
    const deviation = targetValue === 0 ? 0 : ((value - targetValue) / targetValue) * 100;

    if (deviation >= this.trafficLightGreenMin && deviation <= this.trafficLightGreenMax) {
      return 'green';
    } else if (deviation >= this.trafficLightYellowMin && deviation <= this.trafficLightYellowMax) {
      return 'yellow';
    } else {
      return 'red';
    }
  }

  getEffectiveTargetValue(teamTargets: MonthlyDashboardTeamTarget[], organizationalUnitId: number): number {
    // Find team-specific target
    const teamTarget = teamTargets.find(
      target => target.organizationalUnitId === organizationalUnitId &&
      target.kpiId === this.id &&
      (!target.effectiveTo || target.effectiveTo >= new Date())
    );

    return teamTarget ? teamTarget.targetValue : this.targetValue;
  }
}
