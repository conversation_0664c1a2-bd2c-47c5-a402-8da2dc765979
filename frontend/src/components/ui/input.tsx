import React from 'react';
import { TextField, TextFieldProps } from '@mui/material';

interface InputProps extends Omit<TextFieldProps, 'variant'> {
  className?: string;
}

export const Input: React.FC<InputProps> = ({ className, ...props }) => (
  <TextField
    variant="outlined"
    size="small"
    fullWidth
    className={className}
    {...props}
  />
);

export const Textarea: React.FC<InputProps> = ({ className, ...props }) => (
  <TextField
    variant="outlined"
    size="small"
    fullWidth
    multiline
    rows={3}
    className={className}
    {...props}
  />
);
