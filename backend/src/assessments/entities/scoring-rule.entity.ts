import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn, UpdateDateColumn, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AssessmentArea } from './assessment-area.entity';

export enum RuleType {
  ADDITION = 'addition',
  SUBTRACTION = 'subtraction',
  MULTIPLICATION = 'multiplication',
  DIVISION = 'division',
  CONDITIONAL = 'conditional'
}

export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  GREATER_OR_EQUAL = 'greater_or_equal',
  LESS_OR_EQUAL = 'less_or_equal'
}

@Entity('scoring_rules')
export class ScoringRule {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'area_id' })
  areaId: number;

  @ManyToOne(() => AssessmentArea, area => area.scoringRules)
  @JoinColumn({ name: 'area_id' })
  area: AssessmentArea;

  @Column({
    name: 'rule_type',
    type: 'enum',
    enum: RuleType
  })
  ruleType: RuleType;

  @Column({ name: 'condition_field', nullable: true })
  conditionField: string;

  @Column({
    name: 'condition_operator',
    type: 'enum',
    enum: ConditionOperator,
    nullable: true
  })
  conditionOperator: ConditionOperator;

  @Column({ name: 'condition_value', nullable: true })
  conditionValue: string;

  @Column({ name: 'score_adjustment' })
  scoreAdjustment: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
