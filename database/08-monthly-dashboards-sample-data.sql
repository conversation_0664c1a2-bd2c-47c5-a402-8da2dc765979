-- Monthly Dashboards Sample Data (Simplified)
-- This file adds sample team targets and submissions using direct IDs

USE ehrx;

-- =====================================================
-- INSERT TEAM-SPECIFIC TARGETS
-- =====================================================

-- Sample utilization targets for different teams
INSERT INTO monthly_dashboard_team_targets (
  organizational_unit_id, kpi_id, target_value, effective_from, notes, created_by_user_id
) VALUES
-- Frontend Development Team (ID: 15) - UTILIZATION (KPI ID: 5)
(15, 5, 85.0, '2025-01-01', 'Frontend Development team target utilization', 1),
-- Backend Development Team (ID: 16) - UTILIZATION (KPI ID: 5)
(16, 5, 87.0, '2025-01-01', 'Backend Development team target utilization', 1),
-- Mobile Development Team (ID: 17) - UTILIZATION (KPI ID: 5)
(17, 5, 80.0, '2025-01-01', 'Mobile Development team target utilization', 1),
-- Data Engineering Team (ID: 19) - UTILIZATION (KPI ID: 5)
(19, 5, 82.0, '2025-01-01', 'Data Engineering team target utilization', 1),
-- Level 1 Support Team (ID: 35) - UTILIZATION (KPI ID: 5)
(35, 5, 75.0, '2025-01-01', 'Level 1 Support team target utilization', 1),
-- Process Optimization Team (ID: 27) - UTILIZATION (KPI ID: 5)
(27, 5, 90.0, '2025-01-01', 'Process Optimization team target utilization', 1);

-- Sample PTO annual targets per employee for different teams
INSERT INTO monthly_dashboard_team_targets (
  organizational_unit_id, kpi_id, target_value, effective_from, notes, created_by_user_id
) VALUES
-- PTO (KPI ID: 9)
(15, 9, 25.0, '2025-01-01', 'Annual PTO days per employee - Frontend Development', 1),
(16, 9, 25.0, '2025-01-01', 'Annual PTO days per employee - Backend Development', 1),
(17, 9, 25.0, '2025-01-01', 'Annual PTO days per employee - Mobile Development', 1),
(19, 9, 25.0, '2025-01-01', 'Annual PTO days per employee - Data Engineering', 1),
(35, 9, 25.0, '2025-01-01', 'Annual PTO days per employee - Level 1 Support', 1),
(27, 9, 25.0, '2025-01-01', 'Annual PTO days per employee - Process Optimization', 1);

-- Sample RTO expectations for different teams
INSERT INTO monthly_dashboard_team_targets (
  organizational_unit_id, kpi_id, target_value, effective_from, notes, created_by_user_id
) VALUES
-- RTO (KPI ID: 10)
(15, 10, 80.0, '2025-01-01', 'Expected office attendance percentage - Frontend Development', 1),
(16, 10, 80.0, '2025-01-01', 'Expected office attendance percentage - Backend Development', 1),
(17, 10, 85.0, '2025-01-01', 'Expected office attendance percentage - Mobile Development', 1),
(19, 10, 75.0, '2025-01-01', 'Expected office attendance percentage - Data Engineering', 1),
(35, 10, 90.0, '2025-01-01', 'Expected office attendance percentage - Level 1 Support', 1),
(27, 10, 85.0, '2025-01-01', 'Expected office attendance percentage - Process Optimization', 1);

-- =====================================================
-- SAMPLE DASHBOARD SUBMISSIONS
-- =====================================================

-- Sample submissions for January 2025
INSERT INTO monthly_dashboard_submissions (
  organizational_unit_id, submitted_by_user_id, submission_month, submission_year, 
  completion_date, status, notes
) VALUES
(15, 5, 1, 2025, '2025-01-14', 'submitted', 'January 2025 dashboard - Frontend Development Team'),
(16, 6, 1, 2025, '2025-01-15', 'submitted', 'January 2025 dashboard - Backend Development Team'),
(35, 1, 1, 2025, '2025-01-13', 'approved', 'January 2025 dashboard - Level 1 Support Team');

-- =====================================================
-- SAMPLE KPI VALUES
-- =====================================================

-- Sample KPI values for Frontend Development Team (submission_id: 1)
INSERT INTO monthly_dashboard_kpi_values (
  submission_id, kpi_id, value, target_value, traffic_light_status, notes
) VALUES
-- Frontend Development Team - January 2025
(1, 3, 1, 0, 'yellow', '1 resignation this month - ATTRITION'),
(1, 4, 98.5, 100, 'green', 'Good SLA performance'),
(1, 5, 87.0, 85.0, 'green', 'Above target utilization'),
(1, 6, 95.0, 100, 'green', 'Good time registration compliance'),
(1, 7, 0, 0, 'green', 'No compliance issues'),
(1, 8, -2500.00, 0, 'green', 'Under budget by 2.5K'),
(1, 9, 8, 25, 'green', '8 PTO days taken this month'),
(1, 10, 82.0, 80.0, 'green', 'Good office attendance');

-- Sample KPI values for Backend Development Team (submission_id: 2)
INSERT INTO monthly_dashboard_kpi_values (
  submission_id, kpi_id, value, target_value, traffic_light_status, notes
) VALUES
-- Backend Development Team - January 2025
(2, 3, 0, 0, 'green', 'No resignations'),
(2, 4, 92.0, 100, 'yellow', 'SLA slightly below target'),
(2, 5, 89.0, 87.0, 'green', 'Good utilization'),
(2, 6, 88.0, 100, 'red', 'Time registration needs improvement'),
(2, 7, 0, 0, 'green', 'No compliance issues'),
(2, 8, 1200.00, 0, 'yellow', 'Over budget by 1.2K'),
(2, 9, 12, 25, 'green', '12 PTO days taken this month'),
(2, 10, 78.0, 80.0, 'green', 'Slightly below office attendance target');

-- Sample KPI values for Level 1 Support Team (submission_id: 3)
INSERT INTO monthly_dashboard_kpi_values (
  submission_id, kpi_id, value, target_value, traffic_light_status, notes
) VALUES
-- Level 1 Support Team - January 2025
(3, 3, 0, 0, 'green', 'No resignations'),
(3, 4, 94.0, 100, 'green', 'Good SLA performance'),
(3, 5, 77.0, 75.0, 'green', 'Above target utilization'),
(3, 6, 92.0, 100, 'yellow', 'Time registration could be better'),
(3, 7, 1, 0, 'red', '1 minor compliance issue identified'),
(3, 8, -800.00, 0, 'green', 'Under budget by 800'),
(3, 9, 15, 25, 'green', '15 PTO days taken this month'),
(3, 10, 92.0, 90.0, 'green', 'Excellent office attendance');

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Display summary of what was created
SELECT 'Sample data inserted successfully!' AS status;

SELECT 
  'Team Targets' as data_type,
  COUNT(*) as count 
FROM monthly_dashboard_team_targets
UNION ALL
SELECT 
  'Dashboard Submissions' as data_type,
  COUNT(*) as count 
FROM monthly_dashboard_submissions
UNION ALL
SELECT 
  'KPI Values' as data_type,
  COUNT(*) as count 
FROM monthly_dashboard_kpi_values;

-- Show sample dashboard data
SELECT 
  ou.name as team_name,
  mds.submission_month,
  mds.submission_year,
  mds.status,
  COUNT(mdkv.id) as kpi_count
FROM monthly_dashboard_submissions mds
JOIN organizational_units ou ON mds.organizational_unit_id = ou.id
LEFT JOIN monthly_dashboard_kpi_values mdkv ON mds.id = mdkv.submission_id
GROUP BY mds.id, ou.name, mds.submission_month, mds.submission_year, mds.status;
