import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Button,
  Avatar,
  Tooltip
} from '@mui/material';
import { User } from '../../contexts/OrganizationContext';
import { OrganizationalUnit } from '../../types';

interface UnitCardProps {
  unit: OrganizationalUnit;
  members: User[];
  onEdit: (unit: OrganizationalUnit) => void;
  onAddSubunit: (parentId: number) => void;
  onViewMembers?: (unit: OrganizationalUnit) => void;
}

const UnitCard: React.FC<UnitCardProps> = ({
  unit,
  members,
  onEdit,
  onAddSubunit,
  onViewMembers
}) => {
  const getTypeColor = (type: string) => {
    const colors: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'error'> = {
      organization: 'primary',
      division: 'secondary',
      department: 'success',
      team: 'warning',
      squad: 'error'
    };
    return colors[type] || 'default';
  };

  const formatBudget = (budget: number) => {
    if (budget >= 1000000) {
      return `$${(budget / 1000000).toFixed(1)}M`;
    } else if (budget >= 1000) {
      return `$${(budget / 1000).toFixed(0)}K`;
    }
    return `$${budget.toLocaleString()}`;
  };

  return (
    <Card
      elevation={3}
      sx={{
        height: '100%',
        transition: 'all 0.3s ease',
        '&:hover': {
          elevation: 6,
          transform: 'translateY(-2px)'
        }
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 'bold',
              fontSize: '1.1rem',
              lineHeight: 1.3,
              flex: 1,
              mr: 1
            }}
          >
            {unit.name}
          </Typography>
          <Chip
            label={unit.type}
            color={getTypeColor(unit.type)}
            size="small"
            sx={{ textTransform: 'capitalize' }}
          />
        </Box>

        {/* Description */}
        {unit.description && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 2,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {unit.description}
          </Typography>
        )}

        {/* Stats */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" sx={{ mr: 1 }}>
              👥
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {members.length} member{members.length !== 1 ? 's' : ''}
            </Typography>
          </Box>

          {unit.budget > 0 && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ mr: 1 }}>
                💰
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Budget: {formatBudget(unit.budget)}
              </Typography>
            </Box>
          )}

          {unit.manager && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar sx={{ width: 20, height: 20, mr: 1, fontSize: '0.7rem' }}>
                {unit.manager.firstName?.[0]}{unit.manager.lastName?.[0]}
              </Avatar>
              <Typography variant="body2" color="text.secondary">
                {unit.manager.firstName} {unit.manager.lastName}
              </Typography>
            </Box>
          )}
        </Box>

        {/* Member Avatars */}
        {members.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {members.slice(0, 5).map((member) => (
                <Tooltip
                  key={member.id}
                  title={`${member.firstName} ${member.lastName} - ${member.title}`}
                >
                  <Avatar sx={{ width: 24, height: 24, fontSize: '0.7rem' }}>
                    {member.firstName?.[0]}{member.lastName?.[0]}
                  </Avatar>
                </Tooltip>
              ))}
              {members.length > 5 && (
                <Avatar sx={{ width: 24, height: 24, fontSize: '0.6rem', bgcolor: 'grey.400' }}>
                  +{members.length - 5}
                </Avatar>
              )}
            </Box>
          </Box>
        )}

        {/* Actions */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            size="small"
            variant="contained"
            color="primary"
            onClick={() => onEdit(unit)}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            ✏️ Edit
          </Button>
          <Button
            size="small"
            variant="outlined"
            onClick={() => onAddSubunit(unit.id)}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            ➕ Sub-unit
          </Button>
          {onViewMembers && members.length > 0 && (
            <Button
              size="small"
              variant="text"
              onClick={() => onViewMembers(unit)}
              sx={{ minWidth: 'auto', px: 2 }}
            >
              👥 Members
            </Button>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default UnitCard;
