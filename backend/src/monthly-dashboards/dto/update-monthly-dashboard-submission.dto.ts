import { PartialType } from '@nestjs/mapped-types';
import { CreateMonthlyDashboardSubmissionDto } from './create-monthly-dashboard-submission.dto';
import { IsOptional, IsEnum, IsNumber } from 'class-validator';
import { SubmissionStatus } from '../entities/monthly-dashboard-submission.entity';

export class UpdateMonthlyDashboardSubmissionDto extends PartialType(CreateMonthlyDashboardSubmissionDto) {
  @IsOptional()
  @IsEnum(SubmissionStatus)
  status?: SubmissionStatus;

  @IsOptional()
  @IsNumber()
  approvedByUserId?: number;
}
