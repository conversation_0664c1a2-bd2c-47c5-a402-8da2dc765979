import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn, UpdateDateColumn, <PERSON>inColumn } from 'typeorm';
import { AssessmentInstance } from './assessment-instance.entity';
import { AssessmentArea } from './assessment-area.entity';

@Entity('assessment_responses')
export class AssessmentResponse {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'assessment_id' })
  assessmentId: number;

  @ManyToOne(() => AssessmentInstance, assessment => assessment.responses)
  @JoinColumn({ name: 'assessment_id' })
  assessment: AssessmentInstance;

  @Column({ name: 'area_id' })
  areaId: number;

  @ManyToOne(() => AssessmentArea)
  @JoinColumn({ name: 'area_id' })
  area: AssessmentArea;

  @Column({ name: 'area_snapshot', type: 'json', nullable: true })
  areaSnapshot: object;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  score: number;

  @Column({ name: 'evaluator_comments', nullable: true })
  evaluatorComments: string;

  @Column({ name: 'employee_comments', nullable: true })
  employeeComments: string;

  @Column({ name: 'area_weight', type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  areaWeight: number;

  @Column({ name: 'weighted_score', type: 'decimal', precision: 10, scale: 2 })
  weightedScore: number;

  @Column({ name: 'base_score', type: 'decimal', precision: 10, scale: 2, nullable: true })
  baseScore: number;

  @Column({ name: 'score_adjustments', type: 'json', nullable: true })
  scoreAdjustments: string;

  @Column({ name: 'additional_data', type: 'json', nullable: true })
  additionalData: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
