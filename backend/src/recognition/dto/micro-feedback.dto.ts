import { Is<PERSON><PERSON>, IsOptional, IsString, IsN<PERSON>ber } from 'class-validator';
import { FeedbackType, ContextType } from '../entities/micro-feedback.entity';

export class MicroFeedbackDto {
  @IsOptional()
  @IsNumber()
  receiverId?: number;

  @IsEnum(FeedbackType)
  feedbackType: FeedbackType;

  @IsOptional()
  @IsString()
  feedbackValue?: string;

  @IsOptional()
  @IsEnum(ContextType)
  contextType?: ContextType;

  @IsOptional()
  @IsNumber()
  contextId?: number;
}
