import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActionItemsService } from './action-items.service';
import { ActionItemsController } from './action-items.controller';
import { ActionItemsScheduler } from './action-items.scheduler';
import { ActionItem, ActionItemUpdate } from './entities/action-item.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ActionItem, ActionItemUpdate])],
  controllers: [ActionItemsController],
  providers: [ActionItemsService, ActionItemsScheduler],
  exports: [ActionItemsService],
})
export class ActionItemsModule { }
