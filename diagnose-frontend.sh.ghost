#!/bin/bash

echo "🔍 EHRX Frontend Diagnostic Script"
echo "=================================="
echo

echo "📍 Current Directory:"
pwd
echo

echo "📍 Node.js and NPM Versions:"
node --version
npm --version
echo

echo "📍 Frontend Directory Contents:"
ls -la frontend/
echo

echo "📍 Package.json Scripts:"
cat frontend/package.json | grep -A 10 '"scripts"'
echo

echo "📍 Node Modules Status:"
if [ -d "frontend/node_modules" ]; then
    echo "✅ node_modules exists"
    echo "📊 Size: $(du -sh frontend/node_modules | cut -f1)"
    echo "📊 Files: $(find frontend/node_modules -type f | wc -l)"
else
    echo "❌ node_modules missing"
fi
echo

echo "📍 React Scripts Status:"
if [ -f "frontend/node_modules/.bin/react-scripts" ]; then
    echo "✅ react-scripts binary exists"
    frontend/node_modules/.bin/react-scripts --version 2>&1 || echo "❌ react-scripts version failed"
else
    echo "❌ react-scripts binary missing"
fi
echo

echo "📍 TypeScript Files Status:"
echo "App.tsx exists: $([ -f frontend/src/App.tsx ] && echo "✅ Yes" || echo "❌ No")"
echo "App.enhanced.tsx exists: $([ -f frontend/src/App.enhanced.tsx ] && echo "✅ Yes" || echo "❌ No")"
echo "DashboardContent.tsx exists: $([ -f frontend/src/components/dashboard/DashboardContent.tsx ] && echo "✅ Yes" || echo "❌ No")"
echo "TestDashboard.tsx exists: $([ -f frontend/src/components/dashboard/TestDashboard.tsx ] && echo "✅ Yes" || echo "❌ No")"
echo

echo "📍 Import Analysis in App.tsx:"
if [ -f frontend/src/App.tsx ]; then
    echo "Dashboard imports:"
    grep -n "import.*Dashboard" frontend/src/App.tsx || echo "No Dashboard imports found"
    echo
    echo "Dashboard usage:"
    grep -n "Dashboard" frontend/src/App.tsx || echo "No Dashboard usage found"
else
    echo "❌ App.tsx not found"
fi
echo

echo "📍 Port Status:"
echo "Port 3080: $(lsof -i :3080 2>/dev/null | wc -l) processes"
echo "Port 4000: $(lsof -i :4000 2>/dev/null | wc -l) processes"
echo

echo "📍 Process Status:"
echo "NPM processes: $(ps aux | grep npm | grep -v grep | wc -l)"
echo "Node processes: $(ps aux | grep node | grep -v grep | wc -l)"
echo "React processes: $(ps aux | grep react-scripts | grep -v grep | wc -l)"
echo

echo "📍 Cache Status:"
echo "NPM cache: $([ -d frontend/node_modules/.cache ] && echo "✅ Exists" || echo "❌ Missing")"
echo "ESLint cache: $([ -f frontend/.eslintcache ] && echo "✅ Exists" || echo "❌ Missing")"
echo

echo "📍 Testing Simple NPM Command:"
cd frontend
echo "Running 'npm list react-scripts':"
npm list react-scripts 2>&1 | head -5
echo

echo "📍 Testing TypeScript Compilation:"
echo "Running basic TypeScript check:"
npx tsc --noEmit --skipLibCheck 2>&1 | head -10
echo

echo "📍 Environment Variables:"
echo "NODE_ENV: ${NODE_ENV:-not set}"
echo "PORT: ${PORT:-not set}"
echo "REACT_APP_API_URL: ${REACT_APP_API_URL:-not set}"
echo

echo "🎯 Diagnostic Complete!"
echo "=================================="
