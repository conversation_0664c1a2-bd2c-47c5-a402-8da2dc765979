import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import ActionItemsDashboard, { ActionItemStatus, ActionItemPriority, ActionItemCategory } from '../ActionItemsDashboard';
import { UserRole } from '../../../types';
import api from '../../../services/api';

// Mock the API
jest.mock('../../../services/api');
const mockedApi = api as jest.Mocked<typeof api>;

const mockActionItems = [
  {
    id: 1,
    title: 'Improve Communication Skills',
    description: 'Work on verbal and written communication',
    status: ActionItemStatus.IN_PROGRESS,
    priority: ActionItemPriority.HIGH,
    category: ActionItemCategory.SKILL_DEVELOPMENT,
    dueDate: '2024-12-31',
    progressPercentage: 50,
    assignedTo: {
      id: 3,
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>'
    },
    createdBy: {
      id: 2,
      firstName: 'Jane',
      lastName: 'Manager'
    },
    estimatedHours: 10,
    actualHours: 5,
    tags: ['communication', 'skills'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  },
  {
    id: 2,
    title: 'Complete Safety Training',
    description: 'Mandatory safety training course',
    status: ActionItemStatus.OVERDUE,
    priority: ActionItemPriority.CRITICAL,
    category: ActionItemCategory.COMPLIANCE,
    dueDate: '2024-01-15',
    progressPercentage: 25,
    assignedTo: {
      id: 3,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>'
    },
    createdBy: {
      id: 2,
      firstName: 'Jane',
      lastName: 'Manager'
    },
    estimatedHours: 4,
    actualHours: 1,
    tags: ['safety', 'compliance'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z'
  }
];

const mockStatistics = {
  totalItems: 2,
  byStatus: {
    [ActionItemStatus.OPEN]: 0,
    [ActionItemStatus.IN_PROGRESS]: 1,
    [ActionItemStatus.COMPLETED]: 0,
    [ActionItemStatus.CANCELLED]: 0,
    [ActionItemStatus.OVERDUE]: 1
  },
  byPriority: {
    [ActionItemPriority.LOW]: 0,
    [ActionItemPriority.MEDIUM]: 0,
    [ActionItemPriority.HIGH]: 1,
    [ActionItemPriority.CRITICAL]: 1
  },
  byCategory: {
    [ActionItemCategory.SKILL_DEVELOPMENT]: 1,
    [ActionItemCategory.COMPLIANCE]: 1,
    [ActionItemCategory.PERFORMANCE_IMPROVEMENT]: 0,
    [ActionItemCategory.PROCESS_IMPROVEMENT]: 0,
    [ActionItemCategory.SAFETY]: 0,
    [ActionItemCategory.QUALITY]: 0,
    [ActionItemCategory.TRAINING]: 0,
    [ActionItemCategory.GOAL_SETTING]: 0,
    [ActionItemCategory.OTHER]: 0
  },
  overdueItems: 1,
  completedThisMonth: 0,
  upcomingDueDates: 1
};

describe('ActionItemsDashboard', () => {
  const defaultProps = {
    currentUserRole: UserRole.MANAGER,
    currentUserId: 2
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockedApi.get.mockImplementation((url) => {
      if (url.includes('/action-items?')) {
        return Promise.resolve({
          data: {
            items: mockActionItems,
            total: 2,
            page: 1,
            totalPages: 1
          }
        });
      }
      if (url === '/action-items/statistics') {
        return Promise.resolve({ data: mockStatistics });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });
  });

  it('renders dashboard with action items', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    expect(screen.getByText('Action Items')).toBeInTheDocument();
    expect(screen.getByText('Create Action Item')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Improve Communication Skills')).toBeInTheDocument();
      expect(screen.getByText('Complete Safety Training')).toBeInTheDocument();
    });
  });

  it('displays statistics correctly', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Total Items')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
      expect(screen.getByText('Overdue Items')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('Due This Week')).toBeInTheDocument();
    });
  });

  it('handles search functionality', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search action items...');
    fireEvent.change(searchInput, { target: { value: 'communication' } });
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining('search=communication')
      );
    });
  });

  it('handles status filtering', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    const statusSelect = screen.getByDisplayValue('All Status');
    fireEvent.change(statusSelect, { target: { value: ActionItemStatus.IN_PROGRESS } });
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining(`status=${ActionItemStatus.IN_PROGRESS}`)
      );
    });
  });

  it('handles priority filtering', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    const prioritySelect = screen.getByDisplayValue('All Priorities');
    fireEvent.change(prioritySelect, { target: { value: ActionItemPriority.HIGH } });
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining(`priority=${ActionItemPriority.HIGH}`)
      );
    });
  });

  it('handles category filtering', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    const categorySelect = screen.getByDisplayValue('All Categories');
    fireEvent.change(categorySelect, { target: { value: ActionItemCategory.SKILL_DEVELOPMENT } });
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining(`category=${ActionItemCategory.SKILL_DEVELOPMENT}`)
      );
    });
  });

  it('clears filters when clear button is clicked', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    // Set some filters first
    const searchInput = screen.getByPlaceholderText('Search action items...');
    fireEvent.change(searchInput, { target: { value: 'test' } });
    
    const clearButton = screen.getByText('Clear Filters');
    fireEvent.click(clearButton);
    
    expect(searchInput).toHaveValue('');
  });

  it('displays correct status badges', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('IN PROGRESS')).toBeInTheDocument();
      expect(screen.getByText('OVERDUE')).toBeInTheDocument();
    });
  });

  it('displays correct priority badges', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('HIGH')).toBeInTheDocument();
      expect(screen.getByText('CRITICAL')).toBeInTheDocument();
    });
  });

  it('shows progress bars correctly', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('50%')).toBeInTheDocument();
      expect(screen.getByText('25%')).toBeInTheDocument();
    });
  });

  it('highlights overdue items', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      const overdueDate = screen.getByText('1/15/2024');
      expect(overdueDate).toHaveClass('text-red-600', 'font-medium');
    });
  });

  it('handles item selection for bulk operations', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);
    });
    
    const itemCheckbox = screen.getAllByRole('checkbox')[1]; // First item checkbox (skip select all)
    fireEvent.click(itemCheckbox);
    
    await waitFor(() => {
      expect(screen.getByText('1 item(s) selected')).toBeInTheDocument();
      expect(screen.getByText('Bulk Actions')).toBeInTheDocument();
    });
  });

  it('handles select all functionality', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(selectAllCheckbox);
    });
    
    await waitFor(() => {
      expect(screen.getByText('2 item(s) selected')).toBeInTheDocument();
    });
  });

  it('shows create button for managers and HR admins', () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    expect(screen.getByText('Create Action Item')).toBeInTheDocument();
  });

  it('hides create button for employees', () => {
    render(<ActionItemsDashboard {...defaultProps} currentUserRole={UserRole.EMPLOYEE} />);
    expect(screen.queryByText('Create Action Item')).not.toBeInTheDocument();
  });

  it('shows bulk actions for managers and HR admins', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      const itemCheckbox = screen.getAllByRole('checkbox')[1];
      fireEvent.click(itemCheckbox);
    });
    
    expect(screen.getByText('Bulk Actions')).toBeInTheDocument();
  });

  it('hides bulk actions for employees', async () => {
    render(<ActionItemsDashboard {...defaultProps} currentUserRole={UserRole.EMPLOYEE} />);
    
    await waitFor(() => {
      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
    });
  });

  it('handles pagination', async () => {
    // Mock paginated response
    mockedApi.get.mockResolvedValue({
      data: {
        items: mockActionItems,
        total: 10,
        page: 1,
        totalPages: 2
      }
    });
    
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Showing page 1 of 2')).toBeInTheDocument();
    });
    
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining('page=2')
      );
    });
  });

  it('handles API errors gracefully', async () => {
    mockedApi.get.mockRejectedValue(new Error('API Error'));
    
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to fetch action items')).toBeInTheDocument();
    });
  });

  it('shows loading state', () => {
    // Mock delayed response
    mockedApi.get.mockImplementation(() => new Promise(() => {}));
    
    render(<ActionItemsDashboard {...defaultProps} />);
    
    expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
  });

  it('shows empty state when no items found', async () => {
    mockedApi.get.mockResolvedValue({
      data: {
        items: [],
        total: 0,
        page: 1,
        totalPages: 0
      }
    });
    
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('No action items found matching your criteria.')).toBeInTheDocument();
    });
  });

  it('formats categories correctly', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Skill Development')).toBeInTheDocument();
      expect(screen.getByText('Compliance')).toBeInTheDocument();
    });
  });

  it('shows assigned user names', async () => {
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getAllByText('John Doe')).toHaveLength(2);
    });
  });

  it('handles progress updates', async () => {
    const mockUpdateProgress = jest.fn();
    mockedApi.patch.mockResolvedValue({
      data: { ...mockActionItems[0], progressPercentage: 75 }
    });
    
    render(<ActionItemsDashboard {...defaultProps} />);
    
    await waitFor(() => {
      const viewButtons = screen.getAllByText('View');
      fireEvent.click(viewButtons[0]);
    });
    
    // This would open a modal or detailed view
    // The actual implementation would depend on the modal component
  });

  it('displays success messages after operations', async () => {
    mockedApi.patch.mockResolvedValue({
      data: mockActionItems[0]
    });
    
    render(<ActionItemsDashboard {...defaultProps} />);
    
    // Simulate a successful update operation
    // This would typically happen through a modal or form submission
    // The exact implementation depends on how updates are triggered
  });

  it('handles different user roles appropriately', () => {
    const { rerender } = render(<ActionItemsDashboard {...defaultProps} currentUserRole={UserRole.HR_ADMIN} />);
    expect(screen.getByText('Create Action Item')).toBeInTheDocument();
    
    rerender(<ActionItemsDashboard {...defaultProps} currentUserRole={UserRole.MANAGER} />);
    expect(screen.getByText('Create Action Item')).toBeInTheDocument();
    
    rerender(<ActionItemsDashboard {...defaultProps} currentUserRole={UserRole.EMPLOYEE} />);
    expect(screen.queryByText('Create Action Item')).not.toBeInTheDocument();
  });
});
