import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  IconButton,
  Button,
  Divider,
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  Favorite as FavoriteIcon,
  ThumbUp as ThumbUpIcon,
  Refresh as RefreshIcon,
  Visibility as ViewAllIcon,
} from '@mui/icons-material';
import { ApiService, RecognitionInstance } from '../../../services/api';

const RecognitionFeedWidget: React.FC = () => {
  const [recognitions, setRecognitions] = useState<RecognitionInstance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadRecognitionFeed();
  }, []);

  const loadRecognitionFeed = async () => {
    try {
      setLoading(true);
      const data = await ApiService.getRecognitionFeed(10, 0);
      setRecognitions(data);
    } catch (err) {
      setError('Failed to load recognition feed');
      console.error('Error loading recognition feed:', err);
    } finally {
      setLoading(false);
    }
  };

  const getBadgeIcon = (badgeType?: string) => {
    switch (badgeType) {
      case 'achievement':
        return <TrophyIcon sx={{ color: '#ffd700' }} />;
      case 'appreciation':
        return <FavoriteIcon sx={{ color: '#e91e63' }} />;
      case 'milestone':
        return <TrophyIcon sx={{ color: '#9c27b0' }} />;
      case 'skill':
        return <ThumbUpIcon sx={{ color: '#2196f3' }} />;
      default:
        return <TrophyIcon sx={{ color: '#ff9800' }} />;
    }
  };

  const getBadgeColor = (badgeType?: string) => {
    switch (badgeType) {
      case 'achievement':
        return '#ffd700';
      case 'appreciation':
        return '#e91e63';
      case 'milestone':
        return '#9c27b0';
      case 'skill':
        return '#2196f3';
      default:
        return '#ff9800';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return date.toLocaleDateString();
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    if (!firstName && !lastName) return '?';
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  if (loading) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: 400 }}>
      <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
              <TrophyIcon />
            </Avatar>
            <Typography variant="h6">Recognition Feed</Typography>
          </Box>
          
          <IconButton size="small" onClick={loadRecognitionFeed}>
            <RefreshIcon />
          </IconButton>
        </Box>

        {/* Recognition List */}
        <Box flex={1} overflow="auto">
          {recognitions.length === 0 ? (
            <Box textAlign="center" py={4}>
              <Typography variant="body2" color="textSecondary">
                No recent recognitions
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Be the first to recognize someone's great work!
              </Typography>
            </Box>
          ) : (
            <List sx={{ py: 0 }}>
              {recognitions.map((recognition, index) => (
                <React.Fragment key={recognition.id}>
                  <ListItem alignItems="flex-start" sx={{ px: 0, py: 1 }}>
                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          bgcolor: getBadgeColor(recognition.badge?.badgeType),
                          width: 40,
                          height: 40,
                        }}
                      >
                        {recognition.badge ? 
                          getBadgeIcon(recognition.badge.badgeType) :
                          getInitials(recognition.giver?.firstName, recognition.giver?.lastName)
                        }
                      </Avatar>
                    </ListItemAvatar>
                    
                    <ListItemText
                      primary={
                        <Box>
                          <Typography variant="body2" component="span">
                            <strong>
                              {recognition.giver?.firstName} {recognition.giver?.lastName}
                            </strong>
                            {' gave '}
                            <strong>
                              {recognition.receiver?.firstName} {recognition.receiver?.lastName}
                            </strong>
                            {recognition.badge && (
                              <>
                                {' the '}
                                <Chip
                                  label={recognition.badge.name}
                                  size="small"
                                  sx={{
                                    bgcolor: getBadgeColor(recognition.badge.badgeType),
                                    color: 'white',
                                    fontWeight: 'bold',
                                    mx: 0.5,
                                  }}
                                />
                                {' badge'}
                              </>
                            )}
                          </Typography>
                          
                          {recognition.message && (
                            <Typography
                              variant="body2"
                              color="textSecondary"
                              sx={{ mt: 0.5, fontStyle: 'italic' }}
                            >
                              "{recognition.message}"
                            </Typography>
                          )}
                          
                          <Box display="flex" alignItems="center" justifyContent="space-between" mt={1}>
                            <Typography variant="caption" color="textSecondary">
                              {formatTimeAgo(recognition.givenAt)}
                            </Typography>
                            
                            {recognition.pointsAwarded > 0 && (
                              <Chip
                                label={`+${recognition.pointsAwarded} pts`}
                                size="small"
                                color="success"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                  
                  {index < recognitions.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          )}
        </Box>

        {/* Footer */}
        {recognitions.length > 0 && (
          <Box mt={2} textAlign="center">
            <Button
              size="small"
              startIcon={<ViewAllIcon />}
              onClick={() => {
                // Navigate to full recognition feed
                console.log('Navigate to full recognition feed');
              }}
            >
              View All Recognitions
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default RecognitionFeedWidget;
