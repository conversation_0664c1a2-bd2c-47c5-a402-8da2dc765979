import React from 'react';
import { <PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Typography, SxProps, Theme } from '@mui/material';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  sx?: SxProps<Theme>;
}

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
  sx?: SxProps<Theme>;
}

interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
  sx?: SxProps<Theme>;
}

export const Card: React.FC<CardProps> = ({ children, className, sx }) => (
  <MuiCard className={className} sx={{ borderRadius: 2, ...sx }}>
    {children}
  </MuiCard>
);

export const CardContent: React.FC<CardContentProps> = ({ children, className, sx }) => (
  <MuiCardContent className={className} sx={sx}>
    {children}
  </MuiCardContent>
);

export const CardHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <MuiCardHeader className={className} title={children} />
);

export const CardTitle: React.FC<CardTitleProps> = ({ children, className, sx }) => (
  <Typography variant="h6" component="h2" className={className} sx={sx}>
    {children}
  </Typography>
);
