import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AttritionPrediction } from './entities/attrition-prediction.entity';
import { CompetencyFramework } from './entities/competency-framework.entity';
import { CareerPath } from './entities/career-path.entity';
import { AiInsight } from './entities/ai-insight.entity';
import { Ai<PERSON>ontroller } from './ai.controller';
import { AiService } from './ai.service';
import { AttritionPredictionService } from './services/attrition-prediction.service';
import { SentimentAnalysisService } from './services/sentiment-analysis.service';
import { InsightsEngineService } from './services/insights-engine.service';
import { CompetencyMappingService } from './services/competency-mapping.service';
import { CareerPathService } from './services/career-path.service';
import { NlpAnalysisService } from './services/nlp-analysis.service';
import { PredictiveAnalyticsService } from './services/predictive-analytics.service';
import { RiskScoringService } from './services/risk-scoring.service';
import { User } from '../users/entities/user.entity';
import { PerformanceMetric } from '../analytics/entities/performance-metric.entity';
import { SurveyResponse } from '../analytics/entities/survey-response.entity';
import { EngagementSurvey } from '../analytics/entities/engagement-survey.entity';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AttritionPrediction,
      CompetencyFramework,
      CareerPath,
      AiInsight,
      User,
      PerformanceMetric,
      SurveyResponse,
      EngagementSurvey,
      AssessmentInstance,
    ]),
  ],
  controllers: [AiController],
  providers: [
    AiService,
    AttritionPredictionService,
    SentimentAnalysisService,
    InsightsEngineService,
    CompetencyMappingService,
    CareerPathService,
    NlpAnalysisService,
    PredictiveAnalyticsService,
    RiskScoringService,
  ],
  exports: [
    TypeOrmModule,
    AiService,
    AttritionPredictionService,
    SentimentAnalysisService,
    InsightsEngineService,
    CompetencyMappingService,
    CareerPathService,
  ],
})
export class AiModule { }
