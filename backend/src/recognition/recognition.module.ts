import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RecognitionBadge } from './entities/recognition-badge.entity';
import { RecognitionInstance } from './entities/recognition-instance.entity';
import { UserGamification } from './entities/user-gamification.entity';
import { MicroFeedback } from './entities/micro-feedback.entity';
import { RecognitionController } from './recognition.controller';
import { RecognitionService } from './recognition.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RecognitionBadge,
      RecognitionInstance,
      UserGamification,
      MicroFeedback,
    ]),
  ],
  controllers: [RecognitionController],
  providers: [RecognitionService],
  exports: [TypeOrmModule, RecognitionService],
})
export class RecognitionModule {}
