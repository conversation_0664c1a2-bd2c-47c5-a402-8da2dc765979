import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection } from 'typeorm';
import { Team } from './entities/team.entity';
import { TeamMember, TeamMemberRole } from './entities/team-member.entity';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { AddTeamMemberDto, UpdateTeamMemberRoleDto } from './dto/team-member.dto';
import { UserRole } from '../users/entities/user.entity';

@Injectable()
export class TeamsService {
  constructor(
    @InjectRepository(Team)
    private teamRepository: Repository<Team>,
    @InjectRepository(TeamMember)
    private teamMemberRepository: Repository<TeamMember>,
    private connection: Connection,
  ) {}

  async create(createTeamDto: CreateTeamDto, userId: number, userRole: string): Promise<Team> {
    // Only HR_ADMIN and MANAGER roles can create teams
    if (userRole !== UserRole.HR_ADMIN && userRole !== UserRole.MANAGER) {
      throw new ForbiddenException('You do not have permission to create teams');
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the team
      const team = this.teamRepository.create({
        name: createTeamDto.name,
        description: createTeamDto.description,
        createdById: userId,
      });

      const savedTeam = await queryRunner.manager.save(team);

      // Add creator as team lead
      const teamLead = this.teamMemberRepository.create({
        teamId: savedTeam.id,
        userId: userId,
        role: TeamMemberRole.TEAM_LEAD,
      });

      await queryRunner.manager.save(teamLead);

      // Add other members if specified
      if (createTeamDto.memberIds && createTeamDto.memberIds.length > 0) {
        for (const memberId of createTeamDto.memberIds) {
          // Skip if trying to add the creator again
          if (memberId === userId) continue;

          const teamMember = this.teamMemberRepository.create({
            teamId: savedTeam.id,
            userId: memberId,
            role: TeamMemberRole.MEMBER,
          });

          await queryRunner.manager.save(teamMember);
        }
      }

      await queryRunner.commitTransaction();

      return this.findOne(savedTeam.id, userId, userRole);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(userId: number, userRole: string): Promise<Team[]> {
    // HR_ADMIN can see all teams
    if (userRole === UserRole.HR_ADMIN) {
      return this.teamRepository.find({
        relations: ['members', 'members.user', 'createdBy'],
      });
    }

    // MANAGER and EMPLOYEE can only see their own teams
    const teamMembers = await this.teamMemberRepository.find({
      where: { userId },
      relations: ['team', 'team.members', 'team.members.user', 'team.createdBy']
    });

    return teamMembers.map(member => member.team);
  }

  async findOne(id: number, userId: number, userRole: string): Promise<Team> {
    const team = await this.teamRepository.findOne({
      where: { id },
      relations: ['members', 'members.user', 'createdBy'],
    });

    if (!team) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }

    // Check if user has permission to view this team
    if (userRole !== UserRole.HR_ADMIN) {
      const isMember = team.members.some(member => member.userId === userId);
      if (!isMember) {
        throw new ForbiddenException('You do not have permission to view this team');
      }
    }

    return team;
  }

  async update(id: number, updateTeamDto: UpdateTeamDto, userId: number, userRole: string): Promise<Team> {
    const team = await this.findOne(id, userId, userRole);

    // Check if user has permission to update this team
    if (userRole !== UserRole.HR_ADMIN) {
      const isTeamLead = team.members.some(
        member => member.userId === userId && member.role === TeamMemberRole.TEAM_LEAD
      );
      if (!isTeamLead) {
        throw new ForbiddenException('Only team leads and HR admins can update teams');
      }
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update basic team fields
      if (updateTeamDto.name) team.name = updateTeamDto.name;
      if (updateTeamDto.description !== undefined) team.description = updateTeamDto.description;

      await queryRunner.manager.save(team);

      // Add new members if specified
      if (updateTeamDto.addMemberIds && updateTeamDto.addMemberIds.length > 0) {
        for (const memberId of updateTeamDto.addMemberIds) {
          // Check if user is already a member
          const existingMember = team.members.find(member => member.userId === memberId);
          if (existingMember) continue;

          const teamMember = this.teamMemberRepository.create({
            teamId: team.id,
            userId: memberId,
            role: TeamMemberRole.MEMBER,
          });

          await queryRunner.manager.save(teamMember);
        }
      }

      // Remove members if specified
      if (updateTeamDto.removeMemberIds && updateTeamDto.removeMemberIds.length > 0) {
        for (const memberId of updateTeamDto.removeMemberIds) {
          // Find the member to remove
          const memberToRemove = team.members.find(member => member.userId === memberId);
          if (!memberToRemove) continue;

          // Prevent removing the last team lead
          if (memberToRemove.role === TeamMemberRole.TEAM_LEAD) {
            const teamLeadCount = team.members.filter(
              member => member.role === TeamMemberRole.TEAM_LEAD
            ).length;

            if (teamLeadCount <= 1) {
              throw new ForbiddenException(
                'Cannot remove the last team lead. Please assign another team lead first.'
              );
            }
          }

          await queryRunner.manager.remove(memberToRemove);
        }
      }

      await queryRunner.commitTransaction();

      return this.findOne(team.id, userId, userRole);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number, userId: number, userRole: string): Promise<void> {
    const team = await this.findOne(id, userId, userRole);

    // Only HR_ADMIN can delete teams
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR admins can delete teams');
    }

    // First remove all team members
    await this.teamMemberRepository.delete({ teamId: id });

    // Then remove the team
    await this.teamRepository.remove(team);
  }

  async addMember(
    teamId: number,
    addMemberDto: AddTeamMemberDto,
    userId: number,
    userRole: string,
  ): Promise<TeamMember> {
    const team = await this.findOne(teamId, userId, userRole);

    // Check if user has permission
    if (userRole !== UserRole.HR_ADMIN) {
      const isTeamLead = team.members.some(
        member => member.userId === userId && member.role === TeamMemberRole.TEAM_LEAD
      );
      if (!isTeamLead) {
        throw new ForbiddenException('Only team leads and HR admins can add team members');
      }
    }

    // Check if user is already a member
    const existingMember = team.members.find(member => member.userId === addMemberDto.userId);
    if (existingMember) {
      throw new ConflictException('User is already a member of this team');
    }

    // Create and save new team member
    const teamMember = this.teamMemberRepository.create({
      teamId: team.id,
      userId: addMemberDto.userId,
      role: addMemberDto.role || TeamMemberRole.MEMBER,
    });

    return this.teamMemberRepository.save(teamMember);
  }

  async updateMemberRole(
    teamId: number,
    memberId: number,
    updateRoleDto: UpdateTeamMemberRoleDto,
    userId: number,
    userRole: string,
  ): Promise<TeamMember> {
    const team = await this.findOne(teamId, userId, userRole);

    // Check if user has permission
    if (userRole !== UserRole.HR_ADMIN) {
      const isTeamLead = team.members.some(
        member => member.userId === userId && member.role === TeamMemberRole.TEAM_LEAD
      );
      if (!isTeamLead) {
        throw new ForbiddenException('Only team leads and HR admins can update member roles');
      }
    }

    // Find the member to update
    const memberToUpdate = await this.teamMemberRepository.findOne({
      where: { teamId, userId: memberId }
    });

    if (!memberToUpdate) {
      throw new NotFoundException(`User with ID ${memberId} is not a member of this team`);
    }

    // If changing from TEAM_LEAD to another role, ensure there's at least one other team lead
    if (memberToUpdate.role === TeamMemberRole.TEAM_LEAD && 
        updateRoleDto.role !== TeamMemberRole.TEAM_LEAD) {
      
      const teamLeadCount = team.members.filter(
        member => member.role === TeamMemberRole.TEAM_LEAD
      ).length;

      if (teamLeadCount <= 1) {
        throw new ForbiddenException(
          'Cannot demote the last team lead. Please assign another team lead first.'
        );
      }
    }

    // Update and save
    memberToUpdate.role = updateRoleDto.role;
    return this.teamMemberRepository.save(memberToUpdate);
  }

  async removeMember(
    teamId: number,
    memberId: number,
    userId: number,
    userRole: string,
  ): Promise<void> {
    const team = await this.findOne(teamId, userId, userRole);

    // Check if user has permission
    if (userRole !== UserRole.HR_ADMIN) {
      const isTeamLead = team.members.some(
        member => member.userId === userId && member.role === TeamMemberRole.TEAM_LEAD
      );
      if (!isTeamLead) {
        throw new ForbiddenException('Only team leads and HR admins can remove team members');
      }
    }

    // Find the member to remove
    const memberToRemove = team.members.find(member => member.userId === memberId);
    
    if (!memberToRemove) {
      throw new NotFoundException(`User with ID ${memberId} is not a member of this team`);
    }

    // If removing a TEAM_LEAD, ensure there's at least one other team lead
    if (memberToRemove.role === TeamMemberRole.TEAM_LEAD) {
      const teamLeadCount = team.members.filter(
        member => member.role === TeamMemberRole.TEAM_LEAD
      ).length;

      if (teamLeadCount <= 1) {
        throw new ForbiddenException(
          'Cannot remove the last team lead. Please assign another team lead first.'
        );
      }
    }

    // Remove the member
    await this.teamMemberRepository.remove(memberToRemove);
  }

  async getTeamMembers(
    teamId: number,
    userId: number,
    userRole: string,
  ): Promise<TeamMember[]> {
    const team = await this.findOne(teamId, userId, userRole);
    return team.members;
  }
}
