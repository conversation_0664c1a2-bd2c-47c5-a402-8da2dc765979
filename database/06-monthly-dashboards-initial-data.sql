-- Monthly Dashboards Initial Data
-- This file populates the initial KPI definitions and sample data

USE ehrx;

-- =====================================================
-- INSERT DEFAULT KPI DEFINITIONS
-- =====================================================

INSERT INTO monthly_dashboard_kpis (
  name, display_name, description, target_value, unit, calculation_method, help_text, sort_order,
  traffic_light_green_min, traffic_light_green_max, traffic_light_yellow_min, traffic_light_yellow_max,
  special_rules
) VALUES

-- 1. FTE (Full-Time Equivalent)
('FTE', 'Full-Time Equivalent', 'Number of full-time equivalent employees in the team', NULL, 'count', 'auto_fte', 
'This value is automatically calculated based on the number of active team members in the database. No manual input required.', 
1, -5.0, 5.0, -10.0, 10.0, 
'{"auto_calculation": true, "source": "team_members_count", "exclude_statuses": ["resigned", "terminated"]}'),

-- 2. Date of Completion
('COMPLETION_DATE', 'Dashboard Completion Date', 'Date when the team manager completed the monthly dashboard', NULL, 'date', 'manual',
'Enter the date when you completed filling out this monthly dashboard. This should be around the 14th of the month.',
2, 0, 0, 0, 0,
'{"required": true, "validation": "date", "suggested_day": 14}'),

-- 3. Attrition
('ATTRITION', 'Attrition Rate', 'Number of resignations in the month and annual attrition rate', 0, 'count', 'manual',
'Enter the number of team members who resigned this month. The system will calculate the annual attrition rate automatically. Find this data by checking team member status changes.',
3, -5.0, 5.0, -10.0, 10.0,
'{"has_annual_calculation": true, "target_annual_rate": 10, "alert_threshold": 2}'),

-- 4. SLA (Service Level Agreement)
('SLA', 'SLA Performance', 'Team SLA percentage performance', 100, '%', 'manual',
'Enter the team SLA percentage from the BI report: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Csla%20evidence%20-%20aeven%20-%20newco.qvw. Target is 100%. If no data available, leave empty and it will show as N/A.',
4, -5.0, 5.0, -10.0, 10.0,
'{"bi_source": "http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Csla%20evidence%20-%20aeven%20-%20newco.qvw", "target": 100}'),

-- 5. Utilization
('UTILIZATION', 'Billable Utilization', 'Team billable utilization percentage', NULL, '%', 'manual',
'Enter the team billable utilization percentage from the BI report: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cbillable%20utilization%20-%20newco.qvw. Expected utilization varies by team and can be set manually.',
5, -5.0, 5.0, -10.0, 10.0,
'{"bi_source": "http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cbillable%20utilization%20-%20newco.qvw", "team_specific_target": true}'),

-- 6. Time Registration
('TIME_REGISTRATION', 'Time Registration Compliance', 'Percentage of timely time registration for the team', 100, '%', 'manual',
'Enter the team KPI percentage for timely time registration from the BI report: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cax%20time%20registration%20overview.qvw. Target is 100%.',
6, -5.0, 5.0, -10.0, 10.0,
'{"bi_source": "http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cax%20time%20registration%20overview.qvw", "target": 100}'),

-- 7. Compliance
('COMPLIANCE', 'Non-Compliance Count', 'Number of non-compliance issues in the team', 0, 'count', 'manual',
'Enter the number of non-compliance issues for your team this month. This data is provided by the compliance team via email. Expected value is 0. Even 1 non-compliance will show as red.',
7, 0, 0, 0, 0,
'{"target": 0, "red_threshold": 1, "special_traffic_light": true, "data_source": "compliance_team_email"}'),

-- 8. AB (Annual Budget vs Actual)
('AB', 'Annual Budget Performance', 'Team monthly financial result: Full Year 2025 vs. AB', 0, 'currency', 'manual',
'Enter the team monthly financial result as Full Year 2025 vs. AB from the BI report: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Ccost%20-%20newco.qvw. This shows budget variance.',
8, -5.0, 5.0, -10.0, 10.0,
'{"bi_source": "http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Ccost%20-%20newco.qvw", "format": "currency", "variance_calculation": true}'),

-- 9. PTO (Paid Time Off)
('PTO', 'Vacation and Sick Leaves', 'Number of vacation and sick leaves taken vs expected YTD', NULL, 'days', 'manual',
'Enter the number of vacation and sick leaves taken by your team this month. Also set the expected annual leaves per employee. The system will calculate how far you are from 75% of expected Year-To-Date leaves. Data source: Finance email.',
9, -5.0, 5.0, -10.0, 10.0,
'{"has_ytd_calculation": true, "ytd_target_percentage": 75, "configurable_annual_target": true, "data_source": "finance_email"}'),

-- 10. RTO (Return to Office)
('RTO', 'Return to Office Compliance', 'Percentage of expected office attendance fulfillment', NULL, '%', 'manual',
'Enter the raw office attendance percentage from Facility team email, then enter the number of employee leaves (vacation, sick, etc.) to adjust the calculation. System will calculate adjusted percentage accounting for legitimate absences.',
10, -5.0, 5.0, -10.0, 10.0,
'{"has_leave_adjustment": true, "default_expectation": "1_day_per_week_employee_2_days_manager", "data_source": "facility_email", "leave_adjustment_calculation": true}');

-- =====================================================
-- INSERT SAMPLE TEAM-SPECIFIC TARGETS
-- =====================================================

-- Sample utilization targets for different teams (using actual organizational unit IDs)
INSERT INTO monthly_dashboard_team_targets (
  organizational_unit_id, kpi_id, target_value, effective_from, notes, created_by_user_id
) VALUES
-- Frontend Development Team (ID: 15)
(15, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 85.0, '2025-01-01', 'Frontend Development team target utilization', 1),
-- Backend Development Team (ID: 16)
(16, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 87.0, '2025-01-01', 'Backend Development team target utilization', 1),
-- Mobile Development Team (ID: 17)
(17, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 80.0, '2025-01-01', 'Mobile Development team target utilization', 1),
-- Data Engineering Team (ID: 19)
(19, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 82.0, '2025-01-01', 'Data Engineering team target utilization', 1),
-- Level 1 Support Team (ID: 35)
(35, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 75.0, '2025-01-01', 'Level 1 Support team target utilization', 1),
-- Business Consulting Team (ID: 27)
(27, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 90.0, '2025-01-01', 'Process Optimization team target utilization', 1);

-- Sample PTO annual targets per employee for different teams
INSERT INTO monthly_dashboard_team_targets (
  organizational_unit_id, kpi_id, target_value, effective_from, notes, created_by_user_id
) VALUES
(15, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO'), 25.0, '2025-01-01', 'Annual PTO days per employee - Frontend Development', 1),
(16, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO'), 25.0, '2025-01-01', 'Annual PTO days per employee - Backend Development', 1),
(17, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO'), 25.0, '2025-01-01', 'Annual PTO days per employee - Mobile Development', 1),
(19, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO'), 25.0, '2025-01-01', 'Annual PTO days per employee - Data Engineering', 1),
(35, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO'), 25.0, '2025-01-01', 'Annual PTO days per employee - Level 1 Support', 1),
(27, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO'), 25.0, '2025-01-01', 'Annual PTO days per employee - Process Optimization', 1);

-- Sample RTO expectations for different teams
INSERT INTO monthly_dashboard_team_targets (
  organizational_unit_id, kpi_id, target_value, effective_from, notes, created_by_user_id
) VALUES
(15, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO'), 80.0, '2025-01-01', 'Expected office attendance percentage - Frontend Development', 1),
(16, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO'), 80.0, '2025-01-01', 'Expected office attendance percentage - Backend Development', 1),
(17, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO'), 85.0, '2025-01-01', 'Expected office attendance percentage - Mobile Development', 1),
(19, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO'), 75.0, '2025-01-01', 'Expected office attendance percentage - Data Engineering', 1),
(35, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO'), 90.0, '2025-01-01', 'Expected office attendance percentage - Level 1 Support', 1),
(27, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO'), 85.0, '2025-01-01', 'Expected office attendance percentage - Process Optimization', 1);

-- =====================================================
-- SAMPLE DASHBOARD SUBMISSIONS (for testing)
-- =====================================================

-- Sample submission for January 2025 (using actual team IDs and user IDs)
INSERT INTO monthly_dashboard_submissions (
  organizational_unit_id, submitted_by_user_id, submission_month, submission_year,
  completion_date, status, notes
) VALUES
(15, 5, 1, 2025, '2025-01-14', 'submitted', 'January 2025 dashboard - Frontend Development Team'),
(16, 6, 1, 2025, '2025-01-15', 'submitted', 'January 2025 dashboard - Backend Development Team'),
(35, 1, 1, 2025, '2025-01-13', 'approved', 'January 2025 dashboard - Level 1 Support Team');

-- Sample KPI values for the submissions
INSERT INTO monthly_dashboard_kpi_values (
  submission_id, kpi_id, value, target_value, traffic_light_status, notes
) VALUES
-- Frontend Development Team (submission_id: 1) - January 2025
(1, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'ATTRITION'), 1, 0, 'yellow', '1 resignation this month'),
(1, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'SLA'), 98.5, 100, 'green', 'Good SLA performance'),
(1, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 87.0, 85.0, 'green', 'Above target utilization'),
(1, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'TIME_REGISTRATION'), 95.0, 100, 'green', 'Good time registration compliance'),
(1, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'COMPLIANCE'), 0, 0, 'green', 'No compliance issues'),

-- Backend Development Team (submission_id: 2) - January 2025
(2, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'ATTRITION'), 0, 0, 'green', 'No resignations'),
(2, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'SLA'), 92.0, 100, 'yellow', 'SLA slightly below target'),
(2, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 89.0, 87.0, 'green', 'Good utilization'),
(2, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'TIME_REGISTRATION'), 88.0, 100, 'red', 'Time registration needs improvement'),
(2, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'COMPLIANCE'), 0, 0, 'green', 'No compliance issues'),

-- Level 1 Support Team (submission_id: 3) - January 2025
(3, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'ATTRITION'), 0, 0, 'green', 'No resignations'),
(3, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'SLA'), 94.0, 100, 'green', 'Good SLA performance'),
(3, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION'), 77.0, 75.0, 'green', 'Above target utilization'),
(3, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'TIME_REGISTRATION'), 92.0, 100, 'yellow', 'Time registration could be better'),
(3, (SELECT id FROM monthly_dashboard_kpis WHERE name = 'COMPLIANCE'), 1, 0, 'red', '1 minor compliance issue identified');

-- Display confirmation
SELECT 'Monthly Dashboard schema and initial data created successfully' AS status;
