import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Api as ApiIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Description as DocsIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  Key as KeyIcon,
  Timer as TimerIcon,
  Shield as ShieldIcon
} from '@mui/icons-material';
import { ApiService } from '../../services/api';

interface ApiEndpoint {
  id: string;
  path: string;
  method: string;
  description: string;
  isEnabled: boolean;
  requiresAuth: boolean;
  rateLimit: number;
  lastUsed?: string;
}

interface ApiKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  isActive: boolean;
  expiresAt?: string;
  createdAt: string;
}

interface RateLimitConfig {
  id: string;
  endpoint: string;
  requestsPerMinute: number;
  requestsPerHour: number;
  isEnabled: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`api-config-tabpanel-${index}`}
      aria-labelledby={`api-config-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ApiConfiguration: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [endpoints, setEndpoints] = useState<ApiEndpoint[]>([]);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [rateLimits, setRateLimits] = useState<RateLimitConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal states
  const [endpointDialog, setEndpointDialog] = useState(false);
  const [editingEndpoint, setEditingEndpoint] = useState<ApiEndpoint | null>(null);
  const [keyDialog, setKeyDialog] = useState(false);
  const [editingKey, setEditingKey] = useState<ApiKey | null>(null);

  useEffect(() => {
    fetchApiConfiguration();
  }, []);

  const fetchApiConfiguration = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch real API endpoints from database
      const endpointsResponse = await ApiService.getApiEndpoints();
      const endpointsData = Array.isArray(endpointsResponse) ? endpointsResponse : [];

      // Transform API response to match ApiEndpoint interface
      const transformedEndpoints: ApiEndpoint[] = endpointsData.map((endpoint: any) => ({
        id: endpoint.id?.toString() || '',
        path: endpoint.path || endpoint.endpoint_path || '',
        method: endpoint.method || endpoint.http_method || 'GET',
        description: endpoint.description || '',
        isEnabled: endpoint.isEnabled !== undefined ? endpoint.isEnabled : endpoint.is_enabled !== undefined ? endpoint.is_enabled : true,
        requiresAuth: endpoint.requiresAuth !== undefined ? endpoint.requiresAuth : endpoint.requires_auth !== undefined ? endpoint.requires_auth : true,
        rateLimit: endpoint.rateLimit || endpoint.rate_limit || 100,
        lastUsed: endpoint.lastUsed || endpoint.last_used
      }));

      // Fetch real API keys from database
      const keysResponse = await ApiService.getApiKeys();
      const keysData = Array.isArray(keysResponse) ? keysResponse : [];

      // Transform API response to match ApiKey interface
      const transformedKeys: ApiKey[] = keysData.map((key: any) => ({
        id: key.id?.toString() || '',
        name: key.name || key.key_name || '',
        key: key.key || key.api_key || '',
        permissions: key.permissions || [],
        isActive: key.isActive !== undefined ? key.isActive : key.is_active !== undefined ? key.is_active : true,
        expiresAt: key.expiresAt || key.expires_at,
        createdAt: key.createdAt || key.created_at || new Date().toISOString()
      }));

      // Fetch real rate limit configurations from database
      const rateLimitsResponse = await ApiService.getRateLimitConfigs();
      const rateLimitsData = Array.isArray(rateLimitsResponse) ? rateLimitsResponse : [];

      // Transform API response to match RateLimitConfig interface
      const transformedRateLimits: RateLimitConfig[] = rateLimitsData.map((rateLimit: any) => ({
        id: rateLimit.id?.toString() || '',
        endpoint: rateLimit.endpoint || rateLimit.endpoint_pattern || '',
        requestsPerMinute: rateLimit.requestsPerMinute || rateLimit.requests_per_minute || 100,
        requestsPerHour: rateLimit.requestsPerHour || rateLimit.requests_per_hour || 1000,
        isEnabled: rateLimit.isEnabled !== undefined ? rateLimit.isEnabled : rateLimit.is_enabled !== undefined ? rateLimit.is_enabled : true
      }));

      setEndpoints(transformedEndpoints);
      setApiKeys(transformedKeys);
      setRateLimits(transformedRateLimits);
    } catch (err) {
      setError('Error fetching API configuration from database');
      console.error('Error fetching API config:', err);

      // Fallback to empty arrays on error
      setEndpoints([]);
      setApiKeys([]);
      setRateLimits([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditEndpoint = (endpoint: ApiEndpoint) => {
    setEditingEndpoint({ ...endpoint });
    setEndpointDialog(true);
  };

  const handleSaveEndpoint = async () => {
    if (!editingEndpoint) return;

    try {
      setIsLoading(true);

      // Update endpoint configuration via API
      const endpointData = {
        path: editingEndpoint.path,
        method: editingEndpoint.method,
        description: editingEndpoint.description,
        isEnabled: editingEndpoint.isEnabled,
        requiresAuth: editingEndpoint.requiresAuth,
        rateLimit: editingEndpoint.rateLimit
      };

      await ApiService.updateApiEndpoint(editingEndpoint.id, endpointData);
      setSuccess('Endpoint configuration updated successfully');

      // Refresh API configuration
      await fetchApiConfiguration();

      setEndpointDialog(false);
      setEditingEndpoint(null);
    } catch (err) {
      setError(`Error saving endpoint configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error('Error saving endpoint:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditApiKey = (apiKey: ApiKey) => {
    setEditingKey({ ...apiKey });
    setKeyDialog(true);
  };

  const handleSaveApiKey = async () => {
    if (!editingKey) return;

    try {
      setIsLoading(true);

      // Update API key via API
      const keyData = {
        name: editingKey.name,
        permissions: editingKey.permissions,
        isActive: editingKey.isActive,
        expiresAt: editingKey.expiresAt
      };

      await ApiService.updateApiKey(editingKey.id, keyData);
      setSuccess('API key updated successfully');

      // Refresh API configuration
      await fetchApiConfiguration();

      setKeyDialog(false);
      setEditingKey(null);
    } catch (err) {
      setError(`Error saving API key: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error('Error saving API key:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const renderEndpointsTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
          <ApiIcon sx={{ mr: 1 }} />
          API Endpoints
        </Typography>
        <Box>
          <Tooltip title="Refresh Endpoints">
            <IconButton onClick={fetchApiConfiguration}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{ ml: 1 }}
          >
            Add Endpoint
          </Button>
        </Box>
      </Box>

      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Path</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Method</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Auth Required</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Rate Limit</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Last Used</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {endpoints.map((endpoint) => (
              <TableRow key={endpoint.id} hover>
                <TableCell sx={{ fontFamily: 'monospace' }}>{endpoint.path}</TableCell>
                <TableCell>
                  <Chip
                    label={endpoint.method}
                    size="small"
                    color={
                      endpoint.method === 'GET' ? 'primary' :
                        endpoint.method === 'POST' ? 'success' :
                          endpoint.method === 'PUT' ? 'warning' :
                            endpoint.method === 'DELETE' ? 'error' : 'default'
                    }
                  />
                </TableCell>
                <TableCell>{endpoint.description}</TableCell>
                <TableCell>
                  <Chip
                    label={endpoint.isEnabled ? 'Enabled' : 'Disabled'}
                    size="small"
                    color={endpoint.isEnabled ? 'success' : 'error'}
                    icon={endpoint.isEnabled ? <CheckIcon /> : <ErrorIcon />}
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={endpoint.requiresAuth ? 'Required' : 'Public'}
                    size="small"
                    color={endpoint.requiresAuth ? 'warning' : 'default'}
                    icon={<ShieldIcon />}
                  />
                </TableCell>
                <TableCell>{endpoint.rateLimit}/min</TableCell>
                <TableCell>
                  {endpoint.lastUsed ? new Date(endpoint.lastUsed).toLocaleString() : 'Never'}
                </TableCell>
                <TableCell>
                  <Tooltip title="Edit Endpoint">
                    <IconButton size="small" onClick={() => handleEditEndpoint(endpoint)}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderApiKeysTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
          <KeyIcon sx={{ mr: 1 }} />
          API Keys
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
        >
          Generate New Key
        </Button>
      </Box>

      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Name</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Key</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Permissions</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Expires</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Created</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {apiKeys.map((apiKey) => (
              <TableRow key={apiKey.id} hover>
                <TableCell sx={{ fontWeight: 'medium' }}>{apiKey.name}</TableCell>
                <TableCell sx={{ fontFamily: 'monospace' }}>{apiKey.key}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                    {apiKey.permissions.map((permission) => (
                      <Chip key={permission} label={permission} size="small" variant="outlined" />
                    ))}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={apiKey.isActive ? 'Active' : 'Inactive'}
                    size="small"
                    color={apiKey.isActive ? 'success' : 'error'}
                  />
                </TableCell>
                <TableCell>
                  {apiKey.expiresAt ? new Date(apiKey.expiresAt).toLocaleDateString() : 'Never'}
                </TableCell>
                <TableCell>
                  {new Date(apiKey.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Tooltip title="Edit API Key">
                    <IconButton size="small" onClick={() => handleEditApiKey(apiKey)}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete API Key">
                    <IconButton size="small" color="error">
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderRateLimitingTab = () => (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <TimerIcon sx={{ mr: 1 }} />
        Rate Limiting Configuration
      </Typography>

      <Grid container spacing={3}>
        {rateLimits.map((rateLimit) => (
          <Grid item xs={12} md={6} key={rateLimit.id}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" sx={{ fontWeight: 'medium', fontFamily: 'monospace' }}>
                  {rateLimit.endpoint}
                </Typography>
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      {rateLimit.requestsPerMinute} requests/minute
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {rateLimit.requestsPerHour} requests/hour
                    </Typography>
                  </Box>
                  <Chip
                    label={rateLimit.isEnabled ? 'Enabled' : 'Disabled'}
                    size="small"
                    color={rateLimit.isEnabled ? 'success' : 'default'}
                  />
                </Box>
                <Box sx={{ mt: 2 }}>
                  <Button size="small" variant="outlined">
                    Edit Limits
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderDocumentationTab = () => (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <DocsIcon sx={{ mr: 1 }} />
        API Documentation
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Available Endpoints
              </Typography>

              {endpoints.map((endpoint) => (
                <Accordion key={endpoint.id}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Chip
                        label={endpoint.method}
                        size="small"
                        color={
                          endpoint.method === 'GET' ? 'primary' :
                            endpoint.method === 'POST' ? 'success' :
                              endpoint.method === 'PUT' ? 'warning' :
                                endpoint.method === 'DELETE' ? 'error' : 'default'
                        }
                      />
                      <Typography sx={{ fontFamily: 'monospace' }}>
                        {endpoint.path}
                      </Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {endpoint.description}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip
                        label={endpoint.requiresAuth ? 'Authentication Required' : 'Public Endpoint'}
                        size="small"
                        color={endpoint.requiresAuth ? 'warning' : 'default'}
                      />
                      <Chip
                        label={`Rate Limit: ${endpoint.rateLimit}/min`}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Example usage and parameters would be documented here.
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Links
              </Typography>
              <List>
                <ListItem>
                  <ListItemText
                    primary="API Reference"
                    secondary="Complete API documentation"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Authentication Guide"
                    secondary="How to authenticate API requests"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Rate Limiting"
                    secondary="Understanding rate limits"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Error Codes"
                    secondary="Common error responses"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
        API Configuration
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Configure API endpoints, authentication, rate limiting, and documentation
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Endpoints" />
        <Tab label="API Keys" />
        <Tab label="Rate Limiting" />
        <Tab label="Documentation" />
      </Tabs>

      <TabPanel value={selectedTab} index={0}>
        {renderEndpointsTab()}
      </TabPanel>

      <TabPanel value={selectedTab} index={1}>
        {renderApiKeysTab()}
      </TabPanel>

      <TabPanel value={selectedTab} index={2}>
        {renderRateLimitingTab()}
      </TabPanel>

      <TabPanel value={selectedTab} index={3}>
        {renderDocumentationTab()}
      </TabPanel>

      {/* Endpoint Edit Dialog */}
      <Dialog open={endpointDialog} onClose={() => setEndpointDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Edit Endpoint Configuration
        </DialogTitle>
        <DialogContent>
          {editingEndpoint && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Path"
                  value={editingEndpoint.path}
                  onChange={(e) => setEditingEndpoint({
                    ...editingEndpoint,
                    path: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Method</InputLabel>
                  <Select
                    value={editingEndpoint.method}
                    label="Method"
                    onChange={(e) => setEditingEndpoint({
                      ...editingEndpoint,
                      method: e.target.value
                    })}
                  >
                    <MenuItem value="GET">GET</MenuItem>
                    <MenuItem value="POST">POST</MenuItem>
                    <MenuItem value="PUT">PUT</MenuItem>
                    <MenuItem value="DELETE">DELETE</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Rate Limit (per minute)"
                  type="number"
                  value={editingEndpoint.rateLimit}
                  onChange={(e) => setEditingEndpoint({
                    ...editingEndpoint,
                    rateLimit: parseInt(e.target.value)
                  })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={editingEndpoint.description}
                  onChange={(e) => setEditingEndpoint({
                    ...editingEndpoint,
                    description: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={editingEndpoint.isEnabled}
                      onChange={(e) => setEditingEndpoint({
                        ...editingEndpoint,
                        isEnabled: e.target.checked
                      })}
                    />
                  }
                  label="Endpoint Enabled"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={editingEndpoint.requiresAuth}
                      onChange={(e) => setEditingEndpoint({
                        ...editingEndpoint,
                        requiresAuth: e.target.checked
                      })}
                    />
                  }
                  label="Requires Authentication"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEndpointDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveEndpoint} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* API Key Edit Dialog */}
      <Dialog open={keyDialog} onClose={() => setKeyDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Edit API Key
        </DialogTitle>
        <DialogContent>
          {editingKey && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Name"
                  value={editingKey.name}
                  onChange={(e) => setEditingKey({
                    ...editingKey,
                    name: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={editingKey.isActive}
                      onChange={(e) => setEditingKey({
                        ...editingKey,
                        isActive: e.target.checked
                      })}
                    />
                  }
                  label="API Key Active"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setKeyDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveApiKey} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ApiConfiguration;
