import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, IsISO8601, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { AIInitiativeStatus, AIInitiativeCategory } from '../../entities/ai-initiative.entity';

export class CreateAIInitiativeDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsEnum(AIInitiativeStatus)
  @IsOptional()
  status?: AIInitiativeStatus;

  @IsEnum(AIInitiativeCategory)
  @IsOptional()
  category?: AIInitiativeCategory;

  @IsString()
  @IsOptional()
  businessCase?: string;

  @IsString()
  @IsOptional()
  expectedBenefits?: string;

  @IsString()
  @IsOptional()
  implementationChallenges?: string;

  @IsString()
  @IsOptional()
  dataRequirements?: string;

  @IsISO8601()
  @IsOptional()
  targetCompletionDate?: string;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;

  @IsNumber()
  @IsOptional()
  assessmentId?: number;
}
