import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardContent,
  Grid,
  Typography,
  Paper,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import {
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Business as BusinessIcon,
  Psychology as PsychologyIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { ApiService } from '../../services/api';

interface RiskDashboardProps {
  userId?: number;
  teamView?: boolean;
}

const RiskDashboard: React.FC<RiskDashboardProps> = ({ userId, teamView = false }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [riskData, setRiskData] = useState<any>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<number | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [selectedDimension, setSelectedDimension] = useState<string>('all');

  useEffect(() => {
    if (userId) {
      loadRiskData();
    }
  }, [userId]);

  const loadRiskData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ApiService.post('/ai/predictive/multi-dimensional-risk', {
        userId: userId || 1, // Default to user 1 for demo
      });

      if (response.data) {
        setRiskData(response.data);
      } else {
        setError('Failed to load risk data');
      }
    } catch (err) {
      console.error('Error loading risk data:', err);
      setError('Failed to load risk data');
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return '#f44336';
      case 'medium': return '#ff9800';
      case 'low': return '#4caf50';
      default: return '#9e9e9e';
    }
  };

  const getDimensionIcon = (dimension: string) => {
    switch (dimension) {
      case 'performance': return <AssessmentIcon />;
      case 'engagement': return <PsychologyIcon />;
      case 'behavioral': return <PersonIcon />;
      case 'career': return <StarIcon />;
      case 'team': return <GroupIcon />;
      case 'organizational': return <BusinessIcon />;
      default: return <WarningIcon />;
    }
  };

  const formatDimensionName = (dimension: string) => {
    return dimension.charAt(0).toUpperCase() + dimension.slice(1);
  };

  if (loading && !riskData) {
    return (
      <Card>
        <CardHeader title="Multi-Dimensional Risk Analysis" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      <Card>
        <CardHeader
          title="Multi-Dimensional Risk Analysis"
          subheader="Advanced risk scoring across multiple dimensions with AI-powered insights"
          action={
            <Box display="flex" gap={1}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Dimension</InputLabel>
                <Select
                  value={selectedDimension}
                  onChange={(e) => setSelectedDimension(e.target.value)}
                  label="Dimension"
                >
                  <MenuItem value="all">All Dimensions</MenuItem>
                  <MenuItem value="performance">Performance</MenuItem>
                  <MenuItem value="engagement">Engagement</MenuItem>
                  <MenuItem value="behavioral">Behavioral</MenuItem>
                  <MenuItem value="career">Career</MenuItem>
                  <MenuItem value="team">Team</MenuItem>
                  <MenuItem value="organizational">Organizational</MenuItem>
                </Select>
              </FormControl>
              <Button
                variant="outlined"
                startIcon={<ScheduleIcon />}
                onClick={loadRiskData}
                disabled={loading}
                size="small"
              >
                Refresh
              </Button>
            </Box>
          }
        />
        <CardContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {riskData && (
            <>
              {/* Overall Risk Summary */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 3, textAlign: 'center', bgcolor: getRiskColor(riskData.riskLevel) + '20' }}>
                    <Typography variant="h3" sx={{ color: getRiskColor(riskData.riskLevel), fontWeight: 'bold' }}>
                      {(riskData.compositeScore * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="h6" gutterBottom>
                      Overall Risk Score
                    </Typography>
                    <Chip
                      label={riskData.riskLevel.toUpperCase()}
                      color={riskData.riskLevel === 'high' ? 'error' : riskData.riskLevel === 'medium' ? 'warning' : 'success'}
                      sx={{ fontWeight: 'bold' }}
                    />
                  </Paper>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {(riskData.confidence * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="h6" gutterBottom>
                      Confidence Level
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Range: {(riskData.confidenceInterval[0] * 100).toFixed(0)}% - {(riskData.confidenceInterval[1] * 100).toFixed(0)}%
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary">
                      {riskData.riskFactors.length}
                    </Typography>
                    <Typography variant="h6" gutterBottom>
                      Risk Factors
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Identified across all dimensions
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              {/* Risk Dimensions */}
              <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Risk by Dimension
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                {Object.entries(riskData.dimensions).map(([dimension, data]: [string, any]) => (
                  <Grid item xs={12} sm={6} md={4} key={dimension}>
                    <Paper sx={{ p: 2 }}>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        {getDimensionIcon(dimension)}
                        <Typography variant="subtitle1" fontWeight="medium">
                          {formatDimensionName(dimension)}
                        </Typography>
                      </Box>
                      <Box sx={{ mb: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={data.score * 100}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'grey.200',
                            '& .MuiLinearProgress-bar': {
                              bgcolor: getRiskColor(
                                data.score >= 0.7 ? 'high' : data.score >= 0.4 ? 'medium' : 'low'
                              ),
                            },
                          }}
                        />
                      </Box>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="body2" fontWeight="medium">
                          {(data.score * 100).toFixed(0)}% Risk
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {(data.confidence * 100).toFixed(0)}% confidence
                        </Typography>
                      </Box>
                      {data.factors.length > 0 && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Key factor: {data.factors[0]}
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                ))}
              </Grid>

              {/* Risk Factors */}
              <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Top Risk Factors
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Dimension</TableCell>
                      <TableCell>Risk Factor</TableCell>
                      <TableCell>Impact</TableCell>
                      <TableCell>Confidence</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {riskData.riskFactors.slice(0, 8).map((factor: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getDimensionIcon(factor.dimension)}
                            <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                              {factor.dimension}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {factor.factor}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <LinearProgress
                            variant="determinate"
                            value={factor.impact * 100}
                            sx={{
                              width: 80,
                              height: 6,
                              borderRadius: 3,
                              bgcolor: 'grey.200',
                              '& .MuiLinearProgress-bar': {
                                bgcolor: getRiskColor(
                                  factor.impact >= 0.7 ? 'high' : factor.impact >= 0.4 ? 'medium' : 'low'
                                ),
                              },
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {(factor.confidence * 100).toFixed(0)}%
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Mitigation Strategies */}
              <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Recommended Actions
              </Typography>
              <Grid container spacing={2}>
                {riskData.mitigationStrategies.map((strategy: any, index: number) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Paper sx={{ p: 2 }}>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Chip
                          label={strategy.priority}
                          size="small"
                          color={
                            strategy.priority === 'urgent' ? 'error' :
                              strategy.priority === 'high' ? 'warning' : 'primary'
                          }
                        />
                        <Typography variant="subtitle2">
                          {strategy.timeline}
                        </Typography>
                      </Box>
                      <Typography variant="body2" gutterBottom>
                        {strategy.action}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Owner: {strategy.owner}
                      </Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>

              {/* Model Information */}
              <Alert severity="info" sx={{ mt: 3 }}>
                <Typography variant="subtitle2">Analysis Details</Typography>
                <Typography variant="body2">
                  Risk analysis completed using multi-dimensional ensemble model.
                  Last updated: {new Date(riskData.lastUpdated).toLocaleString()}.
                  Weights are dynamically adjusted based on data quality and relevance.
                </Typography>
              </Alert>
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default RiskDashboard;
