import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { MonthlyDashboardSubmission } from './monthly-dashboard-submission.entity';
import { MonthlyDashboardKpi } from './monthly-dashboard-kpi.entity';

export enum TrafficLightStatus {
  GREEN = 'green',
  YELLOW = 'yellow',
  RED = 'red',
  NA = 'na',
}

@Entity('monthly_dashboard_kpi_values')
export class MonthlyDashboardKpiValue {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'submission_id' })
  submissionId: number;

  @Column({ name: 'kpi_id' })
  kpiId: number;

  @Column({ type: 'decimal', precision: 12, scale: 4, nullable: true })
  value: number;

  @Column({ name: 'target_value', type: 'decimal', precision: 12, scale: 4, nullable: true })
  targetValue: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({
    name: 'traffic_light_status',
    type: 'enum',
    enum: TrafficLightStatus,
    default: TrafficLightStatus.NA
  })
  trafficLightStatus: TrafficLightStatus;

  @Column({ name: 'traffic_light_override', default: false })
  trafficLightOverride: boolean;

  @Column({ name: 'additional_data', type: 'json', nullable: true })
  additionalData: object;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => MonthlyDashboardSubmission, (submission) => submission.kpiValues)
  @JoinColumn({ name: 'submission_id' })
  submission: MonthlyDashboardSubmission;

  @ManyToOne(() => MonthlyDashboardKpi, (kpi) => kpi.kpiValues, { eager: true })
  @JoinColumn({ name: 'kpi_id' })
  kpi: MonthlyDashboardKpi;

  // Helper methods
  calculateTrafficLightStatus(): TrafficLightStatus {
    if (!this.trafficLightOverride && this.kpi && this.value !== null && this.targetValue !== null) {
      return this.kpi.calculateTrafficLightStatus(this.value, this.targetValue) as TrafficLightStatus;
    }
    return this.trafficLightStatus;
  }

  getFormattedValue(): string {
    if (this.value === null || this.value === undefined) {
      return 'N/A';
    }

    if (!this.kpi) {
      return this.value.toString();
    }

    switch (this.kpi.unit) {
      case '%':
        return `${this.value.toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(this.value);
      case 'count':
        return Math.round(this.value).toString();
      case 'days':
        return `${Math.round(this.value)} days`;
      case 'date':
        return new Date(this.value).toLocaleDateString();
      default:
        return this.value.toString();
    }
  }

  getFormattedTarget(): string {
    if (this.targetValue === null || this.targetValue === undefined) {
      return 'N/A';
    }

    if (!this.kpi) {
      return this.targetValue.toString();
    }

    switch (this.kpi.unit) {
      case '%':
        return `${this.targetValue.toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(this.targetValue);
      case 'count':
        return Math.round(this.targetValue).toString();
      case 'days':
        return `${Math.round(this.targetValue)} days`;
      default:
        return this.targetValue.toString();
    }
  }

  getVariancePercentage(): number | null {
    if (this.value === null || this.targetValue === null || this.targetValue === 0) {
      return null;
    }
    return ((this.value - this.targetValue) / this.targetValue) * 100;
  }

  getVarianceText(): string {
    const variance = this.getVariancePercentage();
    if (variance === null) {
      return 'N/A';
    }
    
    const sign = variance >= 0 ? '+' : '';
    return `${sign}${variance.toFixed(1)}%`;
  }

  isOnTarget(): boolean {
    if (!this.kpi || this.value === null || this.targetValue === null) {
      return false;
    }

    const variance = Math.abs(this.getVariancePercentage() || 0);
    return variance <= Math.abs(this.kpi.trafficLightGreenMax);
  }
}
