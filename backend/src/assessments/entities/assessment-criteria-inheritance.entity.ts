import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AssessmentCriteria } from './assessment-criteria.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';

export enum InheritanceType {
  HR_MANDATED = 'hr_mandated',
  ORGANIZATIONAL_INHERITED = 'organizational_inherited',
  TEAM_SPECIFIC = 'team_specific'
}

@Entity('assessment_criteria_inheritance')
export class AssessmentCriteriaInheritance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'criteria_id' })
  criteriaId: number;

  @ManyToOne(() => AssessmentCriteria, criteria => criteria.inheritances)
  @JoinColumn({ name: 'criteria_id' })
  criteria: AssessmentCriteria;

  @Column({ name: 'target_organizational_unit_id' })
  targetOrganizationalUnitId: number;

  @ManyToOne(() => OrganizationalUnit)
  @JoinColumn({ name: 'target_organizational_unit_id' })
  targetOrganizationalUnit: OrganizationalUnit;

  @Column({ name: 'inherited_from_unit_id', nullable: true })
  inheritedFromUnitId: number;

  @ManyToOne(() => OrganizationalUnit, { nullable: true })
  @JoinColumn({ name: 'inherited_from_unit_id' })
  inheritedFromUnit: OrganizationalUnit;

  @Column({
    type: 'enum',
    enum: InheritanceType,
    name: 'inheritance_type'
  })
  inheritanceType: InheritanceType;

  @Column({ name: 'is_overridable', default: false })
  isOverridable: boolean;

  @Column({ 
    name: 'local_weight_override', 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    nullable: true 
  })
  localWeightOverride: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
