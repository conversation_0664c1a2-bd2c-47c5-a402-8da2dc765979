import { Injectable, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    console.log('🔐 [JWT-GUARD-DEBUG] JWT Guard canActivate called:', {
      hasAuthHeader: !!authHeader,
      authHeaderValue: authHeader ? `${authHeader.substring(0, 20)}...` : 'none',
      url: request.url,
      method: request.method
    });

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any) {
    console.log('🔐 [JWT-GUARD-DEBUG] JWT Guard handleRequest called:', {
      hasError: !!err,
      hasUser: !!user,
      hasInfo: !!info,
      error: err?.message,
      user: user ? { userId: user.userId, email: user.email, role: user.role } : null,
      info: info?.message || info
    });

    if (err || !user) {
      console.log('🔐 [JWT-GUARD-DEBUG] Authentication failed:', { err, user, info });
      throw err || new Error('Unauthorized');
    }

    return user;
  }
}
