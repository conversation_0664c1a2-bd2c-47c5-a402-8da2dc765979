import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProcessImprovement, ProcessImprovementStatus } from '../entities/process-improvement.entity';
import { CreateProcessImprovementDto } from '../dto/process-improvement/create-process-improvement.dto';
import { UpdateProcessImprovementDto } from '../dto/process-improvement/update-process-improvement.dto';
import { UserRole } from '../../users/entities/user.entity';

@Injectable()
export class ProcessImprovementService {
  constructor(
    @InjectRepository(ProcessImprovement)
    private processImprovementRepository: Repository<ProcessImprovement>,
  ) {}

  async create(createProcessImprovementDto: CreateProcessImprovementDto, userId: number): Promise<ProcessImprovement> {
    const processImprovement = this.processImprovementRepository.create({
      ...createProcessImprovementDto,
      createdById: userId,
    });

    return this.processImprovementRepository.save(processImprovement);
  }

  async findAll(userId: number, userRole: string, filterOptions?: any): Promise<ProcessImprovement[]> {
    const query = this.processImprovementRepository.createQueryBuilder('processImprovement')
      .leftJoinAndSelect('processImprovement.assignedTo', 'assignedTo')
      .leftJoinAndSelect('processImprovement.createdBy', 'createdBy');

    // Apply role-based filtering
    if (userRole !== UserRole.HR_ADMIN) {
      query.where('processImprovement.createdById = :userId OR processImprovement.assignedToId = :userId', { userId });
    }

    // Apply additional filters if provided
    if (filterOptions) {
      if (filterOptions.status) {
        query.andWhere('processImprovement.status = :status', { status: filterOptions.status });
      }
      
      if (filterOptions.impact) {
        query.andWhere('processImprovement.impact = :impact', { impact: filterOptions.impact });
      }
      
      if (filterOptions.effort) {
        query.andWhere('processImprovement.effort = :effort', { effort: filterOptions.effort });
      }
      
      if (filterOptions.targetCompletionDateFrom) {
        query.andWhere('processImprovement.targetCompletionDate >= :targetDateFrom', 
          { targetDateFrom: filterOptions.targetCompletionDateFrom });
      }
      
      if (filterOptions.targetCompletionDateTo) {
        query.andWhere('processImprovement.targetCompletionDate <= :targetDateTo', 
          { targetDateTo: filterOptions.targetCompletionDateTo });
      }
      
      if (filterOptions.assignedToId) {
        query.andWhere('processImprovement.assignedToId = :assignedToId', { assignedToId: filterOptions.assignedToId });
      }

      if (filterOptions.assessmentId) {
        query.andWhere('processImprovement.assessmentId = :assessmentId', { assessmentId: filterOptions.assessmentId });
      }
    }

    return query.orderBy('processImprovement.createdAt', 'DESC').getMany();
  }

  async findOne(id: number, userId: number, userRole: string): Promise<ProcessImprovement> {
    const processImprovement = await this.processImprovementRepository.findOne({
      where: { id },
      relations: ['assignedTo', 'createdBy'],
    });

    if (!processImprovement) {
      throw new NotFoundException(`Process improvement with ID ${id} not found`);
    }

    // Check permissions based on role
    if (userRole !== UserRole.HR_ADMIN && 
        processImprovement.createdById !== userId && 
        processImprovement.assignedToId !== userId) {
      throw new ForbiddenException('You do not have permission to view this process improvement');
    }

    return processImprovement;
  }

  async update(id: number, updateProcessImprovementDto: UpdateProcessImprovementDto, userId: number, userRole: string): Promise<ProcessImprovement> {
    const processImprovement = await this.findOne(id, userId, userRole);

    // Check if user has permission to update
    if (userRole !== UserRole.HR_ADMIN && 
        processImprovement.createdById !== userId && 
        processImprovement.assignedToId !== userId) {
      throw new ForbiddenException('You do not have permission to update this process improvement');
    }

    // Handle status transitions and completion date
    if (updateProcessImprovementDto.status && 
        updateProcessImprovementDto.status === ProcessImprovementStatus.COMPLETED && 
        processImprovement.status !== ProcessImprovementStatus.COMPLETED) {
      
      // If transitioning to COMPLETED and no actual completion date is provided, set it to current date
      if (!updateProcessImprovementDto.actualCompletionDate) {
        processImprovement.actualCompletionDate = new Date();
      }
    }

    // Update all provided fields
    Object.assign(processImprovement, updateProcessImprovementDto);

    return this.processImprovementRepository.save(processImprovement);
  }

  async remove(id: number, userId: number, userRole: string): Promise<void> {
    const processImprovement = await this.findOne(id, userId, userRole);

    // Only creator or HR admin can delete
    if (userRole !== UserRole.HR_ADMIN && processImprovement.createdById !== userId) {
      throw new ForbiddenException('You do not have permission to delete this process improvement');
    }

    await this.processImprovementRepository.remove(processImprovement);
  }

  async getProcessImprovementsByAssessment(assessmentId: number, userId: number, userRole: string): Promise<ProcessImprovement[]> {
    const query = this.processImprovementRepository.createQueryBuilder('processImprovement')
      .leftJoinAndSelect('processImprovement.assignedTo', 'assignedTo')
      .leftJoinAndSelect('processImprovement.createdBy', 'createdBy')
      .where('processImprovement.assessmentId = :assessmentId', { assessmentId });

    // Apply role-based filtering
    if (userRole !== UserRole.HR_ADMIN) {
      query.andWhere('processImprovement.createdById = :userId OR processImprovement.assignedToId = :userId', { userId });
    }

    return query.orderBy('processImprovement.createdAt', 'DESC').getMany();
  }
}
