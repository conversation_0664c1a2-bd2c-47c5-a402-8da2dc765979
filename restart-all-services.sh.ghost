#!/bin/bash

# EHRX Application - Restart All Services Script
# This script stops and then starts all EHRX services

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

PROJECT_ROOT="/var/www/ehrx"

echo -e "${CYAN}🔄 EHRX Application - Restarting All Services${NC}"
echo -e "${CYAN}==============================================${NC}"

# Change to project root
cd "$PROJECT_ROOT" || {
    echo -e "${RED}❌ Failed to change to project root: $PROJECT_ROOT${NC}"
    exit 1
}

# Step 1: Stop all services
echo -e "${YELLOW}🛑 Step 1: Stopping all services...${NC}"
./stop-all-services.sh

# Wait a moment for complete shutdown
echo -e "${BLUE}⏳ Waiting for complete shutdown...${NC}"
sleep 3

# Step 2: Start all services
echo -e "\n${GREEN}🚀 Step 2: Starting all services...${NC}"
./start-all-services.sh
