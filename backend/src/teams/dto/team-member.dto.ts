import { IsN<PERSON>ber, IsEnum, IsOptional } from 'class-validator';
import { TeamMemberRole } from '../entities/team-member.entity';

export class TeamMemberDto {
  @IsNumber()
  userId: number;

  @IsEnum(TeamMemberRole)
  @IsOptional()
  role?: TeamMemberRole;
}

export class AddTeamMemberDto {
  @IsNumber()
  userId: number;

  @IsEnum(TeamMemberRole)
  @IsOptional()
  role?: TeamMemberRole = TeamMemberRole.MEMBER;
}

export class UpdateTeamMemberRoleDto {
  @IsEnum(TeamMemberRole)
  role: TeamMemberRole;
}
