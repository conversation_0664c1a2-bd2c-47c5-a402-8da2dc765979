import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { SurveyResponse } from '../../analytics/entities/survey-response.entity';
import { EngagementSurvey } from '../../analytics/entities/engagement-survey.entity';
import { AiInsight } from '../entities/ai-insight.entity';

@Injectable()
export class SentimentAnalysisService {
  constructor(
    @InjectRepository(SurveyResponse)
    private readonly surveyResponseRepository: Repository<SurveyResponse>,
    @InjectRepository(EngagementSurvey)
    private readonly engagementSurveyRepository: Repository<EngagementSurvey>,
    @InjectRepository(AiInsight)
    private readonly aiInsightRepository: Repository<AiInsight>,
  ) {}

  /**
   * Analyze sentiment for a specific survey
   */
  async analyzeSurveySentiment(surveyId: number) {
    try {
      const survey = await this.engagementSurveyRepository.findOne({
        where: { id: surveyId },
        relations: ['responses'],
      });

      if (!survey) {
        return {
          success: false,
          error: 'Survey not found',
        };
      }

      const responses = await this.surveyResponseRepository.find({
        where: { surveyId },
        relations: ['respondent'],
      });

      if (responses.length === 0) {
        return {
          success: false,
          error: 'No responses found for this survey',
        };
      }

      // Analyze text responses for sentiment
      const sentimentResults = [];
      const overallSentiment = { positive: 0, neutral: 0, negative: 0 };
      const keyThemes = new Map();
      const emotionalDrivers = [];

      for (const response of responses) {
        const responseAnalysis = await this.analyzeResponseSentiment(response);
        sentimentResults.push(responseAnalysis);

        // Aggregate sentiment scores
        overallSentiment.positive += responseAnalysis.sentiment.positive;
        overallSentiment.neutral += responseAnalysis.sentiment.neutral;
        overallSentiment.negative += responseAnalysis.sentiment.negative;

        // Collect themes
        responseAnalysis.themes.forEach(theme => {
          keyThemes.set(theme, (keyThemes.get(theme) || 0) + 1);
        });

        // Collect emotional drivers
        emotionalDrivers.push(...responseAnalysis.emotionalDrivers);
      }

      // Calculate averages
      const totalResponses = responses.length;
      const avgSentiment = {
        positive: Math.round((overallSentiment.positive / totalResponses) * 100) / 100,
        neutral: Math.round((overallSentiment.neutral / totalResponses) * 100) / 100,
        negative: Math.round((overallSentiment.negative / totalResponses) * 100) / 100,
      };

      // Get top themes
      const topThemes = Array.from(keyThemes.entries())
        .map(([theme, count]) => ({ theme, count, percentage: (count / totalResponses) * 100 }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Analyze emotional drivers
      const topDrivers = this.analyzeEmotionalDrivers(emotionalDrivers);

      // Generate insights
      const insights = await this.generateSentimentInsights(surveyId, avgSentiment, topThemes, topDrivers);

      return {
        success: true,
        data: {
          surveyId,
          surveyTitle: survey.title,
          totalResponses,
          overallSentiment: avgSentiment,
          sentimentScore: this.calculateSentimentScore(avgSentiment),
          topThemes,
          emotionalDrivers: topDrivers,
          insights,
          detailedResults: sentimentResults,
          analyzedAt: new Date(),
        },
      };
    } catch (error) {
      console.error('Error analyzing survey sentiment:', error);
      return {
        success: false,
        error: 'Failed to analyze survey sentiment',
      };
    }
  }

  /**
   * Get sentiment trends over time
   */
  async getSentimentTrends(period: string = '6months', teamId?: number) {
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '3months':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(endDate.getMonth() - 6);
      }

      // Get surveys in the period
      const surveys = await this.engagementSurveyRepository.find({
        where: {
          startDate: Between(startDate, endDate),
          status: 'completed',
        },
        order: { startDate: 'ASC' },
      });

      const trendData = [];
      let currentScore = 0;
      let previousScore = 0;

      for (const survey of surveys) {
        const sentimentAnalysis = await this.analyzeSurveySentiment(survey.id);
        if (sentimentAnalysis.success) {
          const score = sentimentAnalysis.data.sentimentScore;
          trendData.push({
            date: survey.startDate,
            score,
            surveyTitle: survey.title,
            responseCount: sentimentAnalysis.data.totalResponses,
          });
          
          if (trendData.length === 1) {
            previousScore = score;
          }
          currentScore = score;
        }
      }

      // Calculate trend direction
      const trend = currentScore > previousScore ? 'improving' : 
                   currentScore < previousScore ? 'declining' : 'stable';

      // Get key drivers from recent surveys
      const keyDrivers = await this.getEngagementKeyDrivers(null, period);

      return {
        success: true,
        data: {
          period,
          currentScore,
          previousScore,
          trend,
          trendData,
          keyDrivers: keyDrivers.data || [],
          analyzedAt: new Date(),
        },
      };
    } catch (error) {
      console.error('Error getting sentiment trends:', error);
      return {
        success: false,
        error: 'Failed to get sentiment trends',
      };
    }
  }

  /**
   * Get engagement key drivers
   */
  async getEngagementKeyDrivers(surveyId?: number, period: string = '3months') {
    try {
      let surveys = [];

      if (surveyId) {
        const survey = await this.engagementSurveyRepository.findOne({
          where: { id: surveyId },
        });
        if (survey) surveys = [survey];
      } else {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(endDate.getMonth() - parseInt(period.replace('months', '')));

        surveys = await this.engagementSurveyRepository.find({
          where: {
            startDate: Between(startDate, endDate),
            status: 'completed',
          },
        });
      }

      const allDrivers = new Map();
      const driverImpact = new Map();

      for (const survey of surveys) {
        const responses = await this.surveyResponseRepository.find({
          where: { surveyId: survey.id },
        });

        for (const response of responses) {
          const drivers = await this.extractEngagementDrivers(response);
          drivers.forEach(driver => {
            allDrivers.set(driver.name, (allDrivers.get(driver.name) || 0) + 1);
            driverImpact.set(driver.name, (driverImpact.get(driver.name) || 0) + driver.impact);
          });
        }
      }

      // Calculate key drivers
      const keyDrivers = Array.from(allDrivers.entries())
        .map(([driver, frequency]) => ({
          driver,
          frequency,
          averageImpact: driverImpact.get(driver) / frequency,
          importance: this.calculateDriverImportance(frequency, driverImpact.get(driver) / frequency),
        }))
        .sort((a, b) => b.importance - a.importance)
        .slice(0, 10);

      return {
        success: true,
        data: keyDrivers,
      };
    } catch (error) {
      console.error('Error getting engagement key drivers:', error);
      return {
        success: false,
        error: 'Failed to get engagement key drivers',
      };
    }
  }

  /**
   * Analyze sentiment for a single response
   */
  private async analyzeResponseSentiment(response: SurveyResponse): Promise<any> {
    const responseData = response.responses as any;
    const textResponses = this.extractTextResponses(responseData);
    
    let totalSentiment = { positive: 0, neutral: 0, negative: 0 };
    const themes = [];
    const emotionalDrivers = [];

    for (const text of textResponses) {
      if (text && text.trim().length > 0) {
        const sentiment = this.analyzeSentiment(text);
        const extractedThemes = this.extractThemes(text);
        const drivers = this.extractEmotionalDrivers(text);

        totalSentiment.positive += sentiment.positive;
        totalSentiment.neutral += sentiment.neutral;
        totalSentiment.negative += sentiment.negative;
        
        themes.push(...extractedThemes);
        emotionalDrivers.push(...drivers);
      }
    }

    // Normalize sentiment scores
    const total = totalSentiment.positive + totalSentiment.neutral + totalSentiment.negative;
    if (total > 0) {
      totalSentiment.positive = totalSentiment.positive / total;
      totalSentiment.neutral = totalSentiment.neutral / total;
      totalSentiment.negative = totalSentiment.negative / total;
    }

    return {
      responseId: response.id,
      respondentId: response.respondentId,
      sentiment: totalSentiment,
      themes,
      emotionalDrivers,
      textCount: textResponses.length,
    };
  }

  /**
   * Extract text responses from response data
   */
  private extractTextResponses(responseData: any): string[] {
    const textResponses = [];
    
    if (typeof responseData === 'object') {
      Object.values(responseData).forEach(value => {
        if (typeof value === 'string' && value.trim().length > 10) {
          textResponses.push(value);
        }
      });
    }

    return textResponses;
  }

  /**
   * Analyze sentiment of text using simple keyword-based approach
   */
  private analyzeSentiment(text: string): { positive: number; neutral: number; negative: number } {
    const positiveWords = [
      'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like',
      'happy', 'satisfied', 'pleased', 'excited', 'motivated', 'engaged', 'positive',
      'supportive', 'helpful', 'friendly', 'collaborative', 'innovative', 'growth',
      'opportunity', 'development', 'success', 'achievement', 'recognition', 'appreciate'
    ];

    const negativeWords = [
      'bad', 'terrible', 'awful', 'horrible', 'hate', 'dislike', 'frustrated', 'angry',
      'disappointed', 'dissatisfied', 'unhappy', 'stressed', 'overwhelmed', 'burnout',
      'toxic', 'negative', 'difficult', 'challenging', 'problem', 'issue', 'concern',
      'lack', 'poor', 'insufficient', 'inadequate', 'unfair', 'biased', 'discrimination'
    ];

    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });

    const total = positiveCount + negativeCount;
    if (total === 0) {
      return { positive: 0, neutral: 1, negative: 0 };
    }

    return {
      positive: positiveCount / total,
      neutral: Math.max(0, 1 - (positiveCount + negativeCount) / words.length),
      negative: negativeCount / total,
    };
  }

  /**
   * Extract themes from text
   */
  private extractThemes(text: string): string[] {
    const themeKeywords = {
      'work-life-balance': ['balance', 'flexible', 'remote', 'hours', 'overtime', 'vacation'],
      'management': ['manager', 'supervisor', 'leadership', 'boss', 'direction', 'guidance'],
      'career-development': ['career', 'growth', 'promotion', 'development', 'learning', 'training'],
      'compensation': ['salary', 'pay', 'compensation', 'benefits', 'bonus', 'raise'],
      'team-collaboration': ['team', 'collaboration', 'colleagues', 'communication', 'support'],
      'workload': ['workload', 'stress', 'pressure', 'deadlines', 'busy', 'overwhelmed'],
      'recognition': ['recognition', 'appreciation', 'feedback', 'acknowledgment', 'praise'],
      'company-culture': ['culture', 'values', 'environment', 'atmosphere', 'workplace'],
    };

    const themes = [];
    const lowerText = text.toLowerCase();

    Object.entries(themeKeywords).forEach(([theme, keywords]) => {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        themes.push(theme);
      }
    });

    return themes;
  }

  /**
   * Extract emotional drivers from text
   */
  private extractEmotionalDrivers(text: string): any[] {
    const drivers = [];
    const lowerText = text.toLowerCase();

    // Simple pattern matching for emotional drivers
    if (lowerText.includes('stress') || lowerText.includes('overwhelmed')) {
      drivers.push({ type: 'stress', intensity: 'high', impact: -0.8 });
    }
    if (lowerText.includes('excited') || lowerText.includes('motivated')) {
      drivers.push({ type: 'motivation', intensity: 'high', impact: 0.8 });
    }
    if (lowerText.includes('frustrated') || lowerText.includes('annoyed')) {
      drivers.push({ type: 'frustration', intensity: 'medium', impact: -0.6 });
    }

    return drivers;
  }

  /**
   * Analyze emotional drivers
   */
  private analyzeEmotionalDrivers(drivers: any[]): any[] {
    const driverMap = new Map();

    drivers.forEach(driver => {
      const key = driver.type;
      if (!driverMap.has(key)) {
        driverMap.set(key, { type: key, count: 0, totalImpact: 0 });
      }
      const existing = driverMap.get(key);
      existing.count++;
      existing.totalImpact += driver.impact;
    });

    return Array.from(driverMap.values())
      .map(driver => ({
        ...driver,
        averageImpact: driver.totalImpact / driver.count,
      }))
      .sort((a, b) => Math.abs(b.averageImpact) - Math.abs(a.averageImpact))
      .slice(0, 5);
  }

  /**
   * Calculate overall sentiment score (0-100)
   */
  private calculateSentimentScore(sentiment: { positive: number; neutral: number; negative: number }): number {
    return Math.round((sentiment.positive * 100 + sentiment.neutral * 50) * 100) / 100;
  }

  /**
   * Extract engagement drivers from response
   */
  private async extractEngagementDrivers(response: SurveyResponse): Promise<any[]> {
    const drivers = [];
    const responseData = response.responses as any;

    // Analyze numerical ratings to identify drivers
    if (typeof responseData === 'object') {
      Object.entries(responseData).forEach(([question, answer]) => {
        if (typeof answer === 'number') {
          const impact = (answer - 5) / 5; // Normalize to -1 to 1 scale
          drivers.push({
            name: this.categorizeQuestion(question),
            impact,
            score: answer,
          });
        }
      });
    }

    return drivers;
  }

  /**
   * Categorize question into driver category
   */
  private categorizeQuestion(question: string): string {
    const lowerQuestion = question.toLowerCase();
    
    if (lowerQuestion.includes('manager') || lowerQuestion.includes('supervisor')) {
      return 'management';
    }
    if (lowerQuestion.includes('career') || lowerQuestion.includes('development')) {
      return 'career-development';
    }
    if (lowerQuestion.includes('balance') || lowerQuestion.includes('flexible')) {
      return 'work-life-balance';
    }
    if (lowerQuestion.includes('team') || lowerQuestion.includes('colleague')) {
      return 'team-collaboration';
    }
    if (lowerQuestion.includes('recognition') || lowerQuestion.includes('appreciation')) {
      return 'recognition';
    }
    
    return 'general-satisfaction';
  }

  /**
   * Calculate driver importance
   */
  private calculateDriverImportance(frequency: number, averageImpact: number): number {
    return frequency * Math.abs(averageImpact);
  }

  /**
   * Generate sentiment insights
   */
  private async generateSentimentInsights(surveyId: number, sentiment: any, themes: any[], drivers: any[]): Promise<any[]> {
    const insights = [];

    // Generate insights based on sentiment analysis
    if (sentiment.negative > 0.3) {
      insights.push({
        type: 'negative_sentiment_alert',
        priority: 'high',
        message: 'High negative sentiment detected in survey responses',
        recommendation: 'Investigate key concerns and develop action plans',
      });
    }

    if (themes.length > 0) {
      const topTheme = themes[0];
      insights.push({
        type: 'theme_insight',
        priority: 'medium',
        message: `Most discussed theme: ${topTheme.theme}`,
        recommendation: `Focus on addressing ${topTheme.theme} related concerns`,
      });
    }

    return insights;
  }
}
