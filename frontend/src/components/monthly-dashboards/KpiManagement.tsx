import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input, Textarea } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Badge } from '../ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Switch } from '../ui/switch';
import { Add as Plus, Edit, ToggleOff as ToggleLeft, ToggleOn as ToggleRight, Save, Close as X } from '@mui/icons-material';
import { monthlyDashboardsApi } from '../../services/api';
import { toast } from '../ui/toast';

interface Kpi {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  targetValue?: number;
  unit?: string;
  calculationMethod: string;
  helpText: string;
  isActive: boolean;
  sortOrder: number;
  trafficLightGreenMin: number;
  trafficLightGreenMax: number;
  trafficLightYellowMin: number;
  trafficLightYellowMax: number;
  specialRules?: object;
}

interface KpiFormData {
  name: string;
  displayName: string;
  description: string;
  targetValue: string;
  unit: string;
  calculationMethod: string;
  helpText: string;
  isActive: boolean;
  sortOrder: string;
  trafficLightGreenMin: string;
  trafficLightGreenMax: string;
  trafficLightYellowMin: string;
  trafficLightYellowMax: string;
}

const initialFormData: KpiFormData = {
  name: '',
  displayName: '',
  description: '',
  targetValue: '',
  unit: '',
  calculationMethod: 'MANUAL',
  helpText: '',
  isActive: true,
  sortOrder: '0',
  trafficLightGreenMin: '-5',
  trafficLightGreenMax: '5',
  trafficLightYellowMin: '-10',
  trafficLightYellowMax: '10'
};

const calculationMethods = [
  { value: 'MANUAL', label: 'Manual Entry' },
  { value: 'CALCULATED', label: 'Calculated' },
  { value: 'AUTO_FILLED', label: 'Auto-filled' }
];

export const KpiManagement: React.FC = () => {
  const [kpis, setKpis] = useState<Kpi[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingKpi, setEditingKpi] = useState<Kpi | null>(null);
  const [formData, setFormData] = useState<KpiFormData>(initialFormData);
  const [includeInactive, setIncludeInactive] = useState(false);

  useEffect(() => {
    fetchKpis();
  }, [includeInactive]);

  const fetchKpis = async () => {
    try {
      setLoading(true);
      const response = await monthlyDashboardsApi.getAllKpis(includeInactive);
      setKpis(response.data);
    } catch (error) {
      console.error('Error fetching KPIs:', error);
      toast.error('Failed to load KPIs');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof KpiFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const openCreateDialog = () => {
    setEditingKpi(null);
    setFormData(initialFormData);
    setIsDialogOpen(true);
  };

  const openEditDialog = (kpi: Kpi) => {
    setEditingKpi(kpi);
    setFormData({
      name: kpi.name,
      displayName: kpi.displayName,
      description: kpi.description || '',
      targetValue: kpi.targetValue?.toString() || '',
      unit: kpi.unit || '',
      calculationMethod: kpi.calculationMethod,
      helpText: kpi.helpText,
      isActive: kpi.isActive,
      sortOrder: kpi.sortOrder.toString(),
      trafficLightGreenMin: kpi.trafficLightGreenMin.toString(),
      trafficLightGreenMax: kpi.trafficLightGreenMax.toString(),
      trafficLightYellowMin: kpi.trafficLightYellowMin.toString(),
      trafficLightYellowMax: kpi.trafficLightYellowMax.toString()
    });
    setIsDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      const payload = {
        name: formData.name,
        displayName: formData.displayName,
        description: formData.description || undefined,
        targetValue: formData.targetValue ? parseFloat(formData.targetValue) : undefined,
        unit: formData.unit || undefined,
        calculationMethod: formData.calculationMethod,
        helpText: formData.helpText,
        isActive: formData.isActive,
        sortOrder: parseInt(formData.sortOrder),
        trafficLightGreenMin: parseFloat(formData.trafficLightGreenMin),
        trafficLightGreenMax: parseFloat(formData.trafficLightGreenMax),
        trafficLightYellowMin: parseFloat(formData.trafficLightYellowMin),
        trafficLightYellowMax: parseFloat(formData.trafficLightYellowMax)
      };

      if (editingKpi) {
        await monthlyDashboardsApi.updateKpi(editingKpi.id, payload);
        toast.success('KPI updated successfully');
      } else {
        await monthlyDashboardsApi.createKpi(payload);
        toast.success('KPI created successfully');
      }

      setIsDialogOpen(false);
      fetchKpis();
    } catch (error) {
      console.error('Error saving KPI:', error);
      toast.error('Failed to save KPI');
    }
  };

  const toggleKpiStatus = async (kpi: Kpi) => {
    try {
      await monthlyDashboardsApi.toggleKpiStatus(kpi.id);
      toast.success(`KPI ${kpi.isActive ? 'deactivated' : 'activated'} successfully`);
      fetchKpis();
    } catch (error) {
      console.error('Error toggling KPI status:', error);
      toast.error('Failed to toggle KPI status');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>KPI Management</CardTitle>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch
                  checked={includeInactive}
                  onCheckedChange={setIncludeInactive}
                />
                <Label>Show Inactive</Label>
              </div>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Add KPI
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Display Name</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Unit</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Traffic Lights</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Order</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {kpis.map(kpi => (
                <TableRow key={kpi.id}>
                  <TableCell className="font-medium">{kpi.displayName}</TableCell>
                  <TableCell className="text-sm text-gray-600">{kpi.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{kpi.calculationMethod}</Badge>
                  </TableCell>
                  <TableCell>{kpi.unit || '-'}</TableCell>
                  <TableCell>{kpi.targetValue || '-'}</TableCell>
                  <TableCell className="text-xs">
                    <div className="space-y-1">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>{kpi.trafficLightGreenMin}% to {kpi.trafficLightGreenMax}%</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span>{kpi.trafficLightYellowMin}% to {kpi.trafficLightYellowMax}%</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={kpi.isActive ? 'default' : 'secondary'}>
                      {kpi.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>{kpi.sortOrder}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(kpi)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleKpiStatus(kpi)}
                      >
                        {kpi.isActive ? (
                          <ToggleLeft className="h-3 w-3" />
                        ) : (
                          <ToggleRight className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* KPI Form Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingKpi ? 'Edit KPI' : 'Create New KPI'}
            </DialogTitle>
          </DialogHeader>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Internal Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., fte_count"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="displayName">Display Name *</Label>
              <Input
                id="displayName"
                value={formData.displayName}
                onChange={(e) => handleInputChange('displayName', e.target.value)}
                placeholder="e.g., FTE"
              />
            </div>

            <div className="col-span-2 space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Brief description of this KPI"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="calculationMethod">Calculation Method *</Label>
              <Select
                value={formData.calculationMethod}
                onValueChange={(value) => handleInputChange('calculationMethod', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {calculationMethods.map(method => (
                    <SelectItem key={method.value} value={method.value}>
                      {method.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="unit">Unit</Label>
              <Input
                id="unit"
                value={formData.unit}
                onChange={(e) => handleInputChange('unit', e.target.value)}
                placeholder="e.g., %, count, days"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetValue">Target Value</Label>
              <Input
                id="targetValue"
                type="number"
                value={formData.targetValue}
                onChange={(e) => handleInputChange('targetValue', e.target.value)}
                placeholder="Target value for this KPI"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sortOrder">Sort Order</Label>
              <Input
                id="sortOrder"
                type="number"
                value={formData.sortOrder}
                onChange={(e) => handleInputChange('sortOrder', e.target.value)}
                placeholder="Display order (0-999)"
              />
            </div>

            <div className="col-span-2 space-y-4">
              <Label>Traffic Light Thresholds (%)</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm text-green-600">Green Range</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      value={formData.trafficLightGreenMin}
                      onChange={(e) => handleInputChange('trafficLightGreenMin', e.target.value)}
                      placeholder="Min %"
                    />
                    <Input
                      type="number"
                      value={formData.trafficLightGreenMax}
                      onChange={(e) => handleInputChange('trafficLightGreenMax', e.target.value)}
                      placeholder="Max %"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm text-yellow-600">Yellow Range</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      value={formData.trafficLightYellowMin}
                      onChange={(e) => handleInputChange('trafficLightYellowMin', e.target.value)}
                      placeholder="Min %"
                    />
                    <Input
                      type="number"
                      value={formData.trafficLightYellowMax}
                      onChange={(e) => handleInputChange('trafficLightYellowMax', e.target.value)}
                      placeholder="Max %"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="col-span-2 space-y-2">
              <Label htmlFor="helpText">Help Text *</Label>
              <Textarea
                id="helpText"
                value={formData.helpText}
                onChange={(e) => handleInputChange('helpText', e.target.value)}
                placeholder="Instructions for users on how to fill this KPI"
              />
            </div>

            <div className="col-span-2 flex items-center gap-2">
              <Switch
                checked={formData.isActive}
                onCheckedChange={(checked) => handleInputChange('isActive', checked)}
              />
              <Label>Active</Label>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              <Save className="h-4 w-4 mr-2" />
              {editingKpi ? 'Update' : 'Create'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
