import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as request from 'supertest';
import { ImprovementPlansModule } from '../improvement-plans.module';
import { UsersModule } from '../../users/users.module';
import { 
  ImprovementPlan, 
  ImprovementPlanGoal, 
  ImprovementPlanReview, 
  ImprovementPlanStatus, 
  ImprovementPlanType 
} from '../entities/improvement-plan.entity';
import { User, UserRole } from '../../users/entities/user.entity';

describe('Improvement Plans Integration', () => {
  let app: INestApplication;
  let improvementPlanRepository: Repository<ImprovementPlan>;
  let goalRepository: Repository<ImprovementPlanGoal>;
  let reviewRepository: Repository<ImprovementPlanReview>;
  let userRepository: Repository<User>;

  const hrAdmin = {
    userId: 1,
    role: UserRole.HR_ADMIN,
    email: '<EMAIL>'
  };

  const manager = {
    userId: 2,
    role: UserRole.MANAGER,
    email: '<EMAIL>'
  };

  const employee = {
    userId: 3,
    role: UserRole.EMPLOYEE,
    email: '<EMAIL>'
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [ImprovementPlan, ImprovementPlanGoal, ImprovementPlanReview, User],
          synchronize: true,
        }),
        ImprovementPlansModule,
        UsersModule,
      ],
    })
    .overrideGuard('JwtAuthGuard')
    .useValue({
      canActivate: (context) => {
        const request = context.switchToHttp().getRequest();
        request.user = request.headers['test-user'] === 'manager' ? manager : 
                      request.headers['test-user'] === 'employee' ? employee : hrAdmin;
        return true;
      },
    })
    .overrideGuard('RolesGuard')
    .useValue({
      canActivate: () => true,
    })
    .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    improvementPlanRepository = moduleFixture.get('ImprovementPlanRepository');
    goalRepository = moduleFixture.get('ImprovementPlanGoalRepository');
    reviewRepository = moduleFixture.get('ImprovementPlanReviewRepository');
    userRepository = moduleFixture.get('UserRepository');

    // Create test users
    await userRepository.save([
      { id: 1, email: '<EMAIL>', firstName: 'HR', lastName: 'Admin', role: UserRole.HR_ADMIN, is_active: true, password_hash: 'hash' },
      { id: 2, email: '<EMAIL>', firstName: 'Team', lastName: 'Manager', role: UserRole.MANAGER, is_active: true, password_hash: 'hash' },
      { id: 3, email: '<EMAIL>', firstName: 'Test', lastName: 'Employee', role: UserRole.EMPLOYEE, is_active: true, password_hash: 'hash' }
    ]);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Improvement Plan CRUD Operations', () => {
    let improvementPlanId: number;

    it('should create a new improvement plan with goals', async () => {
      const planData = {
        title: 'Leadership Development Plan',
        description: 'Comprehensive plan to develop leadership skills',
        type: ImprovementPlanType.SKILL_DEVELOPMENT,
        employeeId: 3,
        managerId: 2,
        startDate: '2024-01-01',
        targetCompletionDate: '2024-12-31',
        objectives: 'Develop leadership and management capabilities',
        successCriteria: 'Complete all training modules and demonstrate improved leadership skills',
        resourcesNeeded: 'Leadership training courses, mentoring sessions',
        reviewFrequency: 'monthly',
        nextReviewDate: '2024-02-01',
        goals: [
          {
            title: 'Complete Leadership Training',
            description: 'Finish online leadership course',
            targetDate: '2024-06-30',
            orderIndex: 1
          },
          {
            title: 'Lead Team Project',
            description: 'Successfully lead a cross-functional project',
            targetDate: '2024-09-30',
            orderIndex: 2
          }
        ]
      };

      const response = await request(app.getHttpServer())
        .post('/improvement-plans')
        .set('test-user', 'manager')
        .send(planData)
        .expect(201);

      improvementPlanId = response.body.id;
      expect(response.body.title).toBe(planData.title);
      expect(response.body.status).toBe(ImprovementPlanStatus.DRAFT);
      expect(response.body.employee.id).toBe(3);
      expect(response.body.manager.id).toBe(2);
      expect(response.body.goals).toBeDefined();
      expect(response.body.goals.length).toBe(2);
    });

    it('should get all improvement plans with filters', async () => {
      const response = await request(app.getHttpServer())
        .get('/improvement-plans?type=skill_development&status=draft')
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body.plans).toBeDefined();
      expect(response.body.total).toBeGreaterThan(0);
      expect(response.body.plans[0].type).toBe(ImprovementPlanType.SKILL_DEVELOPMENT);
      expect(response.body.plans[0].status).toBe(ImprovementPlanStatus.DRAFT);
    });

    it('should get a specific improvement plan', async () => {
      const response = await request(app.getHttpServer())
        .get(`/improvement-plans/${improvementPlanId}`)
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body.id).toBe(improvementPlanId);
      expect(response.body.title).toBe('Leadership Development Plan');
      expect(response.body.employee).toBeDefined();
      expect(response.body.manager).toBeDefined();
      expect(response.body.goals).toBeDefined();
      expect(response.body.goals.length).toBe(2);
    });

    it('should update an improvement plan', async () => {
      const updateData = {
        status: ImprovementPlanStatus.ACTIVE,
        progressPercentage: 25,
        notes: 'Plan activated and initial progress made'
      };

      const response = await request(app.getHttpServer())
        .patch(`/improvement-plans/${improvementPlanId}`)
        .set('test-user', 'manager')
        .send(updateData)
        .expect(200);

      expect(response.body.status).toBe(ImprovementPlanStatus.ACTIVE);
      expect(response.body.progressPercentage).toBe(25);
    });

    it('should complete improvement plan when progress reaches 100%', async () => {
      const updateData = {
        progressPercentage: 100
      };

      const response = await request(app.getHttpServer())
        .patch(`/improvement-plans/${improvementPlanId}`)
        .set('test-user', 'manager')
        .send(updateData)
        .expect(200);

      expect(response.body.progressPercentage).toBe(100);
      expect(response.body.status).toBe(ImprovementPlanStatus.COMPLETED);
      expect(response.body.actualCompletionDate).toBeDefined();
    });
  });

  describe('Goal Management', () => {
    let planId: number;
    let goalId: number;

    beforeEach(async () => {
      const plan = await improvementPlanRepository.save({
        title: 'Goal Management Test Plan',
        description: 'For testing goal operations',
        employeeId: 3,
        managerId: 2,
        createdById: 2,
        startDate: new Date('2024-01-01'),
        targetCompletionDate: new Date('2024-12-31'),
        status: ImprovementPlanStatus.ACTIVE,
        type: ImprovementPlanType.PERFORMANCE,
        progressPercentage: 0
      });
      planId = plan.id;
    });

    it('should add a goal to improvement plan', async () => {
      const goalData = {
        title: 'New Goal',
        description: 'A new goal for the plan',
        targetDate: '2024-06-30',
        notes: 'Important milestone'
      };

      const response = await request(app.getHttpServer())
        .post(`/improvement-plans/${planId}/goals`)
        .set('test-user', 'manager')
        .send(goalData)
        .expect(201);

      goalId = response.body.id;
      expect(response.body.title).toBe(goalData.title);
      expect(response.body.improvementPlanId).toBe(planId);
      expect(response.body.isCompleted).toBe(false);
      expect(response.body.progressPercentage).toBe(0);
    });

    it('should update a goal', async () => {
      const updateData = {
        progressPercentage: 75,
        notes: 'Significant progress made'
      };

      const response = await request(app.getHttpServer())
        .patch(`/improvement-plans/goals/${goalId}`)
        .set('test-user', 'manager')
        .send(updateData)
        .expect(200);

      expect(response.body.progressPercentage).toBe(75);
      expect(response.body.notes).toBe(updateData.notes);
    });

    it('should complete a goal', async () => {
      const updateData = {
        isCompleted: true,
        progressPercentage: 100,
        completionDate: '2024-06-15'
      };

      const response = await request(app.getHttpServer())
        .patch(`/improvement-plans/goals/${goalId}`)
        .set('test-user', 'manager')
        .send(updateData)
        .expect(200);

      expect(response.body.isCompleted).toBe(true);
      expect(response.body.progressPercentage).toBe(100);
      expect(response.body.completionDate).toBeDefined();
    });

    it('should delete a goal', async () => {
      await request(app.getHttpServer())
        .delete(`/improvement-plans/goals/${goalId}`)
        .set('test-user', 'manager')
        .expect(200);

      // Verify goal is deleted
      await request(app.getHttpServer())
        .patch(`/improvement-plans/goals/${goalId}`)
        .set('test-user', 'manager')
        .send({ notes: 'Should fail' })
        .expect(404);
    });
  });

  describe('Review Management', () => {
    let planId: number;

    beforeEach(async () => {
      const plan = await improvementPlanRepository.save({
        title: 'Review Test Plan',
        description: 'For testing review operations',
        employeeId: 3,
        managerId: 2,
        createdById: 2,
        startDate: new Date('2024-01-01'),
        targetCompletionDate: new Date('2024-12-31'),
        status: ImprovementPlanStatus.ACTIVE,
        type: ImprovementPlanType.PERFORMANCE,
        progressPercentage: 50,
        reviewFrequency: 'monthly'
      });
      planId = plan.id;
    });

    it('should add a review to improvement plan', async () => {
      const reviewData = {
        reviewDate: '2024-02-01',
        progressSummary: 'Good progress made in the first month',
        achievements: 'Completed initial training modules',
        challenges: 'Time management needs improvement',
        nextSteps: 'Focus on practical application',
        overallRating: 4,
        isOnTrack: true,
        recommendedActions: 'Continue with current approach'
      };

      const response = await request(app.getHttpServer())
        .post(`/improvement-plans/${planId}/reviews`)
        .set('test-user', 'manager')
        .send(reviewData)
        .expect(201);

      expect(response.body.progressSummary).toBe(reviewData.progressSummary);
      expect(response.body.overallRating).toBe(4);
      expect(response.body.isOnTrack).toBe(true);
      expect(response.body.reviewer.id).toBe(2);
    });

    it('should update next review date after adding review', async () => {
      const reviewData = {
        reviewDate: '2024-03-01',
        progressSummary: 'Continued progress',
        isOnTrack: true
      };

      await request(app.getHttpServer())
        .post(`/improvement-plans/${planId}/reviews`)
        .set('test-user', 'manager')
        .send(reviewData)
        .expect(201);

      // Check that the plan's next review date was updated
      const response = await request(app.getHttpServer())
        .get(`/improvement-plans/${planId}`)
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body.nextReviewDate).toBeDefined();
      const nextReviewDate = new Date(response.body.nextReviewDate);
      const expectedDate = new Date('2024-04-01'); // Monthly frequency
      expect(nextReviewDate.getMonth()).toBe(expectedDate.getMonth());
    });
  });

  describe('Improvement Plan Permissions', () => {
    let restrictedPlanId: number;

    beforeEach(async () => {
      const plan = await improvementPlanRepository.save({
        title: 'Permission Test Plan',
        description: 'Testing permissions',
        employeeId: 3,
        managerId: 2,
        createdById: 2,
        startDate: new Date('2024-01-01'),
        targetCompletionDate: new Date('2024-12-31'),
        status: ImprovementPlanStatus.ACTIVE,
        type: ImprovementPlanType.PERFORMANCE,
        progressPercentage: 0
      });
      restrictedPlanId = plan.id;
    });

    it('should allow employee to view their own plan', async () => {
      await request(app.getHttpServer())
        .get(`/improvement-plans/${restrictedPlanId}`)
        .set('test-user', 'employee')
        .expect(200);
    });

    it('should allow manager to update plan they manage', async () => {
      const updateData = { progressPercentage: 30 };

      await request(app.getHttpServer())
        .patch(`/improvement-plans/${restrictedPlanId}`)
        .set('test-user', 'manager')
        .send(updateData)
        .expect(200);
    });

    it('should allow HR admin to delete any plan', async () => {
      await request(app.getHttpServer())
        .delete(`/improvement-plans/${restrictedPlanId}`)
        .expect(200);
    });

    it('should prevent unauthorized access', async () => {
      // Create a plan for a different employee
      const otherPlan = await improvementPlanRepository.save({
        title: 'Other Employee Plan',
        description: 'Not accessible to current employee',
        employeeId: 1, // Different employee
        managerId: 1,
        createdById: 1,
        startDate: new Date('2024-01-01'),
        targetCompletionDate: new Date('2024-12-31'),
        status: ImprovementPlanStatus.ACTIVE,
        type: ImprovementPlanType.PERFORMANCE,
        progressPercentage: 0
      });

      // Employee should not see plans for other employees
      const response = await request(app.getHttpServer())
        .get('/improvement-plans')
        .set('test-user', 'employee')
        .expect(200);

      const planIds = response.body.plans.map(p => p.id);
      expect(planIds).not.toContain(otherPlan.id);
    });
  });

  describe('Improvement Plan Statistics', () => {
    beforeEach(async () => {
      // Create multiple plans for statistics testing
      await improvementPlanRepository.save([
        {
          title: 'Stats Plan 1',
          description: 'For statistics',
          employeeId: 3,
          managerId: 2,
          createdById: 2,
          startDate: new Date('2024-01-01'),
          targetCompletionDate: new Date('2024-06-30'),
          status: ImprovementPlanStatus.ACTIVE,
          type: ImprovementPlanType.PERFORMANCE,
          progressPercentage: 25
        },
        {
          title: 'Stats Plan 2',
          description: 'For statistics',
          employeeId: 3,
          managerId: 2,
          createdById: 2,
          startDate: new Date('2024-02-01'),
          targetCompletionDate: new Date('2024-08-31'),
          status: ImprovementPlanStatus.COMPLETED,
          type: ImprovementPlanType.SKILL_DEVELOPMENT,
          progressPercentage: 100,
          actualCompletionDate: new Date()
        },
        {
          title: 'Stats Plan 3',
          description: 'For statistics',
          employeeId: 3,
          managerId: 2,
          createdById: 2,
          startDate: new Date('2024-03-01'),
          targetCompletionDate: new Date('2023-12-31'), // Overdue
          status: ImprovementPlanStatus.ACTIVE,
          type: ImprovementPlanType.CAREER_DEVELOPMENT,
          progressPercentage: 10
        }
      ]);
    });

    it('should return improvement plan statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/improvement-plans/statistics')
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body).toHaveProperty('totalPlans');
      expect(response.body).toHaveProperty('byStatus');
      expect(response.body).toHaveProperty('byType');
      expect(response.body).toHaveProperty('overduePlans');
      expect(response.body).toHaveProperty('completedThisMonth');

      expect(typeof response.body.totalPlans).toBe('number');
      expect(response.body.byStatus).toHaveProperty(ImprovementPlanStatus.ACTIVE);
      expect(response.body.byStatus).toHaveProperty(ImprovementPlanStatus.COMPLETED);
      expect(response.body.byType).toHaveProperty(ImprovementPlanType.PERFORMANCE);
      expect(response.body.byType).toHaveProperty(ImprovementPlanType.SKILL_DEVELOPMENT);
    });
  });

  describe('My Improvement Plans', () => {
    beforeEach(async () => {
      // Create plans for different users
      await improvementPlanRepository.save([
        {
          title: 'Employee Plan',
          description: 'For employee',
          employeeId: 3,
          managerId: 2,
          createdById: 2,
          startDate: new Date('2024-01-01'),
          targetCompletionDate: new Date('2024-12-31'),
          status: ImprovementPlanStatus.ACTIVE,
          type: ImprovementPlanType.PERFORMANCE,
          progressPercentage: 50
        },
        {
          title: 'Manager Plan',
          description: 'For manager',
          employeeId: 2,
          managerId: 1,
          createdById: 1,
          startDate: new Date('2024-01-01'),
          targetCompletionDate: new Date('2024-12-31'),
          status: ImprovementPlanStatus.DRAFT,
          type: ImprovementPlanType.SKILL_DEVELOPMENT,
          progressPercentage: 0
        }
      ]);
    });

    it('should return only plans for the current user', async () => {
      const response = await request(app.getHttpServer())
        .get('/improvement-plans/my-plans')
        .set('test-user', 'employee')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(plan => {
        expect(plan.employee.id).toBe(3);
      });
    });

    it('should filter my plans by status', async () => {
      const response = await request(app.getHttpServer())
        .get('/improvement-plans/my-plans?status=active')
        .set('test-user', 'employee')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(plan => {
        expect(plan.status).toBe(ImprovementPlanStatus.ACTIVE);
        expect(plan.employee.id).toBe(3);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent improvement plan', async () => {
      await request(app.getHttpServer())
        .get('/improvement-plans/99999')
        .set('test-user', 'manager')
        .expect(404);
    });

    it('should validate required fields', async () => {
      const invalidData = {
        title: '', // Empty title
        description: '',
        employeeId: 999, // Non-existent user
        managerId: 999,
        startDate: 'invalid-date'
      };

      await request(app.getHttpServer())
        .post('/improvement-plans')
        .set('test-user', 'manager')
        .send(invalidData)
        .expect(400);
    });

    it('should validate date ranges', async () => {
      const invalidData = {
        title: 'Test Plan',
        description: 'Test description',
        employeeId: 3,
        managerId: 2,
        startDate: '2024-12-31',
        targetCompletionDate: '2024-01-01' // End before start
      };

      await request(app.getHttpServer())
        .post('/improvement-plans')
        .set('test-user', 'manager')
        .send(invalidData)
        .expect(400);
    });
  });
});
