import { Injectable, NotFoundException, ConflictException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, QueryRunner, Connection } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User, UserRole, AccountStatus } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

export interface BulkCreateUserDto {
  users: CreateUserDto[];
  sendInvitations?: boolean;
}

export interface BulkUpdateUserDto {
  userIds: number[];
  updates: Partial<UpdateUserDto>;
}

export interface BulkRoleChangeDto {
  userIds: number[];
  newRole: UserRole;
  reason?: string;
}

export interface UserProfileUpdateDto {
  firstName?: string;
  lastName?: string;
  email?: string;
  currentPassword?: string;
  newPassword?: string;
  preferences?: Record<string, any>;
}

export interface UserSearchFilters {
  role?: UserRole;
  isActive?: boolean;
  search?: string;
  departmentId?: number;
  teamId?: number;
  createdAfter?: Date;
  createdBefore?: Date;
}

export interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  byRole: Record<UserRole, number>;
  recentlyCreated: number;
  recentlyUpdated: number;
}

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private connection: Connection,
  ) { }

  async getUserCount(): Promise<number> {
    return this.usersRepository.count();
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    // Check if user with email already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email: createUserDto.email }
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Generate default password if not provided (lastName + 'X123')
    const password = createUserDto.password || this.generateDefaultPassword(createUserDto.lastName);

    const salt = await bcrypt.genSalt(12); // Increased salt rounds for better security
    const hashedPassword = await bcrypt.hash(password, salt);

    const user = this.usersRepository.create({
      email: createUserDto.email,
      password: hashedPassword,
      firstName: createUserDto.firstName,
      lastName: createUserDto.lastName,
      role: createUserDto.role || UserRole.EMPLOYEE,
      isActive: true,
      mustChangePassword: true, // Force password change on first login
      accountStatus: AccountStatus.ACTIVE,
      failedLoginAttempts: 0,
    });

    return this.usersRepository.save(user);
  }

  /**
   * Generate default password for new users (lastName + 'X123')
   */
  private generateDefaultPassword(lastName: string): string {
    return `${lastName}X123`;
  }

  async findAll(): Promise<User[]> {
    return this.usersRepository.find({
      select: ['id', 'email', 'firstName', 'lastName', 'role', 'isActive', 'createdAt', 'updatedAt']
    });
  }

  async findOne(id: number): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { id },
      select: ['id', 'email', 'firstName', 'lastName', 'role', 'isActive', 'createdAt', 'updatedAt']
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | undefined> {
    return this.usersRepository.findOne({
      where: { email }
    });
  }

  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // Update user fields
    if (updateUserDto.firstName) user.firstName = updateUserDto.firstName;
    if (updateUserDto.lastName) user.lastName = updateUserDto.lastName;
    if (updateUserDto.role) user.role = updateUserDto.role;
    if (updateUserDto.isActive !== undefined) user.isActive = updateUserDto.isActive;

    // Update password if provided
    if (updateUserDto.password) {
      const salt = await bcrypt.genSalt();
      user.password = await bcrypt.hash(updateUserDto.password, salt);
    }

    return this.usersRepository.save(user);
  }

  async remove(id: number): Promise<void> {
    const user = await this.findOne(id);
    await this.usersRepository.remove(user);
  }

  // Enhanced User Management Methods

  /**
   * Search users with advanced filters
   */
  async searchUsers(filters: UserSearchFilters, page = 1, limit = 20): Promise<{
    users: User[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const queryBuilder = this.usersRepository.createQueryBuilder('user');

    // Apply filters
    if (filters.role) {
      queryBuilder.andWhere('user.role = :role', { role: filters.role });
    }

    if (filters.isActive !== undefined) {
      queryBuilder.andWhere('user.is_active = :isActive', { isActive: filters.isActive });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(user.first_name ILIKE :search OR user.last_name ILIKE :search OR user.email ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    if (filters.createdAfter) {
      queryBuilder.andWhere('user.created_at >= :createdAfter', { createdAfter: filters.createdAfter });
    }

    if (filters.createdBefore) {
      queryBuilder.andWhere('user.created_at <= :createdBefore', { createdBefore: filters.createdBefore });
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Order by created date
    queryBuilder.orderBy('user.created_at', 'DESC');

    // Select fields (exclude password)
    queryBuilder.select([
      'user.id',
      'user.email',
      'user.first_name',
      'user.last_name',
      'user.role',
      'user.is_active',
      'user.created_at',
      'user.updated_at'
    ]);

    const users = await queryBuilder.getMany();

    return {
      users,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Bulk create users
   */
  async bulkCreateUsers(bulkCreateDto: BulkCreateUserDto): Promise<{
    created: User[];
    errors: { email: string; error: string }[];
  }> {
    const created: User[] = [];
    const errors: { email: string; error: string }[] = [];

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const userDto of bulkCreateDto.users) {
        try {
          // Check if user already exists
          const existingUser = await queryRunner.manager.findOne(User, {
            where: { email: userDto.email }
          });

          if (existingUser) {
            errors.push({
              email: userDto.email,
              error: 'User with this email already exists'
            });
            continue;
          }

          // Create user
          const salt = await bcrypt.genSalt();
          const hashedPassword = await bcrypt.hash(userDto.password, salt);

          const user = queryRunner.manager.create(User, {
            email: userDto.email,
            password: hashedPassword,
            firstName: userDto.firstName,
            lastName: userDto.lastName,
            role: userDto.role || UserRole.EMPLOYEE,
            isActive: true,
          });

          const savedUser = await queryRunner.manager.save(user);
          created.push(savedUser);

        } catch (error) {
          errors.push({
            email: userDto.email,
            error: error.message || 'Failed to create user'
          });
        }
      }

      await queryRunner.commitTransaction();

      // TODO: Send invitations if requested
      if (bulkCreateDto.sendInvitations) {
        // Implementation for sending invitation emails
      }

      return { created, errors };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk update users
   */
  async bulkUpdateUsers(bulkUpdateDto: BulkUpdateUserDto, updatedBy: number): Promise<{
    updated: User[];
    errors: { userId: number; error: string }[];
  }> {
    const updated: User[] = [];
    const errors: { userId: number; error: string }[] = [];

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const userId of bulkUpdateDto.userIds) {
        try {
          const user = await queryRunner.manager.findOne(User, { where: { id: userId } });

          if (!user) {
            errors.push({
              userId,
              error: 'User not found'
            });
            continue;
          }

          // Apply updates
          if (bulkUpdateDto.updates.firstName) user.firstName = bulkUpdateDto.updates.firstName;
          if (bulkUpdateDto.updates.lastName) user.lastName = bulkUpdateDto.updates.lastName;
          if (bulkUpdateDto.updates.role) user.role = bulkUpdateDto.updates.role;
          if (bulkUpdateDto.updates.isActive !== undefined) user.isActive = bulkUpdateDto.updates.isActive;

          const savedUser = await queryRunner.manager.save(user);
          updated.push(savedUser);

        } catch (error) {
          errors.push({
            userId,
            error: error.message || 'Failed to update user'
          });
        }
      }

      await queryRunner.commitTransaction();
      return { updated, errors };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk role change with audit trail
   */
  async bulkChangeRoles(bulkRoleChangeDto: BulkRoleChangeDto, changedBy: number): Promise<{
    updated: User[];
    errors: { userId: number; error: string }[];
  }> {
    const updated: User[] = [];
    const errors: { userId: number; error: string }[] = [];

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const userId of bulkRoleChangeDto.userIds) {
        try {
          const user = await queryRunner.manager.findOne(User, { where: { id: userId } });

          if (!user) {
            errors.push({
              userId,
              error: 'User not found'
            });
            continue;
          }

          const oldRole = user.role;
          user.role = bulkRoleChangeDto.newRole;

          const savedUser = await queryRunner.manager.save(user);
          updated.push(savedUser);

        } catch (error) {
          errors.push({
            userId,
            error: error.message || 'Failed to change role'
          });
        }
      }

      await queryRunner.commitTransaction();
      return { updated, errors };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Update user profile (self-service)
   */
  async updateProfile(userId: number, profileUpdateDto: UserProfileUpdateDto): Promise<User> {
    const user = await this.findOne(userId);

    // Verify current password if changing password
    if (profileUpdateDto.newPassword) {
      if (!profileUpdateDto.currentPassword) {
        throw new BadRequestException('Current password is required to change password');
      }

      const isCurrentPasswordValid = await bcrypt.compare(
        profileUpdateDto.currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        throw new BadRequestException('Current password is incorrect');
      }

      const salt = await bcrypt.genSalt();
      user.password = await bcrypt.hash(profileUpdateDto.newPassword, salt);
    }

    // Update profile fields
    if (profileUpdateDto.firstName) user.firstName = profileUpdateDto.firstName;
    if (profileUpdateDto.lastName) user.lastName = profileUpdateDto.lastName;

    // Email change requires additional verification
    if (profileUpdateDto.email && profileUpdateDto.email !== user.email) {
      const existingUser = await this.usersRepository.findOne({
        where: { email: profileUpdateDto.email }
      });

      if (existingUser) {
        throw new ConflictException('Email is already in use');
      }

      user.email = profileUpdateDto.email;
    }

    return this.usersRepository.save(user);
  }

  /**
   * Get user statistics
   */
  async getUserStatistics(): Promise<UserStatistics> {
    const totalUsers = await this.usersRepository.count();
    const activeUsers = await this.usersRepository.count({ where: { is_active: true } });
    const inactiveUsers = totalUsers - activeUsers;

    // Get counts by role
    const roleStats = await this.usersRepository
      .createQueryBuilder('user')
      .select('user.role', 'role')
      .addSelect('COUNT(*)', 'count')
      .groupBy('user.role')
      .getRawMany();

    const byRole = roleStats.reduce((acc, stat) => {
      acc[stat.role] = parseInt(stat.count);
      return acc;
    }, {} as Record<UserRole, number>);

    // Ensure all roles are represented
    Object.values(UserRole).forEach(role => {
      if (!byRole[role]) {
        byRole[role] = 0;
      }
    });

    // Get recently created/updated counts (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentlyCreated = await this.usersRepository
      .createQueryBuilder('user')
      .where('user.created_at >= :date', { date: thirtyDaysAgo })
      .getCount();

    const recentlyUpdated = await this.usersRepository
      .createQueryBuilder('user')
      .where('user.updated_at >= :date', { date: thirtyDaysAgo })
      .getCount();

    return {
      totalUsers,
      activeUsers,
      inactiveUsers,
      byRole,
      recentlyCreated,
      recentlyUpdated
    };
  }

  /**
   * Get users by role
   */
  async getUsersByRole(role: UserRole, includeInactive = false): Promise<User[]> {
    const where: any = { role };

    if (!includeInactive) {
      where.isActive = true;
    }

    return this.usersRepository.find({
      where,
      select: ['id', 'email', 'firstName', 'lastName', 'role', 'isActive', 'createdAt', 'updatedAt'],
      order: { firstName: 'ASC', lastName: 'ASC' }
    });
  }

  /**
   * Validate user permissions for role changes
   */
  validateRoleChangePermission(currentUserRole: UserRole, targetRole: UserRole): boolean {
    // HR_ADMIN can change any role
    if (currentUserRole === UserRole.HR_ADMIN) {
      return true;
    }

    // MANAGER can only promote/demote to/from EMPLOYEE
    if (currentUserRole === UserRole.MANAGER) {
      return targetRole === UserRole.EMPLOYEE;
    }

    // Other roles cannot change roles
    return false;
  }

  /**
   * Deactivate multiple users
   */
  async deactivateUsers(userIds: number[], currentUserId: number): Promise<User[]> {
    const users = await this.usersRepository.findByIds(userIds);

    for (const user of users) {
      user.isActive = false;
      user.updatedAt = new Date();
    }

    return this.usersRepository.save(users);
  }

  /**
   * Reactivate multiple users
   */
  async reactivateUsers(userIds: number[], currentUserId: number): Promise<User[]> {
    const users = await this.usersRepository.findByIds(userIds);

    for (const user of users) {
      user.isActive = true;
      user.updatedAt = new Date();
    }

    return this.usersRepository.save(users);
  }

  /**
   * Reset user password
   */
  async resetUserPassword(userId: number, newPassword: string, currentUserId: number): Promise<User> {
    const user = await this.findOne(userId);

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedPassword;
    user.updatedAt = new Date();

    return this.usersRepository.save(user);
  }
}
