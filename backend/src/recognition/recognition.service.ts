import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RecognitionBadge } from './entities/recognition-badge.entity';
import { RecognitionInstance } from './entities/recognition-instance.entity';
import { UserGamification } from './entities/user-gamification.entity';
import { MicroFeedback } from './entities/micro-feedback.entity';
import { CreateBadgeDto, UpdateBadgeDto } from './dto/badge.dto';
import { GiveRecognitionDto } from './dto/recognition.dto';
import { MicroFeedbackDto } from './dto/micro-feedback.dto';

@Injectable()
export class RecognitionService {
  constructor(
    @InjectRepository(RecognitionBadge)
    private badgeRepository: Repository<RecognitionBadge>,
    @InjectRepository(RecognitionInstance)
    private recognitionRepository: Repository<RecognitionInstance>,
    @InjectRepository(UserGamification)
    private gamificationRepository: Repository<UserGamification>,
    @InjectRepository(MicroFeedback)
    private feedbackRepository: Repository<MicroFeedback>,
  ) {}

  // Badge management methods
  async getBadges(type?: string) {
    const queryBuilder = this.badgeRepository.createQueryBuilder('badge')
      .where('badge.isActive = :isActive', { isActive: true });

    if (type) {
      queryBuilder.andWhere('badge.badgeType = :type', { type });
    }

    queryBuilder.orderBy('badge.createdAt', 'DESC');

    const badges = await queryBuilder.getMany();

    return {
      success: true,
      data: badges
    };
  }

  async getBadge(id: number) {
    const badge = await this.badgeRepository.findOne({
      where: { id, isActive: true },
      relations: ['createdBy', 'recognitionInstances']
    });

    if (!badge) {
      throw new NotFoundException('Badge not found');
    }

    return {
      success: true,
      data: badge
    };
  }

  async createBadge(createBadgeDto: CreateBadgeDto, userId: number) {
    const badge = this.badgeRepository.create({
      ...createBadgeDto,
      createdById: userId
    });

    const savedBadge = await this.badgeRepository.save(badge);

    return {
      success: true,
      data: savedBadge
    };
  }

  async updateBadge(id: number, updateBadgeDto: UpdateBadgeDto, userId: number) {
    const badge = await this.badgeRepository.findOne({ where: { id } });

    if (!badge) {
      throw new NotFoundException('Badge not found');
    }

    if (badge.createdById !== userId) {
      throw new ForbiddenException('Access denied to this badge');
    }

    await this.badgeRepository.update(id, updateBadgeDto);
    const updatedBadge = await this.badgeRepository.findOne({ where: { id } });

    return {
      success: true,
      data: updatedBadge
    };
  }

  async deleteBadge(id: number, userId: number) {
    const badge = await this.badgeRepository.findOne({ where: { id } });

    if (!badge) {
      throw new NotFoundException('Badge not found');
    }

    if (badge.createdById !== userId) {
      throw new ForbiddenException('Access denied to this badge');
    }

    await this.badgeRepository.update(id, { isActive: false });

    return {
      success: true,
      message: 'Badge deactivated successfully'
    };
  }

  // Recognition methods
  async giveRecognition(giveRecognitionDto: GiveRecognitionDto, giverId: number) {
    const badge = await this.badgeRepository.findOne({
      where: { id: giveRecognitionDto.badgeId }
    });

    if (!badge) {
      throw new NotFoundException('Badge not found');
    }

    const recognition = this.recognitionRepository.create({
      ...giveRecognitionDto,
      giverId,
      pointsAwarded: badge.pointValue
    });

    const savedRecognition = await this.recognitionRepository.save(recognition);

    // Update gamification data
    await this.updateGamificationData(giverId, giveRecognitionDto.receiverId, badge.pointValue);

    return {
      success: true,
      data: savedRecognition
    };
  }

  async getReceivedRecognitions(userId: number, limit: number, offset: number) {
    const recognitions = await this.recognitionRepository.find({
      where: { receiverId: userId },
      relations: ['badge', 'giver'],
      order: { givenAt: 'DESC' },
      take: limit,
      skip: offset
    });

    return {
      success: true,
      data: recognitions
    };
  }

  async getGivenRecognitions(userId: number, limit: number, offset: number) {
    const recognitions = await this.recognitionRepository.find({
      where: { giverId: userId },
      relations: ['badge', 'receiver'],
      order: { givenAt: 'DESC' },
      take: limit,
      skip: offset
    });

    return {
      success: true,
      data: recognitions
    };
  }

  async getPublicRecognitionFeed(limit: number, offset: number) {
    const recognitions = await this.recognitionRepository.find({
      where: { isPublic: true },
      relations: ['badge', 'giver', 'receiver'],
      order: { givenAt: 'DESC' },
      take: limit,
      skip: offset
    });

    return {
      success: true,
      data: recognitions
    };
  }

  async getLeaderboard(period: string) {
    // Implementation for recognition leaderboard
    const recognitions = await this.recognitionRepository
      .createQueryBuilder('recognition')
      .select('recognition.receiverId', 'userId')
      .addSelect('COUNT(*)', 'recognitionCount')
      .addSelect('SUM(recognition.pointsAwarded)', 'totalPoints')
      .leftJoin('recognition.receiver', 'user')
      .addSelect('user.firstName', 'firstName')
      .addSelect('user.lastName', 'lastName')
      .groupBy('recognition.receiverId')
      .orderBy('totalPoints', 'DESC')
      .limit(10)
      .getRawMany();

    return {
      success: true,
      data: recognitions
    };
  }

  // Gamification methods
  async getUserGamification(userId: number) {
    let gamification = await this.gamificationRepository.findOne({
      where: { userId },
      relations: ['user']
    });

    if (!gamification) {
      // Create initial gamification record
      gamification = this.gamificationRepository.create({
        userId,
        totalPoints: 0,
        currentLevel: 1,
        badgesEarned: 0,
        recognitionsGiven: 0,
        recognitionsReceived: 0
      });
      gamification = await this.gamificationRepository.save(gamification);
    }

    return {
      success: true,
      data: gamification
    };
  }

  async getPointsLeaderboard(limit: number) {
    const leaderboard = await this.gamificationRepository.find({
      relations: ['user'],
      order: { totalPoints: 'DESC' },
      take: limit
    });

    return {
      success: true,
      data: leaderboard
    };
  }

  async getBadgesLeaderboard(limit: number) {
    const leaderboard = await this.gamificationRepository.find({
      relations: ['user'],
      order: { badgesEarned: 'DESC' },
      take: limit
    });

    return {
      success: true,
      data: leaderboard
    };
  }

  // Micro feedback methods
  async giveMicroFeedback(microFeedbackDto: MicroFeedbackDto, giverId: number) {
    const feedback = this.feedbackRepository.create({
      ...microFeedbackDto,
      giverId
    });

    const savedFeedback = await this.feedbackRepository.save(feedback);

    return {
      success: true,
      data: savedFeedback
    };
  }

  async getReceivedMicroFeedback(userId: number, limit: number, type?: string) {
    const queryBuilder = this.feedbackRepository.createQueryBuilder('feedback')
      .leftJoinAndSelect('feedback.giver', 'giver')
      .where('feedback.receiverId = :userId', { userId });

    if (type) {
      queryBuilder.andWhere('feedback.feedbackType = :type', { type });
    }

    queryBuilder
      .orderBy('feedback.givenAt', 'DESC')
      .take(limit);

    const feedback = await queryBuilder.getMany();

    return {
      success: true,
      data: feedback
    };
  }

  async getGivenMicroFeedback(userId: number, limit: number, type?: string) {
    const queryBuilder = this.feedbackRepository.createQueryBuilder('feedback')
      .leftJoinAndSelect('feedback.receiver', 'receiver')
      .where('feedback.giverId = :userId', { userId });

    if (type) {
      queryBuilder.andWhere('feedback.feedbackType = :type', { type });
    }

    queryBuilder
      .orderBy('feedback.givenAt', 'DESC')
      .take(limit);

    const feedback = await queryBuilder.getMany();

    return {
      success: true,
      data: feedback
    };
  }

  // Analytics methods
  async getRecognitionAnalytics(user: any) {
    const userGamification = await this.getUserGamification(user.id);
    
    const recentRecognitions = await this.recognitionRepository.count({
      where: { receiverId: user.id }
    });

    const recentFeedback = await this.feedbackRepository.count({
      where: { receiverId: user.id }
    });

    return {
      success: true,
      data: {
        gamification: userGamification.data,
        totalRecognitions: recentRecognitions,
        totalFeedback: recentFeedback
      }
    };
  }

  async getRecognitionTrends(period: string, orgUnitId?: number) {
    // Implementation for recognition trends
    const recognitions = await this.recognitionRepository
      .createQueryBuilder('recognition')
      .select('DATE(recognition.givenAt)', 'date')
      .addSelect('COUNT(*)', 'count')
      .groupBy('DATE(recognition.givenAt)')
      .orderBy('date', 'DESC')
      .limit(30)
      .getRawMany();

    return {
      success: true,
      data: recognitions
    };
  }

  // Helper methods
  private async updateGamificationData(giverId: number, receiverId: number, points: number) {
    // Update giver's data
    let giverGamification = await this.gamificationRepository.findOne({ where: { userId: giverId } });
    if (!giverGamification) {
      giverGamification = this.gamificationRepository.create({
        userId: giverId,
        totalPoints: 0,
        currentLevel: 1,
        badgesEarned: 0,
        recognitionsGiven: 1,
        recognitionsReceived: 0
      });
    } else {
      giverGamification.recognitionsGiven += 1;
    }
    await this.gamificationRepository.save(giverGamification);

    // Update receiver's data
    let receiverGamification = await this.gamificationRepository.findOne({ where: { userId: receiverId } });
    if (!receiverGamification) {
      receiverGamification = this.gamificationRepository.create({
        userId: receiverId,
        totalPoints: points,
        currentLevel: 1,
        badgesEarned: 1,
        recognitionsGiven: 0,
        recognitionsReceived: 1
      });
    } else {
      receiverGamification.totalPoints += points;
      receiverGamification.badgesEarned += 1;
      receiverGamification.recognitionsReceived += 1;
      receiverGamification.currentLevel = Math.floor(receiverGamification.totalPoints / 100) + 1;
    }
    await this.gamificationRepository.save(receiverGamification);
  }
}
