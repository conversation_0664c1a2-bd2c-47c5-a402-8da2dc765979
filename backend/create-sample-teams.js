const mysql = require('mysql2/promise');

async function createSampleTeams() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'debian-sys-maint',
      password: 'a500cd12558364e3f91966f73b1fe8ea009531ff669bf74c',
      database: 'ehrx'
    });

    console.log('🔧 Connected to database');

    // Create sample divisions and teams
    const divisions = [
      { name: 'Technology Division', description: 'IT and Development teams' },
      { name: 'Operations Division', description: 'Operations and Support teams' },
      { name: 'Business Division', description: 'Sales and Marketing teams' }
    ];

    for (const division of divisions) {
      // Insert division
      const [divResult] = await connection.execute(`
        INSERT INTO organizational_units (name, description, type, created_at, updated_at)
        VALUES (?, ?, 'division', NOW(), NOW())
        ON DUPLICATE KEY UPDATE updated_at = NOW()
      `, [division.name, division.description]);

      let divisionId = divResult.insertId;
      
      // If no insertId (duplicate), get the existing ID
      if (!divisionId) {
        const [existing] = await connection.execute(
          'SELECT id FROM organizational_units WHERE name = ? AND type = "division"', 
          [division.name]
        );
        divisionId = existing[0]?.id;
      }

      if (!divisionId) {
        console.log(`❌ Failed to create/find division: ${division.name}`);
        continue;
      }

      console.log(`✅ Created/found division: ${division.name} (ID: ${divisionId})`);

      // Create teams under each division
      const teamNames = division.name.includes('Technology') 
        ? ['Frontend Development', 'Backend Development', 'DevOps']
        : division.name.includes('Operations')
        ? ['Customer Support', 'Quality Assurance', 'Infrastructure']
        : ['Sales Team', 'Marketing Team', 'Business Development'];

      for (const teamName of teamNames) {
        await connection.execute(`
          INSERT INTO organizational_units (name, description, type, parent_id, created_at, updated_at)
          VALUES (?, ?, 'team', ?, NOW(), NOW())
          ON DUPLICATE KEY UPDATE updated_at = NOW()
        `, [teamName, `${teamName} under ${division.name}`, divisionId]);
        
        console.log(`✅ Created team: ${teamName}`);
      }
    }

    // Verify teams were created
    const [teams] = await connection.execute('SELECT id, name, type FROM organizational_units WHERE type = "team"');
    console.log(`📊 Total teams created: ${teams.length}`);
    teams.forEach(team => {
      console.log(`  - ${team.name} (ID: ${team.id})`);
    });

    await connection.end();
    console.log('🔧 Database connection closed');

  } catch (error) {
    console.error('❌ Error creating sample teams:', error.message);
    process.exit(1);
  }
}

createSampleTeams();
