import React from 'react';
import {
  Box,
  Typography,
  Avatar,
} from '@mui/material';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  icon,
  actions
}) => {
  return (
    <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box display="flex" alignItems="center">
          {icon && (
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
              {icon}
            </Avatar>
          )}
          <Box>
            <Typography variant="h4" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="h6" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
        {actions && (
          <Box>
            {actions}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default PageHeader;
