import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/users.service';
import { User, AccountStatus } from '../users/entities/user.entity';
import { SecurityAuditService, SecurityEventType, SecuritySeverity } from './services/security-audit.service';
// import { RateLimitingService } from '../security/services/rate-limiting.service'; // GHOST MODULE

@Injectable()
export class AuthService {
  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private securityAuditService: SecurityAuditService,
    // private rateLimitingService: RateLimitingService, // GHOST MODULE
  ) { }

  async validateUser(email: string, pass: string, clientIp?: string): Promise<any> {
    // 🔐 NIS2-COMPLIANT: Check rate limiting before processing
    // if (clientIp && this.rateLimitingService.isIPBlocked(clientIp)) { // GHOST MODULE
    //   await this.securityAuditService.logSecurityEvent({
    //     eventType: SecurityEventType.LOGIN_FAILED,
    //     email,
    //     ipAddress: clientIp,
    //     details: { reason: 'IP_BLOCKED', email },
    //     severity: SecuritySeverity.HIGH
    //   });
    //   throw new UnauthorizedException('Too many failed attempts. IP temporarily blocked.');
    // }

    const user = await this.usersService.findByEmail(email);

    if (!user) {
      // 🔐 Log failed attempt for non-existent user
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        email,
        ipAddress: clientIp,
        details: { reason: 'USER_NOT_FOUND', email },
        severity: SecuritySeverity.MEDIUM
      });
      if (clientIp) {
        // this.rateLimitingService.recordFailedAttempt(clientIp); // GHOST MODULE
      }
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if account is locked
    if (user.accountStatus === AccountStatus.LOCKED ||
      (user.accountLockedUntil && new Date() < user.accountLockedUntil)) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        userId: user.id,
        email,
        ipAddress: clientIp,
        details: { reason: 'ACCOUNT_LOCKED', email },
        severity: SecuritySeverity.HIGH
      });
      throw new UnauthorizedException('Account is locked due to too many failed login attempts');
    }

    // Check if account is active
    if (user.accountStatus !== AccountStatus.ACTIVE || !user.isActive) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        userId: user.id,
        email,
        ipAddress: clientIp,
        details: { reason: 'ACCOUNT_INACTIVE', email },
        severity: SecuritySeverity.MEDIUM
      });
      throw new UnauthorizedException('Account is not active');
    }

    // Validate password
    const isPasswordValid = await bcrypt.compare(pass, user.password);

    if (!isPasswordValid) {
      // 🔐 Log failed password attempt
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        userId: user.id,
        email,
        ipAddress: clientIp,
        details: { reason: 'INVALID_PASSWORD', email },
        severity: SecuritySeverity.MEDIUM
      });
      if (clientIp) {
        // this.rateLimitingService.recordFailedAttempt(clientIp, user.id.toString()); // GHOST MODULE
      }
      await this.handleFailedLogin(user);
      throw new UnauthorizedException('Invalid credentials');
    }

    // 🔐 Successful login - log and reset counters
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      email,
      ipAddress: clientIp,
      details: { email },
      severity: SecuritySeverity.LOW
    });
    if (clientIp) {
      // this.rateLimitingService.recordSuccessfulAttempt(clientIp, user.id.toString()); // GHOST MODULE
    }
    await this.handleSuccessfulLogin(user, clientIp);

    const { password, twoFactorSecret, sessionToken, ...result } = user;
    return result;
  }

  async login(user: any, clientIp?: string) {
    const payload = {
      email: user.email,
      sub: user.id,
      role: user.role,
      sessionId: this.generateSessionId()
    };

    const accessToken = this.jwtService.sign(payload);

    // Update session information
    await this.updateUserSession(user.id, accessToken, clientIp);

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        mustChangePassword: user.mustChangePassword,
        twoFactorEnabled: user.twoFactorEnabled,
        lastLoginAt: user.lastLoginAt,
      },
      access_token: accessToken,
      expires_in: 3600, // 1 hour
    };
  }

  async logout(userId: number): Promise<void> {
    await this.userRepository.update(userId, {
      sessionToken: null,
      sessionExpiresAt: null,
    });
  }

  async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    // Update password and related fields
    await this.userRepository.update(userId, {
      password: hashedNewPassword,
      passwordChangedAt: new Date(),
      mustChangePassword: false,
    });
  }

  private async handleFailedLogin(user: User): Promise<void> {
    const failedAttempts = user.failedLoginAttempts + 1;
    const updateData: Partial<User> = {
      failedLoginAttempts: failedAttempts,
    };

    // Lock account if max attempts reached
    if (failedAttempts >= this.MAX_LOGIN_ATTEMPTS) {
      updateData.accountStatus = AccountStatus.LOCKED;
      updateData.accountLockedUntil = new Date(Date.now() + this.LOCKOUT_DURATION);
    }

    await this.userRepository.update(user.id, updateData);
  }

  private async handleSuccessfulLogin(user: User, clientIp?: string): Promise<void> {
    await this.userRepository.update(user.id, {
      failedLoginAttempts: 0,
      lastLoginAt: new Date(),
      lastLoginIp: clientIp,
      accountLockedUntil: null,
    });
  }

  private async updateUserSession(userId: number, token: string, clientIp?: string): Promise<void> {
    const expiresAt = new Date(Date.now() + 3600 * 1000); // 1 hour from now

    await this.userRepository.update(userId, {
      sessionToken: token,
      sessionExpiresAt: expiresAt,
    });
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);
  }

  async validateSession(token: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { sessionToken: token },
    });

    if (!user || !user.sessionExpiresAt || new Date() > user.sessionExpiresAt) {
      return null;
    }

    return user;
  }

  /**
   * Generate default password for new users (lastName + 'X123')
   */
  static generateDefaultPassword(lastName: string): string {
    return `${lastName}X123`;
  }

  /**
   * Hash password for storage
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12);
  }
}
