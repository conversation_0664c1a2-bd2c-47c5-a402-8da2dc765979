import { IsNotEmpty, Is<PERSON>num, IsOptional, IsN<PERSON>ber, IsString } from 'class-validator';
import { RuleType, ConditionOperator } from '../entities/scoring-rule.entity';

export class CreateScoringRuleDto {
  @IsNotEmpty()
  @IsEnum(RuleType)
  ruleType: RuleType;

  @IsOptional()
  @IsString()
  conditionField?: string;

  @IsOptional()
  @IsEnum(ConditionOperator)
  conditionOperator?: ConditionOperator;

  @IsOptional()
  @IsString()
  conditionValue?: string;

  @IsNotEmpty()
  @IsNumber()
  scoreAdjustment: number;
}
