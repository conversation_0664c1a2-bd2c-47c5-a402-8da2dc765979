import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum AIInitiativeStatus {
  PROPOSED = 'proposed',
  EVALUATING = 'evaluating',
  APPROVED = 'approved', 
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  DEPLOYED = 'deployed',
  REJECTED = 'rejected'
}

export enum AIInitiativeCategory {
  AUTOMATION = 'automation',
  ANALYTICS = 'analytics',
  DECISION_SUPPORT = 'decision_support',
  NATURAL_LANGUAGE = 'natural_language',
  COMPUTER_VISION = 'computer_vision',
  PREDICTIVE_MODELING = 'predictive_modeling',
  OTHER = 'other'
}

@Entity('ai_initiatives')
export class AIInitiative {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: AIInitiativeStatus,
    default: AIInitiativeStatus.PROPOSED
  })
  status: AIInitiativeStatus;

  @Column({
    type: 'enum',
    enum: AIInitiativeCategory,
    default: AIInitiativeCategory.OTHER
  })
  category: AIInitiativeCategory;

  @Column({ name: 'business_case', type: 'text', nullable: true })
  businessCase: string;

  @Column({ name: 'expected_benefits', type: 'text', nullable: true })
  expectedBenefits: string;

  @Column({ name: 'implementation_challenges', type: 'text', nullable: true })
  implementationChallenges: string;

  @Column({ name: 'data_requirements', type: 'text', nullable: true })
  dataRequirements: string;

  @Column({ name: 'target_completion_date', nullable: true })
  targetCompletionDate: Date;

  @Column({ name: 'actual_completion_date', nullable: true })
  actualCompletionDate: Date;

  @Column({ name: 'created_by' })
  createdById: number;

  @Column({ name: 'assigned_to', nullable: true })
  assignedToId: number;

  @Column({ name: 'assessment_id', nullable: true })
  assessmentId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_to' })
  assignedTo: User;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
