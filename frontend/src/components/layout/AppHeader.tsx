import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
} from '@mui/material';
import UserAuthenticationModule from '../auth/UserAuthenticationModule';

interface AppHeaderProps {
  title?: string;
}

const AppHeader: React.FC<AppHeaderProps> = ({
  title = "EHRX Performance Management System"
}) => {
  console.log('🔍 AppHeader rendering');

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
          {title}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <UserAuthenticationModule />
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader;
