-- Create KPI values for July 2025 submissions
-- Get KPI IDs
SET @fte_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'FTE' LIMIT 1);
SET @attrition_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'ATTRITION' LIMIT 1);
SET @sla_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'SLA' LIMIT 1);
SET @utilization_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'UTILIZATION' LIMIT 1);
SET @time_reg_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'TIME_REGISTRATION' LIMIT 1);
SET @compliance_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'COMPLIANCE' LIMIT 1);
SET @ab_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'AB' LIMIT 1);
SET @pto_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'PTO' LIMIT 1);
SET @rto_kpi = (SELECT id FROM monthly_dashboard_kpis WHERE name = 'RTO' LIMIT 1);

-- Frontend Development Team (ID: 15) - July 2025 submission (ID: 14)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(14, @fte_kpi, 12, 12, 'green', NULL, NOW(), NOW()),
(14, @attrition_kpi, 4.2, 5.0, 'green', '{"resignations_this_month": 0, "annual_rate": 4.2}', NOW(), NOW()),
(14, @sla_kpi, 98.5, 100.0, 'green', NULL, NOW(), NOW()),
(14, @utilization_kpi, 87.3, 85.0, 'green', NULL, NOW(), NOW()),
(14, @time_reg_kpi, 95.8, 100.0, 'green', NULL, NOW(), NOW()),
(14, @compliance_kpi, 0, 0, 'green', NULL, NOW(), NOW()),
(14, @ab_kpi, 15000, 0, 'green', NULL, NOW(), NOW()),
(14, @pto_kpi, 78.2, 75.0, 'green', '{"days_taken_this_month": 8, "ytd_percentage": 78.2}', NOW(), NOW()),
(14, @rto_kpi, 92.1, 100.0, 'yellow', '{"raw_attendance_percentage": 88.1, "leave_days_adjustment": 4, "adjusted_percentage": 92.1}', NOW(), NOW());

-- Backend Development Team (ID: 16) - July 2025 submission (ID: 25)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(25, @fte_kpi, 8, 8, 'green', NULL, NOW(), NOW()),
(25, @attrition_kpi, 8.1, 5.0, 'yellow', '{"resignations_this_month": 1, "annual_rate": 8.1}', NOW(), NOW()),
(25, @sla_kpi, 87.2, 100.0, 'red', NULL, NOW(), NOW()),
(25, @utilization_kpi, 82.7, 85.0, 'green', NULL, NOW(), NOW()),
(25, @time_reg_kpi, 89.3, 100.0, 'yellow', NULL, NOW(), NOW()),
(25, @compliance_kpi, 1, 0, 'red', NULL, NOW(), NOW()),
(25, @ab_kpi, -8500, 0, 'yellow', NULL, NOW(), NOW()),
(25, @pto_kpi, 68.9, 75.0, 'yellow', '{"days_taken_this_month": 5, "ytd_percentage": 68.9}', NOW(), NOW()),
(25, @rto_kpi, 85.4, 100.0, 'red', '{"raw_attendance_percentage": 81.4, "leave_days_adjustment": 4, "adjusted_percentage": 85.4}', NOW(), NOW());

-- Mobile Development Team (ID: 17) - July 2025 submission (ID: 37)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(37, @fte_kpi, 6, 6, 'green', NULL, NOW(), NOW()),
(37, @attrition_kpi, 3.8, 5.0, 'green', '{"resignations_this_month": 0, "annual_rate": 3.8}', NOW(), NOW()),
(37, @sla_kpi, 99.2, 100.0, 'green', NULL, NOW(), NOW()),
(37, @utilization_kpi, 91.5, 85.0, 'green', NULL, NOW(), NOW()),
(37, @time_reg_kpi, 97.1, 100.0, 'green', NULL, NOW(), NOW()),
(37, @compliance_kpi, 0, 0, 'green', NULL, NOW(), NOW()),
(37, @ab_kpi, 22000, 0, 'green', NULL, NOW(), NOW()),
(37, @pto_kpi, 72.4, 75.0, 'green', '{"days_taken_this_month": 4, "ytd_percentage": 72.4}', NOW(), NOW()),
(37, @rto_kpi, 96.8, 100.0, 'green', '{"raw_attendance_percentage": 94.8, "leave_days_adjustment": 2, "adjusted_percentage": 96.8}', NOW(), NOW());

-- Full-Stack Development Team (ID: 18) - July 2025 submission (ID: 49)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(49, @fte_kpi, 10, 10, 'green', NULL, NOW(), NOW()),
(49, @attrition_kpi, 6.7, 5.0, 'yellow', '{"resignations_this_month": 1, "annual_rate": 6.7}', NOW(), NOW()),
(49, @sla_kpi, 94.8, 100.0, 'yellow', NULL, NOW(), NOW()),
(49, @utilization_kpi, 88.9, 85.0, 'green', NULL, NOW(), NOW()),
(49, @time_reg_kpi, 92.4, 100.0, 'yellow', NULL, NOW(), NOW()),
(49, @compliance_kpi, 0, 0, 'green', NULL, NOW(), NOW()),
(49, @ab_kpi, 5200, 0, 'green', NULL, NOW(), NOW()),
(49, @pto_kpi, 81.3, 75.0, 'green', '{"days_taken_this_month": 7, "ytd_percentage": 81.3}', NOW(), NOW()),
(49, @rto_kpi, 89.7, 100.0, 'yellow', '{"raw_attendance_percentage": 86.7, "leave_days_adjustment": 3, "adjusted_percentage": 89.7}', NOW(), NOW());

-- Data Engineering Team (ID: 19) - July 2025 submission (ID: 61)
INSERT INTO monthly_dashboard_kpi_values (
    submission_id, kpi_id, value, target_value, traffic_light_status, additional_data, created_at, updated_at
) VALUES
(61, @fte_kpi, 7, 7, 'green', NULL, NOW(), NOW()),
(61, @attrition_kpi, 2.1, 5.0, 'green', '{"resignations_this_month": 0, "annual_rate": 2.1}', NOW(), NOW()),
(61, @sla_kpi, 96.7, 100.0, 'green', NULL, NOW(), NOW()),
(61, @utilization_kpi, 79.3, 85.0, 'yellow', NULL, NOW(), NOW()),
(61, @time_reg_kpi, 98.9, 100.0, 'green', NULL, NOW(), NOW()),
(61, @compliance_kpi, 2, 0, 'red', NULL, NOW(), NOW()),
(61, @ab_kpi, -12000, 0, 'red', NULL, NOW(), NOW()),
(61, @pto_kpi, 69.8, 75.0, 'yellow', '{"days_taken_this_month": 6, "ytd_percentage": 69.8}', NOW(), NOW()),
(61, @rto_kpi, 94.2, 100.0, 'green', '{"raw_attendance_percentage": 91.2, "leave_days_adjustment": 3, "adjusted_percentage": 94.2}', NOW(), NOW());
