import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { AssessmentArea } from './assessment-area.entity';
import { AssessmentTemplateCriteria } from './assessment-template-criteria.entity';

export enum TemplateLevel {
  HR_LEVEL = 'hr_level',
  ORGANIZATIONAL_LEVEL = 'organizational_level',
  TEAM_LEVEL = 'team_level'
}

@Entity('assessment_templates')
export class AssessmentTemplate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @Column({ name: 'organizational_unit_id', nullable: true })
  organizationalUnitId: number;

  @ManyToOne(() => OrganizationalUnit, { nullable: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @Column({
    type: 'enum',
    enum: TemplateLevel,
    name: 'template_level',
    default: TemplateLevel.TEAM_LEVEL
  })
  templateLevel: TemplateLevel;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ default: '1.0' })
  version: string;

  @Column({ name: 'parent_template_id', nullable: true })
  parentTemplateId: number;

  @ManyToOne(() => AssessmentTemplate, { nullable: true })
  @JoinColumn({ name: 'parent_template_id' })
  parentTemplate: AssessmentTemplate;

  @Column({ name: 'is_global', default: false })
  isGlobal: boolean;

  @OneToMany(() => AssessmentArea, area => area.template)
  areas: AssessmentArea[];

  @OneToMany(() => AssessmentTemplateCriteria, templateCriteria => templateCriteria.template)
  templateCriteria: AssessmentTemplateCriteria[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
