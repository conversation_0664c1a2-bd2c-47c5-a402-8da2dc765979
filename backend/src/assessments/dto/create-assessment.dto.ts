import { IsNotEmpty, IsEnum, IsNumber, IsOptional, IsDate, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AssessmentStatus } from '../entities/assessment-instance.entity';
import { CreateAssessmentResponseDto } from './create-assessment-response.dto';

export class CreateAssessmentDto {
  @IsNotEmpty()
  @IsNumber()
  templateId: number;

  @IsNotEmpty()
  @IsNumber()
  employeeId: number;

  @IsOptional()
  @IsEnum(AssessmentStatus)
  status?: AssessmentStatus;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  assessmentDate: Date;

  @IsOptional()
  notes?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAssessmentResponseDto)
  responses?: CreateAssessmentResponseDto[];
}
