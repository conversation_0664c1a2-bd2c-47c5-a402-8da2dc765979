-- Sample Organizational Structure for Monthly Dashboards
-- This creates a realistic IT company organizational hierarchy

USE ehrx;

-- =====================================================
-- INSERT ORGANIZATIONAL STRUCTURE
-- =====================================================

-- Level 0: Organization (Root)
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(1, 'TrustHansen Technologies', 'organization', 'Main organization - IT consulting and development company', NULL, 0, NULL, 10000000.00, TRUE);

-- Level 1: Divisions
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(2, 'Technology Division', 'division', 'Core technology development and engineering', 1, 1, NULL, 5000000.00, TRUE),
(3, 'Consulting Division', 'division', 'Client consulting and professional services', 1, 1, NULL, 3000000.00, TRUE),
(4, 'Operations Division', 'division', 'IT operations, support, and infrastructure', 1, 1, NULL, 2000000.00, TRUE);

-- Level 2: Departments within Technology Division
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(5, 'Software Development', 'department', 'Custom software development and applications', 2, 2, NULL, 2000000.00, TRUE),
(6, 'Data & Analytics', 'department', 'Data science, analytics, and BI solutions', 2, 2, NULL, 1500000.00, TRUE),
(7, 'DevOps & Infrastructure', 'department', 'DevOps, cloud infrastructure, and automation', 2, 2, NULL, 1000000.00, TRUE),
(8, 'Quality Assurance', 'department', 'Software testing and quality assurance', 2, 2, NULL, 500000.00, TRUE);

-- Level 2: Departments within Consulting Division
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(9, 'Business Consulting', 'department', 'Business process consulting and optimization', 3, 2, NULL, 1500000.00, TRUE),
(10, 'Technical Consulting', 'department', 'Technical architecture and implementation consulting', 3, 2, NULL, 1000000.00, TRUE),
(11, 'Project Management', 'department', 'Project management and delivery services', 3, 2, NULL, 500000.00, TRUE);

-- Level 2: Departments within Operations Division
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(12, 'IT Support', 'department', 'Technical support and helpdesk services', 4, 2, NULL, 800000.00, TRUE),
(13, 'System Administration', 'department', 'System administration and maintenance', 4, 2, NULL, 600000.00, TRUE),
(14, 'Security & Compliance', 'department', 'Information security and compliance management', 4, 2, NULL, 600000.00, TRUE);

-- Level 3: Teams within Software Development Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(15, 'Frontend Development Team', 'team', 'React, Angular, and frontend technologies', 5, 3, NULL, 600000.00, TRUE),
(16, 'Backend Development Team', 'team', 'Node.js, .NET, and backend services', 5, 3, NULL, 700000.00, TRUE),
(17, 'Mobile Development Team', 'team', 'iOS and Android mobile applications', 5, 3, NULL, 400000.00, TRUE),
(18, 'Full-Stack Development Team', 'team', 'End-to-end application development', 5, 3, NULL, 300000.00, TRUE);

-- Level 3: Teams within Data & Analytics Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(19, 'Data Engineering Team', 'team', 'Data pipelines and ETL processes', 6, 3, NULL, 500000.00, TRUE),
(20, 'Business Intelligence Team', 'team', 'BI dashboards and reporting solutions', 6, 3, NULL, 400000.00, TRUE),
(21, 'Machine Learning Team', 'team', 'AI/ML models and data science', 6, 3, NULL, 600000.00, TRUE);

-- Level 3: Teams within DevOps & Infrastructure Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(22, 'Cloud Infrastructure Team', 'team', 'AWS, Azure, and cloud services', 7, 3, NULL, 400000.00, TRUE),
(23, 'DevOps Automation Team', 'team', 'CI/CD pipelines and automation', 7, 3, NULL, 300000.00, TRUE),
(24, 'Platform Engineering Team', 'team', 'Internal platforms and tooling', 7, 3, NULL, 300000.00, TRUE);

-- Level 3: Teams within Quality Assurance Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(25, 'Manual Testing Team', 'team', 'Manual testing and user acceptance testing', 8, 3, NULL, 200000.00, TRUE),
(26, 'Automation Testing Team', 'team', 'Test automation and performance testing', 8, 3, NULL, 300000.00, TRUE);

-- Level 3: Teams within Business Consulting Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(27, 'Process Optimization Team', 'team', 'Business process analysis and optimization', 9, 3, NULL, 500000.00, TRUE),
(28, 'Digital Transformation Team', 'team', 'Digital transformation consulting', 9, 3, NULL, 600000.00, TRUE),
(29, 'Change Management Team', 'team', 'Organizational change management', 9, 3, NULL, 400000.00, TRUE);

-- Level 3: Teams within Technical Consulting Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(30, 'Solution Architecture Team', 'team', 'Technical solution design and architecture', 10, 3, NULL, 500000.00, TRUE),
(31, 'Integration Services Team', 'team', 'System integration and API development', 10, 3, NULL, 300000.00, TRUE),
(32, 'Performance Optimization Team', 'team', 'System performance and optimization', 10, 3, NULL, 200000.00, TRUE);

-- Level 3: Teams within Project Management Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(33, 'Agile Delivery Team', 'team', 'Agile project management and Scrum', 11, 3, NULL, 250000.00, TRUE),
(34, 'Enterprise PMO Team', 'team', 'Enterprise project management office', 11, 3, NULL, 250000.00, TRUE);

-- Level 3: Teams within IT Support Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(35, 'Level 1 Support Team', 'team', 'First-line technical support', 12, 3, NULL, 300000.00, TRUE),
(36, 'Level 2 Support Team', 'team', 'Advanced technical support', 12, 3, NULL, 300000.00, TRUE),
(37, 'Customer Success Team', 'team', 'Customer success and account management', 12, 3, NULL, 200000.00, TRUE);

-- Level 3: Teams within System Administration Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(38, 'Server Administration Team', 'team', 'Server management and maintenance', 13, 3, NULL, 300000.00, TRUE),
(39, 'Network Administration Team', 'team', 'Network infrastructure and management', 13, 3, NULL, 300000.00, TRUE);

-- Level 3: Teams within Security & Compliance Department
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(40, 'Information Security Team', 'team', 'Cybersecurity and threat management', 14, 3, NULL, 400000.00, TRUE),
(41, 'Compliance & Audit Team', 'team', 'Regulatory compliance and internal audits', 14, 3, NULL, 200000.00, TRUE);

-- =====================================================
-- CREATE SAMPLE USERS FOR TEAMS
-- =====================================================

-- Insert sample users if they don't exist
INSERT IGNORE INTO users (id, email, password, first_name, last_name, role, organizational_unit_id, is_active) VALUES
(1, '<EMAIL>', '$2b$10$example', 'John', 'Hansen', 'ceo', 1, TRUE),
(2, '<EMAIL>', '$2b$10$example', 'Sarah', 'Johnson', 'director', 2, TRUE),
(3, '<EMAIL>', '$2b$10$example', 'Michael', 'Brown', 'director', 3, TRUE),
(4, '<EMAIL>', '$2b$10$example', 'Lisa', 'Davis', 'director', 4, TRUE),
(5, '<EMAIL>', '$2b$10$example', 'Alex', 'Wilson', 'manager', 15, TRUE),
(6, '<EMAIL>', '$2b$10$example', 'Emma', 'Taylor', 'manager', 16, TRUE),
(7, '<EMAIL>', '$2b$10$example', 'David', 'Anderson', 'manager', 17, TRUE),
(8, '<EMAIL>', '$2b$10$example', 'Jennifer', 'Martinez', 'manager', 19, TRUE),
(9, '<EMAIL>', '$2b$10$example', 'Robert', 'Garcia', 'manager', 22, TRUE),
(10, '<EMAIL>', '$2b$10$example', 'Michelle', 'Rodriguez', 'manager', 25, TRUE);

-- Update organizational units with manager assignments
UPDATE organizational_units SET manager_id = 1 WHERE id = 1;  -- CEO manages organization
UPDATE organizational_units SET manager_id = 2 WHERE id = 2;  -- Tech Director
UPDATE organizational_units SET manager_id = 3 WHERE id = 3;  -- Consulting Director  
UPDATE organizational_units SET manager_id = 4 WHERE id = 4;  -- Ops Director
UPDATE organizational_units SET manager_id = 5 WHERE id = 15; -- Frontend Manager
UPDATE organizational_units SET manager_id = 6 WHERE id = 16; -- Backend Manager
UPDATE organizational_units SET manager_id = 7 WHERE id = 17; -- Mobile Manager
UPDATE organizational_units SET manager_id = 8 WHERE id = 19; -- Data Manager
UPDATE organizational_units SET manager_id = 9 WHERE id = 22; -- DevOps Manager
UPDATE organizational_units SET manager_id = 10 WHERE id = 25; -- QA Manager

SELECT 'Organizational structure created successfully!' AS status;
SELECT COUNT(*) as total_units FROM organizational_units;
SELECT type, COUNT(*) as count FROM organizational_units GROUP BY type;
