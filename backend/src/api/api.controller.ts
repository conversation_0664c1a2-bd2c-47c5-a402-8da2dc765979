import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('api')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ApiController {

  @Get('endpoints')
  @Roles(UserRole.HR_ADMIN)
  async getApiEndpoints() {
    return {
      endpoints: [
        {
          id: 1,
          path: '/api/users',
          method: 'GET',
          description: 'Get all users',
          authentication: 'required',
          roles: ['hr_admin', 'admin'],
          status: 'active'
        },
        {
          id: 2,
          path: '/api/users',
          method: 'POST',
          description: 'Create new user',
          authentication: 'required',
          roles: ['hr_admin', 'admin'],
          status: 'active'
        },
        {
          id: 3,
          path: '/api/auth/login',
          method: 'POST',
          description: 'User authentication',
          authentication: 'none',
          roles: ['public'],
          status: 'active'
        },
        {
          id: 4,
          path: '/api/teams',
          method: 'GET',
          description: 'Get all teams',
          authentication: 'required',
          roles: ['hr_admin', 'admin', 'manager'],
          status: 'active'
        },
        {
          id: 5,
          path: '/api/assessments',
          method: 'GET',
          description: 'Get assessments',
          authentication: 'required',
          roles: ['hr_admin', 'admin', 'manager'],
          status: 'active'
        }
      ],
      totalCount: 5,
      categories: ['Authentication', 'Users', 'Teams', 'Assessments', 'System']
    };
  }

  @Get('configuration')
  @Roles(UserRole.HR_ADMIN)
  async getApiConfiguration() {
    return {
      baseUrl: process.env.API_BASE_URL || 'http://localhost:4000/api',
      version: '1.0.0',
      rateLimit: {
        enabled: true,
        requestsPerMinute: 100,
        burstLimit: 200
      },
      cors: {
        enabled: true,
        allowedOrigins: ['http://localhost:3080', 'https://dev.trusthansen.dk'],
        allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        allowedHeaders: ['Content-Type', 'Authorization']
      },
      security: {
        httpsOnly: true,
        jwtExpiration: '1h',
        refreshTokenExpiration: '7d'
      },
      documentation: {
        swaggerEnabled: true,
        swaggerPath: '/api/docs',
        version: '3.0.0'
      }
    };
  }

  @Put('configuration')
  @Roles(UserRole.HR_ADMIN)
  async updateApiConfiguration(@Body() config: any) {
    return {
      success: true,
      message: 'API configuration updated successfully',
      updatedConfig: config
    };
  }

  @Get('metrics')
  @Roles(UserRole.HR_ADMIN)
  async getApiMetrics() {
    return {
      requests: {
        total: 15420,
        today: 342,
        lastHour: 28,
        averageResponseTime: 145
      },
      endpoints: {
        mostUsed: '/api/users',
        slowest: '/api/assessments/templates',
        errors: 12
      },
      authentication: {
        successfulLogins: 89,
        failedLogins: 3,
        activeTokens: 45
      },
      performance: {
        uptime: '99.8%',
        averageLoad: 0.65,
        memoryUsage: '78%'
      }
    };
  }

  @Get('status')
  async getApiStatus() {
    return {
      status: 'operational',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: 'connected',
        authentication: 'active',
        cache: 'active',
        storage: 'active'
      }
    };
  }

  // ===================================================================
  // API KEYS MANAGEMENT
  // ===================================================================

  @Get('keys')
  @Roles(UserRole.HR_ADMIN)
  async getApiKeys() {
    return {
      success: true,
      data: [
        {
          id: '1',
          name: 'Frontend Application',
          key: 'ehrx_frontend_key_' + Math.random().toString(36).substr(2, 16),
          permissions: ['read_users', 'manage_assessments', 'view_analytics'],
          isActive: true,
          expiresAt: null,
          createdAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Mobile Application',
          key: 'ehrx_mobile_key_' + Math.random().toString(36).substr(2, 16),
          permissions: ['read_users', 'participate_assessments'],
          isActive: true,
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Analytics Service',
          key: 'ehrx_analytics_key_' + Math.random().toString(36).substr(2, 16),
          permissions: ['read_analytics', 'generate_reports'],
          isActive: false,
          expiresAt: null,
          createdAt: new Date().toISOString()
        }
      ]
    };
  }

  @Put('keys/:keyId')
  @Roles(UserRole.HR_ADMIN)
  async updateApiKey(@Param('keyId') keyId: string, @Body() keyData: any) {
    return {
      success: true,
      data: {
        id: keyId,
        ...keyData,
        updatedAt: new Date().toISOString()
      },
      message: 'API key updated successfully'
    };
  }

  // ===================================================================
  // RATE LIMITING CONFIGURATION
  // ===================================================================

  @Get('rate-limits')
  @Roles(UserRole.HR_ADMIN)
  async getRateLimitConfigs() {
    return {
      success: true,
      data: [
        {
          id: '1',
          endpoint: '/api/users',
          requestsPerMinute: 100,
          requestsPerHour: 1000,
          isEnabled: true
        },
        {
          id: '2',
          endpoint: '/api/auth/login',
          requestsPerMinute: 10,
          requestsPerHour: 50,
          isEnabled: true
        },
        {
          id: '3',
          endpoint: '/api/assessments',
          requestsPerMinute: 50,
          requestsPerHour: 500,
          isEnabled: true
        },
        {
          id: '4',
          endpoint: '/api/analytics/*',
          requestsPerMinute: 30,
          requestsPerHour: 300,
          isEnabled: true
        }
      ]
    };
  }

  @Put('rate-limits/:limitId')
  @Roles(UserRole.HR_ADMIN)
  async updateRateLimit(@Param('limitId') limitId: string, @Body() limitData: any) {
    return {
      success: true,
      data: {
        id: limitId,
        ...limitData,
        updatedAt: new Date().toISOString()
      },
      message: 'Rate limit configuration updated successfully'
    };
  }
}
