import { IsEnum, IsOptional, IsString, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AssessmentStatus } from '../entities/assessment-instance.entity';
import { UpdateAssessmentResponseDto } from './update-assessment-response.dto';

export class UpdateAssessmentDto {
  @IsOptional()
  @IsEnum(AssessmentStatus)
  status?: AssessmentStatus;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateAssessmentResponseDto)
  responses?: UpdateAssessmentResponseDto[];
}
