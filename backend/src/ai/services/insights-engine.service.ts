import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiInsight, InsightStatus } from '../entities/ai-insight.entity';
import { User } from '../../users/entities/user.entity';
import { AttritionPrediction } from '../entities/attrition-prediction.entity';
import { SurveyResponse } from '../../analytics/entities/survey-response.entity';
import { PerformanceMetric } from '../../analytics/entities/performance-metric.entity';

@Injectable()
export class InsightsEngineService {
  constructor(
    @InjectRepository(AiInsight)
    private readonly aiInsightRepository: Repository<AiInsight>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AttritionPrediction)
    private readonly attritionPredictionRepository: Repository<AttritionPrediction>,
    @InjectRepository(SurveyResponse)
    private readonly surveyResponseRepository: Repository<SurveyResponse>,
    @InjectRepository(PerformanceMetric)
    private readonly performanceMetricRepository: Repository<PerformanceMetric>,
  ) { }

  /**
   * Get AI insights with filtering
   */
  async getInsights(filters: {
    type?: string;
    priority?: string;
    status?: string;
    limit?: number;
  }) {
    try {
      const queryBuilder = this.aiInsightRepository.createQueryBuilder('insight');

      if (filters.type) {
        queryBuilder.andWhere('insight.insightType = :type', { type: filters.type });
      }

      if (filters.priority) {
        queryBuilder.andWhere('insight.priority = :priority', { priority: filters.priority });
      }

      if (filters.status) {
        queryBuilder.andWhere('insight.status = :status', { status: filters.status });
      }

      queryBuilder
        .orderBy('insight.priority', 'DESC')
        .addOrderBy('insight.generatedAt', 'DESC')
        .limit(filters.limit || 20);

      const insights = await queryBuilder.getMany();

      return {
        success: true,
        data: insights.map(insight => ({
          id: insight.id,
          type: insight.insightType,
          priority: insight.priority,
          status: insight.status,
          title: this.extractInsightTitle(insight.insightData),
          summary: this.extractInsightSummary(insight.insightData),
          recommendations: this.extractRecommendations(insight.insightData),
          confidence: insight.confidenceScore,
          generatedAt: insight.generatedAt,
        })),
      };
    } catch (error) {
      console.error('Error getting insights:', error);
      return {
        success: false,
        error: 'Failed to get insights',
      };
    }
  }

  /**
   * Generate new AI insights
   */
  async generateInsights(userId: number) {
    try {
      const insights = [];

      // Generate attrition insights
      const attritionInsights = await this.generateAttritionInsights();
      insights.push(...attritionInsights);

      // Generate engagement insights
      const engagementInsights = await this.generateEngagementInsights();
      insights.push(...engagementInsights);

      // Generate performance insights
      const performanceInsights = await this.generatePerformanceInsights();
      insights.push(...performanceInsights);

      // Generate team dynamics insights
      const teamInsights = await this.generateTeamDynamicsInsights();
      insights.push(...teamInsights);

      // Generate skill gap insights
      const skillInsights = await this.generateSkillGapInsights();
      insights.push(...skillInsights);

      // Save insights to database
      const savedInsights = await this.aiInsightRepository.save(
        insights.map(insight => ({
          ...insight,
          generatedBy: userId,
          generatedAt: new Date(),
          status: 'new',
        }))
      );

      return {
        success: true,
        data: {
          insightsGenerated: savedInsights.length,
          highPriorityCount: savedInsights.filter(i => i.priority === 'high').length,
          mediumPriorityCount: savedInsights.filter(i => i.priority === 'medium').length,
          lowPriorityCount: savedInsights.filter(i => i.priority === 'low').length,
          generatedAt: new Date(),
          generatedBy: userId,
        },
      };
    } catch (error) {
      console.error('Error generating insights:', error);
      return {
        success: false,
        error: 'Failed to generate insights',
      };
    }
  }

  /**
   * Acknowledge an insight
   */
  async acknowledgeInsight(insightId: number, userId: number) {
    try {
      const insight = await this.aiInsightRepository.findOne({
        where: { id: insightId },
      });

      if (!insight) {
        return {
          success: false,
          error: 'Insight not found',
        };
      }

      await this.aiInsightRepository.update(insightId, {
        status: InsightStatus.ACKNOWLEDGED,
      });

      return {
        success: true,
        data: {
          insightId,
          acknowledgedAt: new Date(),
          acknowledgedBy: userId,
        },
      };
    } catch (error) {
      console.error('Error acknowledging insight:', error);
      return {
        success: false,
        error: 'Failed to acknowledge insight',
      };
    }
  }

  /**
   * Dismiss an insight
   */
  async dismissInsight(insightId: number, userId: number) {
    try {
      const insight = await this.aiInsightRepository.findOne({
        where: { id: insightId },
      });

      if (!insight) {
        return {
          success: false,
          error: 'Insight not found',
        };
      }

      await this.aiInsightRepository.update(insightId, {
        status: InsightStatus.DISMISSED,
      });

      return {
        success: true,
        data: {
          insightId,
          dismissedAt: new Date(),
          dismissedBy: userId,
        },
      };
    } catch (error) {
      console.error('Error dismissing insight:', error);
      return {
        success: false,
        error: 'Failed to dismiss insight',
      };
    }
  }

  /**
   * Generate attrition-related insights
   */
  private async generateAttritionInsights(): Promise<any[]> {
    const insights = [];

    // Get high-risk employees
    const highRiskEmployees = await this.attritionPredictionRepository.find({
      where: { riskLevel: 'high', isActive: true },
      relations: ['user'],
      take: 10,
    });

    if (highRiskEmployees.length > 0) {
      insights.push({
        insightType: 'attrition_risk',
        priority: 'high',
        confidence: 85,
        insightData: {
          title: 'High Attrition Risk Alert',
          summary: `${highRiskEmployees.length} employees identified with high attrition risk`,
          details: {
            employeeCount: highRiskEmployees.length,
            employees: highRiskEmployees.map(p => ({
              id: p.user.id,
              name: `${p.user.firstName} ${p.user.lastName}`,
              riskScore: p.riskScore,
              department: p.user.organizationalUnit?.name,
            })),
          },
          recommendations: [
            'Schedule immediate stay interviews with high-risk employees',
            'Review compensation and benefits for at-risk individuals',
            'Implement targeted retention strategies',
            'Analyze common risk factors across high-risk employees',
          ],
        },
      });
    }

    // Analyze attrition patterns
    const attritionPatterns = await this.analyzeAttritionPatterns();
    if (attritionPatterns.length > 0) {
      insights.push({
        insightType: 'attrition_pattern',
        priority: 'medium',
        confidence: 75,
        insightData: {
          title: 'Attrition Pattern Analysis',
          summary: 'Identified patterns in employee attrition risk factors',
          details: { patterns: attritionPatterns },
          recommendations: [
            'Address common risk factors across departments',
            'Implement preventive measures for identified patterns',
          ],
        },
      });
    }

    return insights;
  }

  /**
   * Generate engagement-related insights
   */
  private async generateEngagementInsights(): Promise<any[]> {
    const insights = [];

    // Analyze recent survey responses
    const recentResponses = await this.surveyResponseRepository.find({
      where: {
        submittedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      },
      relations: ['survey', 'respondent'],
      take: 100,
    });

    if (recentResponses.length > 0) {
      const engagementAnalysis = await this.analyzeEngagementTrends(recentResponses);

      if (engagementAnalysis.concerningTrends.length > 0) {
        insights.push({
          insightType: 'engagement_trend',
          priority: 'medium',
          confidence: 80,
          insightData: {
            title: 'Engagement Trend Alert',
            summary: 'Concerning engagement trends detected in recent surveys',
            details: engagementAnalysis,
            recommendations: [
              'Investigate declining engagement areas',
              'Implement targeted engagement initiatives',
              'Conduct focus groups to understand root causes',
            ],
          },
        });
      }
    }

    return insights;
  }

  /**
   * Generate performance-related insights
   */
  private async generatePerformanceInsights(): Promise<any[]> {
    const insights = [];

    // Analyze performance trends
    const performanceMetrics = await this.performanceMetricRepository.find({
      where: {
        recordedAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 90 days
      },
      relations: ['user'],
      take: 200,
    });

    if (performanceMetrics.length > 0) {
      const performanceAnalysis = await this.analyzePerformanceTrends(performanceMetrics);

      if (performanceAnalysis.alerts.length > 0) {
        insights.push({
          insightType: 'performance_trend',
          priority: 'medium',
          confidence: 75,
          insightData: {
            title: 'Performance Trend Analysis',
            summary: 'Performance patterns requiring attention identified',
            details: performanceAnalysis,
            recommendations: [
              'Provide additional support for underperforming employees',
              'Recognize and reward top performers',
              'Implement performance improvement plans where needed',
            ],
          },
        });
      }
    }

    return insights;
  }

  /**
   * Generate team dynamics insights
   */
  private async generateTeamDynamicsInsights(): Promise<any[]> {
    const insights = [];

    // Analyze team collaboration and dynamics
    const teamAnalysis = await this.analyzeTeamDynamics();

    if (teamAnalysis.issues.length > 0) {
      insights.push({
        insightType: 'team_dynamics',
        priority: 'medium',
        confidence: 70,
        insightData: {
          title: 'Team Dynamics Analysis',
          summary: 'Team collaboration and dynamics insights',
          details: teamAnalysis,
          recommendations: [
            'Facilitate team building activities',
            'Address communication gaps',
            'Implement cross-functional collaboration initiatives',
          ],
        },
      });
    }

    return insights;
  }

  /**
   * Generate skill gap insights
   */
  private async generateSkillGapInsights(): Promise<any[]> {
    const insights = [];

    // Analyze skill gaps across organization
    const skillGapAnalysis = await this.analyzeSkillGaps();

    if (skillGapAnalysis.criticalGaps.length > 0) {
      insights.push({
        insightType: 'skill_gap',
        priority: 'high',
        confidence: 85,
        insightData: {
          title: 'Critical Skill Gaps Identified',
          summary: 'Critical skill gaps that may impact business objectives',
          details: skillGapAnalysis,
          recommendations: [
            'Develop targeted training programs',
            'Consider external hiring for critical skills',
            'Implement mentorship programs',
            'Create skill development pathways',
          ],
        },
      });
    }

    return insights;
  }

  /**
   * Extract insight title from data
   */
  private extractInsightTitle(insightData: any): string {
    if (typeof insightData === 'object' && insightData.title) {
      return insightData.title;
    }
    return 'AI-Generated Insight';
  }

  /**
   * Extract insight summary from data
   */
  private extractInsightSummary(insightData: any): string {
    if (typeof insightData === 'object' && insightData.summary) {
      return insightData.summary;
    }
    return 'Insight details available';
  }

  /**
   * Extract recommendations from data
   */
  private extractRecommendations(insightData: any): string[] {
    if (typeof insightData === 'object' && insightData.recommendations) {
      return insightData.recommendations;
    }
    return [];
  }

  /**
   * Analyze attrition patterns
   */
  private async analyzeAttritionPatterns(): Promise<any[]> {
    // Implementation for pattern analysis
    return [];
  }

  /**
   * Analyze engagement trends
   */
  private async analyzeEngagementTrends(responses: SurveyResponse[]): Promise<any> {
    // Implementation for engagement trend analysis
    return { concerningTrends: [] };
  }

  /**
   * Analyze performance trends
   */
  private async analyzePerformanceTrends(metrics: PerformanceMetric[]): Promise<any> {
    // Implementation for performance trend analysis
    return { alerts: [] };
  }

  /**
   * Analyze team dynamics
   */
  private async analyzeTeamDynamics(): Promise<any> {
    // Implementation for team dynamics analysis
    return { issues: [] };
  }

  /**
   * Analyze skill gaps
   */
  private async analyzeSkillGaps(): Promise<any> {
    // Implementation for skill gap analysis
    return { criticalGaps: [] };
  }
}
