import { <PERSON><PERSON>ty, Column, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';

export enum MetricType {
  INDIVIDUAL = 'individual',
  TEAM = 'team',
  DEPARTMENT = 'department',
  ORGANIZATION = 'organization',
}

@Entity('performance_metrics')
export class PerformanceMetric {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', nullable: true })
  userId: number;

  @Column({ name: 'organizational_unit_id', nullable: true })
  organizationalUnitId: number;

  @Column({
    name: 'metric_type',
    type: 'enum',
    enum: MetricType,
  })
  metricType: MetricType;

  @Column({ name: 'metric_name', length: 255 })
  metricName: string;

  @Column({ name: 'metric_value', type: 'decimal', precision: 10, scale: 4, nullable: true })
  metricValue: number;

  @Column({ name: 'period_start', type: 'date', nullable: true })
  periodStart: Date;

  @Column({ name: 'period_end', type: 'date', nullable: true })
  periodEnd: Date;

  @CreateDateColumn({ name: 'calculation_date' })
  calculationDate: Date;

  @Column({ type: 'json', nullable: true })
  metadata: object;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => OrganizationalUnit, { nullable: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;
}
