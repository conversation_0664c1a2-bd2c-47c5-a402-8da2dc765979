import { Injectable, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { AssessmentInstance, AssessmentStatus } from '../entities/assessment-instance.entity';
import { UserRole } from '../../users/entities/user.entity';
import * as moment from 'moment';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
  ) {}

  // Dashboard metrics
  async getDashboardMetrics(
    startDate: string,
    endDate: string,
    teamId: number | undefined,
    userId: number,
    userRole: string,
  ) {
    const dateFilter = this.getDateFilter(startDate, endDate);
    let whereClause: any = dateFilter;
    
    // Apply team filter if provided
    if (teamId) {
      // This would require a join with users table to filter by team
      whereClause = {
        ...whereClause,
        // In a real implementation, we would join with users and filter by teamId
      };
    }
    
    // Filter by manager's employees if manager role
    if (userRole === UserRole.MANAGER) {
      // In a real implementation, we would filter to show only assessments of employees 
      // managed by this manager. For now, we'll use a mock approach
      whereClause = {
        ...whereClause,
        // Mock implementation: filter by evaluatorId instead
        evaluatorId: userId,
      };
    }
    
    // For employees, only show their own assessments
    if (userRole === UserRole.EMPLOYEE) {
      whereClause = {
        ...whereClause,
        employeeId: userId,
      };
    }
    
    const assessments = await this.assessmentRepository.find({
      where: whereClause,
      relations: ['responses'],
    });
    
    // Basic metrics
    const totalAssessments = assessments.length;
    const pendingAssessments = assessments.filter(a =>
      a.status === AssessmentStatus.DRAFT || a.status === AssessmentStatus.IN_PROGRESS).length;
    const completedAssessments = assessments.filter(a =>
      a.status === AssessmentStatus.COMPLETED || a.status === AssessmentStatus.APPROVED).length;

    // Average score calculation
    const completedAssessmentsArray = assessments.filter(a =>
      a.status === AssessmentStatus.COMPLETED || a.status === AssessmentStatus.APPROVED);
    const averageScore = completedAssessmentsArray.length > 0 
      ? completedAssessmentsArray.reduce((sum, a) => sum + Number(a.totalScore), 0) / completedAssessmentsArray.length 
      : 0;
    
    // Recent assessments
    const recentAssessments = assessments
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      .slice(0, 5)
      .map(a => ({
        id: a.id,
        employeeId: a.employeeId,
        status: a.status,
        score: a.totalScore,
        date: a.assessmentDate,
      }));
    
    // Performance distribution
    const performanceDistribution = this.calculatePerformanceDistribution(completedAssessmentsArray);
    
    // Status breakdown
    const statusBreakdown = this.calculateStatusBreakdown(assessments);
    
    return {
      totalAssessments,
      pendingAssessments,
      completedAssessments,
      averageScore,
      recentAssessments,
      performanceDistribution,
      statusBreakdown,
    };
  }

  // Performance trends over time
  async getPerformanceTrends(
    period: 'monthly' | 'quarterly' | 'yearly',
    count: number,
    teamId: number | undefined,
    userId: number,
    userRole: string,
  ) {
    // Only HR Admin and Managers can access trends
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('You do not have permission to access performance trends');
    }
    
    // Calculate date ranges based on period
    const dateRanges = this.generateDateRanges(period, count);
    
    const trends = [];
    
    // For each period, get assessments and calculate metrics
    for (const range of dateRanges) {
      let whereClause: any = {
        assessmentDate: Between(range.start, range.end),
        status: In(['COMPLETED', 'APPROVED']),
      };
      
      // Apply team filter if provided
      if (teamId) {
        // This would require a join with users table to filter by team
        // For simplicity in this implementation, we're just setting up the structure
      }
      
      // Filter by manager's employees if manager role
      if (userRole === UserRole.MANAGER) {
        whereClause = {
          ...whereClause,
          evaluatorId: userId,
        };
      }
      
      const assessments = await this.assessmentRepository.find({
        where: whereClause,
      });
      
      // Calculate average score for this period
      const averageScore = assessments.length > 0
        ? assessments.reduce((sum, a) => sum + Number(a.totalScore), 0) / assessments.length
        : 0;
      
      // Calculate completion rate for this period (completed / total)
      const totalInPeriod = await this.assessmentRepository.count({
        where: {
          assessmentDate: Between(range.start, range.end),
          ...teamId && { /* team filter would go here */ },
          ...userRole === UserRole.MANAGER && { evaluatorId: userId },
        },
      });
      
      const completionRate = totalInPeriod > 0 ? assessments.length / totalInPeriod : 0;
      
      trends.push({
        period: range.label,
        averageScore,
        completionRate,
        assessmentCount: assessments.length,
      });
    }
    
    return trends;
  }

  // Team performance benchmarks
  async getTeamBenchmarks(
    teamIds: number[],
    startDate: string,
    endDate: string,
    userId: number,
    userRole: string,
  ) {
    // Only HR Admin and Managers can access benchmarks
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('You do not have permission to access team benchmarks');
    }
    
    const dateFilter = this.getDateFilter(startDate, endDate);
    
    // If no specific teams are requested, get benchmarks for all teams
    // In a real implementation, we would need to query the teams first
    const teamsToQuery = teamIds.length > 0 ? teamIds : [1, 2, 3]; // Mock team IDs
    
    const benchmarks = [];
    
    // For each team, calculate metrics
    for (const teamId of teamsToQuery) {
      // Filter assessments by team members
      // This would require a join with users table
      // For this implementation, we'll just structure the query
      const assessments = await this.assessmentRepository
        .createQueryBuilder('assessment')
        .leftJoin('users', 'user', 'assessment.employeeId = user.id')
        .where('user.teamId = :teamId', { teamId })
        .andWhere(startDate && endDate ? 'assessment.assessmentDate BETWEEN :startDate AND :endDate' : '1=1', {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
        })
        .andWhere('assessment.status IN (:...statuses)', {
          statuses: ['COMPLETED', 'APPROVED'],
        })
        .getMany();
      
      // Calculate metrics for this team
      const averageScore = assessments.length > 0
        ? assessments.reduce((sum, a) => sum + Number(a.totalScore), 0) / assessments.length
        : 0;
        
      // Get completion rate
      const totalAssessments = await this.assessmentRepository
        .createQueryBuilder('assessment')
        .leftJoin('users', 'user', 'assessment.employeeId = user.id')
        .where('user.teamId = :teamId', { teamId })
        .andWhere(startDate && endDate ? 'assessment.assessmentDate BETWEEN :startDate AND :endDate' : '1=1', {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
        })
        .getCount();
        
      const completionRate = totalAssessments > 0 ? assessments.length / totalAssessments : 0;
      
      // Get team name (mock for now)
      const teamName = `Team ${teamId}`;
      
      // Get team size (mock for now)
      const teamSize = 5; // Mock value
      
      benchmarks.push({
        teamId,
        teamName,
        teamSize,
        averageScore,
        completionRate,
        assessmentCount: assessments.length,
      });
    }
    
    // Sort by average score (highest first)
    benchmarks.sort((a, b) => b.averageScore - a.averageScore);
    
    return benchmarks;
  }

  // Helper methods
  private getDateFilter(startDate?: string, endDate?: string) {
    if (startDate && endDate) {
      return {
        assessmentDate: Between(new Date(startDate), new Date(endDate)),
      };
    }
    return {};
  }

  private calculateStatusBreakdown(assessments: AssessmentInstance[]) {
    const statusBreakdown = {
      draft: 0,
      inProgress: 0,
      completed: 0,
      approved: 0,
      rejected: 0,
    };

    for (const assessment of assessments) {
      switch (assessment.status.toLowerCase()) {
        case 'draft':
          statusBreakdown.draft++;
          break;
        case 'in_progress':
          statusBreakdown.inProgress++;
          break;
        case 'completed':
          statusBreakdown.completed++;
          break;
        case 'approved':
          statusBreakdown.approved++;
          break;
        case 'rejected':
          statusBreakdown.rejected++;
          break;
      }
    }

    return statusBreakdown;
  }

  private calculatePerformanceDistribution(assessments: AssessmentInstance[]) {
    const distribution = {
      excellent: 0,
      good: 0,
      satisfactory: 0,
      needsImprovement: 0,
      poor: 0,
    };

    for (const assessment of assessments) {
      const score = assessment.totalScore;
      
      if (score >= 0.9) {
        distribution.excellent++;
      } else if (score >= 0.8) {
        distribution.good++;
      } else if (score >= 0.7) {
        distribution.satisfactory++;
      } else if (score >= 0.6) {
        distribution.needsImprovement++;
      } else {
        distribution.poor++;
      }
    }

    return distribution;
  }

  private generateDateRanges(period: 'monthly' | 'quarterly' | 'yearly', count: number) {
    const ranges = [];
    const endDate = moment().endOf('day');
    
    for (let i = 0; i < count; i++) {
      let start, end, label;
      
      if (period === 'monthly') {
        end = moment(endDate).subtract(i, 'months').endOf('month');
        start = moment(end).startOf('month');
        label = start.format('MMM YYYY');
      } else if (period === 'quarterly') {
        end = moment(endDate).subtract(i * 3, 'months').endOf('quarter');
        start = moment(end).startOf('quarter');
        label = `Q${start.quarter()} ${start.year()}`;
      } else { // yearly
        end = moment(endDate).subtract(i, 'years').endOf('year');
        start = moment(end).startOf('year');
        label = start.format('YYYY');
      }
      
      ranges.push({
        start: start.toDate(),
        end: end.toDate(),
        label,
      });
    }
    
    return ranges;
  }
}
