import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection } from 'typeorm';
import {
  ImprovementPlan,
  ImprovementPlanGoal,
  ImprovementPlanReview,
  ImprovementPlanStatus,
  ImprovementPlanType
} from './entities/improvement-plan.entity';
import { CreateImprovementPlanDto } from './dto/create-improvement-plan.dto';
import {
  UpdateImprovementPlanDto,
  UpdateImprovementPlanGoalDto,
  CreateImprovementPlanReviewDto
} from './dto/update-improvement-plan.dto';
import { UserRole } from '../users/entities/user.entity';

export interface ImprovementPlanFilters {
  status?: ImprovementPlanStatus;
  type?: ImprovementPlanType;
  employeeId?: number;
  managerId?: number;
  assessmentId?: number;
  startDateFrom?: Date;
  startDateTo?: Date;
  isOverdue?: boolean;
  search?: string;
}

export interface ImprovementPlanStatistics {
  totalPlans: number;
  byStatus: Record<ImprovementPlanStatus, number>;
  byType: Record<ImprovementPlanType, number>;
  overduePlans: number;
  completedThisMonth: number;
  averageCompletionTime: number;
  upcomingReviews: number;
}

@Injectable()
export class ImprovementPlansService {
  constructor(
    @InjectRepository(ImprovementPlan)
    private improvementPlanRepository: Repository<ImprovementPlan>,
    @InjectRepository(ImprovementPlanGoal)
    private goalRepository: Repository<ImprovementPlanGoal>,
    @InjectRepository(ImprovementPlanReview)
    private reviewRepository: Repository<ImprovementPlanReview>,
    private connection: Connection,
  ) { }

  async create(
    createImprovementPlanDto: CreateImprovementPlanDto,
    createdById: number
  ): Promise<ImprovementPlan> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the improvement plan
      const improvementPlan = queryRunner.manager.create(ImprovementPlan, {
        ...createImprovementPlanDto,
        createdById,
        startDate: new Date(createImprovementPlanDto.startDate),
        targetCompletionDate: new Date(createImprovementPlanDto.targetCompletionDate),
        nextReviewDate: createImprovementPlanDto.nextReviewDate
          ? new Date(createImprovementPlanDto.nextReviewDate)
          : null,
      });

      const savedPlan = await queryRunner.manager.save(improvementPlan);

      // Create goals if provided
      if (createImprovementPlanDto.goals && createImprovementPlanDto.goals.length > 0) {
        const goals = createImprovementPlanDto.goals.map((goalDto, index) =>
          queryRunner.manager.create(ImprovementPlanGoal, {
            ...goalDto,
            improvementPlanId: savedPlan.id,
            targetDate: new Date(goalDto.targetDate),
            orderIndex: goalDto.orderIndex ?? index,
          })
        );

        await queryRunner.manager.save(goals);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedPlan.id);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(
    filters: ImprovementPlanFilters = {},
    page = 1,
    limit = 20,
    userRole: UserRole,
    userId: number
  ): Promise<{
    plans: ImprovementPlan[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const queryBuilder = this.improvementPlanRepository.createQueryBuilder('plan')
      .leftJoinAndSelect('plan.employee', 'employee')
      .leftJoinAndSelect('plan.manager', 'manager')
      .leftJoinAndSelect('plan.createdBy', 'createdBy')
      .leftJoinAndSelect('plan.assessment', 'assessment')
      .leftJoinAndSelect('plan.goals', 'goals')
      .leftJoinAndSelect('plan.reviews', 'reviews');

    // Apply role-based filtering
    if (userRole === UserRole.EMPLOYEE) {
      queryBuilder.andWhere('plan.employeeId = :userId', { userId });
    } else if (userRole === UserRole.MANAGER) {
      queryBuilder.andWhere('(plan.managerId = :userId OR plan.employeeId = :userId)', { userId });
    }
    // HR_ADMIN can see all plans

    // Apply filters
    if (filters.status) {
      queryBuilder.andWhere('plan.status = :status', { status: filters.status });
    }

    if (filters.type) {
      queryBuilder.andWhere('plan.type = :type', { type: filters.type });
    }

    if (filters.employeeId) {
      queryBuilder.andWhere('plan.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters.managerId) {
      queryBuilder.andWhere('plan.managerId = :managerId', { managerId: filters.managerId });
    }

    if (filters.assessmentId) {
      queryBuilder.andWhere('plan.assessmentId = :assessmentId', { assessmentId: filters.assessmentId });
    }

    if (filters.startDateFrom && filters.startDateTo) {
      queryBuilder.andWhere('plan.startDate BETWEEN :startDateFrom AND :startDateTo', {
        startDateFrom: filters.startDateFrom,
        startDateTo: filters.startDateTo
      });
    }

    if (filters.isOverdue) {
      queryBuilder.andWhere('plan.targetCompletionDate < :today AND plan.status != :completed', {
        today: new Date(),
        completed: ImprovementPlanStatus.COMPLETED
      });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(plan.title ILIKE :search OR plan.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Order by creation date
    queryBuilder.orderBy('plan.createdAt', 'DESC');

    const plans = await queryBuilder.getMany();

    return {
      plans,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  async findOne(id: number): Promise<ImprovementPlan> {
    const plan = await this.improvementPlanRepository.findOne({
      where: { id },
      relations: [
        'employee',
        'manager',
        'createdBy',
        'assessment',
        'goals',
        'reviews',
        'reviews.reviewer',
        'actionItems',
        'actionItems.assignedTo'
      ]
    });

    if (!plan) {
      throw new NotFoundException(`Improvement plan with ID ${id} not found`);
    }

    return plan;
  }

  async update(
    id: number,
    updateImprovementPlanDto: UpdateImprovementPlanDto,
    updatedById: number,
    userRole: UserRole
  ): Promise<ImprovementPlan> {
    const plan = await this.findOne(id);

    // Check permissions
    this.checkUpdatePermissions(plan, updatedById, userRole);

    // Update the plan
    Object.assign(plan, {
      ...updateImprovementPlanDto,
      startDate: updateImprovementPlanDto.startDate ? new Date(updateImprovementPlanDto.startDate) : plan.startDate,
      targetCompletionDate: updateImprovementPlanDto.targetCompletionDate
        ? new Date(updateImprovementPlanDto.targetCompletionDate)
        : plan.targetCompletionDate,
      actualCompletionDate: updateImprovementPlanDto.actualCompletionDate
        ? new Date(updateImprovementPlanDto.actualCompletionDate)
        : plan.actualCompletionDate,
      nextReviewDate: updateImprovementPlanDto.nextReviewDate
        ? new Date(updateImprovementPlanDto.nextReviewDate)
        : plan.nextReviewDate,
    });

    // Auto-complete if progress is 100%
    if (updateImprovementPlanDto.progressPercentage === 100 && plan.status !== ImprovementPlanStatus.COMPLETED) {
      plan.status = ImprovementPlanStatus.COMPLETED;
      plan.actualCompletionDate = new Date();
    }

    await this.improvementPlanRepository.save(plan);
    return this.findOne(id);
  }

  async remove(id: number, userId: number, userRole: UserRole): Promise<void> {
    const plan = await this.findOne(id);

    // Check permissions - only creator, manager, or HR admin can delete
    if (plan.createdById !== userId && plan.managerId !== userId && userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('You can only delete improvement plans you created or manage');
    }

    await this.improvementPlanRepository.remove(plan);
  }

  // Goal Management
  async addGoal(
    planId: number,
    goalData: any,
    userId: number,
    userRole: UserRole
  ): Promise<ImprovementPlanGoal> {
    const plan = await this.findOne(planId);
    this.checkUpdatePermissions(plan, userId, userRole);

    const goal = this.goalRepository.create({
      ...goalData,
      improvementPlanId: planId,
      targetDate: new Date(goalData.targetDate),
    });

    return this.goalRepository.save(goal);
  }

  async updateGoal(
    goalId: number,
    updateGoalDto: UpdateImprovementPlanGoalDto,
    userId: number,
    userRole: UserRole
  ): Promise<ImprovementPlanGoal> {
    const goal = await this.goalRepository.findOne({
      where: { id: goalId },
      relations: ['improvementPlan']
    });

    if (!goal) {
      throw new NotFoundException(`Goal with ID ${goalId} not found`);
    }

    this.checkUpdatePermissions(goal.improvementPlan, userId, userRole);

    Object.assign(goal, {
      ...updateGoalDto,
      targetDate: updateGoalDto.targetDate ? new Date(updateGoalDto.targetDate) : goal.targetDate,
      completionDate: updateGoalDto.completionDate ? new Date(updateGoalDto.completionDate) : goal.completionDate,
    });

    return this.goalRepository.save(goal);
  }

  async removeGoal(goalId: number, userId: number, userRole: UserRole): Promise<void> {
    const goal = await this.goalRepository.findOne({
      where: { id: goalId },
      relations: ['improvementPlan']
    });

    if (!goal) {
      throw new NotFoundException(`Goal with ID ${goalId} not found`);
    }

    this.checkUpdatePermissions(goal.improvementPlan, userId, userRole);
    await this.goalRepository.remove(goal);
  }

  // Review Management
  async addReview(
    planId: number,
    reviewData: CreateImprovementPlanReviewDto,
    reviewerId: number,
    userRole: UserRole
  ): Promise<ImprovementPlanReview> {
    const plan = await this.findOne(planId);
    this.checkUpdatePermissions(plan, reviewerId, userRole);

    const review = this.reviewRepository.create({
      ...reviewData,
      improvementPlanId: planId,
      reviewerId,
      reviewDate: new Date(reviewData.reviewDate),
    });

    const savedReview = await this.reviewRepository.save(review);

    // Update next review date based on frequency
    if (plan.reviewFrequency) {
      plan.nextReviewDate = this.calculateNextReviewDate(new Date(reviewData.reviewDate), plan.reviewFrequency);
      await this.improvementPlanRepository.save(plan);
    }

    return savedReview;
  }

  async getStatistics(userId?: number, userRole?: UserRole): Promise<ImprovementPlanStatistics> {
    let queryBuilder = this.improvementPlanRepository.createQueryBuilder('plan');

    // Apply role-based filtering for statistics
    if (userRole === UserRole.EMPLOYEE && userId) {
      queryBuilder = queryBuilder.where('plan.employeeId = :userId', { userId });
    } else if (userRole === UserRole.MANAGER && userId) {
      queryBuilder = queryBuilder.where('(plan.managerId = :userId OR plan.employeeId = :userId)', { userId });
    }

    const totalPlans = await queryBuilder.getCount();

    // Get counts by status
    const statusCounts = await queryBuilder
      .select('plan.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('plan.status')
      .getRawMany();

    const byStatus = statusCounts.reduce((acc, item) => {
      acc[item.status] = parseInt(item.count);
      return acc;
    }, {} as Record<ImprovementPlanStatus, number>);

    // Get counts by type
    const typeCounts = await queryBuilder
      .select('plan.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('plan.type')
      .getRawMany();

    const byType = typeCounts.reduce((acc, item) => {
      acc[item.type] = parseInt(item.count);
      return acc;
    }, {} as Record<ImprovementPlanType, number>);

    // Get overdue plans
    const overduePlans = await queryBuilder
      .where('plan.targetCompletionDate < :today AND plan.status != :completed', {
        today: new Date(),
        completed: ImprovementPlanStatus.COMPLETED
      })
      .getCount();

    // Get completed this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const completedThisMonth = await queryBuilder
      .where('plan.status = :completed AND plan.actualCompletionDate >= :startOfMonth', {
        completed: ImprovementPlanStatus.COMPLETED,
        startOfMonth
      })
      .getCount();

    // Get upcoming reviews (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const upcomingReviews = await queryBuilder
      .where('plan.nextReviewDate BETWEEN :today AND :nextWeek', {
        today: new Date(),
        nextWeek
      })
      .getCount();

    return {
      totalPlans,
      byStatus,
      byType,
      overduePlans,
      completedThisMonth,
      averageCompletionTime: 0, // Would need to calculate based on start and completion dates
      upcomingReviews
    };
  }

  private checkUpdatePermissions(plan: ImprovementPlan, userId: number, userRole: UserRole): void {
    // HR Admin can update any plan
    if (userRole === UserRole.HR_ADMIN) {
      return;
    }

    // Manager can update plans they manage
    if (plan.managerId === userId) {
      return;
    }

    // Employee can update their own plans (limited)
    if (plan.employeeId === userId && userRole === UserRole.EMPLOYEE) {
      return;
    }

    // Creator can update plans they created
    if (plan.createdById === userId) {
      return;
    }

    throw new ForbiddenException('You do not have permission to update this improvement plan');
  }

  private calculateNextReviewDate(currentReviewDate: Date, frequency: string): Date {
    const nextDate = new Date(currentReviewDate);

    switch (frequency.toLowerCase()) {
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'biweekly':
        nextDate.setDate(nextDate.getDate() + 14);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'quarterly':
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
      default:
        nextDate.setMonth(nextDate.getMonth() + 1); // Default to monthly
    }

    return nextDate;
  }

  async getMyPlans(userId: number, status?: ImprovementPlanStatus): Promise<ImprovementPlan[]> {
    const where: any = { employeeId: userId };
    if (status) {
      where.status = status;
    }

    return this.improvementPlanRepository.find({
      where,
      relations: ['manager', 'goals', 'reviews'],
      order: { createdAt: 'DESC' }
    });
  }

  async getPlansByAssessment(assessmentId: number): Promise<ImprovementPlan[]> {
    return this.improvementPlanRepository.find({
      where: { assessmentId },
      relations: ['employee', 'manager', 'goals'],
      order: { createdAt: 'DESC' }
    });
  }
}
