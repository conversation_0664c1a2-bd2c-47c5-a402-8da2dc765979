import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AssessmentTemplate,
  AssessmentAreaInput,
  CreateAssessmentWithScoring,
  AssessmentScoringData,
  User,
  UserRole
} from '../../types';
import api from '../../services/api';
import { ApiService } from '../../services/api';

interface AssessmentCreationWizardProps {
  userRole: UserRole;
  currentUserId: number;
}

const AssessmentCreationWizard: React.FC<AssessmentCreationWizardProps> = ({
  userRole,
  currentUserId
}) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form data
  const [selectedTemplate, setSelectedTemplate] = useState<AssessmentTemplate | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<User | null>(null);
  const [assessmentDate, setAssessmentDate] = useState(new Date().toISOString().split('T')[0]);
  const [notes, setNotes] = useState('');
  const [areaInputs, setAreaInputs] = useState<AssessmentAreaInput[]>([]);

  // Data for dropdowns
  const [templates, setTemplates] = useState<AssessmentTemplate[]>([]);
  const [employees, setEmployees] = useState<User[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);

  useEffect(() => {
    fetchTemplates();
    fetchEmployees();
  }, []);

  const fetchTemplates = async () => {
    try {
      setIsLoadingTemplates(true);
      // Use centralized database API for assessment templates
      const response = await ApiService.getTableData('assessment_templates', 1, 100);

      if (response && response.records) {
        // Transform database records to match AssessmentTemplate interface
        const transformedTemplates = response.records.map((template: any) => ({
          id: template.id,
          name: template.name,
          description: template.description,
          isActive: template.isActive !== false,
          isGlobal: template.isGlobal !== false,
          version: template.version || 1,
          createdById: template.createdById || template.created_by_id,
          createdAt: template.createdAt || template.created_at,
          updatedAt: template.updatedAt || template.updated_at,
          areas: template.areas || []
        }));
        setTemplates(transformedTemplates);
      } else {
        setTemplates([]);
      }
    } catch (err) {
      console.error('Error fetching templates from database API:', err);
      // Fallback to empty array
      setTemplates([]);
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      setIsLoadingEmployees(true);
      // Use centralized database API for users
      const response = await ApiService.getTableData('users', 1, 100);

      if (response && response.records) {
        // Transform and filter for employees only
        const transformedEmployees = response.records
          .filter((user: any) => user.role === 'employee' || user.role === 'EMPLOYEE')
          .map((user: any) => ({
            id: user.id,
            firstName: user.firstName || user.first_name,
            lastName: user.lastName || user.last_name,
            name: user.name || `${user.firstName || user.first_name} ${user.lastName || user.last_name}`,
            email: user.email,
            role: user.role,
            teamId: user.teamId || user.team_id,
            managerId: user.managerId || user.manager_id,
            profileImage: user.profileImage || user.profile_image || '',
            isActive: user.isActive !== false
          }));
        setEmployees(transformedEmployees);
      } else {
        setEmployees([]);
      }
    } catch (err) {
      console.error('Error fetching employees from database API:', err);
      // Fallback to empty array
      setEmployees([]);
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  const handleTemplateSelect = (template: AssessmentTemplate) => {
    setSelectedTemplate(template);

    // Initialize area inputs based on template
    const initialInputs: AssessmentAreaInput[] = template.areas.map(area => ({
      areaId: area.id!,
      score: 0,
      baseScore: 0,
      evaluatorComments: '',
      employeeComments: '',
      additionalData: {}
    }));

    setAreaInputs(initialInputs);
  };

  const updateAreaInput = (areaId: number, field: keyof AssessmentAreaInput, value: any) => {
    setAreaInputs(prev => prev.map(input =>
      input.areaId === areaId
        ? { ...input, [field]: value }
        : input
    ));
  };

  const updateAdditionalData = (areaId: number, field: string, value: any) => {
    setAreaInputs(prev => prev.map(input =>
      input.areaId === areaId
        ? {
          ...input,
          additionalData: {
            ...input.additionalData,
            [field]: value
          }
        }
        : input
    ));
  };

  const handleSubmit = async (saveAsDraft = false) => {
    if (!selectedTemplate || !selectedEmployee) {
      setError('Please select both a template and an employee');
      return;
    }

    // For draft, allow incomplete scores
    if (!saveAsDraft) {
      const incompleteAreas = areaInputs.filter(input => input.baseScore === 0);
      if (incompleteAreas.length > 0) {
        setError('Please provide scores for all assessment areas');
        return;
      }
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const assessmentData: CreateAssessmentWithScoring = {
        templateId: selectedTemplate.id!,
        employeeId: selectedEmployee.id,
        evaluatorId: currentUserId,
        assessmentDate,
        notes,
        areaInputs,
        status: saveAsDraft ? 'draft' : 'in_progress'
      };

      const response = await api.post('/assessments/create-with-scoring', assessmentData);

      // Navigate to the created assessment
      navigate(`/assessments/${response.data.id}`);
    } catch (err: any) {
      console.error('Error creating assessment:', err);
      setError(err.response?.data?.message || 'Failed to create assessment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const canProceedToStep = (step: number) => {
    switch (step) {
      case 2:
        return selectedTemplate !== null;
      case 3:
        return selectedTemplate !== null && selectedEmployee !== null;
      case 4:
        return selectedTemplate !== null && selectedEmployee !== null &&
          areaInputs.every(input => input.baseScore > 0);
      default:
        return true;
    }
  };

  const steps = [
    { number: 1, title: 'Select Template', description: 'Choose an assessment template' },
    { number: 2, title: 'Select Employee', description: 'Choose the employee to assess' },
    { number: 3, title: 'Score Areas', description: 'Provide scores and additional data' },
    { number: 4, title: 'Review & Submit', description: 'Review and submit the assessment' }
  ];

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <nav aria-label="Progress">
          <ol className="flex items-center">
            {steps.map((step, index) => (
              <li key={step.number} className={`relative ${index !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''}`}>
                <div className="flex items-center">
                  <div className={`relative flex h-8 w-8 items-center justify-center rounded-full ${currentStep > step.number
                    ? 'bg-blue-600 text-white'
                    : currentStep === step.number
                      ? 'border-2 border-blue-600 bg-white text-blue-600'
                      : 'border-2 border-gray-300 bg-white text-gray-500'
                    }`}>
                    {currentStep > step.number ? (
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <span className="text-sm font-medium">{step.number}</span>
                    )}
                  </div>
                  <div className="ml-4 min-w-0">
                    <p className={`text-sm font-medium ${currentStep >= step.number ? 'text-blue-600' : 'text-gray-500'
                      }`}>
                      {step.title}
                    </p>
                    <p className="text-sm text-gray-500">{step.description}</p>
                  </div>
                </div>
                {index !== steps.length - 1 && (
                  <div className="absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300" aria-hidden="true" />
                )}
              </li>
            ))}
          </ol>
        </nav>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Step Content */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            {steps[currentStep - 1].title}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {steps[currentStep - 1].description}
          </p>
        </div>

        <div className="px-6 py-6">
          {/* Step 1: Select Template */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Choose Assessment Template
                </label>
                {isLoadingTemplates ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {templates.map(template => (
                      <div
                        key={template.id}
                        onClick={() => handleTemplateSelect(template)}
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${selectedTemplate?.id === template.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                          }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-medium text-gray-900">{template.name}</h3>
                          {template.isGlobal && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              Global
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-3">
                          {template.description || 'No description provided'}
                        </p>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>{template.areas?.length || 0} areas</span>
                          <span>v{template.version || 1}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 2: Select Employee */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Choose Employee to Assess
                </label>
                {isLoadingEmployees ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {employees.map(employee => (
                      <div
                        key={employee.id}
                        onClick={() => setSelectedEmployee(employee)}
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${selectedEmployee?.id === employee.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                          }`}
                      >
                        <h3 className="font-medium text-gray-900">
                          {employee.firstName} {employee.lastName}
                        </h3>
                        <p className="text-sm text-gray-600">{employee.email}</p>
                        <p className="text-xs text-gray-500 mt-1 capitalize">
                          {employee.role.replace('_', ' ')}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assessment Date
                  </label>
                  <input
                    type="date"
                    value={assessmentDate}
                    onChange={(e) => setAssessmentDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (Optional)
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={3}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Add any general notes about this assessment..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Score Areas */}
          {currentStep === 3 && selectedTemplate && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-blue-800 mb-2">
                  Assessment Template: {selectedTemplate.name}
                </h3>
                <p className="text-sm text-blue-700">
                  Provide base scores for each area. Additional scoring rules will be applied automatically.
                </p>
              </div>

              {selectedTemplate.areas.map((area, index) => {
                const areaInput = areaInputs.find(input => input.areaId === area.id);
                if (!areaInput) return null;

                return (
                  <div key={area.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900">{area.name}</h4>
                        {area.description && (
                          <p className="text-sm text-gray-600 mt-1">{area.description}</p>
                        )}
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>Weight: {(area.weight * 100).toFixed(1)}%</span>
                          <span>Max Score: {area.maxScore}</span>
                          {area.scoringRules && area.scoringRules.length > 0 && (
                            <span className="text-blue-600">
                              {area.scoringRules.length} scoring rule(s)
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Base Score */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Base Score * (0 - {area.maxScore})
                        </label>
                        <input
                          type="number"
                          min="0"
                          max={area.maxScore}
                          value={areaInput.baseScore}
                          onChange={(e) => updateAreaInput(area.id!, 'baseScore', parseFloat(e.target.value) || 0)}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Enter base score"
                        />
                      </div>

                      {/* Evaluator Comments */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Evaluator Comments
                        </label>
                        <textarea
                          value={areaInput.evaluatorComments}
                          onChange={(e) => updateAreaInput(area.id!, 'evaluatorComments', e.target.value)}
                          rows={2}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Add comments about this area..."
                        />
                      </div>
                    </div>

                    {/* Additional Scoring Data */}
                    {area.scoringRules && area.scoringRules.length > 0 && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-md">
                        <h5 className="text-sm font-medium text-gray-900 mb-3">
                          Additional Scoring Data
                        </h5>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                          {/* Common scoring fields based on rules */}
                          {area.scoringRules.some(rule => rule.conditionField === 'nc_incidents') && (
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Non-Conformance Incidents
                              </label>
                              <input
                                type="number"
                                min="0"
                                value={areaInput.additionalData?.nc_incidents || 0}
                                onChange={(e) => updateAdditionalData(area.id!, 'nc_incidents', parseInt(e.target.value) || 0)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              />
                            </div>
                          )}

                          {area.scoringRules.some(rule => rule.conditionField === 'training_completed') && (
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Training Completed
                              </label>
                              <input
                                type="number"
                                min="0"
                                value={areaInput.additionalData?.training_completed || 0}
                                onChange={(e) => updateAdditionalData(area.id!, 'training_completed', parseInt(e.target.value) || 0)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              />
                            </div>
                          )}

                          {area.scoringRules.some(rule => rule.conditionField === 'certifications') && (
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Certifications
                              </label>
                              <input
                                type="number"
                                min="0"
                                value={areaInput.additionalData?.certifications || 0}
                                onChange={(e) => updateAdditionalData(area.id!, 'certifications', parseInt(e.target.value) || 0)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              />
                            </div>
                          )}

                          {area.scoringRules.some(rule => rule.conditionField === 'customer_complaints') && (
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Customer Complaints
                              </label>
                              <input
                                type="number"
                                min="0"
                                value={areaInput.additionalData?.customer_complaints || 0}
                                onChange={(e) => updateAdditionalData(area.id!, 'customer_complaints', parseInt(e.target.value) || 0)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              />
                            </div>
                          )}

                          {area.scoringRules.some(rule => rule.conditionField === 'safety_incidents') && (
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Safety Incidents
                              </label>
                              <input
                                type="number"
                                min="0"
                                value={areaInput.additionalData?.safety_incidents || 0}
                                onChange={(e) => updateAdditionalData(area.id!, 'safety_incidents', parseInt(e.target.value) || 0)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              />
                            </div>
                          )}

                          {area.scoringRules.some(rule => rule.conditionField === 'attendance_score') && (
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Attendance Score (%)
                              </label>
                              <input
                                type="number"
                                min="0"
                                max="100"
                                value={areaInput.additionalData?.attendance_score || 100}
                                onChange={(e) => updateAdditionalData(area.id!, 'attendance_score', parseInt(e.target.value) || 100)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              />
                            </div>
                          )}
                        </div>

                        {/* Show applicable scoring rules */}
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <h6 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2">
                            Applicable Scoring Rules
                          </h6>
                          <div className="space-y-1">
                            {area.scoringRules.map((rule, ruleIndex) => (
                              <div key={ruleIndex} className="text-xs text-gray-600">
                                <span className="font-medium capitalize">{rule.ruleType}</span>
                                {rule.scoreAdjustment !== 0 && (
                                  <span className="ml-1">
                                    ({rule.scoreAdjustment > 0 ? '+' : ''}{rule.scoreAdjustment} points)
                                  </span>
                                )}
                                {rule.description && (
                                  <span className="ml-1">- {rule.description}</span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}

          {/* Step 4: Review & Submit */}
          {currentStep === 4 && selectedTemplate && selectedEmployee && (
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-green-800 mb-2">
                  Ready to Create Assessment
                </h3>
                <p className="text-sm text-green-700">
                  Review the details below and click "Create Assessment" to finalize.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Assessment Details</h4>
                  <dl className="space-y-2">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Template</dt>
                      <dd className="text-sm text-gray-900">{selectedTemplate.name}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Employee</dt>
                      <dd className="text-sm text-gray-900">
                        {selectedEmployee.firstName} {selectedEmployee.lastName}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Assessment Date</dt>
                      <dd className="text-sm text-gray-900">
                        {new Date(assessmentDate).toLocaleDateString()}
                      </dd>
                    </div>
                    {notes && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Notes</dt>
                        <dd className="text-sm text-gray-900">{notes}</dd>
                      </div>
                    )}
                  </dl>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Score Summary</h4>
                  <div className="space-y-2">
                    {selectedTemplate.areas.map(area => {
                      const areaInput = areaInputs.find(input => input.areaId === area.id);
                      return (
                        <div key={area.id} className="flex justify-between items-center py-2 border-b border-gray-200">
                          <span className="text-sm text-gray-900">{area.name}</span>
                          <span className="text-sm font-medium text-gray-900">
                            {areaInput?.baseScore || 0} / {area.maxScore}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
          <button
            type="button"
            onClick={() => currentStep > 1 ? setCurrentStep(currentStep - 1) : navigate('/assessments')}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </button>

          {currentStep < 4 ? (
            <button
              type="button"
              onClick={() => setCurrentStep(currentStep + 1)}
              disabled={!canProceedToStep(currentStep + 1)}
              className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${canProceedToStep(currentStep + 1)
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-gray-400 cursor-not-allowed'
                }`}
            >
              Next
            </button>
          ) : (
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => handleSubmit(true)}
                disabled={isSubmitting}
                className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${isSubmitting
                  ? 'bg-gray-400 cursor-not-allowed text-white'
                  : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }`}
              >
                {isSubmitting ? 'Saving...' : 'Save as Draft'}
              </button>

              <button
                type="button"
                onClick={() => handleSubmit(false)}
                disabled={isSubmitting || !canProceedToStep(4)}
                className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${isSubmitting || !canProceedToStep(4)
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-700'
                  }`}
              >
                {isSubmitting ? 'Creating...' : 'Create & Start Assessment'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentCreationWizard;
