[2025-06-28 14:23:24 Manila] Starting React frontend server on port 3080
[2025-06-28 14:23:25 Manila] 
[2025-06-28 14:23:25 Manila] > ehrx-frontend@1.0.0 start
[2025-06-28 14:23:25 Manila] > react-scripts start
[2025-06-28 14:23:25 Manila] 
[2025-06-28 14:23:33 Manila] Attempting to bind to HOST environment variable: 0.0.0.0
[2025-06-28 14:23:33 Manila] If this was unintentional, check that you haven't mistakenly set it in your shell.
[2025-06-28 14:23:33 Manila] Learn more here: https://cra.link/advanced-config
[2025-06-28 14:23:33 Manila] 
[2025-06-28 14:23:36 Manila] (node:1093182) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
[2025-06-28 14:23:36 Manila] (Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-28 14:23:36 Manila] (node:1093182) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
[2025-06-28 14:23:37 Manila] Starting the development server...
[2025-06-28 14:23:37 Manila] 
[2025-06-28 14:23:40 Manila] Compiled successfully!
[2025-06-28 14:23:40 Manila] 
[2025-06-28 14:23:40 Manila] You can now view ehrx-frontend in the browser.
[2025-06-28 14:23:40 Manila] 
[2025-06-28 14:23:40 Manila] http://localhost:3080
[2025-06-28 14:23:40 Manila] 
[2025-06-28 14:23:40 Manila] Note that the development build is not optimized.
[2025-06-28 14:23:40 Manila] To create a production build, use npm run build.
[2025-06-28 14:23:40 Manila] 
[2025-06-28 14:23:40 Manila] webpack compiled successfully
