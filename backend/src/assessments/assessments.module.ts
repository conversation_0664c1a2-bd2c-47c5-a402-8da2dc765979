import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplatesController } from './templates/templates.controller';
import { TemplatesService } from './templates/templates.service';
import { AssessmentsController } from './assessments/assessments.controller';
import { AssessmentsService } from './assessments/assessments.service';
import { ScoringService } from './services/scoring.service';
import { WorkflowService } from './services/workflow.service';
import { AssessmentTemplateController } from './controllers/assessment-template.controller';
import { EmployeeAssessmentController } from './controllers/employee-assessment.controller';
import { AssessmentTemplateService } from './services/assessment-template.service';
import { EmployeeAssessmentService } from './services/employee-assessment.service';
import { AssessmentCriteriaService } from './services/assessment-criteria.service';
import { AssessmentTemplate } from './entities/assessment-template.entity';
import { AssessmentArea } from './entities/assessment-area.entity';
import { ScoringRule } from './entities/scoring-rule.entity';
import { AssessmentInstance } from './entities/assessment-instance.entity';
import { AssessmentResponse } from './entities/assessment-response.entity';
import { AssessmentTemplateCriteria } from './entities/assessment-template-criteria.entity';
import { AssessmentCriteria } from './entities/assessment-criteria.entity';
import { AssessmentCriteriaLevel } from './entities/assessment-criteria-level.entity';
import { AssessmentCriteriaInheritance } from './entities/assessment-criteria-inheritance.entity';
import { AssessmentCriteriaResponse } from './entities/assessment-criteria-response.entity';
import { AssessmentScoringScale } from './entities/assessment-scoring-scale.entity';
import { AssessmentScoringScaleLevel } from './entities/assessment-scoring-scale-level.entity';
import { AssessmentCriteriaScoring } from './entities/assessment-criteria-scoring.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AssessmentTemplate,
      AssessmentArea,
      ScoringRule,
      AssessmentInstance,
      AssessmentResponse,
      AssessmentTemplateCriteria,
      AssessmentCriteria,
      AssessmentCriteriaLevel,
      AssessmentCriteriaInheritance,
      AssessmentCriteriaResponse,
      AssessmentScoringScale,
      AssessmentScoringScaleLevel,
      AssessmentCriteriaScoring,
      OrganizationalUnit,
      User,
    ]),
  ],
  controllers: [
    TemplatesController,
    AssessmentsController,
    AssessmentTemplateController,
    EmployeeAssessmentController
  ],
  providers: [
    TemplatesService,
    AssessmentsService,
    ScoringService,
    WorkflowService,
    AssessmentTemplateService,
    EmployeeAssessmentService,
    AssessmentCriteriaService
  ],
  exports: [
    TemplatesService,
    AssessmentsService,
    ScoringService,
    WorkflowService,
    AssessmentTemplateService,
    EmployeeAssessmentService,
    AssessmentCriteriaService
  ],
})
export class AssessmentsModule { }
