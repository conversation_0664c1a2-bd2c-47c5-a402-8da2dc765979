import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum FeedbackType {
  EMOJI = 'emoji',
  QUICK_POLL = 'quick_poll',
  CHECK_IN = 'check_in',
  THUMBS_UP = 'thumbs_up',
}

export enum ContextType {
  TASK = 'task',
  MEETING = 'meeting',
  PROJECT = 'project',
  GENERAL = 'general',
}

@Entity('micro_feedback')
export class MicroFeedback {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'giver_id' })
  giverId: number;

  @Column({ name: 'receiver_id', nullable: true })
  receiverId: number;

  @Column({
    name: 'feedback_type',
    type: 'enum',
    enum: FeedbackType,
  })
  feedbackType: FeedbackType;

  @Column({ name: 'feedback_value', length: 255, nullable: true })
  feedbackValue: string;

  @Column({
    name: 'context_type',
    type: 'enum',
    enum: ContextType,
    nullable: true,
  })
  contextType: ContextType;

  @Column({ name: 'context_id', nullable: true })
  contextId: number;

  @CreateDateColumn({ name: 'given_at' })
  givenAt: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'giver_id' })
  giver: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'receiver_id' })
  receiver: User;
}
