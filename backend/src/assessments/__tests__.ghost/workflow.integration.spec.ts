import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as request from 'supertest';
import { AssessmentsModule } from '../assessments.module';
import { UsersModule } from '../../users/users.module';
import { AssessmentInstance, AssessmentStatus } from '../entities/assessment-instance.entity';
import { AssessmentTemplate } from '../entities/assessment-template.entity';
import { AssessmentArea } from '../entities/assessment-area.entity';
import { AssessmentResponse } from '../entities/assessment-response.entity';
import { User, UserRole } from '../../users/entities/user.entity';

describe('Assessment Workflow Integration', () => {
  let app: INestApplication;
  let assessmentRepository: Repository<AssessmentInstance>;
  let templateRepository: Repository<AssessmentTemplate>;
  let userRepository: Repository<User>;

  const hrAdmin = {
    userId: 1,
    role: UserRole.HR_ADMIN,
    email: '<EMAIL>'
  };

  const manager = {
    userId: 2,
    role: UserRole.MANAGER,
    email: '<EMAIL>'
  };

  const employee = {
    userId: 3,
    role: UserRole.EMPLOYEE,
    email: '<EMAIL>'
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [AssessmentInstance, AssessmentTemplate, AssessmentArea, AssessmentResponse, User],
          synchronize: true,
        }),
        AssessmentsModule,
        UsersModule,
      ],
    })
    .overrideGuard('JwtAuthGuard')
    .useValue({
      canActivate: (context) => {
        const request = context.switchToHttp().getRequest();
        request.user = request.headers['test-user'] === 'manager' ? manager : 
                      request.headers['test-user'] === 'employee' ? employee : hrAdmin;
        return true;
      },
    })
    .overrideGuard('RolesGuard')
    .useValue({
      canActivate: () => true,
    })
    .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    assessmentRepository = moduleFixture.get('AssessmentInstanceRepository');
    templateRepository = moduleFixture.get('AssessmentTemplateRepository');
    userRepository = moduleFixture.get('UserRepository');

    // Create test users
    await userRepository.save([
      { id: 1, email: '<EMAIL>', firstName: 'HR', lastName: 'Admin', role: UserRole.HR_ADMIN, is_active: true, password_hash: 'hash' },
      { id: 2, email: '<EMAIL>', firstName: 'Team', lastName: 'Manager', role: UserRole.MANAGER, is_active: true, password_hash: 'hash' },
      { id: 3, email: '<EMAIL>', firstName: 'Test', lastName: 'Employee', role: UserRole.EMPLOYEE, is_active: true, password_hash: 'hash' }
    ]);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Complete Assessment Workflow', () => {
    let templateId: number;
    let assessmentId: number;

    beforeEach(async () => {
      // Create a test template
      const template = await templateRepository.save({
        name: 'Workflow Test Template',
        description: 'Template for workflow testing',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 2,
        areas: []
      });
      templateId = template.id;
    });

    it('should create assessment in draft status', async () => {
      const assessmentData = {
        templateId,
        employeeId: 3,
        assessmentDate: '2024-12-15',
        notes: 'Test assessment',
        areaInputs: [],
        status: 'draft'
      };

      const response = await request(app.getHttpServer())
        .post('/assessments/create-with-scoring')
        .set('test-user', 'manager')
        .send(assessmentData)
        .expect(201);

      assessmentId = response.body.id;
      expect(response.body.status).toBe(AssessmentStatus.DRAFT);
    });

    it('should start assessment (draft -> in_progress)', async () => {
      const response = await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/start`)
        .set('test-user', 'manager')
        .expect(201);

      expect(response.body.status).toBe(AssessmentStatus.IN_PROGRESS);
    });

    it('should complete assessment (in_progress -> completed)', async () => {
      const response = await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/complete`)
        .set('test-user', 'manager')
        .send({ comments: 'Assessment completed by manager' })
        .expect(201);

      expect(response.body.status).toBe(AssessmentStatus.COMPLETED);
    });

    it('should approve assessment (completed -> approved) - HR Admin only', async () => {
      const response = await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/approve`)
        .send({ comments: 'Assessment approved by HR' })
        .expect(201);

      expect(response.body.status).toBe(AssessmentStatus.APPROVED);
      expect(response.body.approvedById).toBe(1);
      expect(response.body.approvedAt).toBeDefined();
    });

    it('should reject assessment (completed -> rejected) - HR Admin only', async () => {
      // First, reopen the assessment to completed status
      await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/reopen`)
        .send({ reason: 'Reopening for rejection test' })
        .expect(201);

      await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/complete`)
        .set('test-user', 'manager')
        .send({ comments: 'Completed again' })
        .expect(201);

      const response = await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/reject`)
        .send({ rejectionReason: 'Insufficient documentation' })
        .expect(201);

      expect(response.body.status).toBe(AssessmentStatus.REJECTED);
      expect(response.body.rejectedById).toBe(1);
      expect(response.body.rejectionReason).toBe('Insufficient documentation');
    });

    it('should get available actions based on status and role', async () => {
      // Manager should see different actions than HR Admin
      const managerResponse = await request(app.getHttpServer())
        .get(`/assessments/${assessmentId}/available-actions`)
        .set('test-user', 'manager')
        .expect(200);

      const hrResponse = await request(app.getHttpServer())
        .get(`/assessments/${assessmentId}/available-actions`)
        .expect(200);

      expect(Array.isArray(managerResponse.body)).toBe(true);
      expect(Array.isArray(hrResponse.body)).toBe(true);
    });

    it('should track workflow history', async () => {
      const response = await request(app.getHttpServer())
        .get(`/assessments/${assessmentId}/workflow-history`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should validate transitions before execution', async () => {
      const response = await request(app.getHttpServer())
        .get(`/assessments/${assessmentId}/validate-transition/approved`)
        .set('test-user', 'manager')
        .expect(200);

      expect(response.body).toHaveProperty('isValid');
      expect(response.body).toHaveProperty('errors');
      expect(response.body).toHaveProperty('warnings');
    });
  });

  describe('Workflow Permissions', () => {
    let assessmentId: number;

    beforeEach(async () => {
      // Create assessment for permission testing
      const template = await templateRepository.save({
        name: 'Permission Test Template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 2,
        areas: []
      });

      const assessment = await assessmentRepository.save({
        templateId: template.id,
        employeeId: 3,
        evaluatorId: 2,
        assessmentDate: new Date(),
        status: AssessmentStatus.COMPLETED,
        notes: 'Test assessment'
      });

      assessmentId = assessment.id;
    });

    it('should prevent manager from approving assessments', async () => {
      await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/approve`)
        .set('test-user', 'manager')
        .send({ comments: 'Trying to approve as manager' })
        .expect(403);
    });

    it('should prevent employee from changing assessment status', async () => {
      await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/complete`)
        .set('test-user', 'employee')
        .send({ comments: 'Employee trying to complete' })
        .expect(403);
    });

    it('should allow HR admin to perform any workflow action', async () => {
      await request(app.getHttpServer())
        .post(`/assessments/${assessmentId}/approve`)
        .send({ comments: 'HR admin approval' })
        .expect(201);
    });
  });

  describe('Workflow Statistics', () => {
    beforeEach(async () => {
      // Create multiple assessments in different statuses
      const template = await templateRepository.save({
        name: 'Stats Test Template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 1,
        areas: []
      });

      await assessmentRepository.save([
        {
          templateId: template.id,
          employeeId: 3,
          evaluatorId: 2,
          assessmentDate: new Date(),
          status: AssessmentStatus.DRAFT,
          notes: 'Draft assessment'
        },
        {
          templateId: template.id,
          employeeId: 3,
          evaluatorId: 2,
          assessmentDate: new Date(),
          status: AssessmentStatus.IN_PROGRESS,
          notes: 'In progress assessment'
        },
        {
          templateId: template.id,
          employeeId: 3,
          evaluatorId: 2,
          assessmentDate: new Date(),
          status: AssessmentStatus.COMPLETED,
          notes: 'Completed assessment'
        }
      ]);
    });

    it('should return workflow statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/assessments/workflow/stats')
        .expect(200);

      expect(response.body).toHaveProperty('totalAssessments');
      expect(response.body).toHaveProperty('byStatus');
      expect(response.body).toHaveProperty('averageCompletionTime');
      expect(response.body).toHaveProperty('pendingApprovals');
      expect(typeof response.body.totalAssessments).toBe('number');
    });

    it('should return pending approvals', async () => {
      const response = await request(app.getHttpServer())
        .get('/assessments/pending-approvals')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should filter assessments by status', async () => {
      const response = await request(app.getHttpServer())
        .get('/assessments/status/completed')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(assessment => {
        expect(assessment.status).toBe(AssessmentStatus.COMPLETED);
      });
    });
  });

  describe('Assessment Queries with Workflow Support', () => {
    beforeEach(async () => {
      const template = await templateRepository.save({
        name: 'Query Test Template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 2,
        areas: []
      });

      await assessmentRepository.save([
        {
          templateId: template.id,
          employeeId: 3,
          evaluatorId: 2,
          assessmentDate: new Date(),
          status: AssessmentStatus.APPROVED,
          notes: 'Approved assessment'
        },
        {
          templateId: template.id,
          employeeId: 3,
          evaluatorId: 1,
          assessmentDate: new Date(),
          status: AssessmentStatus.IN_PROGRESS,
          notes: 'In progress assessment'
        }
      ]);
    });

    it('should find assessments by evaluator', async () => {
      const response = await request(app.getHttpServer())
        .get('/assessments/evaluator/2')
        .set('test-user', 'manager')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(assessment => {
        expect(assessment.evaluatorId).toBe(2);
      });
    });

    it('should find assessments by employee', async () => {
      const response = await request(app.getHttpServer())
        .get('/assessments/employee/3')
        .set('test-user', 'employee')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(assessment => {
        expect(assessment.employeeId).toBe(3);
      });
    });

    it('should filter assessments by evaluator and status', async () => {
      const response = await request(app.getHttpServer())
        .get('/assessments/evaluator/2?status=approved')
        .set('test-user', 'manager')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(assessment => {
        expect(assessment.evaluatorId).toBe(2);
        expect(assessment.status).toBe(AssessmentStatus.APPROVED);
      });
    });
  });

  describe('Error Handling in Workflow', () => {
    it('should handle invalid status transitions', async () => {
      const template = await templateRepository.save({
        name: 'Error Test Template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 2,
        areas: []
      });

      const assessment = await assessmentRepository.save({
        templateId: template.id,
        employeeId: 3,
        evaluatorId: 2,
        assessmentDate: new Date(),
        status: AssessmentStatus.DRAFT,
        notes: 'Test assessment'
      });

      // Try to approve a draft assessment (should fail)
      await request(app.getHttpServer())
        .post(`/assessments/${assessment.id}/approve`)
        .send({ comments: 'Invalid transition' })
        .expect(400);
    });

    it('should handle non-existent assessment workflow operations', async () => {
      await request(app.getHttpServer())
        .post('/assessments/99999/start')
        .expect(404);

      await request(app.getHttpServer())
        .get('/assessments/99999/workflow-history')
        .expect(404);
    });

    it('should validate required fields for rejection', async () => {
      const template = await templateRepository.save({
        name: 'Rejection Test Template',
        isGlobal: false,
        isActive: true,
        version: 1,
        createdById: 2,
        areas: []
      });

      const assessment = await assessmentRepository.save({
        templateId: template.id,
        employeeId: 3,
        evaluatorId: 2,
        assessmentDate: new Date(),
        status: AssessmentStatus.COMPLETED,
        notes: 'Test assessment'
      });

      // Try to reject without reason (should fail)
      await request(app.getHttpServer())
        .post(`/assessments/${assessment.id}/reject`)
        .send({}) // No rejection reason
        .expect(400);
    });
  });
});
