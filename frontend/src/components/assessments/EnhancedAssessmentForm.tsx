import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  AssessmentInstance, 
  AssessmentResponse, 
  AssessmentStatus,
  UserRole,
  AssessmentAreaInput 
} from '../../types';
import api from '../../services/api';

interface EnhancedAssessmentFormProps {
  assessment: AssessmentInstance;
  userRole: UserRole;
  currentUserId: number;
  onSave?: (assessment: AssessmentInstance) => void;
  onStatusChange?: (assessment: AssessmentInstance) => void;
  readOnly?: boolean;
}

const EnhancedAssessmentForm: React.FC<EnhancedAssessmentFormProps> = ({
  assessment,
  userRole,
  currentUserId,
  onSave,
  onStatusChange,
  readOnly = false
}) => {
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isChangingStatus, setIsChangingStatus] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form state
  const [responses, setResponses] = useState<AssessmentResponse[]>(assessment.responses || []);
  const [notes, setNotes] = useState(assessment.notes || '');
  const [availableActions, setAvailableActions] = useState<any[]>([]);
  const [workflowHistory, setWorkflowHistory] = useState<any[]>([]);
  const [showWorkflowModal, setShowWorkflowModal] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [actionComments, setActionComments] = useState('');

  useEffect(() => {
    fetchAvailableActions();
    fetchWorkflowHistory();
  }, [assessment.id, assessment.status]);

  const fetchAvailableActions = async () => {
    try {
      const response = await api.get(`/assessments/${assessment.id}/available-actions`);
      setAvailableActions(response.data);
    } catch (err) {
      console.error('Error fetching available actions:', err);
    }
  };

  const fetchWorkflowHistory = async () => {
    try {
      const response = await api.get(`/assessments/${assessment.id}/workflow-history`);
      setWorkflowHistory(response.data);
    } catch (err) {
      console.error('Error fetching workflow history:', err);
    }
  };

  // Calculate live scores
  const calculatedScores = useMemo(() => {
    if (!assessment.template?.areas) return {};

    const scores: Record<number, { baseScore: number; finalScore: number; adjustments: any[] }> = {};
    
    assessment.template.areas.forEach(area => {
      const response = responses.find(r => r.areaId === area.id);
      if (response) {
        let finalScore = response.score || 0;
        const adjustments: any[] = [];

        // Apply scoring rules (simplified for demo)
        if (area.scoringRules) {
          area.scoringRules.forEach(rule => {
            if (rule.ruleType === 'addition') {
              finalScore += rule.scoreAdjustment;
              adjustments.push({
                type: 'addition',
                value: rule.scoreAdjustment,
                description: rule.description
              });
            } else if (rule.ruleType === 'subtraction') {
              finalScore -= rule.scoreAdjustment;
              adjustments.push({
                type: 'subtraction',
                value: -rule.scoreAdjustment,
                description: rule.description
              });
            }
          });
        }

        finalScore = Math.min(Math.max(finalScore, 0), area.maxScore);

        scores[area.id!] = {
          baseScore: response.score || 0,
          finalScore,
          adjustments
        };
      }
    });

    return scores;
  }, [responses, assessment.template]);

  // Calculate total score
  const totalScore = useMemo(() => {
    if (!assessment.template?.areas) return 0;

    let weightedSum = 0;
    let totalWeight = 0;

    assessment.template.areas.forEach(area => {
      const score = calculatedScores[area.id!];
      if (score) {
        weightedSum += score.finalScore * area.weight;
        totalWeight += area.weight;
      }
    });

    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }, [calculatedScores, assessment.template]);

  const updateResponse = (areaId: number, field: keyof AssessmentResponse, value: any) => {
    setResponses(prev => prev.map(response => 
      response.areaId === areaId 
        ? { ...response, [field]: value }
        : response
    ));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);

      const updateData = {
        responses: responses.map(r => ({
          areaId: r.areaId,
          score: r.score,
          evaluatorComments: r.evaluatorComments,
          employeeComments: r.employeeComments
        })),
        notes
      };

      const response = await api.patch(`/assessments/${assessment.id}`, updateData);
      
      setSuccess('Assessment saved successfully');
      setIsEditing(false);
      
      if (onSave) {
        onSave(response.data);
      }
    } catch (err: any) {
      console.error('Error saving assessment:', err);
      setError(err.response?.data?.message || 'Failed to save assessment');
    } finally {
      setIsSaving(false);
    }
  };

  const handleStatusChange = async () => {
    if (!selectedAction) return;

    try {
      setIsChangingStatus(true);
      setError(null);

      const endpoint = `/assessments/${assessment.id}/${selectedAction}`;
      const payload = actionComments ? { comments: actionComments } : {};

      const response = await api.post(endpoint, payload);
      
      setSuccess(`Assessment ${selectedAction}d successfully`);
      setShowWorkflowModal(false);
      setSelectedAction('');
      setActionComments('');
      
      if (onStatusChange) {
        onStatusChange(response.data);
      }

      await fetchAvailableActions();
      await fetchWorkflowHistory();
    } catch (err: any) {
      console.error('Error changing status:', err);
      setError(err.response?.data?.message || `Failed to ${selectedAction} assessment`);
    } finally {
      setIsChangingStatus(false);
    }
  };

  const canEdit = !readOnly && 
    (userRole === UserRole.HR_ADMIN || 
     (userRole === UserRole.MANAGER && assessment.evaluatorId === currentUserId)) &&
    [AssessmentStatus.DRAFT, AssessmentStatus.IN_PROGRESS].includes(assessment.status);

  const getStatusColor = (status: AssessmentStatus) => {
    switch (status) {
      case AssessmentStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case AssessmentStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case AssessmentStatus.COMPLETED:
        return 'bg-yellow-100 text-yellow-800';
      case AssessmentStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case AssessmentStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {assessment.template?.name} Assessment
            </h1>
            <p className="text-gray-600 mt-1">
              Employee: {assessment.employee?.firstName} {assessment.employee?.lastName}
            </p>
            <p className="text-sm text-gray-500">
              Evaluator: {assessment.evaluator?.firstName} {assessment.evaluator?.lastName}
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(assessment.status)}`}>
              {assessment.status.replace('_', ' ').toUpperCase()}
            </span>
            
            {canEdit && !isEditing && (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Edit
              </button>
            )}

            {availableActions.length > 0 && (
              <div className="relative">
                <button
                  onClick={() => setShowWorkflowModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700"
                >
                  Actions
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Score Summary */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800">Total Score</h3>
            <p className="text-2xl font-bold text-blue-900">{totalScore.toFixed(1)}</p>
            <p className="text-sm text-blue-600">out of 100</p>
          </div>
          
          <div className="bg-green-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-green-800">Assessment Date</h3>
            <p className="text-lg font-semibold text-green-900">
              {new Date(assessment.assessmentDate).toLocaleDateString()}
            </p>
          </div>
          
          <div className="bg-purple-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-purple-800">Areas Completed</h3>
            <p className="text-lg font-semibold text-purple-900">
              {responses.filter(r => r.score !== null && r.score !== undefined).length} / {assessment.template?.areas?.length || 0}
            </p>
          </div>

          <div className="bg-orange-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-orange-800">Progress</h3>
            <div className="flex items-center mt-1">
              <div className="flex-1 bg-orange-200 rounded-full h-2">
                <div 
                  className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${((responses.filter(r => r.score !== null).length) / (assessment.template?.areas?.length || 1)) * 100}%` 
                  }}
                />
              </div>
              <span className="ml-2 text-sm font-medium text-orange-900">
                {Math.round(((responses.filter(r => r.score !== null).length) / (assessment.template?.areas?.length || 1)) * 100)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      {/* Assessment Areas */}
      <div className="space-y-6">
        {assessment.template?.areas?.map((area, index) => {
          const response = responses.find(r => r.areaId === area.id);
          const calculatedScore = calculatedScores[area.id!];
          
          return (
            <div key={area.id} className="bg-white shadow rounded-lg p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900">{area.name}</h3>
                  {area.description && (
                    <p className="text-sm text-gray-600 mt-1">{area.description}</p>
                  )}
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>Weight: {(area.weight * 100).toFixed(1)}%</span>
                    <span>Max Score: {area.maxScore}</span>
                    {area.scoringRules && area.scoringRules.length > 0 && (
                      <span className="text-blue-600">
                        {area.scoringRules.length} scoring rule(s)
                      </span>
                    )}
                  </div>
                </div>
                
                {calculatedScore && (
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {calculatedScore.finalScore.toFixed(1)}
                    </div>
                    <div className="text-sm text-gray-500">
                      Base: {calculatedScore.baseScore}
                    </div>
                    {calculatedScore.adjustments.length > 0 && (
                      <div className="text-xs text-blue-600">
                        {calculatedScore.adjustments.length} adjustment(s)
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Score Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Score (0 - {area.maxScore}) *
                  </label>
                  {isEditing ? (
                    <input
                      type="number"
                      min="0"
                      max={area.maxScore}
                      value={response?.score || ''}
                      onChange={(e) => updateResponse(area.id!, 'score', parseFloat(e.target.value) || 0)}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="Enter score"
                    />
                  ) : (
                    <div className="text-lg font-semibold text-gray-900">
                      {response?.score !== null && response?.score !== undefined ? response.score : 'Not scored'}
                    </div>
                  )}
                </div>

                {/* Evaluator Comments */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Evaluator Comments
                  </label>
                  {isEditing ? (
                    <textarea
                      value={response?.evaluatorComments || ''}
                      onChange={(e) => updateResponse(area.id!, 'evaluatorComments', e.target.value)}
                      rows={3}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="Add comments about this area..."
                    />
                  ) : (
                    <div className="text-sm text-gray-900 whitespace-pre-wrap">
                      {response?.evaluatorComments || 'No comments provided'}
                    </div>
                  )}
                </div>
              </div>

              {/* Employee Comments (if available) */}
              {response?.employeeComments && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Employee Comments
                  </label>
                  <div className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md whitespace-pre-wrap">
                    {response.employeeComments}
                  </div>
                </div>
              )}

              {/* Score Adjustments */}
              {calculatedScore && calculatedScore.adjustments.length > 0 && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Score Adjustments</h4>
                  <div className="space-y-1">
                    {calculatedScore.adjustments.map((adjustment, idx) => (
                      <div key={idx} className="text-xs text-gray-600 flex justify-between">
                        <span>{adjustment.description}</span>
                        <span className={`font-medium ${adjustment.value > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {adjustment.value > 0 ? '+' : ''}{adjustment.value} points
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* General Notes */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">General Notes</h3>
        {isEditing ? (
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={4}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Add any general notes about this assessment..."
          />
        ) : (
          <div className="text-sm text-gray-900 whitespace-pre-wrap">
            {notes || 'No general notes provided'}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      {isEditing && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => {
                setIsEditing(false);
                setResponses(assessment.responses || []);
                setNotes(assessment.notes || '');
              }}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${
                isSaving
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      )}

      {/* Workflow Action Modal */}
      {showWorkflowModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Assessment Actions
              </h3>

              <div className="space-y-3">
                {availableActions.map((action, index) => (
                  <label key={index} className="flex items-center">
                    <input
                      type="radio"
                      name="action"
                      value={action.action}
                      checked={selectedAction === action.action}
                      onChange={(e) => setSelectedAction(e.target.value)}
                      className="mr-3"
                    />
                    <span className="text-sm text-gray-900">{action.label}</span>
                  </label>
                ))}
              </div>

              {selectedAction && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Comments {selectedAction === 'reject' ? '(Required)' : '(Optional)'}
                  </label>
                  <textarea
                    value={actionComments}
                    onChange={(e) => setActionComments(e.target.value)}
                    rows={3}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder={`Add comments for ${selectedAction}...`}
                  />
                </div>
              )}

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowWorkflowModal(false);
                    setSelectedAction('');
                    setActionComments('');
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleStatusChange}
                  disabled={!selectedAction || isChangingStatus || (selectedAction === 'reject' && !actionComments.trim())}
                  className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${
                    !selectedAction || isChangingStatus || (selectedAction === 'reject' && !actionComments.trim())
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {isChangingStatus ? 'Processing...' : 'Confirm'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Workflow History */}
      {workflowHistory.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Workflow History</h3>
          <div className="space-y-3">
            {workflowHistory.map((entry, index) => (
              <div key={index} className="flex items-start space-x-3 text-sm">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <span className="font-medium text-gray-900">
                        {entry.from} → {entry.to}
                      </span>
                      {entry.comments && (
                        <p className="text-gray-600 mt-1">{entry.comments}</p>
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(entry.timestamp).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedAssessmentForm;
