import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate, useParams } from 'react-router-dom';
import AssessmentList from '../components/assessments/AssessmentList';
import AssessmentForm from '../components/assessments/AssessmentForm';
import AssessmentDetail from '../components/assessments/AssessmentDetail';
import { UserRole } from '../types';

interface AssessmentsPageProps {
  // In a real application, these would come from authentication context
  userId: number;
  userRole: UserRole;
  teamId?: number;
}

const AssessmentsPage: React.FC<AssessmentsPageProps> = ({ userId, userRole, teamId }) => {
  return (
    <div className="container mx-auto px-4 py-6">
      <Routes>
        <Route 
          path="/" 
          element={
            <AssessmentList 
              userRole={userRole} 
              userId={userId} 
              teamId={teamId} 
            />
          } 
        />
        <Route 
          path="/create" 
          element={
            <AssessmentForm 
              userRole={userRole} 
              userId={userId} 
            />
          } 
        />
        <Route 
          path="/:id" 
          element={<AssessmentDetailWrapper userRole={userRole} userId={userId} />} 
        />
        <Route 
          path="/:id/edit" 
          element={<AssessmentFormWrapper userRole={userRole} userId={userId} />} 
        />
      </Routes>
    </div>
  );
};

// Wrapper components to handle route parameters
const AssessmentDetailWrapper: React.FC<{userRole: UserRole, userId: number}> = ({ userRole, userId }) => {
  const { id } = useParams<{id: string}>();
  const assessmentId = id ? parseInt(id) : 0;
  
  if (!assessmentId) {
    return <div className="text-red-600">Invalid assessment ID</div>;
  }
  
  return <AssessmentDetail assessmentId={assessmentId} userRole={userRole} userId={userId} />;
};

const AssessmentFormWrapper: React.FC<{userRole: UserRole, userId: number}> = ({ userRole, userId }) => {
  const { id } = useParams<{id: string}>();
  const assessmentId = id ? parseInt(id) : undefined;
  
  return <AssessmentForm assessmentId={assessmentId} userRole={userRole} userId={userId} />;
};

export default AssessmentsPage;
