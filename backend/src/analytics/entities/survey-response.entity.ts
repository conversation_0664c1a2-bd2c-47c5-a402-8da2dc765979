import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { EngagementSurvey } from './engagement-survey.entity';

@Entity('survey_responses')
export class SurveyResponse {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'survey_id' })
  surveyId: number;

  @Column({ name: 'respondent_id', nullable: true })
  respondentId: number;

  @Column({ type: 'json' })
  responses: object;

  @Column({ name: 'completion_time', nullable: true })
  completionTime: number; // seconds

  @CreateDateColumn({ name: 'submitted_at' })
  submittedAt: Date;

  @Column({ name: 'ip_address', length: 45, nullable: true })
  ipAddress: string;

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  // Relations
  @ManyToOne(() => EngagementSurvey, survey => survey.responses)
  @JoinColumn({ name: 'survey_id' })
  survey: EngagementSurvey;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'respondent_id' })
  respondent: User;
}
