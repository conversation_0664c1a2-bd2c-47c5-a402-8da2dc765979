#!/usr/bin/env node

/**
 * Systematic TypeScript Error Fixer
 * This script fixes all common TypeScript errors in the EHRX frontend
 */

const fs = require('fs');
const path = require('path');

// Define all the systematic fixes
const fixes = [
  // 1. Fix API response type mismatches
  {
    file: 'frontend/src/components/settings/DatabaseManagement.tsx',
    search: /const tablesData = Array\.isArray\(response\) \? response : response\.data \|\| response\.tables \|\| \[\];/g,
    replace: 'const tablesData = Array.isArray(response) ? response : [];'
  },
  
  // 2. Fix missing properties in interfaces
  {
    file: 'frontend/src/components/settings/DatabaseManagement.tsx',
    search: /setTableData\(\{\s*data: \[\],/g,
    replace: 'setTableData({\n        records: [],'
  },
  
  // 3. Fix UserManagement API response
  {
    file: 'frontend/src/components/settings/UserManagement.tsx',
    search: /const usersData = Array\.isArray\(response\) \? response : response\.data \|\| response\.users \|\| \[\];/g,
    replace: 'const usersData = Array.isArray(response) ? response : [];'
  },
  
  // 4. Fix role type issues
  {
    file: 'frontend/src/components/organization/modals/MemberSelectorModal.tsx',
    search: /role: string;/g,
    replace: 'role: UserRole | string;'
  },
  
  // 5. Fix OrganizationalUnit type issues
  {
    file: 'frontend/src/components/organization/modals/AddUnitModal.tsx',
    search: /type: string;/g,
    replace: 'type: "organization" | "division" | "department" | "team" | "squad" | "unit";'
  }
];

// Function to apply fixes
function applyFixes() {
  console.log('🔧 Starting systematic TypeScript error fixes...\n');
  
  let fixedCount = 0;
  
  fixes.forEach((fix, index) => {
    const filePath = path.join('/var/www/ehrx', fix.file);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${fix.file}`);
      return;
    }
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      content = content.replace(fix.search, fix.replace);
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ Fixed: ${fix.file}`);
        fixedCount++;
      } else {
        console.log(`ℹ️  No changes needed: ${fix.file}`);
      }
    } catch (error) {
      console.log(`❌ Error fixing ${fix.file}:`, error.message);
    }
  });
  
  console.log(`\n🎉 Applied ${fixedCount} fixes successfully!`);
}

// Run the fixes
applyFixes();
