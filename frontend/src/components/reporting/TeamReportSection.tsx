import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  Grid, 
  Paper, 
  CircularProgress,
  Autocomplete,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { Bar, Pie } from 'react-chartjs-2';
import { ReportingService } from '../../services/reporting';
import { TeamReport, PerformanceDistribution } from '../../types/reporting';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface TeamReportSectionProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateRangeChange: (newDateRange: { startDate: string; endDate: string }) => void;
  onError: (error: string | null) => void;
}

interface Team {
  id: number;
  name: string;
}

const TeamReportSection: React.FC<TeamReportSectionProps> = ({
  dateRange,
  onDateRangeChange,
  onError
}) => {
  const [teams, setTeams] = useState<Team[]>([
    { id: 1, name: 'Engineering' },
    { id: 2, name: 'Sales' },
    { id: 3, name: 'Marketing' },
    { id: 4, name: 'Customer Success' },
    // Mocked teams data - would come from API in real implementation
  ]);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [teamReport, setTeamReport] = useState<TeamReport | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchTeamReport = async () => {
    if (!selectedTeam) return;
    
    setLoading(true);
    try {
      const report = await ReportingService.getTeamReport(
        selectedTeam.id,
        dateRange.startDate,
        dateRange.endDate
      );
      setTeamReport(report);
    } catch (error) {
      console.error('Error fetching team report:', error);
      onError('Failed to fetch team report. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      onDateRangeChange({
        ...dateRange,
        startDate: date.toISOString().split('T')[0]
      });
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      onDateRangeChange({
        ...dateRange,
        endDate: date.toISOString().split('T')[0]
      });
    }
  };

  const handleTeamChange = (_event: React.SyntheticEvent, value: Team | null) => {
    setSelectedTeam(value);
    setTeamReport(null);
  };

  const renderEmployeeScoresChart = () => {
    if (!teamReport?.employeeScores || teamReport.employeeScores.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 2 }}>
          <Typography variant="body1">No employee data available</Typography>
        </Box>
      );
    }

    // Create employee names based on IDs (in a real app, we'd have actual names)
    const employeeNames = teamReport.employeeScores.map(emp => `Employee ${emp.employeeId}`);

    const chartData = {
      labels: employeeNames,
      datasets: [
        {
          label: 'Average Score',
          data: teamReport.employeeScores.map(emp => emp.averageScore * 100), // Convert to percentage
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgb(54, 162, 235)',
          borderWidth: 1,
        },
      ],
    };

    const chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: 'Employee Performance',
        },
      },
      scales: {
        y: {
          min: 0,
          max: 100,
          title: {
            display: true,
            text: 'Score (%)'
          }
        }
      }
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Bar data={chartData} options={chartOptions} />
      </Box>
    );
  };

  const renderPerformanceDistribution = () => {
    if (!teamReport?.performanceDistribution) return null;

    const distribution = teamReport.performanceDistribution;
    
    const chartData = {
      labels: ['Excellent', 'Good', 'Satisfactory', 'Needs Improvement', 'Poor'],
      datasets: [
        {
          data: [
            distribution.excellent,
            distribution.good,
            distribution.satisfactory,
            distribution.needsImprovement,
            distribution.poor,
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(255, 99, 132, 0.6)',
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(255, 99, 132, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };

    const chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'right' as const,
        },
        title: {
          display: true,
          text: 'Performance Distribution',
        },
      },
    };

    return (
      <Box sx={{ mt: 2, maxHeight: 300 }}>
        <Pie data={chartData} options={chartOptions} />
      </Box>
    );
  };

  const renderEmployeeTable = () => {
    if (!teamReport?.employeeScores || teamReport.employeeScores.length === 0) return null;

    return (
      <TableContainer>
        <Table sx={{ minWidth: 650 }} aria-label="employee scores table">
          <TableHead>
            <TableRow>
              <TableCell>Employee ID</TableCell>
              <TableCell align="right">Average Score</TableCell>
              <TableCell align="right">Assessments</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {teamReport.employeeScores.map((employee) => (
              <TableRow key={employee.employeeId}>
                <TableCell component="th" scope="row">
                  Employee {employee.employeeId}
                </TableCell>
                <TableCell align="right">{(employee.averageScore * 100).toFixed(2)}%</TableCell>
                <TableCell align="right">{employee.assessmentCount}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  const renderTeamReport = () => {
    if (!teamReport) return null;

    return (
      <Box sx={{ mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h5" component="h2" gutterBottom>
              Team Report: {selectedTeam?.name}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" gutterBottom>
              Period: {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
            </Typography>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Summary" />
              <Divider />
              <CardContent>
                <Typography variant="body1" gutterBottom>
                  <strong>Team Size:</strong> {teamReport.employeeScores.length}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Total Assessments:</strong> {teamReport.assessmentCount}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Average Score:</strong> {(teamReport.averageScore * 100).toFixed(2)}%
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader title="Performance Distribution" />
              <Divider />
              <CardContent>
                {renderPerformanceDistribution()}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardHeader title="Employee Performance" />
              <Divider />
              <CardContent>
                {renderEmployeeScoresChart()}
                <Divider sx={{ my: 2 }} />
                {renderEmployeeTable()}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="flex-end" mt={2}>
              <Button 
                variant="contained"
                onClick={() => {
                  if (selectedTeam) {
                    window.open(
                      `${window.location.origin}/api/reports/team/${selectedTeam.id}/export/pdf?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`,
                      '_blank'
                    );
                  }
                }}
                disabled={!selectedTeam}
                sx={{ mr: 2 }}
              >
                Export PDF
              </Button>
              <Button 
                variant="outlined"
                onClick={() => {
                  if (selectedTeam) {
                    const csvFilters = {
                      startDate: dateRange.startDate,
                      endDate: dateRange.endDate,
                      teamIds: [selectedTeam.id]
                    };
                    ReportingService.exportAssessmentsCsv(csvFilters);
                  }
                }}
                disabled={!selectedTeam}
              >
                Export CSV
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    );
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Team Performance Report
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Autocomplete
              options={teams}
              getOptionLabel={(option) => option.name}
              value={selectedTeam}
              onChange={handleTeamChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Team"
                  variant="outlined"
                  fullWidth
                />
              )}
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Start Date"
                value={new Date(dateRange.startDate)}
                onChange={handleStartDateChange}
                slotProps={{ textField: { variant: 'outlined', fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} md={2}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="End Date"
                value={new Date(dateRange.endDate)}
                onChange={handleEndDateChange}
                slotProps={{ textField: { variant: 'outlined', fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} md={1}>
            <Button
              variant="contained"
              fullWidth
              onClick={fetchTeamReport}
              disabled={!selectedTeam || loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Run'}
            </Button>
          </Grid>
        </Grid>
      </Paper>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : renderTeamReport()}
    </Box>
  );
};

export default TeamReportSection;
