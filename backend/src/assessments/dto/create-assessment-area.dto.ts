import { IsNotEmpty, IsString, IsOptional, IsNumber, Min, <PERSON>, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateScoringRuleDto } from './create-scoring-rule.dto';

export class CreateAssessmentAreaDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNumber()
  @Min(0.01)
  @Max(10.0)
  weight: number;

  @IsNumber()
  @Min(1)
  @Max(100)
  maxScore: number;

  @IsNumber()
  @Min(0)
  orderIndex: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateScoringRuleDto)
  scoringRules?: CreateScoringRuleDto[];
}
