-- EHRX Database Initialization Script

-- Create database
CREATE DATABASE IF NOT EXISTS ehrx CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ehrx;

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
  last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
  role ENUM('hr_admin', 'manager', 'employee', 'guest') NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email)
) ENGINE=InnoDB;

-- Teams table
CREATE TABLE IF NOT EXISTS teams (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(100) NOT NULL,
  description TEXT,
  created_by INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB;

-- Team Membership table
CREATE TABLE IF NOT EXISTS team_members (
  id INT AUTO_INCREMENT PRIMARY KEY,
  team_id INT NOT NULL,
  user_id INT NOT NULL,
  role ENUM('team_lead', 'member', 'guest') NOT NULL,
  added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_team_user (team_id, user_id),
  FOREIGN KEY (team_id) REFERENCES teams(id),
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB;

-- Assessment Templates table
CREATE TABLE IF NOT EXISTS assessment_templates (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  created_by INT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  version INT DEFAULT 1,
  parent_template_id INT NULL,
  is_global BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id),
  FOREIGN KEY (parent_template_id) REFERENCES assessment_templates(id)
) ENGINE=InnoDB;

-- Assessment Areas table
CREATE TABLE IF NOT EXISTS assessment_areas (
  id INT AUTO_INCREMENT PRIMARY KEY,
  template_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  weight DECIMAL(5,2) DEFAULT 1.00,
  max_score INT NOT NULL,
  order_index INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (template_id) REFERENCES assessment_templates(id)
) ENGINE=InnoDB;

-- Scoring Rules table
CREATE TABLE IF NOT EXISTS scoring_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  area_id INT NOT NULL,
  rule_type ENUM('addition', 'subtraction', 'multiplication', 'division', 'conditional') NOT NULL,
  condition_field VARCHAR(100),
  condition_operator ENUM('equals', 'not_equals', 'greater_than', 'less_than', 'greater_or_equal', 'less_or_equal') NULL,
  condition_value VARCHAR(255) NULL,
  score_adjustment INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (area_id) REFERENCES assessment_areas(id)
) ENGINE=InnoDB;

-- Performance Assessments table
CREATE TABLE IF NOT EXISTS performance_assessments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id INT NOT NULL,
  template_id INT NOT NULL,
  assessor_id INT NOT NULL,
  team_id INT NOT NULL,
  assessment_date DATE NOT NULL,
  status ENUM('draft', 'in_review', 'completed') NOT NULL DEFAULT 'draft',
  total_score DECIMAL(7,2) NULL,
  comments TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES users(id),
  FOREIGN KEY (template_id) REFERENCES assessment_templates(id),
  FOREIGN KEY (assessor_id) REFERENCES users(id),
  FOREIGN KEY (team_id) REFERENCES teams(id),
  INDEX idx_employee_date (employee_id, assessment_date),
  INDEX idx_team_date (team_id, assessment_date)
) ENGINE=InnoDB;

-- Assessment Scores table
CREATE TABLE IF NOT EXISTS assessment_scores (
  id INT AUTO_INCREMENT PRIMARY KEY,
  assessment_id INT NOT NULL,
  area_id INT NOT NULL,
  raw_score DECIMAL(7,2) NOT NULL,
  adjusted_score DECIMAL(7,2) NOT NULL,
  comments TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (assessment_id) REFERENCES performance_assessments(id),
  FOREIGN KEY (area_id) REFERENCES assessment_areas(id),
  INDEX idx_assessment_area (assessment_id, area_id)
) ENGINE=InnoDB;

-- Action Items table
CREATE TABLE IF NOT EXISTS action_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id INT NOT NULL,
  assessment_id INT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  due_date DATE NULL,
  status ENUM('pending', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
  created_by INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES users(id),
  FOREIGN KEY (assessment_id) REFERENCES performance_assessments(id),
  FOREIGN KEY (created_by) REFERENCES users(id),
  INDEX idx_employee_status (employee_id, status)
) ENGINE=InnoDB;

-- Process Improvements table
CREATE TABLE IF NOT EXISTS process_improvements (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  status ENUM('proposed', 'in_review', 'approved', 'implemented', 'rejected') NOT NULL DEFAULT 'proposed',
  created_by INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES users(id),
  FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB;

-- AI Initiatives table
CREATE TABLE IF NOT EXISTS ai_initiatives (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  status ENUM('proposed', 'in_development', 'completed', 'on_hold') NOT NULL DEFAULT 'proposed',
  created_by INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES users(id),
  FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB;

-- Stakeholder Feedback table
CREATE TABLE IF NOT EXISTS stakeholder_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_id INT NOT NULL,
  source VARCHAR(255) NOT NULL,
  feedback_text TEXT NOT NULL,
  received_date DATE NOT NULL,
  recorded_by INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (employee_id) REFERENCES users(id),
  FOREIGN KEY (recorded_by) REFERENCES users(id)
) ENGINE=InnoDB;

-- Insert admin user (password: Admin@123 - this should be changed in production)
INSERT INTO users (email, password_hash, first_name, last_name, role, is_active)
VALUES (
  '<EMAIL>',
  '.cdeGcI0.]7D]zJJO1{jj]YQu1o_|']6_s2', -- This is a placeholder hash; real hash will be generated by the app
  'Admin',
  'User',
  'hr_admin',
  true
);

-- End of initialization script
