import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum ProcessImprovementStatus {
  PROPOSED = 'proposed',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  IMPLEMENTING = 'implementing',
  COMPLETED = 'completed',
  REJECTED = 'rejected'
}

export enum ImprovementImpact {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  TRANSFORMATIONAL = 'transformational'
}

export enum ImprovementEffort {
  MINIMAL = 'minimal',
  MODERATE = 'moderate',
  SIGNIFICANT = 'significant',
  MAJOR = 'major'
}

@Entity('process_improvements')
export class ProcessImprovement {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'current_process', type: 'text' })
  currentProcess: string;

  @Column({ name: 'proposed_process', type: 'text' })
  proposedProcess: string;

  @Column({
    type: 'enum',
    enum: ProcessImprovementStatus,
    default: ProcessImprovementStatus.PROPOSED
  })
  status: ProcessImprovementStatus;

  @Column({
    type: 'enum',
    enum: ImprovementImpact,
    default: ImprovementImpact.MEDIUM
  })
  impact: ImprovementImpact;

  @Column({
    type: 'enum',
    enum: ImprovementEffort,
    default: ImprovementEffort.MODERATE
  })
  effort: ImprovementEffort;

  @Column({ name: 'target_completion_date', nullable: true })
  targetCompletionDate: Date;

  @Column({ name: 'actual_completion_date', nullable: true })
  actualCompletionDate: Date;

  @Column({ name: 'created_by' })
  createdById: number;

  @Column({ name: 'assigned_to', nullable: true })
  assignedToId: number;

  @Column({ name: 'assessment_id', nullable: true })
  assessmentId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_to' })
  assignedTo: User;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
