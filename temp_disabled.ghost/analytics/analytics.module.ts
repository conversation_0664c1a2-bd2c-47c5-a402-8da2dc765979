import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsService } from './analytics.service';
import { AnalyticsController } from './analytics.controller';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { ActionItem } from '../action-items/entities/action-item.entity';
import { ImprovementPlan } from '../improvement-plans/entities/improvement-plan.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AssessmentInstance,
      ActionItem,
      ImprovementPlan,
      User
    ])
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService],
  exports: [AnalyticsService],
})
export class AnalyticsModule { }
