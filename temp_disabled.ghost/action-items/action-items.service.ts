import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection, Between, In, LessThan } from 'typeorm';
import { ActionItem, ActionItemUpdate, ActionItemStatus, ActionItemPriority, ActionItemCategory } from './entities/action-item.entity';
import { CreateActionItemDto } from './dto/create-action-item.dto';
import { UpdateActionItemDto, UpdateActionItemProgressDto, BulkUpdateActionItemsDto } from './dto/update-action-item.dto';
import { UserRole } from '../users/entities/user.entity';

export interface ActionItemFilters {
  status?: ActionItemStatus;
  priority?: ActionItemPriority;
  category?: ActionItemCategory;
  assignedToId?: number;
  createdById?: number;
  assessmentId?: number;
  dueDateFrom?: Date;
  dueDateTo?: Date;
  isOverdue?: boolean;
  tags?: string[];
  search?: string;
}

export interface ActionItemStatistics {
  totalItems: number;
  byStatus: Record<ActionItemStatus, number>;
  byPriority: Record<ActionItemPriority, number>;
  byCategory: Record<ActionItemCategory, number>;
  overdueItems: number;
  completedThisMonth: number;
  averageCompletionTime: number;
  upcomingDueDates: number;
}

@Injectable()
export class ActionItemsService {
  constructor(
    @InjectRepository(ActionItem)
    private actionItemRepository: Repository<ActionItem>,
    @InjectRepository(ActionItemUpdate)
    private actionItemUpdateRepository: Repository<ActionItemUpdate>,
    private connection: Connection,
  ) { }

  async create(createActionItemDto: CreateActionItemDto, createdById: number): Promise<ActionItem> {
    const actionItem = this.actionItemRepository.create({
      ...createActionItemDto,
      createdById,
      dueDate: createActionItemDto.dueDate ? new Date(createActionItemDto.dueDate) : null,
    });

    const savedActionItem = await this.actionItemRepository.save(actionItem);

    // Create initial update record
    await this.createUpdateRecord(savedActionItem.id, createdById, {
      newStatus: ActionItemStatus.OPEN,
      newProgress: createActionItemDto.progressPercentage || 0,
      comments: 'Action item created'
    });

    return this.findOne(savedActionItem.id);
  }

  async findAll(
    filters: ActionItemFilters = {},
    page = 1,
    limit = 20,
    userRole: UserRole,
    userId: number
  ): Promise<{
    items: ActionItem[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const queryBuilder = this.actionItemRepository.createQueryBuilder('actionItem')
      .leftJoinAndSelect('actionItem.assignedTo', 'assignedTo')
      .leftJoinAndSelect('actionItem.createdBy', 'createdBy')
      .leftJoinAndSelect('actionItem.assessment', 'assessment')
      .leftJoinAndSelect('actionItem.parentActionItem', 'parentActionItem')
      .leftJoinAndSelect('actionItem.subItems', 'subItems');

    // Apply role-based filtering
    if (userRole === UserRole.EMPLOYEE) {
      queryBuilder.andWhere('(actionItem.assignedToId = :userId OR actionItem.createdById = :userId)', { userId });
    } else if (userRole === UserRole.MANAGER) {
      // Managers can see items assigned to their team members (simplified - would need team structure)
      queryBuilder.andWhere('(actionItem.assignedToId = :userId OR actionItem.createdById = :userId)', { userId });
    }
    // HR_ADMIN can see all items

    // Apply filters
    if (filters.status) {
      queryBuilder.andWhere('actionItem.status = :status', { status: filters.status });
    }

    if (filters.priority) {
      queryBuilder.andWhere('actionItem.priority = :priority', { priority: filters.priority });
    }

    if (filters.category) {
      queryBuilder.andWhere('actionItem.category = :category', { category: filters.category });
    }

    if (filters.assignedToId) {
      queryBuilder.andWhere('actionItem.assignedToId = :assignedToId', { assignedToId: filters.assignedToId });
    }

    if (filters.createdById) {
      queryBuilder.andWhere('actionItem.createdById = :createdById', { createdById: filters.createdById });
    }

    if (filters.assessmentId) {
      queryBuilder.andWhere('actionItem.assessmentId = :assessmentId', { assessmentId: filters.assessmentId });
    }

    if (filters.dueDateFrom && filters.dueDateTo) {
      queryBuilder.andWhere('actionItem.dueDate BETWEEN :dueDateFrom AND :dueDateTo', {
        dueDateFrom: filters.dueDateFrom,
        dueDateTo: filters.dueDateTo
      });
    }

    if (filters.isOverdue) {
      queryBuilder.andWhere('actionItem.dueDate < :today AND actionItem.status != :completed', {
        today: new Date(),
        completed: ActionItemStatus.COMPLETED
      });
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('actionItem.tags && :tags', { tags: filters.tags });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(actionItem.title ILIKE :search OR actionItem.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Order by priority and due date
    queryBuilder.orderBy('actionItem.priority', 'DESC')
      .addOrderBy('actionItem.dueDate', 'ASC')
      .addOrderBy('actionItem.createdAt', 'DESC');

    const items = await queryBuilder.getMany();

    return {
      items,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  async findOne(id: number): Promise<ActionItem> {
    const actionItem = await this.actionItemRepository.findOne({
      where: { id },
      relations: [
        'assignedTo',
        'createdBy',
        'assessment',
        'parentActionItem',
        'subItems',
        'updates',
        'updates.updatedBy'
      ]
    });

    if (!actionItem) {
      throw new NotFoundException(`Action item with ID ${id} not found`);
    }

    return actionItem;
  }

  async update(
    id: number,
    updateActionItemDto: UpdateActionItemDto,
    updatedById: number,
    userRole: UserRole
  ): Promise<ActionItem> {
    const actionItem = await this.findOne(id);

    // Check permissions
    this.checkUpdatePermissions(actionItem, updatedById, userRole);

    const previousStatus = actionItem.status;
    const previousProgress = actionItem.progressPercentage;

    // Update the action item
    Object.assign(actionItem, {
      ...updateActionItemDto,
      dueDate: updateActionItemDto.dueDate ? new Date(updateActionItemDto.dueDate) : actionItem.dueDate,
      completedDate: updateActionItemDto.completedDate ? new Date(updateActionItemDto.completedDate) : actionItem.completedDate,
    });

    // Auto-update status based on progress
    if (updateActionItemDto.progressPercentage !== undefined) {
      if (updateActionItemDto.progressPercentage === 100 && actionItem.status !== ActionItemStatus.COMPLETED) {
        actionItem.status = ActionItemStatus.COMPLETED;
        actionItem.completedDate = new Date();
      } else if (updateActionItemDto.progressPercentage > 0 && actionItem.status === ActionItemStatus.OPEN) {
        actionItem.status = ActionItemStatus.IN_PROGRESS;
      }
    }

    const savedActionItem = await this.actionItemRepository.save(actionItem);

    // Create update record if status or progress changed
    if (previousStatus !== savedActionItem.status || previousProgress !== savedActionItem.progressPercentage) {
      await this.createUpdateRecord(id, updatedById, {
        previousStatus,
        newStatus: savedActionItem.status,
        previousProgress,
        newProgress: savedActionItem.progressPercentage,
        comments: 'Action item updated'
      });
    }

    return this.findOne(id);
  }

  async updateProgress(
    id: number,
    updateProgressDto: UpdateActionItemProgressDto,
    updatedById: number,
    userRole: UserRole
  ): Promise<ActionItem> {
    const actionItem = await this.findOne(id);

    // Check permissions
    this.checkUpdatePermissions(actionItem, updatedById, userRole);

    const previousProgress = actionItem.progressPercentage;
    const previousStatus = actionItem.status;

    actionItem.progressPercentage = updateProgressDto.progressPercentage;

    // Auto-update status based on progress
    if (updateProgressDto.progressPercentage === 100) {
      actionItem.status = ActionItemStatus.COMPLETED;
      actionItem.completedDate = new Date();
    } else if (updateProgressDto.progressPercentage > 0 && actionItem.status === ActionItemStatus.OPEN) {
      actionItem.status = ActionItemStatus.IN_PROGRESS;
    }

    // Update actual hours if provided
    if (updateProgressDto.hoursLogged) {
      actionItem.actualHours = (actionItem.actualHours || 0) + updateProgressDto.hoursLogged;
    }

    const savedActionItem = await this.actionItemRepository.save(actionItem);

    // Create update record
    await this.createUpdateRecord(id, updatedById, {
      previousStatus,
      newStatus: savedActionItem.status,
      previousProgress,
      newProgress: savedActionItem.progressPercentage,
      comments: updateProgressDto.comments,
      hoursLogged: updateProgressDto.hoursLogged
    });

    return this.findOne(id);
  }

  async bulkUpdate(
    bulkUpdateDto: BulkUpdateActionItemsDto,
    updatedById: number,
    userRole: UserRole
  ): Promise<{
    updated: ActionItem[];
    errors: { id: number; error: string }[];
  }> {
    const updated: ActionItem[] = [];
    const errors: { id: number; error: string }[] = [];

    for (const id of bulkUpdateDto.actionItemIds) {
      try {
        const actionItem = await this.findOne(id);
        this.checkUpdatePermissions(actionItem, updatedById, userRole);

        const updateData: Partial<ActionItem> = {};
        if (bulkUpdateDto.status) updateData.status = bulkUpdateDto.status;
        if (bulkUpdateDto.assignedToId) updateData.assignedToId = bulkUpdateDto.assignedToId;
        if (bulkUpdateDto.dueDate) updateData.dueDate = new Date(bulkUpdateDto.dueDate);

        Object.assign(actionItem, updateData);
        const savedItem = await this.actionItemRepository.save(actionItem);
        updated.push(savedItem);

        // Create update record
        await this.createUpdateRecord(id, updatedById, {
          newStatus: savedItem.status,
          newProgress: savedItem.progressPercentage,
          comments: bulkUpdateDto.comments || 'Bulk update'
        });

      } catch (error) {
        errors.push({ id, error: error.message });
      }
    }

    return { updated, errors };
  }

  async remove(id: number, userId: number, userRole: UserRole): Promise<void> {
    const actionItem = await this.findOne(id);

    // Check permissions - only creator or HR admin can delete
    if (actionItem.createdById !== userId && userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('You can only delete action items you created');
    }

    await this.actionItemRepository.remove(actionItem);
  }

  async getStatistics(userId?: number, userRole?: UserRole): Promise<ActionItemStatistics> {
    let queryBuilder = this.actionItemRepository.createQueryBuilder('actionItem');

    // Apply role-based filtering for statistics
    if (userRole === UserRole.EMPLOYEE && userId) {
      queryBuilder = queryBuilder.where('(actionItem.assignedToId = :userId OR actionItem.createdById = :userId)', { userId });
    } else if (userRole === UserRole.MANAGER && userId) {
      queryBuilder = queryBuilder.where('(actionItem.assignedToId = :userId OR actionItem.createdById = :userId)', { userId });
    }

    const totalItems = await queryBuilder.getCount();

    // Get counts by status
    const statusCounts = await queryBuilder
      .select('actionItem.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('actionItem.status')
      .getRawMany();

    const byStatus = statusCounts.reduce((acc, item) => {
      acc[item.status] = parseInt(item.count);
      return acc;
    }, {} as Record<ActionItemStatus, number>);

    // Ensure all statuses are represented
    Object.values(ActionItemStatus).forEach(status => {
      if (!byStatus[status]) byStatus[status] = 0;
    });

    // Get counts by priority
    const priorityCounts = await queryBuilder
      .select('actionItem.priority', 'priority')
      .addSelect('COUNT(*)', 'count')
      .groupBy('actionItem.priority')
      .getRawMany();

    const byPriority = priorityCounts.reduce((acc, item) => {
      acc[item.priority] = parseInt(item.count);
      return acc;
    }, {} as Record<ActionItemPriority, number>);

    // Get counts by category
    const categoryCounts = await queryBuilder
      .select('actionItem.category', 'category')
      .addSelect('COUNT(*)', 'count')
      .groupBy('actionItem.category')
      .getRawMany();

    const byCategory = categoryCounts.reduce((acc, item) => {
      acc[item.category] = parseInt(item.count);
      return acc;
    }, {} as Record<ActionItemCategory, number>);

    // Get overdue items
    const overdueItems = await queryBuilder
      .where('actionItem.dueDate < :today AND actionItem.status != :completed', {
        today: new Date(),
        completed: ActionItemStatus.COMPLETED
      })
      .getCount();

    // Get completed this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const completedThisMonth = await queryBuilder
      .where('actionItem.status = :completed AND actionItem.completedDate >= :startOfMonth', {
        completed: ActionItemStatus.COMPLETED,
        startOfMonth
      })
      .getCount();

    // Get upcoming due dates (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const upcomingDueDates = await queryBuilder
      .where('actionItem.dueDate BETWEEN :today AND :nextWeek AND actionItem.status != :completed', {
        today: new Date(),
        nextWeek,
        completed: ActionItemStatus.COMPLETED
      })
      .getCount();

    return {
      totalItems,
      byStatus,
      byPriority,
      byCategory,
      overdueItems,
      completedThisMonth,
      averageCompletionTime: 0, // Would need to calculate based on creation and completion dates
      upcomingDueDates
    };
  }

  private async createUpdateRecord(
    actionItemId: number,
    updatedById: number,
    updateData: {
      previousStatus?: ActionItemStatus;
      newStatus: ActionItemStatus;
      previousProgress?: number;
      newProgress: number;
      comments?: string;
      hoursLogged?: number;
    }
  ): Promise<ActionItemUpdate> {
    const update = this.actionItemUpdateRepository.create({
      actionItemId,
      updatedById,
      ...updateData
    });

    return this.actionItemUpdateRepository.save(update);
  }

  private checkUpdatePermissions(actionItem: ActionItem, userId: number, userRole: UserRole): void {
    // HR Admin can update any action item
    if (userRole === UserRole.HR_ADMIN) {
      return;
    }

    // Assigned user can update their own action items
    if (actionItem.assignedToId === userId) {
      return;
    }

    // Creator can update action items they created
    if (actionItem.createdById === userId) {
      return;
    }

    // Managers can update action items for their team (simplified check)
    if (userRole === UserRole.MANAGER) {
      return;
    }

    throw new ForbiddenException('You do not have permission to update this action item');
  }

  async markOverdueItems(): Promise<number> {
    const overdueItems = await this.actionItemRepository.find({
      where: {
        dueDate: LessThan(new Date()),
        status: In([ActionItemStatus.OPEN, ActionItemStatus.IN_PROGRESS])
      }
    });

    for (const item of overdueItems) {
      item.status = ActionItemStatus.OVERDUE;
      await this.actionItemRepository.save(item);
    }

    return overdueItems.length;
  }

  async getMyActionItems(userId: number, status?: ActionItemStatus): Promise<ActionItem[]> {
    const where: any = { assignedToId: userId };
    if (status) {
      where.status = status;
    }

    return this.actionItemRepository.find({
      where,
      relations: ['createdBy', 'assessment', 'parentActionItem'],
      order: { dueDate: 'ASC', priority: 'DESC' }
    });
  }

  async getActionItemsByAssessment(assessmentId: number): Promise<ActionItem[]> {
    return this.actionItemRepository.find({
      where: { assessmentId },
      relations: ['assignedTo', 'createdBy', 'updates'],
      order: { createdAt: 'DESC' }
    });
  }
}
