import { IsString, IsOptional, <PERSON><PERSON><PERSON>, IsISO8601, Is<PERSON><PERSON><PERSON> } from 'class-validator';
import { AIInitiativeStatus, AIInitiativeCategory } from '../../entities/ai-initiative.entity';

export class UpdateAIInitiativeDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(AIInitiativeStatus)
  @IsOptional()
  status?: AIInitiativeStatus;

  @IsEnum(AIInitiativeCategory)
  @IsOptional()
  category?: AIInitiativeCategory;

  @IsString()
  @IsOptional()
  businessCase?: string;

  @IsString()
  @IsOptional()
  expectedBenefits?: string;

  @IsString()
  @IsOptional()
  implementationChallenges?: string;

  @IsString()
  @IsOptional()
  dataRequirements?: string;

  @IsISO8601()
  @IsOptional()
  targetCompletionDate?: string;

  @IsISO8601()
  @IsOptional()
  actualCompletionDate?: string;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;
}
