import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ActionItemsService, ActionItemFilters } from './action-items.service';
import { CreateActionItemDto } from './dto/create-action-item.dto';
import { UpdateActionItemDto, UpdateActionItemProgressDto, BulkUpdateActionItemsDto } from './dto/update-action-item.dto';
import { ActionItemStatus, ActionItemPriority, ActionItemCategory } from './entities/action-item.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('action-items')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ActionItemsController {
  constructor(private readonly actionItemsService: ActionItemsService) {}

  @Post()
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() createActionItemDto: CreateActionItemDto, @Request() req) {
    return this.actionItemsService.create(createActionItemDto, req.user.userId);
  }

  @Get()
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  findAll(
    @Query('status') status?: ActionItemStatus,
    @Query('priority') priority?: ActionItemPriority,
    @Query('category') category?: ActionItemCategory,
    @Query('assignedToId') assignedToId?: number,
    @Query('createdById') createdById?: number,
    @Query('assessmentId') assessmentId?: number,
    @Query('dueDateFrom') dueDateFrom?: string,
    @Query('dueDateTo') dueDateTo?: string,
    @Query('isOverdue') isOverdue?: boolean,
    @Query('tags') tags?: string,
    @Query('search') search?: string,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Request() req
  ) {
    const filters: ActionItemFilters = {
      status,
      priority,
      category,
      assignedToId: assignedToId ? +assignedToId : undefined,
      createdById: createdById ? +createdById : undefined,
      assessmentId: assessmentId ? +assessmentId : undefined,
      dueDateFrom: dueDateFrom ? new Date(dueDateFrom) : undefined,
      dueDateTo: dueDateTo ? new Date(dueDateTo) : undefined,
      isOverdue,
      tags: tags ? tags.split(',') : undefined,
      search,
    };

    return this.actionItemsService.findAll(
      filters,
      +page,
      +limit,
      req.user.role,
      req.user.userId
    );
  }

  @Get('my-items')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getMyActionItems(
    @Query('status') status?: ActionItemStatus,
    @Request() req
  ) {
    return this.actionItemsService.getMyActionItems(req.user.userId, status);
  }

  @Get('statistics')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getStatistics(@Request() req) {
    return this.actionItemsService.getStatistics(req.user.userId, req.user.role);
  }

  @Get('assessment/:assessmentId')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getActionItemsByAssessment(@Param('assessmentId') assessmentId: string) {
    return this.actionItemsService.getActionItemsByAssessment(+assessmentId);
  }

  @Get(':id')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  findOne(@Param('id') id: string) {
    return this.actionItemsService.findOne(+id);
  }

  @Patch(':id')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  update(
    @Param('id') id: string,
    @Body() updateActionItemDto: UpdateActionItemDto,
    @Request() req
  ) {
    return this.actionItemsService.update(
      +id,
      updateActionItemDto,
      req.user.userId,
      req.user.role
    );
  }

  @Patch(':id/progress')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  updateProgress(
    @Param('id') id: string,
    @Body() updateProgressDto: UpdateActionItemProgressDto,
    @Request() req
  ) {
    return this.actionItemsService.updateProgress(
      +id,
      updateProgressDto,
      req.user.userId,
      req.user.role
    );
  }

  @Patch('bulk/update')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  bulkUpdate(
    @Body() bulkUpdateDto: BulkUpdateActionItemsDto,
    @Request() req
  ) {
    return this.actionItemsService.bulkUpdate(
      bulkUpdateDto,
      req.user.userId,
      req.user.role
    );
  }

  @Post('mark-overdue')
  @Roles(UserRole.HR_ADMIN)
  markOverdueItems() {
    return this.actionItemsService.markOverdueItems();
  }

  @Delete(':id')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  remove(@Param('id') id: string, @Request() req) {
    return this.actionItemsService.remove(+id, req.user.userId, req.user.role);
  }
}
