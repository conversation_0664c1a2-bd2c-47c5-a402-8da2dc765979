import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { AssessmentTemplate } from './assessment-template.entity';
import { ScoringRule } from './scoring-rule.entity';

@Entity('assessment_areas')
export class AssessmentArea {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'template_id' })
  templateId: number;

  @ManyToOne(() => AssessmentTemplate, template => template.areas)
  @JoinColumn({ name: 'template_id' })
  template: AssessmentTemplate;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  weight: number;

  @Column({ name: 'max_score' })
  maxScore: number;

  @Column({ name: 'order_index' })
  orderIndex: number;

  @OneToMany(() => ScoringRule, rule => rule.area)
  scoringRules: ScoringRule[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
