import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import EnhancedAssessmentForm from '../EnhancedAssessmentForm';
import { AssessmentStatus, UserRole } from '../../../types';
import api from '../../../services/api';

// Mock the API
jest.mock('../../../services/api');
const mockedApi = api as jest.Mocked<typeof api>;

const mockAssessment = {
  id: 1,
  status: AssessmentStatus.IN_PROGRESS,
  assessmentDate: '2024-12-15',
  notes: 'Test assessment notes',
  evaluatorId: 2,
  employee: {
    id: 3,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>'
  },
  evaluator: {
    id: 2,
    firstName: 'Jane',
    lastName: 'Manager',
    email: '<EMAIL>'
  },
  template: {
    id: 1,
    name: 'Performance Review Template',
    areas: [
      {
        id: 1,
        name: 'Communication',
        description: 'Communication skills assessment',
        weight: 0.4,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: [
          {
            id: 1,
            ruleType: 'addition',
            scoreAdjustment: 5,
            description: 'Training completion bonus'
          }
        ]
      },
      {
        id: 2,
        name: 'Technical Skills',
        description: 'Technical proficiency assessment',
        weight: 0.6,
        maxScore: 100,
        orderIndex: 2,
        scoringRules: []
      }
    ]
  },
  responses: [
    {
      id: 1,
      areaId: 1,
      score: 85,
      evaluatorComments: 'Good communication skills',
      employeeComments: 'I feel confident in this area'
    },
    {
      id: 2,
      areaId: 2,
      score: 90,
      evaluatorComments: 'Excellent technical skills',
      employeeComments: null
    }
  ]
};

const mockAvailableActions = [
  { action: 'complete', label: 'Complete Assessment', requiresComment: true },
  { action: 'save_draft', label: 'Save as Draft' }
];

const mockWorkflowHistory = [
  {
    from: 'draft',
    to: 'in_progress',
    userId: 2,
    timestamp: '2024-12-15T10:00:00Z',
    comments: 'Assessment started'
  }
];

describe('EnhancedAssessmentForm', () => {
  const defaultProps = {
    assessment: mockAssessment,
    userRole: UserRole.MANAGER,
    currentUserId: 2,
    onSave: jest.fn(),
    onStatusChange: jest.fn(),
    readOnly: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockedApi.get.mockImplementation((url) => {
      if (url.includes('/available-actions')) {
        return Promise.resolve({ data: mockAvailableActions });
      }
      if (url.includes('/workflow-history')) {
        return Promise.resolve({ data: mockWorkflowHistory });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });
  });

  it('renders assessment form with basic information', async () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    expect(screen.getByText('Performance Review Template Assessment')).toBeInTheDocument();
    expect(screen.getByText('Employee: John Doe')).toBeInTheDocument();
    expect(screen.getByText('Evaluator: Jane Manager')).toBeInTheDocument();
    expect(screen.getByText('IN PROGRESS')).toBeInTheDocument();
  });

  it('displays score summary correctly', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    expect(screen.getByText('Total Score')).toBeInTheDocument();
    expect(screen.getByText('Assessment Date')).toBeInTheDocument();
    expect(screen.getByText('Areas Completed')).toBeInTheDocument();
    expect(screen.getByText('Progress')).toBeInTheDocument();
  });

  it('calculates and displays live scores', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    // Check that scores are displayed
    expect(screen.getByText('90.0')).toBeInTheDocument(); // Final score for communication (85 + 5 bonus)
    expect(screen.getByText('90.0')).toBeInTheDocument(); // Technical skills score
  });

  it('shows assessment areas with scores and comments', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    expect(screen.getByText('Communication')).toBeInTheDocument();
    expect(screen.getByText('Technical Skills')).toBeInTheDocument();
    expect(screen.getByText('Good communication skills')).toBeInTheDocument();
    expect(screen.getByText('Excellent technical skills')).toBeInTheDocument();
  });

  it('displays scoring rules and adjustments', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    expect(screen.getByText('1 scoring rule(s)')).toBeInTheDocument();
    expect(screen.getByText('1 adjustment(s)')).toBeInTheDocument();
  });

  it('enables editing mode when edit button is clicked', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Should show input fields instead of read-only text
    expect(screen.getByDisplayValue('85')).toBeInTheDocument();
    expect(screen.getByDisplayValue('90')).toBeInTheDocument();
  });

  it('updates scores in editing mode', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    const scoreInput = screen.getByDisplayValue('85');
    fireEvent.change(scoreInput, { target: { value: '88' } });
    
    expect(scoreInput).toHaveValue(88);
  });

  it('updates comments in editing mode', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    const commentTextarea = screen.getByDisplayValue('Good communication skills');
    fireEvent.change(commentTextarea, { target: { value: 'Updated comments' } });
    
    expect(commentTextarea).toHaveValue('Updated comments');
  });

  it('saves changes when save button is clicked', async () => {
    mockedApi.patch.mockResolvedValue({ data: { ...mockAssessment, notes: 'Updated notes' } });
    
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockedApi.patch).toHaveBeenCalledWith(`/assessments/${mockAssessment.id}`, {
        responses: expect.any(Array),
        notes: mockAssessment.notes
      });
    });
  });

  it('cancels editing when cancel button is clicked', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Make a change
    const scoreInput = screen.getByDisplayValue('85');
    fireEvent.change(scoreInput, { target: { value: '88' } });
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    // Should revert changes and exit edit mode
    expect(screen.queryByDisplayValue('88')).not.toBeInTheDocument();
    expect(screen.getByText('Edit')).toBeInTheDocument();
  });

  it('shows workflow actions button when actions are available', async () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Actions')).toBeInTheDocument();
    });
  });

  it('opens workflow modal when actions button is clicked', async () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    await waitFor(() => {
      const actionsButton = screen.getByText('Actions');
      fireEvent.click(actionsButton);
    });
    
    expect(screen.getByText('Assessment Actions')).toBeInTheDocument();
    expect(screen.getByText('Complete Assessment')).toBeInTheDocument();
    expect(screen.getByText('Save as Draft')).toBeInTheDocument();
  });

  it('handles workflow action selection', async () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    await waitFor(() => {
      const actionsButton = screen.getByText('Actions');
      fireEvent.click(actionsButton);
    });
    
    const completeRadio = screen.getByDisplayValue('complete');
    fireEvent.click(completeRadio);
    
    expect(completeRadio).toBeChecked();
  });

  it('requires comments for certain actions', async () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    await waitFor(() => {
      const actionsButton = screen.getByText('Actions');
      fireEvent.click(actionsButton);
    });
    
    const completeRadio = screen.getByDisplayValue('complete');
    fireEvent.click(completeRadio);
    
    expect(screen.getByText('Comments (Optional)')).toBeInTheDocument();
  });

  it('executes workflow action when confirmed', async () => {
    mockedApi.post.mockResolvedValue({ 
      data: { ...mockAssessment, status: AssessmentStatus.COMPLETED } 
    });
    
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    await waitFor(() => {
      const actionsButton = screen.getByText('Actions');
      fireEvent.click(actionsButton);
    });
    
    const completeRadio = screen.getByDisplayValue('complete');
    fireEvent.click(completeRadio);
    
    const confirmButton = screen.getByText('Confirm');
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith(
        `/assessments/${mockAssessment.id}/complete`,
        { comments: '' }
      );
    });
  });

  it('displays workflow history', async () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Workflow History')).toBeInTheDocument();
      expect(screen.getByText('draft → in_progress')).toBeInTheDocument();
      expect(screen.getByText('Assessment started')).toBeInTheDocument();
    });
  });

  it('shows employee comments when available', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    expect(screen.getByText('Employee Comments')).toBeInTheDocument();
    expect(screen.getByText('I feel confident in this area')).toBeInTheDocument();
  });

  it('displays score adjustments details', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    expect(screen.getByText('Score Adjustments')).toBeInTheDocument();
    expect(screen.getByText('Training completion bonus')).toBeInTheDocument();
    expect(screen.getByText('+5 points')).toBeInTheDocument();
  });

  it('handles read-only mode', () => {
    render(<EnhancedAssessmentForm {...defaultProps} readOnly={true} />);
    
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });

  it('respects user permissions for editing', () => {
    const assessment = {
      ...mockAssessment,
      status: AssessmentStatus.APPROVED
    };
    
    render(<EnhancedAssessmentForm {...defaultProps} assessment={assessment} />);
    
    // Should not show edit button for approved assessments
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });

  it('handles different user roles appropriately', () => {
    render(<EnhancedAssessmentForm {...defaultProps} userRole={UserRole.EMPLOYEE} />);
    
    // Employee should not be able to edit
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });

  it('displays error messages', async () => {
    mockedApi.patch.mockRejectedValue({
      response: { data: { message: 'Save failed' } }
    });
    
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Save failed')).toBeInTheDocument();
    });
  });

  it('displays success messages', async () => {
    mockedApi.patch.mockResolvedValue({ data: mockAssessment });
    
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Assessment saved successfully')).toBeInTheDocument();
    });
  });

  it('calculates progress percentage correctly', () => {
    render(<EnhancedAssessmentForm {...defaultProps} />);
    
    // Both areas have scores, so progress should be 100%
    expect(screen.getByText('100%')).toBeInTheDocument();
  });

  it('handles missing scores gracefully', () => {
    const assessmentWithMissingScores = {
      ...mockAssessment,
      responses: [
        { ...mockAssessment.responses[0] },
        { ...mockAssessment.responses[1], score: null }
      ]
    };
    
    render(<EnhancedAssessmentForm {...defaultProps} assessment={assessmentWithMissingScores} />);
    
    expect(screen.getByText('Not scored')).toBeInTheDocument();
  });
});
