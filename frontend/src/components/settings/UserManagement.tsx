import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  People as PeopleIcon,
  Security as SecurityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';
import { ApiService } from '../../services/api';

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  title?: string;
  phone?: string;
  organizationalUnitId?: number;
  managerId?: number;
  accountStatus: string;
  lastLoginAt?: string;
  createdAt: string;
}

interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-mgmt-tabpanel-${index}`}
      aria-labelledby={`user-mgmt-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const UserManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal states
  const [editDialog, setEditDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isNewUser, setIsNewUser] = useState(false);

  // Role modal states
  const [roleDialog, setRoleDialog] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [isNewRole, setIsNewRole] = useState(false);

  useEffect(() => {
    if (selectedTab === 0) {
      fetchUsers();
    } else if (selectedTab === 1) {
      fetchRoles();
    }
  }, [selectedTab]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check authentication before making API call
      if (!ApiService.isAuthenticated()) {
        setError('Please log in to access user management');
        setUsers([]);
        return;
      }

      // Fetch real users from database
      const response = await ApiService.getUsers();
      const usersData = Array.isArray(response) ? response : [];

      // Transform API response to match User interface
      const transformedUsers: User[] = usersData.map((user: any) => ({
        id: user.id,
        firstName: user.firstName || user.first_name || '',
        lastName: user.lastName || user.last_name || '',
        email: user.email || '',
        role: user.role || 'engineer',
        title: user.title || user.jobTitle || '',
        phone: user.phone || '',
        organizationalUnitId: user.organizationalUnitId || user.organizational_unit_id,
        managerId: user.managerId || user.manager_id,
        accountStatus: user.accountStatus || user.account_status || 'active',
        lastLoginAt: user.lastLoginAt || user.last_login_at,
        createdAt: user.createdAt || user.created_at || new Date().toISOString()
      }));

      setUsers(transformedUsers);
    } catch (err: any) {
      if (err?.response?.status === 401) {
        setError('Authentication expired. Please log in again to access user management.');
      } else {
        setError(`Error fetching users from database: ${err?.message || 'Unknown error'}`);
      }
      console.error('Error fetching users:', err);

      // Fallback to empty array on error
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch real roles from database
      const response = await ApiService.getRoles();
      const rolesData = Array.isArray(response) ? response : [];

      // Transform API response to match Role interface
      const transformedRoles: Role[] = rolesData.map((role: any) => ({
        id: role.id,
        name: role.name || '',
        description: role.description || '',
        permissions: role.permissions || []
      }));

      setRoles(transformedRoles);
    } catch (err) {
      setError('Error fetching roles from database');
      console.error('Error fetching roles:', err);

      // Fallback to basic roles on error
      setRoles([
        {
          id: 1,
          name: 'hr_admin',
          description: 'HR Administrator with full system access',
          permissions: ['user_management', 'database_access', 'system_settings', 'reports']
        },
        {
          id: 2,
          name: 'manager',
          description: 'Team Manager with assessment and team management capabilities',
          permissions: ['team_management', 'assessments', 'reports']
        },
        {
          id: 3,
          name: 'engineer',
          description: 'Regular employee with basic access',
          permissions: ['self_assessment', 'view_reports']
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser({ ...user });
    setIsNewUser(false);
    setEditDialog(true);
  };

  const handleNewUser = () => {
    setEditingUser({
      id: 0,
      firstName: '',
      lastName: '',
      email: '',
      role: 'engineer',
      title: '',
      phone: '',
      accountStatus: 'active',
      createdAt: new Date().toISOString()
    });
    setIsNewUser(true);
    setEditDialog(true);
  };

  const handleSaveUser = async () => {
    if (!editingUser) return;

    try {
      setIsLoading(true);

      if (isNewUser) {
        // Create new user via API
        const userData = {
          firstName: editingUser.firstName,
          lastName: editingUser.lastName,
          email: editingUser.email,
          role: editingUser.role,
          title: editingUser.title,
          phone: editingUser.phone,
          organizationalUnitId: editingUser.organizationalUnitId,
          managerId: editingUser.managerId,
          accountStatus: editingUser.accountStatus,
          password: `${editingUser.lastName}X123` // Default password pattern
        };

        await ApiService.createUser(userData);
        setSuccess('User created successfully');

        // Refresh users list
        await fetchUsers();
      } else {
        // Update existing user via API
        const userData = {
          firstName: editingUser.firstName,
          lastName: editingUser.lastName,
          email: editingUser.email,
          role: editingUser.role,
          title: editingUser.title,
          phone: editingUser.phone,
          organizationalUnitId: editingUser.organizationalUnitId,
          managerId: editingUser.managerId,
          accountStatus: editingUser.accountStatus
        };

        await ApiService.updateUser(editingUser.id, userData);
        setSuccess('User updated successfully');

        // Refresh users list
        await fetchUsers();
      }

      setEditDialog(false);
      setEditingUser(null);
    } catch (err) {
      setError(`Error ${isNewUser ? 'creating' : 'updating'} user: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error('Error saving user:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) return;

    try {
      setIsLoading(true);

      // Delete user via API
      await ApiService.deleteUser(userId);
      setSuccess('User deleted successfully');

      // Refresh users list
      await fetchUsers();
    } catch (err) {
      setError(`Error deleting user: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error('Error deleting user:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const renderUsersTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
          <PeopleIcon sx={{ mr: 1 }} />
          User Management
        </Typography>
        <Box>
          <Tooltip title="Refresh Users">
            <IconButton onClick={fetchUsers}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleNewUser}
            sx={{ ml: 1 }}
          >
            Add User
          </Button>
        </Box>
      </Box>

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>Name</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Role</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Title</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Last Login</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell>{`${user.firstName} ${user.lastName}`}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={user.role}
                      size="small"
                      color={user.role === 'hr_admin' ? 'primary' : user.role === 'manager' ? 'secondary' : 'default'}
                    />
                  </TableCell>
                  <TableCell>{user.title || '-'}</TableCell>
                  <TableCell>
                    <Chip
                      label={user.accountStatus}
                      size="small"
                      color={user.accountStatus === 'active' ? 'success' : 'error'}
                    />
                  </TableCell>
                  <TableCell>
                    {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Edit User">
                      <IconButton size="small" onClick={() => handleEditUser(user)}>
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete User">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteUser(user.id)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
        User Management
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage users, roles, and permissions for the EHRX system
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Users" />
        <Tab label="Roles & Permissions" />
        <Tab label="User Activity" />
      </Tabs>

      <TabPanel value={selectedTab} index={0}>
        {renderUsersTab()}
      </TabPanel>

      <TabPanel value={selectedTab} index={1}>
        <Typography variant="h6">Roles & Permissions</Typography>
        <Typography variant="body2" color="text.secondary">
          Role management functionality will be implemented here
        </Typography>
      </TabPanel>

      <TabPanel value={selectedTab} index={2}>
        <Typography variant="h6">User Activity</Typography>
        <Typography variant="body2" color="text.secondary">
          User activity monitoring will be implemented here
        </Typography>
      </TabPanel>

      {/* User Edit Dialog */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {isNewUser ? 'Create New User' : 'Edit User'}
        </DialogTitle>
        <DialogContent>
          {editingUser && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  value={editingUser.firstName}
                  onChange={(e) => setEditingUser({
                    ...editingUser,
                    firstName: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  value={editingUser.lastName}
                  onChange={(e) => setEditingUser({
                    ...editingUser,
                    lastName: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={editingUser.email}
                  onChange={(e) => setEditingUser({
                    ...editingUser,
                    email: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Role</InputLabel>
                  <Select
                    value={editingUser.role}
                    label="Role"
                    onChange={(e) => setEditingUser({
                      ...editingUser,
                      role: e.target.value
                    })}
                  >
                    <MenuItem value="hr_admin">HR Admin</MenuItem>
                    <MenuItem value="manager">Manager</MenuItem>
                    <MenuItem value="engineer">Engineer</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Job Title"
                  value={editingUser.title || ''}
                  onChange={(e) => setEditingUser({
                    ...editingUser,
                    title: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Phone"
                  value={editingUser.phone || ''}
                  onChange={(e) => setEditingUser({
                    ...editingUser,
                    phone: e.target.value
                  })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Account Status</InputLabel>
                  <Select
                    value={editingUser.accountStatus}
                    label="Account Status"
                    onChange={(e) => setEditingUser({
                      ...editingUser,
                      accountStatus: e.target.value
                    })}
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="suspended">Suspended</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveUser} variant="contained">
            {isNewUser ? 'Create' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement;
