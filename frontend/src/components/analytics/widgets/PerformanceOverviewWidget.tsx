import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  Grid,
  Avatar,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface PerformanceData {
  individual: {
    userScore: number;
    orgAverage: number;
    trend: 'up' | 'down' | 'stable';
    lastAssessment: string;
  };
  team: {
    averageScore: number;
    teamSize: number;
    completionRate: number;
  };
  organization: {
    overallScore: number;
    totalEmployees: number;
    assessmentCoverage: number;
  };
}

const PerformanceOverviewWidget: React.FC = () => {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPerformanceData();
  }, []);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      const metricsData = await ApiService.getMetricsSummary();
      
      // Process the metrics data into our widget format
      const processedData: PerformanceData = {
        individual: {
          userScore: metricsData.individual?.find((m: any) => m.metricName === 'overall_performance_score')?.metricValue || 0,
          orgAverage: metricsData.organization?.find((m: any) => m.metricName === 'average_performance_score')?.metricValue || 0,
          trend: 'stable', // Would calculate from historical data
          lastAssessment: metricsData.individual?.[0]?.calculationDate || new Date().toISOString(),
        },
        team: {
          averageScore: metricsData.team?.find((m: any) => m.metricName === 'average_performance_score')?.metricValue || 0,
          teamSize: metricsData.team?.find((m: any) => m.metadata?.team_size)?.metadata?.team_size || 0,
          completionRate: metricsData.team?.find((m: any) => m.metadata?.completion_rate)?.metadata?.completion_rate || 0,
        },
        organization: {
          overallScore: metricsData.organization?.find((m: any) => m.metricName === 'average_performance_score')?.metricValue || 0,
          totalEmployees: 50, // Would get from actual data
          assessmentCoverage: 95, // Would calculate from actual data
        },
      };
      
      setData(processedData);
    } catch (err) {
      setError('Failed to load performance data');
      console.error('Error loading performance data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon color="success" />;
      case 'down':
        return <TrendingDownIcon color="error" />;
      default:
        return <TrendingFlatIcon color="action" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8.5) return 'success';
    if (score >= 7.0) return 'warning';
    return 'error';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Typography variant="body2" color="textSecondary">
            No performance data available
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: 400 }}>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <AssessmentIcon />
          </Avatar>
          <Typography variant="h6">Performance Overview</Typography>
        </Box>

        <Grid container spacing={2}>
          {/* Individual Performance */}
          <Grid item xs={12}>
            <Box mb={2}>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Your Performance
              </Typography>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box display="flex" alignItems="center">
                  <Typography variant="h4" color={`${getScoreColor(data.individual.userScore)}.main`}>
                    {data.individual.userScore.toFixed(1)}
                  </Typography>
                  <Box ml={1}>
                    {getTrendIcon(data.individual.trend)}
                  </Box>
                </Box>
                <Chip
                  label={`Org Avg: ${data.individual.orgAverage.toFixed(1)}`}
                  size="small"
                  variant="outlined"
                />
              </Box>
              <Typography variant="caption" color="textSecondary">
                Last assessment: {formatDate(data.individual.lastAssessment)}
              </Typography>
            </Box>
          </Grid>

          {/* Team Performance */}
          <Grid item xs={12}>
            <Box mb={2}>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Team Performance
              </Typography>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="h5" color={`${getScoreColor(data.team.averageScore)}.main`}>
                  {data.team.averageScore.toFixed(1)}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {data.team.teamSize} members
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color="textSecondary">
                  Assessment Completion: {data.team.completionRate}%
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={data.team.completionRate}
                  sx={{ mt: 0.5, height: 6, borderRadius: 3 }}
                />
              </Box>
            </Box>
          </Grid>

          {/* Organization Performance */}
          <Grid item xs={12}>
            <Box>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Organization
              </Typography>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="h5" color={`${getScoreColor(data.organization.overallScore)}.main`}>
                  {data.organization.overallScore.toFixed(1)}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {data.organization.totalEmployees} employees
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color="textSecondary">
                  Coverage: {data.organization.assessmentCoverage}%
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={data.organization.assessmentCoverage}
                  sx={{ mt: 0.5, height: 6, borderRadius: 3 }}
                />
              </Box>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default PerformanceOverviewWidget;
