import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';

import { ReportingService } from '../../../src/assessments/reporting/reporting.service';
import { Assessment } from '../../../src/assessments/entities/assessment.entity';
import { AssessmentResponse } from '../../../src/assessments/entities/assessment-response.entity';
import { User } from '../../../src/users/entities/user.entity';
import { Team } from '../../../src/teams/entities/team.entity';
import { AssessmentStatus } from '../../../src/assessments/enums/assessment-status.enum';

// Mock data
const mockAssessments: Partial<Assessment>[] = [
  {
    id: 1,
    employeeId: 1,
    managerId: 2,
    templateId: 1,
    status: AssessmentStatus.APPROVED,
    score: 0.85,
    createdAt: new Date('2025-01-15'),
    updatedAt: new Date('2025-01-20'),
    submittedAt: new Date('2025-01-18'),
    dueDate: new Date('2025-01-25'),
    notes: 'Good performance'
  },
  {
    id: 2,
    employeeId: 1,
    managerId: 2,
    templateId: 1,
    status: AssessmentStatus.APPROVED,
    score: 0.78,
    createdAt: new Date('2025-02-15'),
    updatedAt: new Date('2025-02-20'),
    submittedAt: new Date('2025-02-18'),
    dueDate: new Date('2025-02-25'),
    notes: 'Satisfactory performance'
  }
];

const mockUsers: Partial<User>[] = [
  {
    id: 1,
    name: 'John Employee',
    email: '<EMAIL>',
    teamId: 1
  },
  {
    id: 2,
    name: 'Sarah Manager',
    email: '<EMAIL>',
    teamId: 1
  }
];

const mockTeams: Partial<Team>[] = [
  {
    id: 1,
    name: 'Engineering',
    managerId: 2
  }
];

const mockResponses: Partial<AssessmentResponse>[] = [
  {
    id: 1,
    assessmentId: 1,
    areaId: 1,
    areaName: 'Technical Skills',
    score: 0.85,
    notes: 'Strong technical skills'
  },
  {
    id: 2,
    assessmentId: 1,
    areaId: 2,
    areaName: 'Communication',
    score: 0.90,
    notes: 'Excellent communication'
  }
];

describe('ReportingService', () => {
  let service: ReportingService;
  let assessmentRepository: Repository<Assessment>;
  let responseRepository: Repository<AssessmentResponse>;
  let userRepository: Repository<User>;
  let teamRepository: Repository<Team>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportingService,
        {
          provide: getRepositoryToken(Assessment),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AssessmentResponse),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Team),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<ReportingService>(ReportingService);
    assessmentRepository = module.get<Repository<Assessment>>(getRepositoryToken(Assessment));
    responseRepository = module.get<Repository<AssessmentResponse>>(getRepositoryToken(AssessmentResponse));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    teamRepository = module.get<Repository<Team>>(getRepositoryToken(Team));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getEmployeeReport', () => {
    it('should return employee report with assessment history', async () => {
      // Mock repository methods
      jest.spyOn(assessmentRepository, 'find').mockResolvedValue(mockAssessments as Assessment[]);
      jest.spyOn(userRepository, 'findOneBy').mockResolvedValue(mockUsers[0] as User);
      
      const result = await service.getEmployeeReport(1);
      
      expect(result).toBeDefined();
      expect(result.employeeId).toBe(1);
      expect(result.assessmentCount).toBe(2);
      expect(result.scoreHistory.length).toBe(2);
      expect(result.latestAssessment).toBeDefined();
      expect(result.latestAssessment.score).toBe(0.78); // Most recent assessment
    });

    it('should filter assessments by date range', async () => {
      // Mock repository method with filter
      jest.spyOn(assessmentRepository, 'find').mockImplementation((options) => {
        const startDate = options.where['createdAt'].gte;
        const endDate = options.where['createdAt'].lte;
        
        const filtered = mockAssessments.filter(assessment => {
          const date = assessment.createdAt;
          return date >= startDate && date <= endDate;
        });
        
        return Promise.resolve(filtered as Assessment[]);
      });
      
      jest.spyOn(userRepository, 'findOneBy').mockResolvedValue(mockUsers[0] as User);
      
      // Only get January assessment
      const result = await service.getEmployeeReport(1, '2025-01-01', '2025-01-31');
      
      expect(result.assessmentCount).toBe(1);
      expect(result.scoreHistory.length).toBe(1);
      expect(result.scoreHistory[0].score).toBe(0.85);
    });
  });

  describe('getTeamReport', () => {
    it('should return team report with employee scores', async () => {
      // Mock repository methods
      jest.spyOn(teamRepository, 'findOneBy').mockResolvedValue(mockTeams[0] as Team);
      jest.spyOn(userRepository, 'find').mockResolvedValue([mockUsers[0]] as User[]);
      jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { employeeId: 1, average_score: 0.815, assessment_count: 2 }
        ])
      } as any);
      
      const result = await service.getTeamReport(1);
      
      expect(result).toBeDefined();
      expect(result.teamId).toBe(1);
      expect(result.employeeScores.length).toBe(1);
      expect(result.employeeScores[0].employeeId).toBe(1);
      expect(result.employeeScores[0].assessmentCount).toBe(2);
    });
  });

  describe('getOrganizationReport', () => {
    it('should return organization-wide metrics', async () => {
      // Mock repository methods
      jest.spyOn(assessmentRepository, 'count').mockResolvedValue(2);
      jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ average_score: 0.815 })
      } as any);
      
      const result = await service.getOrganizationReport();
      
      expect(result).toBeDefined();
      expect(result.assessmentCount).toBe(2);
      expect(result.averageScore).toBeCloseTo(0.815);
    });
  });

  describe('generateAssessmentPdf', () => {
    it('should generate a PDF file for an assessment', async () => {
      // Mock repository methods
      jest.spyOn(assessmentRepository, 'findOne').mockResolvedValue(mockAssessments[0] as Assessment);
      jest.spyOn(responseRepository, 'find').mockResolvedValue(mockResponses as AssessmentResponse[]);
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        if (options.id === 1) return Promise.resolve(mockUsers[0] as User);
        if (options.id === 2) return Promise.resolve(mockUsers[1] as User);
        return Promise.resolve(null);
      });
      
      // Mock the fs.writeFileSync to prevent actual file writing during tests
      jest.spyOn(fs, 'writeFileSync').mockImplementation(() => {});
      
      const result = await service.generateAssessmentPdf(1);
      
      expect(result).toBeDefined();
      expect(path.basename(result)).toMatch(/assessment_1_\d+\.pdf/);
    });
  });

  describe('exportAssessmentJson', () => {
    it('should export assessment data as JSON', async () => {
      // Mock repository methods
      jest.spyOn(assessmentRepository, 'findOne').mockResolvedValue(mockAssessments[0] as Assessment);
      jest.spyOn(responseRepository, 'find').mockResolvedValue(mockResponses as AssessmentResponse[]);
      jest.spyOn(userRepository, 'findOneBy').mockImplementation((options) => {
        if (options.id === 1) return Promise.resolve(mockUsers[0] as User);
        if (options.id === 2) return Promise.resolve(mockUsers[1] as User);
        return Promise.resolve(null);
      });
      
      const result = await service.exportAssessmentJson(1);
      
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
      expect(result.employeeName).toBe('John Employee');
      expect(result.managerName).toBe('Sarah Manager');
      expect(result.responses.length).toBe(2);
    });
  });

  describe('Helper methods', () => {
    it('getPerformanceDistribution should categorize scores correctly', () => {
      const scores = [0.95, 0.85, 0.75, 0.65, 0.55, 0.45, 0.35];
      const result = service['getPerformanceDistribution'](scores);
      
      expect(result.excellent).toBe(1); // 0.95
      expect(result.good).toBe(1);      // 0.85
      expect(result.satisfactory).toBe(1); // 0.75
      expect(result.needsImprovement).toBe(2); // 0.65, 0.55
      expect(result.poor).toBe(2);      // 0.45, 0.35
    });

    it('getStatusBreakdown should count statuses correctly', () => {
      const statuses = [
        AssessmentStatus.DRAFT, 
        AssessmentStatus.IN_PROGRESS,
        AssessmentStatus.COMPLETED, 
        AssessmentStatus.APPROVED,
        AssessmentStatus.APPROVED, 
        AssessmentStatus.REJECTED
      ];
      
      const result = service['getStatusBreakdown'](statuses);
      
      expect(result.draft).toBe(1);
      expect(result.inProgress).toBe(1);
      expect(result.completed).toBe(1);
      expect(result.approved).toBe(2);
      expect(result.rejected).toBe(1);
    });
  });
});
