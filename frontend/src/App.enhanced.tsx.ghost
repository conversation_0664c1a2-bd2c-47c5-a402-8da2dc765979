import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import {
  Box,
  CssBaseline,
  ThemeProvider,
  createTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Assessment as AssessmentIcon,
  People as PeopleIcon,
  BarChart as ReportsIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';

// Import Modular Components
import TestDashboard from './components/dashboard/TestDashboard';
import MonthlyAnalyticsDashboard from './components/analytics/MonthlyAnalyticsDashboard';
import AssessmentsPage from './components/assessments/AssessmentsPage';
import OrganizationManagement from './components/organization/OrganizationManagement';
import ReportsPage from './components/reports/ReportsPage';
import SettingsPage from './components/settings/SettingsPage';

// Import Layout Components
import AppHeader from './components/layout/AppHeader';
import AppSidebar from './components/layout/AppSidebar';
import MainContent from './components/layout/MainContent';

// Import Context Providers
import { OrganizationProvider } from './contexts/OrganizationContext';

// Theme Configuration
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});

const App: React.FC = () => {
  const [selectedPage, setSelectedPage] = useState('dashboard');
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const menuItems = [
    { id: 'dashboard', text: 'Dashboard', icon: <DashboardIcon /> },
    { id: 'analytics', text: 'Analytics', icon: <AnalyticsIcon /> },
    { id: 'assessments', text: 'Assessments', icon: <AssessmentIcon /> },
    { id: 'organization', text: 'Organization', icon: <PeopleIcon /> },
    { id: 'reports', text: 'Reports', icon: <ReportsIcon /> },
    { id: 'settings', text: 'Settings', icon: <SettingsIcon /> },
  ];

  const renderContent = () => {
    switch (selectedPage) {
      case 'dashboard':
        return <TestDashboard />;
      case 'analytics':
        return <MonthlyAnalyticsDashboard />;
      case 'assessments':
        return <AssessmentsPage />;
      case 'organization':
        return <OrganizationManagement />;
      case 'reports':
        return <ReportsPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <TestDashboard />;
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <OrganizationProvider>
        <Router>
          <CssBaseline />
          <Box sx={{ display: 'flex', minHeight: '100vh' }}>
            <AppHeader />
            <AppSidebar
              menuItems={menuItems}
              selectedPage={selectedPage}
              onPageSelect={setSelectedPage}
              mobileOpen={mobileOpen}
              onMobileToggle={handleDrawerToggle}
              isMobile={isMobile}
            />
            <MainContent renderContent={renderContent}>
              {/* Additional content can be added here if needed */}
            </MainContent>
          </Box>
        </Router>
      </OrganizationProvider>
    </ThemeProvider>
  );
};

export default App;
