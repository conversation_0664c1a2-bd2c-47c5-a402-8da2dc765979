import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SurveyResponse } from '../../analytics/entities/survey-response.entity';
import { EngagementSurvey } from '../../analytics/entities/engagement-survey.entity';

@Injectable()
export class NlpAnalysisService {
  constructor(
    @InjectRepository(SurveyResponse)
    private readonly surveyResponseRepository: Repository<SurveyResponse>,
    @InjectRepository(EngagementSurvey)
    private readonly engagementSurveyRepository: Repository<EngagementSurvey>,
  ) { }

  /**
   * Analyze survey comments using advanced NLP techniques
   */
  async analyzeSurveyComments(surveyId: number) {
    try {
      // Get survey responses with comments
      const responses = await this.surveyResponseRepository.find({
        where: { surveyId },
        relations: ['user'],
      });

      const comments = responses
        .map(response => {
          const responseData = response.responses as any;
          if (responseData && typeof responseData === 'object') {
            // Extract text responses from the JSON responses
            return Object.values(responseData)
              .filter(value => typeof value === 'string' && value.trim().length > 0)
              .join(' ');
          }
          return '';
        })
        .filter(comment => comment && comment.trim().length > 0);

      if (comments.length === 0) {
        return {
          success: true,
          data: {
            totalComments: 0,
            sentimentDistribution: { positive: 0, neutral: 0, negative: 0 },
            topThemes: [],
            emotionalDrivers: [],
            insights: [],
          },
        };
      }

      // Perform sentiment analysis
      const sentimentResults = await this.performSentimentAnalysis(comments);

      // Extract themes and topics
      const themes = await this.extractThemes(comments);

      // Analyze emotional drivers
      const emotionalDrivers = await this.analyzeEmotionalDrivers(comments);

      // Generate insights
      const insights = await this.generateInsights(sentimentResults, themes, emotionalDrivers);

      // Analyze comment samples for detailed view
      const sampleComments = await this.analyzeSampleComments(comments.slice(0, 20));

      return {
        success: true,
        data: {
          totalComments: comments.length,
          overallScore: sentimentResults.overallScore,
          overallSentiment: sentimentResults.overallSentiment,
          sentimentDistribution: sentimentResults.distribution,
          topThemes: themes,
          emotionalDrivers,
          insights,
          sampleComments,
        },
      };
    } catch (error) {
      console.error('Error analyzing survey comments:', error);
      return {
        success: false,
        error: 'Failed to analyze survey comments',
      };
    }
  }

  /**
   * Perform sentiment analysis on comments
   */
  private async performSentimentAnalysis(comments: string[]): Promise<any> {
    const sentimentScores = [];
    let positiveCount = 0;
    let neutralCount = 0;
    let negativeCount = 0;

    for (const comment of comments) {
      const score = this.calculateSentimentScore(comment);
      sentimentScores.push(score);

      if (score > 0.1) positiveCount++;
      else if (score < -0.1) negativeCount++;
      else neutralCount++;
    }

    const totalComments = comments.length;
    const overallScore = sentimentScores.reduce((sum, score) => sum + score, 0) / totalComments;

    // Normalize to 0-100 scale
    const normalizedScore = ((overallScore + 1) / 2) * 100;

    return {
      overallScore: normalizedScore,
      overallSentiment: overallScore > 0.1 ? 'positive' : overallScore < -0.1 ? 'negative' : 'neutral',
      distribution: {
        positive: positiveCount / totalComments,
        neutral: neutralCount / totalComments,
        negative: negativeCount / totalComments,
      },
      scores: sentimentScores,
    };
  }

  /**
   * Calculate sentiment score for a single comment
   */
  private calculateSentimentScore(comment: string): number {
    const positiveWords = [
      'excellent', 'great', 'amazing', 'fantastic', 'wonderful', 'outstanding', 'superb',
      'good', 'nice', 'happy', 'satisfied', 'pleased', 'love', 'enjoy', 'appreciate',
      'helpful', 'supportive', 'friendly', 'professional', 'efficient', 'effective',
      'innovative', 'creative', 'inspiring', 'motivating', 'encouraging', 'positive',
      'successful', 'productive', 'valuable', 'beneficial', 'rewarding', 'fulfilling'
    ];

    const negativeWords = [
      'terrible', 'awful', 'horrible', 'bad', 'poor', 'disappointing', 'frustrating',
      'annoying', 'difficult', 'challenging', 'stressful', 'overwhelming', 'confusing',
      'unclear', 'unhelpful', 'unsupportive', 'unprofessional', 'inefficient', 'ineffective',
      'boring', 'monotonous', 'repetitive', 'outdated', 'inadequate', 'insufficient',
      'unfair', 'biased', 'discriminatory', 'toxic', 'negative', 'demotivating'
    ];

    const words = comment.toLowerCase().split(/\s+/);
    let score = 0;

    words.forEach(word => {
      // Remove punctuation
      const cleanWord = word.replace(/[^\w]/g, '');

      if (positiveWords.includes(cleanWord)) {
        score += 1;
      } else if (negativeWords.includes(cleanWord)) {
        score -= 1;
      }
    });

    // Normalize by comment length
    return Math.max(-1, Math.min(1, score / Math.max(1, words.length / 10)));
  }

  /**
   * Extract themes and topics from comments
   */
  private async extractThemes(comments: string[]): Promise<any[]> {
    const themeKeywords = {
      'Work-Life Balance': ['balance', 'flexible', 'remote', 'hours', 'overtime', 'family', 'personal'],
      'Management': ['manager', 'supervisor', 'leadership', 'boss', 'management', 'lead', 'director'],
      'Career Development': ['career', 'growth', 'promotion', 'development', 'training', 'learning', 'skill'],
      'Compensation': ['salary', 'pay', 'compensation', 'benefits', 'bonus', 'raise', 'money'],
      'Team Collaboration': ['team', 'colleague', 'collaboration', 'communication', 'cooperation', 'support'],
      'Work Environment': ['office', 'environment', 'culture', 'atmosphere', 'workplace', 'facility'],
      'Job Satisfaction': ['satisfaction', 'fulfilling', 'meaningful', 'purpose', 'motivation', 'engagement'],
      'Workload': ['workload', 'busy', 'pressure', 'deadline', 'stress', 'overwhelming', 'capacity'],
      'Recognition': ['recognition', 'appreciation', 'acknowledge', 'reward', 'praise', 'credit'],
      'Technology': ['technology', 'tools', 'software', 'system', 'platform', 'equipment', 'tech']
    };

    const themeCounts = new Map<string, number>();
    const totalComments = comments.length;

    // Initialize theme counts
    Object.keys(themeKeywords).forEach(theme => {
      themeCounts.set(theme, 0);
    });

    // Count theme occurrences
    comments.forEach(comment => {
      const lowerComment = comment.toLowerCase();

      Object.entries(themeKeywords).forEach(([theme, keywords]) => {
        const hasTheme = keywords.some(keyword => lowerComment.includes(keyword));
        if (hasTheme) {
          themeCounts.set(theme, (themeCounts.get(theme) || 0) + 1);
        }
      });
    });

    // Convert to array and sort by frequency
    const themes = Array.from(themeCounts.entries())
      .map(([theme, count]) => ({
        theme,
        count,
        percentage: (count / totalComments) * 100,
      }))
      .filter(theme => theme.count > 0)
      .sort((a, b) => b.count - a.count);

    return themes;
  }

  /**
   * Analyze emotional drivers in comments
   */
  private async analyzeEmotionalDrivers(comments: string[]): Promise<any[]> {
    const emotionalCategories = {
      'satisfaction': {
        keywords: ['satisfied', 'happy', 'content', 'pleased', 'fulfilled'],
        weight: 1,
      },
      'frustration': {
        keywords: ['frustrated', 'annoyed', 'irritated', 'upset', 'angry'],
        weight: -1,
      },
      'motivation': {
        keywords: ['motivated', 'inspired', 'energized', 'driven', 'passionate'],
        weight: 1,
      },
      'stress': {
        keywords: ['stressed', 'overwhelmed', 'pressure', 'burnout', 'exhausted'],
        weight: -1,
      },
      'appreciation': {
        keywords: ['appreciated', 'valued', 'recognized', 'acknowledged', 'respected'],
        weight: 1,
      },
      'confusion': {
        keywords: ['confused', 'unclear', 'uncertain', 'lost', 'bewildered'],
        weight: -0.5,
      },
    };

    const driverResults = [];

    Object.entries(emotionalCategories).forEach(([type, config]) => {
      let count = 0;
      let totalImpact = 0;

      comments.forEach(comment => {
        const lowerComment = comment.toLowerCase();
        const hasEmotion = config.keywords.some(keyword => lowerComment.includes(keyword));

        if (hasEmotion) {
          count++;
          totalImpact += config.weight;
        }
      });

      if (count > 0) {
        driverResults.push({
          type,
          count,
          averageImpact: totalImpact / count,
        });
      }
    });

    return driverResults.sort((a, b) => b.count - a.count);
  }

  /**
   * Generate insights based on analysis results
   */
  private async generateInsights(sentimentResults: any, themes: any[], emotionalDrivers: any[]): Promise<any[]> {
    const insights = [];

    // Sentiment insights
    if (sentimentResults.distribution.negative > 0.3) {
      insights.push({
        message: 'High negative sentiment detected in survey responses',
        confidence: 0.9,
        priority: 'high',
        recommendation: 'Consider conducting focus groups to understand specific concerns',
      });
    }

    if (sentimentResults.distribution.positive > 0.7) {
      insights.push({
        message: 'Strong positive sentiment indicates high employee satisfaction',
        confidence: 0.85,
        priority: 'medium',
        recommendation: 'Identify and replicate successful practices across other teams',
      });
    }

    // Theme insights
    const topTheme = themes[0];
    if (topTheme && topTheme.percentage > 20) {
      insights.push({
        message: `${topTheme.theme} is a major concern mentioned by ${topTheme.percentage.toFixed(1)}% of respondents`,
        confidence: 0.8,
        priority: 'high',
        recommendation: `Develop action plan to address ${topTheme.theme} issues`,
      });
    }

    // Emotional driver insights
    const negativeDrivers = emotionalDrivers.filter(driver => driver.averageImpact < 0);
    if (negativeDrivers.length > 0) {
      const topNegativeDriver = negativeDrivers[0];
      insights.push({
        message: `${topNegativeDriver.type} is affecting ${topNegativeDriver.count} employees negatively`,
        confidence: 0.75,
        priority: 'medium',
        recommendation: `Implement support programs to address ${topNegativeDriver.type}`,
      });
    }

    return insights;
  }

  /**
   * Analyze sample comments for detailed view
   */
  private async analyzeSampleComments(comments: string[]): Promise<any[]> {
    return comments.map(comment => {
      const score = this.calculateSentimentScore(comment);
      const sentiment = score > 0.1 ? 'positive' : score < -0.1 ? 'negative' : 'neutral';

      // Extract themes for this comment
      const themes = this.extractCommentThemes(comment);

      return {
        text: comment,
        score,
        sentiment,
        themes,
      };
    });
  }

  /**
   * Extract themes from a single comment
   */
  private extractCommentThemes(comment: string): string[] {
    const themeKeywords = {
      'Work-Life Balance': ['balance', 'flexible', 'remote', 'hours'],
      'Management': ['manager', 'supervisor', 'leadership', 'boss'],
      'Career Development': ['career', 'growth', 'promotion', 'development'],
      'Compensation': ['salary', 'pay', 'compensation', 'benefits'],
      'Team Collaboration': ['team', 'colleague', 'collaboration'],
      'Work Environment': ['office', 'environment', 'culture'],
    };

    const detectedThemes = [];
    const lowerComment = comment.toLowerCase();

    Object.entries(themeKeywords).forEach(([theme, keywords]) => {
      const hasTheme = keywords.some(keyword => lowerComment.includes(keyword));
      if (hasTheme) {
        detectedThemes.push(theme);
      }
    });

    return detectedThemes;
  }

  /**
   * Get engagement trends over time
   */
  async getEngagementTrends(period: string = '6months') {
    try {
      const surveys = await this.engagementSurveyRepository.find({
        order: { createdAt: 'DESC' },
        take: period === '1year' ? 12 : period === '6months' ? 6 : 3,
      });

      const trendData = [];

      for (const survey of surveys) {
        const responses = await this.surveyResponseRepository.find({
          where: { surveyId: survey.id },
        });

        if (responses.length > 0) {
          const comments = responses
            .map(response => {
              const responseData = response.responses as any;
              if (responseData && typeof responseData === 'object') {
                return Object.values(responseData)
                  .filter(value => typeof value === 'string' && value.trim().length > 0)
                  .join(' ');
              }
              return '';
            })
            .filter(c => c && c.trim().length > 0);

          if (comments.length > 0) {
            const sentimentResults = await this.performSentimentAnalysis(comments);

            trendData.push({
              date: survey.createdAt,
              score: sentimentResults.overallScore,
              responseCount: responses.length,
              commentCount: comments.length,
            });
          }
        }
      }

      return {
        success: true,
        data: {
          trendData: trendData.reverse(), // Chronological order
          currentScore: trendData.length > 0 ? trendData[trendData.length - 1].score : 0,
          trend: this.calculateTrend(trendData),
        },
      };
    } catch (error) {
      console.error('Error getting engagement trends:', error);
      return {
        success: false,
        error: 'Failed to get engagement trends',
      };
    }
  }

  /**
   * Calculate trend direction
   */
  private calculateTrend(trendData: any[]): string {
    if (trendData.length < 2) return 'stable';

    const recent = trendData.slice(-3);
    const older = trendData.slice(0, 3);

    if (recent.length === 0 || older.length === 0) return 'stable';

    const recentAvg = recent.reduce((sum, point) => sum + point.score, 0) / recent.length;
    const olderAvg = older.reduce((sum, point) => sum + point.score, 0) / older.length;

    if (recentAvg > olderAvg + 5) return 'improving';
    if (recentAvg < olderAvg - 5) return 'declining';
    return 'stable';
  }
}
