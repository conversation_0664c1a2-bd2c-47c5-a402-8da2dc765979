#!/bin/bash

# EHRX Website Test Script
# Tests HTTPS functionality and React application loading

DOMAIN="dev.trusthansen.dk"
URL="https://$DOMAIN"

echo "=== EHRX Website Test for $DOMAIN ==="
echo

# Test 1: HTTPS Connection
echo "1. Testing HTTPS Connection:"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $URL)
if [ "$HTTP_STATUS" = "200" ]; then
    echo "   ✅ HTTPS connection successful (Status: $HTTP_STATUS)"
else
    echo "   ❌ HTTPS connection failed (Status: $HTTP_STATUS)"
fi

# Test 2: HTML Content
echo
echo "2. Testing HTML Content:"
HTML_CONTENT=$(curl -s $URL)
if echo "$HTML_CONTENT" | grep -q "EHRX"; then
    echo "   ✅ EHRX content found in HTML"
else
    echo "   ❌ EHRX content not found in HTML"
fi

# Test 3: React Bundle
echo
echo "3. Testing React JavaScript Bundle:"
BUNDLE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $URL/static/js/bundle.js)
if [ "$BUNDLE_STATUS" = "200" ]; then
    echo "   ✅ React bundle loading successfully (Status: $BUNDLE_STATUS)"
else
    echo "   ❌ React bundle failed to load (Status: $BUNDLE_STATUS)"
fi

# Test 4: Security Headers
echo
echo "4. Testing Security Headers:"
HEADERS=$(curl -s -I $URL)
if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
    echo "   ✅ HSTS header present"
else
    echo "   ❌ HSTS header missing"
fi

if echo "$HEADERS" | grep -q "X-Frame-Options"; then
    echo "   ✅ X-Frame-Options header present"
else
    echo "   ❌ X-Frame-Options header missing"
fi

# Test 5: HTTP to HTTPS Redirect
echo
echo "5. Testing HTTP to HTTPS Redirect:"
REDIRECT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN)
if [ "$REDIRECT_STATUS" = "301" ]; then
    echo "   ✅ HTTP redirects to HTTPS (Status: $REDIRECT_STATUS)"
else
    echo "   ❌ HTTP redirect not working (Status: $REDIRECT_STATUS)"
fi

# Test 6: React Dev Server Status
echo
echo "6. Testing React Dev Server:"
DEV_SERVER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3080)
if [ "$DEV_SERVER_STATUS" = "200" ]; then
    echo "   ✅ React dev server running (Status: $DEV_SERVER_STATUS)"
else
    echo "   ❌ React dev server not responding (Status: $DEV_SERVER_STATUS)"
fi

echo
echo "=== Test Complete ==="
echo
echo "Website URL: $URL"
echo "Direct access: http://localhost:3080"
