import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs/promises';
import * as path from 'path';

@Injectable()
export class LogCleanupService {
  private readonly logger = new Logger(LogCleanupService.name);
  private readonly logDirectory = path.join(process.cwd(), 'logs');
  private readonly maxLogAge = 30; // days
  private readonly maxLogSize = 100 * 1024 * 1024; // 100MB total
  private readonly compressionEnabled = true;

  // Run cleanup every day at 2 AM
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async performCleanup(): Promise<void> {
    this.logger.log('Starting log cleanup process...');
    
    try {
      await this.cleanupOldLogs();
      await this.compressLogs();
      await this.enforceStorageLimit();
      
      this.logger.log('Log cleanup completed successfully');
    } catch (error) {
      this.logger.error(`Log cleanup failed: ${error.message}`);
    }
  }

  // Manual cleanup trigger
  async manualCleanup(): Promise<{ 
    deletedFiles: string[]; 
    compressedFiles: string[]; 
    totalSpaceFreed: number; 
  }> {
    this.logger.log('Starting manual log cleanup...');
    
    const result = {
      deletedFiles: [],
      compressedFiles: [],
      totalSpaceFreed: 0,
    };

    try {
      const deletedFiles = await this.cleanupOldLogs();
      const compressedFiles = await this.compressLogs();
      const spaceFreed = await this.enforceStorageLimit();

      result.deletedFiles = deletedFiles;
      result.compressedFiles = compressedFiles;
      result.totalSpaceFreed = spaceFreed;

      this.logger.log(`Manual cleanup completed: ${deletedFiles.length} deleted, ${compressedFiles.length} compressed, ${spaceFreed} bytes freed`);
    } catch (error) {
      this.logger.error(`Manual cleanup failed: ${error.message}`);
      throw error;
    }

    return result;
  }

  private async cleanupOldLogs(): Promise<string[]> {
    const deletedFiles: string[] = [];
    
    try {
      const files = await fs.readdir(this.logDirectory);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.maxLogAge);

      for (const file of files) {
        if (!file.endsWith('.log') && !file.endsWith('.log.gz')) continue;

        const filePath = path.join(this.logDirectory, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedFiles.push(file);
          this.logger.log(`Deleted old log file: ${file}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup old logs: ${error.message}`);
    }

    return deletedFiles;
  }

  private async compressLogs(): Promise<string[]> {
    if (!this.compressionEnabled) return [];

    const compressedFiles: string[] = [];
    
    try {
      const files = await fs.readdir(this.logDirectory);
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      for (const file of files) {
        if (!file.endsWith('.log') || file.includes(yesterdayStr)) continue;

        // Only compress logs older than yesterday
        const filePath = path.join(this.logDirectory, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < yesterday) {
          await this.compressFile(filePath);
          compressedFiles.push(file);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to compress logs: ${error.message}`);
    }

    return compressedFiles;
  }

  private async compressFile(filePath: string): Promise<void> {
    try {
      const zlib = await import('zlib');
      const { promisify } = await import('util');
      const gzip = promisify(zlib.gzip);

      const data = await fs.readFile(filePath);
      const compressed = await gzip(data);
      
      const compressedPath = `${filePath}.gz`;
      await fs.writeFile(compressedPath, compressed);
      await fs.unlink(filePath);
      
      this.logger.log(`Compressed log file: ${path.basename(filePath)}`);
    } catch (error) {
      this.logger.error(`Failed to compress file ${filePath}: ${error.message}`);
    }
  }

  private async enforceStorageLimit(): Promise<number> {
    let totalSpaceFreed = 0;
    
    try {
      const files = await this.getLogFilesWithStats();
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);

      if (totalSize <= this.maxLogSize) {
        return 0; // No cleanup needed
      }

      // Sort by modification time (oldest first)
      files.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

      let currentSize = totalSize;
      for (const file of files) {
        if (currentSize <= this.maxLogSize) break;

        await fs.unlink(file.path);
        currentSize -= file.size;
        totalSpaceFreed += file.size;
        
        this.logger.log(`Deleted log file to enforce storage limit: ${file.name}`);
      }
    } catch (error) {
      this.logger.error(`Failed to enforce storage limit: ${error.message}`);
    }

    return totalSpaceFreed;
  }

  private async getLogFilesWithStats(): Promise<Array<{
    name: string;
    path: string;
    size: number;
    mtime: Date;
  }>> {
    const files = [];
    
    try {
      const fileNames = await fs.readdir(this.logDirectory);
      
      for (const fileName of fileNames) {
        if (!fileName.endsWith('.log') && !fileName.endsWith('.log.gz')) continue;

        const filePath = path.join(this.logDirectory, fileName);
        const stats = await fs.stat(filePath);
        
        files.push({
          name: fileName,
          path: filePath,
          size: stats.size,
          mtime: stats.mtime,
        });
      }
    } catch (error) {
      this.logger.error(`Failed to get log file stats: ${error.message}`);
    }

    return files;
  }

  async getLogDirectoryStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile: Date | null;
    newestFile: Date | null;
    compressedFiles: number;
    uncompressedFiles: number;
  }> {
    const files = await this.getLogFilesWithStats();
    
    return {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      oldestFile: files.length > 0 ? new Date(Math.min(...files.map(f => f.mtime.getTime()))) : null,
      newestFile: files.length > 0 ? new Date(Math.max(...files.map(f => f.mtime.getTime()))) : null,
      compressedFiles: files.filter(f => f.name.endsWith('.gz')).length,
      uncompressedFiles: files.filter(f => f.name.endsWith('.log')).length,
    };
  }
}
