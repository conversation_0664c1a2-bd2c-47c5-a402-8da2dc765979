import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AnalyticsService } from '../../../src/assessments/reporting/analytics.service';
import { Assessment } from '../../../src/assessments/entities/assessment.entity';
import { User } from '../../../src/users/entities/user.entity';
import { Team } from '../../../src/teams/entities/team.entity';
import { AssessmentStatus } from '../../../src/assessments/enums/assessment-status.enum';

describe('AnalyticsService', () => {
  let service: AnalyticsService;
  let assessmentRepository: Repository<Assessment>;
  let userRepository: Repository<User>;
  let teamRepository: Repository<Team>;

  // Mock data
  const mockAssessmentData = [
    {
      period: '2025-01',
      averageScore: 0.78,
      completionRate: 0.92,
      assessmentCount: 25
    },
    {
      period: '2025-02',
      averageScore: 0.82,
      completionRate: 0.95,
      assessmentCount: 20
    },
    {
      period: '2025-03',
      averageScore: 0.84,
      completionRate: 0.96,
      assessmentCount: 18
    }
  ];

  const mockTeamBenchmarks = [
    {
      teamId: 1,
      teamName: 'Engineering',
      teamSize: 12,
      averageScore: 0.82,
      completionRate: 0.94,
      assessmentCount: 36
    },
    {
      teamId: 2,
      teamName: 'Product',
      teamSize: 8,
      averageScore: 0.85,
      completionRate: 0.98,
      assessmentCount: 24
    }
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AnalyticsService,
        {
          provide: getRepositoryToken(Assessment),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Team),
          useClass: Repository,
        }
      ],
    }).compile();

    service = module.get<AnalyticsService>(AnalyticsService);
    assessmentRepository = module.get<Repository<Assessment>>(getRepositoryToken(Assessment));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    teamRepository = module.get<Repository<Team>>(getRepositoryToken(Team));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDashboardMetrics', () => {
    it('should return dashboard metrics for the specified team', async () => {
      // Mock repository methods
      jest.spyOn(assessmentRepository, 'count').mockResolvedValueOnce(45).mockResolvedValueOnce(7);
      
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValueOnce([
          { id: 1, employee_id: 101, status: AssessmentStatus.COMPLETED, score: 0.85, created_at: '2025-03-28' },
          { id: 2, employee_id: 102, status: AssessmentStatus.APPROVED, score: 0.76, created_at: '2025-03-25' }
        ]),
        getRawOne: jest.fn().mockResolvedValue({ avg_score: 0.81 })
      };
      
      jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
      
      const statusMock = jest.fn().mockResolvedValue({
        draft: 2,
        inProgress: 5,
        completed: 4,
        approved: 33,
        rejected: 1
      });
      
      const distributionMock = jest.fn().mockResolvedValue({
        excellent: 8,
        good: 20,
        satisfactory: 12,
        needsImprovement: 4,
        poor: 1
      });
      
      // Mock the internal methods
      service['getStatusBreakdown'] = statusMock;
      service['getPerformanceDistribution'] = distributionMock;
      
      const teamId = 5;
      const startDate = '2025-01-01';
      const endDate = '2025-03-31';
      
      const result = await service.getDashboardMetrics(teamId, startDate, endDate);
      
      expect(result).toBeDefined();
      expect(result.totalAssessments).toBe(45);
      expect(result.pendingAssessments).toBe(7);
      expect(result.completedAssessments).toBe(38); // 45 - 7
      expect(result.averageScore).toBe(0.81);
      expect(result.recentAssessments.length).toBe(2);
      expect(result.statusBreakdown).toEqual({
        draft: 2,
        inProgress: 5,
        completed: 4,
        approved: 33,
        rejected: 1
      });
      expect(result.performanceDistribution).toEqual({
        excellent: 8,
        good: 20,
        satisfactory: 12,
        needsImprovement: 4,
        poor: 1
      });
      
      // Verify that filters were applied correctly
      expect(queryBuilderMock.where).toHaveBeenCalledWith('u.team_id = :teamId', { teamId });
      expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('a.created_at >= :startDate', { 
        startDate: new Date(startDate) 
      });
      expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('a.created_at <= :endDate', { 
        endDate: new Date(endDate) 
      });
    });
  });

  describe('getPerformanceTrends', () => {
    it('should return performance trends for monthly periods', async () => {
      // Mock the repository method
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { period: '2025-01', avg_score: 0.78, completion_rate: 0.92, assessment_count: 25 },
          { period: '2025-02', avg_score: 0.82, completion_rate: 0.95, assessment_count: 20 },
          { period: '2025-03', avg_score: 0.84, completion_rate: 0.96, assessment_count: 18 }
        ])
      };
      
      jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
      
      const period = 'monthly';
      const count = 3;
      const teamId = 1;
      
      const result = await service.getPerformanceTrends(period, count, teamId);
      
      expect(result).toBeDefined();
      expect(result.length).toBe(3);
      expect(result[0].period).toBe('2025-01');
      expect(result[0].averageScore).toBe(0.78);
      expect(result[0].completionRate).toBe(0.92);
      expect(result[0].assessmentCount).toBe(25);
      
      // Verify that filters were applied correctly
      expect(queryBuilderMock.where).toHaveBeenCalledWith('u.team_id = :teamId', { teamId });
      expect(queryBuilderMock.select).toHaveBeenCalledWith("DATE_FORMAT(a.created_at, '%Y-%m')", 'period');
      expect(queryBuilderMock.orderBy).toHaveBeenCalledWith('period', 'DESC');
    });

    it('should return performance trends for quarterly periods', async () => {
      // Mock the repository method
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { period: '2025-Q1', avg_score: 0.79, completion_rate: 0.93, assessment_count: 60 },
          { period: '2024-Q4', avg_score: 0.77, completion_rate: 0.90, assessment_count: 58 }
        ])
      };
      
      jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
      
      const period = 'quarterly';
      const count = 2;
      
      const result = await service.getPerformanceTrends(period, count);
      
      expect(result).toBeDefined();
      expect(result.length).toBe(2);
      expect(result[0].period).toBe('2025-Q1');
      expect(result[0].averageScore).toBe(0.79);
      
      // Verify that the quarterly format was used
      expect(queryBuilderMock.select).toHaveBeenCalledWith(
        "CONCAT(YEAR(a.created_at), '-Q', QUARTER(a.created_at))",
        'period'
      );
    });
  });

  describe('getTeamBenchmarks', () => {
    it('should return performance benchmarks for specified teams', async () => {
      // Mock team repository
      jest.spyOn(teamRepository, 'findBy').mockResolvedValue([
        { id: 1, name: 'Engineering', managerId: 101 },
        { id: 2, name: 'Product', managerId: 102 }
      ] as Team[]);
      
      // Mock user repository to get team sizes
      jest.spyOn(userRepository, 'count').mockImplementation((options) => {
        if (options.where.teamId === 1) return Promise.resolve(12);
        if (options.where.teamId === 2) return Promise.resolve(8);
        return Promise.resolve(0);
      });
      
      // Mock assessment repository for metrics
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockImplementation((options) => {
          if (options?.where?.teamId === 1) {
            return Promise.resolve({ avg_score: 0.82, count: 36, completed: 34 });
          }
          if (options?.where?.teamId === 2) {
            return Promise.resolve({ avg_score: 0.85, count: 24, completed: 23 });
          }
          return Promise.resolve(null);
        })
      };
      
      jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
      
      const teamIds = [1, 2];
      const startDate = '2025-01-01';
      const endDate = '2025-03-31';
      
      const result = await service.getTeamBenchmarks(teamIds, startDate, endDate);
      
      expect(result).toBeDefined();
      expect(result.length).toBe(2);
      
      expect(result[0].teamId).toBe(1);
      expect(result[0].teamName).toBe('Engineering');
      expect(result[0].teamSize).toBe(12);
      expect(result[0].averageScore).toBe(0.82);
      expect(result[0].completionRate).toBeCloseTo(34/36);
      expect(result[0].assessmentCount).toBe(36);
      
      expect(result[1].teamId).toBe(2);
      expect(result[1].teamName).toBe('Product');
      
      // Verify that date filters were applied
      expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('a.created_at >= :startDate', { 
        startDate: new Date(startDate) 
      });
      expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('a.created_at <= :endDate', { 
        endDate: new Date(endDate) 
      });
    });

    it('should return all team benchmarks if no teamIds specified', async () => {
      // Mock team repository to return all teams
      jest.spyOn(teamRepository, 'find').mockResolvedValue([
        { id: 1, name: 'Engineering', managerId: 101 },
        { id: 2, name: 'Product', managerId: 102 },
        { id: 3, name: 'Marketing', managerId: 103 }
      ] as Team[]);
      
      // Mock other repositories
      jest.spyOn(userRepository, 'count').mockResolvedValue(10);
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ avg_score: 0.80, count: 30, completed: 28 })
      };
      
      jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
      
      const result = await service.getTeamBenchmarks();
      
      expect(result).toBeDefined();
      expect(result.length).toBe(3);
      
      // Verify that teamRepository.find was called without where clause
      expect(teamRepository.find).toHaveBeenCalled();
    });
  });

  describe('Helper methods', () => {
    describe('getStatusBreakdown', () => {
      it('should count assessments by status', async () => {
        // Mock the query builder
        const queryBuilderMock = {
          select: jest.fn().mockReturnThis(),
          addSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          innerJoin: jest.fn().mockReturnThis(),
          groupBy: jest.fn().mockReturnThis(),
          getRawMany: jest.fn().mockResolvedValue([
            { status: 'DRAFT', count: 2 },
            { status: 'IN_PROGRESS', count: 5 },
            { status: 'COMPLETED', count: 4 },
            { status: 'APPROVED', count: 33 },
            { status: 'REJECTED', count: 1 }
          ])
        };
        
        jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
        
        const teamId = 5;
        const startDate = '2025-01-01';
        const endDate = '2025-03-31';
        
        const result = await service['getStatusBreakdown'](teamId, startDate, endDate);
        
        expect(result).toEqual({
          draft: 2,
          inProgress: 5,
          completed: 4,
          approved: 33,
          rejected: 1
        });
        
        // Verify filters were applied
        expect(queryBuilderMock.where).toHaveBeenCalledWith('u.team_id = :teamId', { teamId });
        expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('a.created_at >= :startDate', { 
          startDate: new Date(startDate) 
        });
        expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('a.created_at <= :endDate', { 
          endDate: new Date(endDate) 
        });
      });
    });

    describe('getPerformanceDistribution', () => {
      it('should categorize scores into performance bands', async () => {
        // Mock the query builder
        const queryBuilderMock = {
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getRawMany: jest.fn().mockResolvedValue([
            { score: 0.95 }, // Excellent
            { score: 0.85 }, // Good
            { score: 0.82 }, // Good
            { score: 0.75 }, // Satisfactory
            { score: 0.65 }, // Satisfactory
            { score: 0.55 }, // Needs Improvement
            { score: 0.35 }  // Poor
          ])
        };
        
        jest.spyOn(assessmentRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
        
        const teamId = 5;
        const startDate = '2025-01-01';
        const endDate = '2025-03-31';
        
        const result = await service['getPerformanceDistribution'](teamId, startDate, endDate);
        
        expect(result).toEqual({
          excellent: 1,
          good: 2,
          satisfactory: 2,
          needsImprovement: 1,
          poor: 1
        });
      });
    });
  });
});
