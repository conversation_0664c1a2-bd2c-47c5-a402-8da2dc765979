import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
} from '@nestjs/common';
import {
  UsersService,
  BulkCreateUserDto,
  BulkUpdateUserDto,
  BulkRoleChangeDto,
  UserProfileUpdateDto,
  UserSearchFilters
} from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from './entities/user.entity';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  // Public endpoint for user count
  @Get('count')
  async getUserCount() {
    const count = await this.usersService.getUserCount();
    return { success: true, data: count };
  }

  // Protected endpoints
  @UseGuards(JwtAuthGuard)
  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  findAll() {
    return this.usersService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  findOne(@Param('id') id: string) {
    return this.usersService.findOne(+id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(+id, updateUserDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  remove(@Param('id') id: string) {
    return this.usersService.remove(+id);
  }

  // Enhanced User Management Endpoints

  @Get('search/advanced')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  searchUsers(
    @Query('role') role?: UserRole,
    @Query('isActive') isActive?: boolean,
    @Query('search') search?: string,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Query('createdAfter') createdAfter?: string,
    @Query('createdBefore') createdBefore?: string
  ) {
    const filters: UserSearchFilters = {
      role,
      isActive: isActive !== undefined ? isActive === true : undefined,
      search,
      createdAfter: createdAfter ? new Date(createdAfter) : undefined,
      createdBefore: createdBefore ? new Date(createdBefore) : undefined
    };

    return this.usersService.searchUsers(filters, +page, +limit);
  }

  @Post('bulk/create')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  bulkCreateUsers(@Body() bulkCreateDto: BulkCreateUserDto) {
    return this.usersService.bulkCreateUsers(bulkCreateDto);
  }

  @Patch('bulk/update')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  bulkUpdateUsers(@Body() bulkUpdateDto: BulkUpdateUserDto, @Request() req) {
    return this.usersService.bulkUpdateUsers(bulkUpdateDto, req.user.userId);
  }

  @Patch('bulk/change-roles')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  bulkChangeRoles(@Body() bulkRoleChangeDto: BulkRoleChangeDto, @Request() req) {
    // Validate permissions
    const hasPermission = bulkRoleChangeDto.userIds.every(userId =>
      this.usersService.validateRoleChangePermission(req.user.role, bulkRoleChangeDto.newRole)
    );

    if (!hasPermission) {
      throw new Error('Insufficient permissions for role change');
    }

    return this.usersService.bulkChangeRoles(bulkRoleChangeDto, req.user.userId);
  }

  @Patch('profile')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  updateProfile(@Body() profileUpdateDto: UserProfileUpdateDto, @Request() req) {
    return this.usersService.updateProfile(req.user.userId, profileUpdateDto);
  }

  @Get('statistics/overview')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  getUserStatistics() {
    return this.usersService.getUserStatistics();
  }

  @Get('role/:role')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getUsersByRole(
    @Param('role') role: UserRole,
    @Query('includeInactive') includeInactive = false
  ) {
    return this.usersService.getUsersByRole(role, includeInactive);
  }

  @Post('bulk/deactivate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  deactivateUsers(@Body() body: { userIds: number[] }, @Request() req) {
    return this.usersService.deactivateUsers(body.userIds, req.user.userId);
  }

  @Post('bulk/reactivate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  reactivateUsers(@Body() body: { userIds: number[] }, @Request() req) {
    return this.usersService.reactivateUsers(body.userIds, req.user.userId);
  }

  @Patch(':id/reset-password')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  resetUserPassword(
    @Param('id') id: string,
    @Body() body: { newPassword: string },
    @Request() req
  ) {
    return this.usersService.resetUserPassword(+id, body.newPassword, req.user.userId);
  }

  @Get('validate/role-change/:targetRole')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  validateRoleChangePermission(
    @Param('targetRole') targetRole: UserRole,
    @Request() req
  ) {
    const hasPermission = this.usersService.validateRoleChangePermission(
      req.user.role,
      targetRole
    );

    return { hasPermission };
  }

  // ===================================================================
  // ROLES ENDPOINT FOR SETTINGS MENU
  // ===================================================================

  @Get('roles')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.CEO)
  async getRoles() {
    // Return all available user roles for Settings menu
    const roles = [
      {
        id: 'ceo',
        name: 'CEO',
        description: 'Chief Executive Officer',
        level: 'executive',
        permissions: ['all']
      },
      {
        id: 'vp',
        name: 'Vice President',
        description: 'Vice President',
        level: 'executive',
        permissions: ['manage_departments', 'view_all_reports', 'manage_users']
      },
      {
        id: 'director',
        name: 'Director',
        description: 'Department Director',
        level: 'management',
        permissions: ['manage_department', 'view_department_reports', 'manage_team_leads']
      },
      {
        id: 'manager',
        name: 'Manager',
        description: 'Team Manager',
        level: 'management',
        permissions: ['manage_team', 'view_team_reports', 'conduct_assessments']
      },
      {
        id: 'senior_engineer',
        name: 'Senior Engineer',
        description: 'Senior Software Engineer',
        level: 'senior',
        permissions: ['mentor_juniors', 'technical_decisions', 'code_reviews']
      },
      {
        id: 'engineer',
        name: 'Engineer',
        description: 'Software Engineer',
        level: 'individual',
        permissions: ['develop_features', 'participate_assessments']
      },
      {
        id: 'junior_engineer',
        name: 'Junior Engineer',
        description: 'Junior Software Engineer',
        level: 'individual',
        permissions: ['develop_features', 'participate_assessments']
      },
      {
        id: 'intern',
        name: 'Intern',
        description: 'Software Engineering Intern',
        level: 'trainee',
        permissions: ['learn', 'participate_assessments']
      },
      {
        id: 'hr_admin',
        name: 'HR Administrator',
        description: 'Human Resources Administrator',
        level: 'admin',
        permissions: ['manage_hr', 'view_all_assessments', 'manage_templates']
      },
      {
        id: 'guest',
        name: 'Guest',
        description: 'Guest User',
        level: 'limited',
        permissions: ['view_public']
      },
      {
        id: 'employee',
        name: 'Employee',
        description: 'General Employee',
        level: 'individual',
        permissions: ['participate_assessments', 'view_own_data']
      }
    ];

    return {
      success: true,
      data: roles,
      total: roles.length
    };
  }
}
