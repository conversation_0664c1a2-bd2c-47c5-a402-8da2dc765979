import { AxiosInstance } from 'axios';
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
});
import {
  Team,
  TeamMember,
  CreateTeamDto,
  UpdateTeamDto,
  AddTeamMemberDto,
  UpdateTeamMemberRoleDto,
} from '../interfaces/team.interface';

class TeamService {
  private api: AxiosInstance;
  private baseUrl: string = '/teams';

  constructor() {
    this.api = apiClient;
  }

  async getAllTeams(): Promise<Team[]> {
    const response = await this.api.get<Team[]>(this.baseUrl);
    return response.data;
  }

  async getTeam(id: number): Promise<Team> {
    const response = await this.api.get<Team>(`${this.baseUrl}/${id}`);
    return response.data;
  }

  async createTeam(team: CreateTeamDto): Promise<Team> {
    const response = await this.api.post<Team>(this.baseUrl, team);
    return response.data;
  }

  async updateTeam(id: number, team: UpdateTeamDto): Promise<Team> {
    const response = await this.api.patch<Team>(`${this.baseUrl}/${id}`, team);
    return response.data;
  }

  async deleteTeam(id: number): Promise<void> {
    await this.api.delete(`${this.baseUrl}/${id}`);
  }

  async getTeamMembers(teamId: number): Promise<TeamMember[]> {
    const response = await this.api.get<TeamMember[]>(`${this.baseUrl}/${teamId}/members`);
    return response.data;
  }

  async addTeamMember(teamId: number, member: AddTeamMemberDto): Promise<TeamMember> {
    const response = await this.api.post<TeamMember>(`${this.baseUrl}/${teamId}/members`, member);
    return response.data;
  }

  async updateTeamMemberRole(
    teamId: number,
    memberId: number,
    roleUpdate: UpdateTeamMemberRoleDto
  ): Promise<TeamMember> {
    const response = await this.api.patch<TeamMember>(
      `${this.baseUrl}/${teamId}/members/${memberId}`,
      roleUpdate
    );
    return response.data;
  }

  async removeTeamMember(teamId: number, memberId: number): Promise<void> {
    await this.api.delete(`${this.baseUrl}/${teamId}/members/${memberId}`);
  }
}

export const teamService = new TeamService();
