import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>inC<PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AssessmentCriteria } from './assessment-criteria.entity';
import { AssessmentScoringScale } from './assessment-scoring-scale.entity';

@Entity('assessment_criteria_scoring')
export class AssessmentCriteriaScoring {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'criteria_id' })
  criteriaId: number;

  @ManyToOne(() => AssessmentCriteria)
  @JoinColumn({ name: 'criteria_id' })
  criteria: AssessmentCriteria;

  @Column({ name: 'scoring_scale_id' })
  scoringScaleId: number;

  @ManyToOne(() => AssessmentScoringScale, scale => scale.criteriaScorings)
  @JoinColumn({ name: 'scoring_scale_id' })
  scoringScale: AssessmentScoringScale;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
