-- Direct Sample Monthly Dashboard Data for July 2025
-- This creates realistic sample data using direct inserts

USE ehrx;

-- Clean up any existing July 2025 data first
DELETE FROM monthly_dashboard_kpi_values WHERE submission_id IN (
  SELECT id FROM monthly_dashboard_submissions WHERE submission_month = 7 AND submission_year = 2025
);
DELETE FROM monthly_dashboard_submissions WHERE submission_month = 7 AND submission_year = 2025;

-- Create monthly dashboard submissions for July 2025 using existing teams
INSERT INTO monthly_dashboard_submissions (
  organizational_unit_id, 
  submitted_by_user_id, 
  submission_month, 
  submission_year, 
  status, 
  completion_date,
  notes
) VALUES
(15, 5, 7, 2025, 'submitted', '2025-07-14', 'July 2025 dashboard - Frontend Team'),
(16, 6, 7, 2025, 'approved', '2025-07-13', 'July 2025 dashboard - Backend Team'),
(17, 7, 7, 2025, 'submitted', '2025-07-15', 'July 2025 dashboard - Mobile Team'),
(18, 8, 7, 2025, 'approved', '2025-07-12', 'July 2025 dashboard - Full-Stack Team'),
(19, 9, 7, 2025, 'submitted', '2025-07-16', 'July 2025 dashboard - Data Engineering Team'),
(20, 10, 7, 2025, 'submitted', '2025-07-14', 'July 2025 dashboard - BI Team'),
(21, 5, 7, 2025, 'approved', '2025-07-11', 'July 2025 dashboard - ML Team'),
(22, 6, 7, 2025, 'submitted', '2025-07-17', 'July 2025 dashboard - Cloud Infrastructure Team'),
(23, 7, 7, 2025, 'submitted', '2025-07-13', 'July 2025 dashboard - DevOps Automation Team'),
(24, 8, 7, 2025, 'approved', '2025-07-10', 'July 2025 dashboard - Platform Engineering Team');

-- Insert realistic KPI values for all teams
-- We'll use the submission IDs that were just created (starting from the next available ID)

-- Get the starting submission ID for our new submissions
SET @start_id = (SELECT MAX(id) FROM monthly_dashboard_submissions WHERE submission_month = 7 AND submission_year = 2025) - 9;

INSERT INTO monthly_dashboard_kpi_values (submission_id, kpi_id, value, target_value, traffic_light_status, notes) VALUES

-- Frontend Team (submission_id: @start_id) - Good performance
(@start_id, 1, 12, 12, 'green', 'Team at target size'),
(@start_id, 3, 0, 0, 'green', 'No attrition'),
(@start_id, 4, 97.5, 95.0, 'green', 'Exceeded SLA target'),
(@start_id, 5, 85.0, 80.0, 'green', 'Good utilization'),
(@start_id, 6, 96.0, 95.0, 'green', 'Good time registration'),
(@start_id, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id, 8, -1200.00, 0, 'green', 'Under budget'),
(@start_id, 9, 15, 25, 'green', '15 PTO days taken'),
(@start_id, 10, 88.0, 80.0, 'green', 'Good office attendance'),

-- Backend Team (submission_id: @start_id + 1) - Excellent performance
(@start_id + 1, 1, 15, 15, 'green', 'Team at target size'),
(@start_id + 1, 3, 0, 0, 'green', 'No attrition'),
(@start_id + 1, 4, 99.1, 95.0, 'green', 'Excellent SLA'),
(@start_id + 1, 5, 92.0, 80.0, 'green', 'High utilization'),
(@start_id + 1, 6, 98.0, 95.0, 'green', 'Excellent time registration'),
(@start_id + 1, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 1, 8, -2500.00, 0, 'green', 'Well under budget'),
(@start_id + 1, 9, 8, 25, 'green', '8 PTO days taken'),
(@start_id + 1, 10, 95.0, 80.0, 'green', 'Excellent attendance'),

-- Mobile Team (submission_id: @start_id + 2) - Mixed performance
(@start_id + 2, 1, 8, 8, 'green', 'Team at target size'),
(@start_id + 2, 3, 1, 0, 'yellow', '1 resignation this month'),
(@start_id + 2, 4, 94.2, 95.0, 'yellow', 'Slightly below SLA'),
(@start_id + 2, 5, 78.0, 80.0, 'yellow', 'Below utilization target'),
(@start_id + 2, 6, 93.0, 95.0, 'yellow', 'Below time reg target'),
(@start_id + 2, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 2, 8, 800.00, 0, 'yellow', 'Slightly over budget'),
(@start_id + 2, 9, 20, 25, 'green', '20 PTO days taken'),
(@start_id + 2, 10, 75.0, 80.0, 'yellow', 'Below attendance target'),

-- Full-Stack Team (submission_id: @start_id + 3) - Some issues
(@start_id + 3, 1, 10, 10, 'green', 'Team at target size'),
(@start_id + 3, 3, 2, 0, 'red', '2 resignations this month'),
(@start_id + 3, 4, 91.5, 95.0, 'red', 'Below SLA target'),
(@start_id + 3, 5, 72.0, 80.0, 'red', 'Low utilization'),
(@start_id + 3, 6, 88.0, 95.0, 'red', 'Poor time registration'),
(@start_id + 3, 7, 1, 0, 'red', '1 compliance issue'),
(@start_id + 3, 8, 1500.00, 0, 'red', 'Over budget'),
(@start_id + 3, 9, 25, 25, 'green', '25 PTO days taken'),
(@start_id + 3, 10, 65.0, 80.0, 'red', 'Poor attendance'),

-- Data Engineering Team (submission_id: @start_id + 4) - Good performance
(@start_id + 4, 1, 14, 14, 'green', 'Team at target size'),
(@start_id + 4, 3, 0, 0, 'green', 'No attrition'),
(@start_id + 4, 4, 96.8, 95.0, 'green', 'Good SLA performance'),
(@start_id + 4, 5, 83.0, 80.0, 'green', 'Good utilization'),
(@start_id + 4, 6, 95.0, 95.0, 'green', 'Target time registration'),
(@start_id + 4, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 4, 8, -900.00, 0, 'green', 'Under budget'),
(@start_id + 4, 9, 12, 25, 'green', '12 PTO days taken'),
(@start_id + 4, 10, 82.0, 80.0, 'green', 'Good attendance'),

-- BI Team (submission_id: @start_id + 5) - Average performance
(@start_id + 5, 1, 6, 6, 'green', 'Team at target size'),
(@start_id + 5, 3, 0, 0, 'green', 'No attrition'),
(@start_id + 5, 4, 93.5, 95.0, 'yellow', 'Below SLA target'),
(@start_id + 5, 5, 79.0, 80.0, 'yellow', 'Slightly below utilization'),
(@start_id + 5, 6, 92.0, 95.0, 'yellow', 'Below time registration'),
(@start_id + 5, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 5, 8, 200.00, 0, 'yellow', 'Slightly over budget'),
(@start_id + 5, 9, 18, 25, 'green', '18 PTO days taken'),
(@start_id + 5, 10, 77.0, 80.0, 'yellow', 'Below attendance target'),

-- ML Team (submission_id: @start_id + 6) - Excellent performance
(@start_id + 6, 1, 9, 9, 'green', 'Team at target size'),
(@start_id + 6, 3, 0, 0, 'green', 'No attrition'),
(@start_id + 6, 4, 98.7, 95.0, 'green', 'Excellent SLA'),
(@start_id + 6, 5, 89.0, 80.0, 'green', 'High utilization'),
(@start_id + 6, 6, 97.0, 95.0, 'green', 'Excellent time registration'),
(@start_id + 6, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 6, 8, -1800.00, 0, 'green', 'Well under budget'),
(@start_id + 6, 9, 10, 25, 'green', '10 PTO days taken'),
(@start_id + 6, 10, 91.0, 80.0, 'green', 'Excellent attendance'),

-- Cloud Infrastructure Team (submission_id: @start_id + 7) - Mixed performance
(@start_id + 7, 1, 11, 11, 'green', 'Team at target size'),
(@start_id + 7, 3, 1, 0, 'yellow', '1 resignation this month'),
(@start_id + 7, 4, 95.2, 95.0, 'green', 'Good SLA performance'),
(@start_id + 7, 5, 81.0, 80.0, 'green', 'Good utilization'),
(@start_id + 7, 6, 94.0, 95.0, 'yellow', 'Slightly below time reg'),
(@start_id + 7, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 7, 8, 500.00, 0, 'yellow', 'Slightly over budget'),
(@start_id + 7, 9, 16, 25, 'green', '16 PTO days taken'),
(@start_id + 7, 10, 84.0, 80.0, 'green', 'Good attendance'),

-- DevOps Automation Team (submission_id: @start_id + 8) - Good performance
(@start_id + 8, 1, 7, 7, 'green', 'Team at target size'),
(@start_id + 8, 3, 0, 0, 'green', 'No attrition'),
(@start_id + 8, 4, 97.1, 95.0, 'green', 'Good SLA performance'),
(@start_id + 8, 5, 86.0, 80.0, 'green', 'High utilization'),
(@start_id + 8, 6, 96.0, 95.0, 'green', 'Good time registration'),
(@start_id + 8, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 8, 8, -600.00, 0, 'green', 'Under budget'),
(@start_id + 8, 9, 14, 25, 'green', '14 PTO days taken'),
(@start_id + 8, 10, 87.0, 80.0, 'green', 'Good attendance'),

-- Platform Engineering Team (submission_id: @start_id + 9) - Excellent performance
(@start_id + 9, 1, 13, 13, 'green', 'Team at target size'),
(@start_id + 9, 3, 0, 0, 'green', 'No attrition'),
(@start_id + 9, 4, 99.3, 95.0, 'green', 'Excellent SLA'),
(@start_id + 9, 5, 90.0, 80.0, 'green', 'High utilization'),
(@start_id + 9, 6, 98.0, 95.0, 'green', 'Excellent time registration'),
(@start_id + 9, 7, 0, 0, 'green', 'No compliance issues'),
(@start_id + 9, 8, -2200.00, 0, 'green', 'Well under budget'),
(@start_id + 9, 9, 9, 25, 'green', '9 PTO days taken'),
(@start_id + 9, 10, 93.0, 80.0, 'green', 'Excellent attendance');

-- Display confirmation
SELECT 'Sample monthly dashboard data for July 2025 created successfully' AS status;

-- Show summary
SELECT 
    COUNT(DISTINCT s.id) as total_submissions,
    COUNT(kv.id) as total_kpi_values,
    SUM(CASE WHEN kv.traffic_light_status = 'green' THEN 1 ELSE 0 END) as green_count,
    SUM(CASE WHEN kv.traffic_light_status = 'yellow' THEN 1 ELSE 0 END) as yellow_count,
    SUM(CASE WHEN kv.traffic_light_status = 'red' THEN 1 ELSE 0 END) as red_count
FROM monthly_dashboard_submissions s
LEFT JOIN monthly_dashboard_kpi_values kv ON s.id = kv.submission_id
WHERE s.submission_month = 7 AND s.submission_year = 2025;
