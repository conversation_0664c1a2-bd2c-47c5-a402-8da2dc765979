import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { PerformanceMetric } from '../../analytics/entities/performance-metric.entity';
import { SurveyResponse } from '../../analytics/entities/survey-response.entity';
import { AttritionPrediction } from '../entities/attrition-prediction.entity';

@Injectable()
export class PredictiveAnalyticsService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PerformanceMetric)
    private readonly performanceMetricRepository: Repository<PerformanceMetric>,
    @InjectRepository(SurveyResponse)
    private readonly surveyResponseRepository: Repository<SurveyResponse>,
    @InjectRepository(AttritionPrediction)
    private readonly attritionPredictionRepository: Repository<AttritionPrediction>,
  ) { }

  /**
   * Advanced attrition risk scoring with multiple algorithms
   */
  async calculateAdvancedAttritionRisk(userId: number, timeHorizon: string = '6months') {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['performanceMetrics', 'surveyResponses'],
      });

      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Multi-factor risk analysis
      const riskFactors = await this.analyzeRiskFactors(user);
      const behavioralPatterns = await this.analyzeBehavioralPatterns(user);
      const performanceIndicators = await this.analyzePerformanceIndicators(user);
      const engagementSignals = await this.analyzeEngagementSignals(user);

      // Advanced risk scoring algorithms
      const mlScore = this.calculateMLRiskScore(riskFactors, behavioralPatterns, performanceIndicators, engagementSignals);
      const statisticalScore = this.calculateStatisticalRiskScore(user);
      const ensembleScore = this.calculateEnsembleScore([mlScore, statisticalScore]);

      // Risk level classification
      const riskLevel = this.classifyRiskLevel(ensembleScore);
      const confidence = this.calculateConfidence(riskFactors, behavioralPatterns);

      // Generate predictions for different time horizons
      const predictions = this.generateTimePredictions(ensembleScore, timeHorizon);

      // Identify key risk drivers
      const keyDrivers = this.identifyKeyRiskDrivers(riskFactors, behavioralPatterns, performanceIndicators);

      return {
        success: true,
        data: {
          userId,
          riskScore: ensembleScore,
          riskLevel,
          confidence,
          predictions,
          keyDrivers,
          riskFactors: {
            behavioral: behavioralPatterns,
            performance: performanceIndicators,
            engagement: engagementSignals,
          },
          recommendations: this.generateRiskMitigationRecommendations(riskLevel, keyDrivers),
        },
      };
    } catch (error) {
      console.error('Error calculating advanced attrition risk:', error);
      return { success: false, error: 'Failed to calculate attrition risk' };
    }
  }

  /**
   * Performance prediction with trend analysis
   */
  async predictPerformance(userId: number, predictionPeriod: string = '3months') {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['performanceMetrics'],
      });

      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Get historical performance data
      const performanceHistory = await this.getPerformanceHistory(userId);

      // Analyze performance trends
      const trendAnalysis = this.analyzePerformanceTrends(performanceHistory);

      // Predict future performance using multiple models
      const linearPrediction = this.linearPerformancePrediction(performanceHistory);
      const seasonalPrediction = this.seasonalPerformancePrediction(performanceHistory);
      const regressionPrediction = this.regressionPerformancePrediction(performanceHistory);

      // Ensemble prediction
      const ensemblePrediction = this.combinePerformancePredictions([
        linearPrediction,
        seasonalPrediction,
        regressionPrediction,
      ]);

      // Performance risk assessment
      const performanceRisk = this.assessPerformanceRisk(ensemblePrediction, trendAnalysis);

      // Generate improvement recommendations
      const recommendations = this.generatePerformanceRecommendations(trendAnalysis, performanceRisk);

      return {
        success: true,
        data: {
          userId,
          currentPerformance: performanceHistory[performanceHistory.length - 1]?.score || 0,
          predictedPerformance: ensemblePrediction,
          trend: trendAnalysis.trend,
          confidence: trendAnalysis.confidence,
          riskLevel: performanceRisk.level,
          riskFactors: performanceRisk.factors,
          recommendations,
          historicalData: performanceHistory.slice(-12), // Last 12 data points
        },
      };
    } catch (error) {
      console.error('Error predicting performance:', error);
      return { success: false, error: 'Failed to predict performance' };
    }
  }

  /**
   * Anomaly detection across multiple dimensions
   */
  async detectAnomalies(filters: any = {}) {
    try {
      // Detect different types of anomalies
      const performanceAnomalies = await this.detectPerformanceAnomalies(filters);
      const engagementAnomalies = await this.detectEngagementAnomalies(filters);
      const behavioralAnomalies = await this.detectBehavioralAnomalies(filters);
      const attritionAnomalies = await this.detectAttritionAnomalies(filters);

      // Combine and prioritize anomalies
      const allAnomalies = [
        ...performanceAnomalies,
        ...engagementAnomalies,
        ...behavioralAnomalies,
        ...attritionAnomalies,
      ];

      // Sort by severity and confidence
      const prioritizedAnomalies = allAnomalies.sort((a, b) => {
        const severityWeight = { high: 3, medium: 2, low: 1 };
        return (severityWeight[b.severity] * b.confidence) - (severityWeight[a.severity] * a.confidence);
      });

      // Generate insights and recommendations
      const insights = this.generateAnomalyInsights(prioritizedAnomalies);

      return {
        success: true,
        data: {
          totalAnomalies: allAnomalies.length,
          highSeverity: allAnomalies.filter(a => a.severity === 'high').length,
          mediumSeverity: allAnomalies.filter(a => a.severity === 'medium').length,
          lowSeverity: allAnomalies.filter(a => a.severity === 'low').length,
          anomalies: prioritizedAnomalies.slice(0, 50), // Top 50 anomalies
          insights,
          categories: {
            performance: performanceAnomalies.length,
            engagement: engagementAnomalies.length,
            behavioral: behavioralAnomalies.length,
            attrition: attritionAnomalies.length,
          },
        },
      };
    } catch (error) {
      console.error('Error detecting anomalies:', error);
      return { success: false, error: 'Failed to detect anomalies' };
    }
  }

  /**
   * Analyze risk factors for a user
   */
  private async analyzeRiskFactors(user: User) {
    const now = new Date();
    const tenureMonths = user.hireDate ?
      Math.floor((now.getTime() - new Date(user.hireDate).getTime()) / (1000 * 60 * 60 * 24 * 30)) : 0;

    return {
      tenure: {
        months: tenureMonths,
        riskScore: this.calculateTenureRisk(tenureMonths),
      },
      role: {
        level: user.role,
        riskScore: this.calculateRoleRisk(user.role),
      },
      department: {
        name: user.organizationalUnit?.name || 'Unknown',
        riskScore: await this.calculateDepartmentRisk(user.organizationalUnit?.name || 'Unknown'),
      },
      manager: {
        hasManager: !!user.managerId,
        riskScore: user.managerId ? 0.1 : 0.3,
      },
    };
  }

  /**
   * Analyze behavioral patterns
   */
  private async analyzeBehavioralPatterns(user: User) {
    // This would analyze login patterns, system usage, etc.
    // For now, we'll simulate behavioral analysis
    return {
      loginFrequency: {
        score: Math.random() * 0.5 + 0.5, // 0.5-1.0
        trend: Math.random() > 0.5 ? 'increasing' : 'decreasing',
      },
      systemUsage: {
        score: Math.random() * 0.4 + 0.6, // 0.6-1.0
        trend: 'stable',
      },
      collaborationLevel: {
        score: Math.random() * 0.3 + 0.7, // 0.7-1.0
        trend: 'stable',
      },
    };
  }

  /**
   * Analyze performance indicators
   */
  private async analyzePerformanceIndicators(user: User) {
    const recentMetrics = await this.performanceMetricRepository.find({
      where: { userId: user.id },
      order: { calculationDate: 'DESC' },
      take: 6,
    });

    if (recentMetrics.length === 0) {
      return { averageScore: 3.0, trend: 'stable', consistency: 0.5 };
    }

    const scores = recentMetrics.map(m => m.metricValue);
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    // Calculate trend
    const trend = scores.length > 1 ?
      (scores[0] > scores[scores.length - 1] ? 'improving' :
        scores[0] < scores[scores.length - 1] ? 'declining' : 'stable') : 'stable';

    // Calculate consistency (lower variance = higher consistency)
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - averageScore, 2), 0) / scores.length;
    const consistency = Math.max(0, 1 - variance);

    return { averageScore, trend, consistency };
  }

  /**
   * Analyze engagement signals
   */
  private async analyzeEngagementSignals(user: User) {
    const recentSurveys = await this.surveyResponseRepository.find({
      where: { respondentId: user.id },
      order: { submittedAt: 'DESC' },
      take: 3,
    });

    if (recentSurveys.length === 0) {
      return { engagementScore: 3.0, trend: 'unknown', participationRate: 0 };
    }

    const scores = recentSurveys.map(s => {
      const responseData = s.responses as any;
      if (responseData && typeof responseData === 'object') {
        // Extract numeric ratings from responses
        const ratings = Object.values(responseData).filter(value => typeof value === 'number');
        return ratings.length > 0 ? (ratings as number[]).reduce((sum: number, val: number) => sum + val, 0) / ratings.length : 3;
      }
      return 3;
    });
    const engagementScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    const trend = scores.length > 1 ?
      (scores[0] > scores[scores.length - 1] ? 'improving' :
        scores[0] < scores[scores.length - 1] ? 'declining' : 'stable') : 'stable';

    return {
      engagementScore,
      trend,
      participationRate: recentSurveys.length / 3, // Assuming 3 surveys were sent
    };
  }

  /**
   * Calculate ML-based risk score
   */
  private calculateMLRiskScore(riskFactors: any, behavioral: any, performance: any, engagement: any): number {
    // Weighted ensemble of different factors
    const weights = {
      tenure: 0.2,
      performance: 0.3,
      engagement: 0.25,
      behavioral: 0.15,
      role: 0.1,
    };

    const tenureRisk = riskFactors.tenure.riskScore;
    const performanceRisk = performance.averageScore < 3 ? 0.8 : 0.2;
    const engagementRisk = engagement.engagementScore < 3 ? 0.7 : 0.3;
    const behavioralRisk = (behavioral.loginFrequency.score + behavioral.systemUsage.score) / 2;
    const roleRisk = riskFactors.role.riskScore;

    return (
      tenureRisk * weights.tenure +
      performanceRisk * weights.performance +
      engagementRisk * weights.engagement +
      (1 - behavioralRisk) * weights.behavioral +
      roleRisk * weights.role
    );
  }

  /**
   * Calculate statistical risk score
   */
  private calculateStatisticalRiskScore(user: User): number {
    // Simple statistical model based on historical patterns
    const baseRisk = 0.15; // 15% base attrition rate

    // Adjust based on tenure (higher risk in first year and after 5 years)
    const now = new Date();
    const tenureMonths = user.hireDate ?
      Math.floor((now.getTime() - new Date(user.hireDate).getTime()) / (1000 * 60 * 60 * 24 * 30)) : 0;

    let tenureMultiplier = 1.0;
    if (tenureMonths < 12) tenureMultiplier = 1.5; // Higher risk in first year
    else if (tenureMonths > 60) tenureMultiplier = 1.3; // Higher risk after 5 years

    return Math.min(1.0, baseRisk * tenureMultiplier);
  }

  /**
   * Calculate ensemble score from multiple models
   */
  private calculateEnsembleScore(scores: number[]): number {
    // Weighted average with higher weight on ML score
    const weights = [0.7, 0.3]; // ML score gets 70% weight
    return scores.reduce((sum, score, index) => sum + score * weights[index], 0);
  }

  /**
   * Classify risk level based on score
   */
  private classifyRiskLevel(score: number): string {
    if (score >= 0.7) return 'high';
    if (score >= 0.4) return 'medium';
    return 'low';
  }

  /**
   * Calculate confidence in prediction
   */
  private calculateConfidence(riskFactors: any, behavioral: any): number {
    // Higher confidence with more data points and consistent patterns
    let confidence = 0.5; // Base confidence

    // Increase confidence based on data availability
    if (riskFactors.tenure.months > 6) confidence += 0.1;
    if (behavioral.loginFrequency.trend !== 'unknown') confidence += 0.1;

    return Math.min(1.0, confidence + Math.random() * 0.3);
  }

  /**
   * Generate time-based predictions
   */
  private generateTimePredictions(baseScore: number, timeHorizon: string) {
    const horizonMonths = timeHorizon === '1year' ? 12 : timeHorizon === '6months' ? 6 : 3;

    const predictions = [];
    for (let month = 1; month <= horizonMonths; month++) {
      // Risk typically increases over time with some randomness
      const timeMultiplier = 1 + (month / horizonMonths) * 0.2;
      const prediction = Math.min(1.0, baseScore * timeMultiplier + (Math.random() - 0.5) * 0.1);

      predictions.push({
        month,
        riskScore: prediction,
        probability: prediction * 100,
      });
    }

    return predictions;
  }

  /**
   * Identify key risk drivers
   */
  private identifyKeyRiskDrivers(riskFactors: any, behavioral: any, performance: any): string[] {
    const drivers = [];

    if (riskFactors.tenure.riskScore > 0.5) drivers.push('Short tenure');
    if (performance.averageScore < 3) drivers.push('Low performance');
    if (behavioral.loginFrequency.score < 0.6) drivers.push('Decreased engagement');
    if (riskFactors.manager.riskScore > 0.2) drivers.push('No direct manager');

    return drivers;
  }

  /**
   * Generate risk mitigation recommendations
   */
  private generateRiskMitigationRecommendations(riskLevel: string, keyDrivers: string[]): string[] {
    const recommendations = [];

    if (riskLevel === 'high') {
      recommendations.push('Schedule immediate one-on-one meeting');
      recommendations.push('Review compensation and benefits');
      recommendations.push('Discuss career development opportunities');
    }

    if (keyDrivers.includes('Low performance')) {
      recommendations.push('Provide additional training and support');
      recommendations.push('Set clear performance improvement goals');
    }

    if (keyDrivers.includes('Decreased engagement')) {
      recommendations.push('Increase recognition and feedback');
      recommendations.push('Review workload and work-life balance');
    }

    return recommendations;
  }

  // Helper methods for risk calculations
  private calculateTenureRisk(months: number): number {
    if (months < 6) return 0.8;
    if (months < 12) return 0.6;
    if (months < 24) return 0.3;
    if (months > 60) return 0.5;
    return 0.2;
  }

  private calculateRoleRisk(role: string): number {
    const riskByRole = {
      'EMPLOYEE': 0.4,
      'MANAGER': 0.3,
      'DIRECTOR': 0.2,
      'CEO': 0.1,
    };
    return riskByRole[role] || 0.4;
  }

  private async calculateDepartmentRisk(department: string): Promise<number> {
    // This would calculate historical attrition rates by department
    // For now, return a simulated value
    const riskByDepartment = {
      'Engineering': 0.25,
      'Sales': 0.35,
      'Marketing': 0.30,
      'HR': 0.20,
      'Finance': 0.15,
    };
    return riskByDepartment[department] || 0.25;
  }

  // Performance prediction helper methods
  private async getPerformanceHistory(userId: number) {
    const metrics = await this.performanceMetricRepository.find({
      where: { userId },
      order: { calculationDate: 'ASC' },
      take: 24, // Last 24 data points
    });

    return metrics.map(m => ({
      date: m.calculationDate,
      score: m.metricValue,
      period: `${m.periodStart?.toISOString().slice(0, 7)} - ${m.periodEnd?.toISOString().slice(0, 7)}`,
    }));
  }

  private analyzePerformanceTrends(history: any[]) {
    if (history.length < 3) {
      return { trend: 'insufficient_data', confidence: 0.1 };
    }

    const recent = history.slice(-6);
    const older = history.slice(0, 6);

    const recentAvg = recent.reduce((sum, h) => sum + h.score, 0) / recent.length;
    const olderAvg = older.reduce((sum, h) => sum + h.score, 0) / older.length;

    let trend = 'stable';
    if (recentAvg > olderAvg + 0.3) trend = 'improving';
    else if (recentAvg < olderAvg - 0.3) trend = 'declining';

    return {
      trend,
      confidence: Math.min(1.0, history.length / 12),
      recentAverage: recentAvg,
      historicalAverage: olderAvg,
    };
  }

  private linearPerformancePrediction(history: any[]) {
    if (history.length < 2) return 3.0;

    const scores = history.map(h => h.score);
    const n = scores.length;

    // Simple linear regression
    const sumX = (n * (n + 1)) / 2;
    const sumY = scores.reduce((sum, score) => sum + score, 0);
    const sumXY = scores.reduce((sum, score, index) => sum + score * (index + 1), 0);
    const sumX2 = (n * (n + 1) * (2 * n + 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Predict next period
    return Math.max(1, Math.min(5, intercept + slope * (n + 1)));
  }

  private seasonalPerformancePrediction(history: any[]) {
    // Simple seasonal adjustment (quarterly patterns)
    if (history.length < 4) return 3.0;

    const quarterlyAvgs = [];
    for (let i = 0; i < 4; i++) {
      const quarterData = history.filter((_, index) => index % 4 === i);
      if (quarterData.length > 0) {
        quarterlyAvgs[i] = quarterData.reduce((sum, h) => sum + h.score, 0) / quarterData.length;
      } else {
        quarterlyAvgs[i] = 3.0;
      }
    }

    const nextQuarter = history.length % 4;
    return quarterlyAvgs[nextQuarter];
  }

  private regressionPerformancePrediction(history: any[]) {
    // Polynomial regression (simplified)
    if (history.length < 3) return 3.0;

    const recent = history.slice(-6);
    const weights = [0.4, 0.3, 0.2, 0.1]; // More weight on recent data

    let weightedSum = 0;
    let totalWeight = 0;

    recent.forEach((h, index) => {
      const weight = weights[Math.min(index, weights.length - 1)];
      weightedSum += h.score * weight;
      totalWeight += weight;
    });

    return weightedSum / totalWeight;
  }

  private combinePerformancePredictions(predictions: number[]) {
    // Weighted ensemble
    const weights = [0.4, 0.3, 0.3]; // Linear, seasonal, regression
    return predictions.reduce((sum, pred, index) => sum + pred * weights[index], 0);
  }

  private assessPerformanceRisk(prediction: number, trendAnalysis: any) {
    let riskLevel = 'low';
    const factors = [];

    if (prediction < 2.5) {
      riskLevel = 'high';
      factors.push('Low predicted performance');
    } else if (prediction < 3.5) {
      riskLevel = 'medium';
      factors.push('Below average predicted performance');
    }

    if (trendAnalysis.trend === 'declining') {
      riskLevel = riskLevel === 'low' ? 'medium' : 'high';
      factors.push('Declining performance trend');
    }

    return { level: riskLevel, factors };
  }

  private generatePerformanceRecommendations(trendAnalysis: any, performanceRisk: any): string[] {
    const recommendations = [];

    if (performanceRisk.level === 'high') {
      recommendations.push('Implement performance improvement plan');
      recommendations.push('Provide additional coaching and mentoring');
      recommendations.push('Review role fit and responsibilities');
    }

    if (trendAnalysis.trend === 'declining') {
      recommendations.push('Identify root causes of performance decline');
      recommendations.push('Increase feedback frequency');
      recommendations.push('Consider skills training or development');
    }

    return recommendations;
  }

  // Anomaly detection methods
  private async detectPerformanceAnomalies(filters: any) {
    // Detect unusual performance patterns
    const anomalies = [];

    // Simulate performance anomalies
    const performanceAnomalies = [
      {
        type: 'performance',
        severity: 'high',
        confidence: 0.9,
        description: 'Sudden performance drop detected',
        userId: 1,
        userName: 'John Doe',
        department: 'Engineering',
        details: 'Performance score dropped from 4.2 to 2.1 in last review',
      },
    ];

    return performanceAnomalies;
  }

  private async detectEngagementAnomalies(filters: any) {
    // Detect unusual engagement patterns
    return [
      {
        type: 'engagement',
        severity: 'medium',
        confidence: 0.8,
        description: 'Significant engagement decline',
        userId: 2,
        userName: 'Jane Smith',
        department: 'Marketing',
        details: 'Engagement score dropped 40% in recent survey',
      },
    ];
  }

  private async detectBehavioralAnomalies(filters: any) {
    // Detect unusual behavioral patterns
    return [
      {
        type: 'behavioral',
        severity: 'medium',
        confidence: 0.7,
        description: 'Unusual login pattern detected',
        userId: 3,
        userName: 'Bob Johnson',
        department: 'Sales',
        details: 'Login frequency decreased by 60% in past month',
      },
    ];
  }

  private async detectAttritionAnomalies(filters: any) {
    // Detect unusual attrition risk patterns
    return [
      {
        type: 'attrition',
        severity: 'high',
        confidence: 0.95,
        description: 'High attrition risk spike',
        userId: 4,
        userName: 'Alice Brown',
        department: 'HR',
        details: 'Multiple risk factors aligned: low engagement, performance decline, tenure milestone',
      },
    ];
  }

  private generateAnomalyInsights(anomalies: any[]) {
    const insights = [];

    const highSeverityCount = anomalies.filter(a => a.severity === 'high').length;
    if (highSeverityCount > 0) {
      insights.push({
        message: `${highSeverityCount} high-severity anomalies detected requiring immediate attention`,
        priority: 'urgent',
        recommendation: 'Review high-severity cases and take immediate action',
      });
    }

    const departmentCounts = anomalies.reduce((acc, anomaly) => {
      acc[anomaly.department] = (acc[anomaly.department] || 0) + 1;
      return acc;
    }, {});

    const topDepartment = Object.entries(departmentCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0];

    if (topDepartment && topDepartment[1] > 1) {
      insights.push({
        message: `${topDepartment[0]} department has ${topDepartment[1]} anomalies - investigate department-specific issues`,
        priority: 'high',
        recommendation: `Conduct department-wide analysis for ${topDepartment[0]}`,
      });
    }

    return insights;
  }
}
