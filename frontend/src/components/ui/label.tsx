import React from 'react';
import { Typography, TypographyProps } from '@mui/material';

interface LabelProps extends TypographyProps {
  htmlFor?: string;
}

export const Label: React.FC<LabelProps> = ({ children, htmlFor, ...props }) => (
  <Typography
    component="label"
    htmlFor={htmlFor}
    variant="body2"
    sx={{ fontWeight: 500, mb: 1, display: 'block' }}
    {...props}
  >
    {children}
  </Typography>
);
