import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardContent,
  Typography,
  Grid,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Psychology as AiIcon,
  SentimentSatisfied as PositiveIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as NegativeIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Analytics as AnalyticsIcon,
  Insights as InsightsIcon,
  Comment as CommentIcon,
  Assessment as SurveyIcon,
} from '@mui/icons-material';
import { ApiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`survey-analysis-tabpanel-${index}`}
      aria-labelledby={`survey-analysis-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const EngagementSurveyAnalysis: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [selectedSurvey, setSelectedSurvey] = useState<string>('latest');
  const [analysisData, setAnalysisData] = useState<any>(null);
  const [surveys, setSurveys] = useState<any[]>([]);

  useEffect(() => {
    loadSurveys();
  }, []);

  useEffect(() => {
    if (selectedSurvey) {
      loadSurveyAnalysis();
    }
  }, [selectedSurvey]);

  const loadSurveys = async () => {
    try {
      const response = await ApiService.get('/analytics/surveys');
      if (response.data) {
        setSurveys(response.data || []);
        if (response.data && response.data.length > 0) {
          setSelectedSurvey(response.data[0].id.toString());
        }
      }
    } catch (err) {
      console.error('Error loading surveys:', err);
      setError('Failed to load surveys');
    }
  };

  const loadSurveyAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);

      const surveyId = selectedSurvey === 'latest' ? surveys[0]?.id : parseInt(selectedSurvey);

      // Load sentiment analysis for the survey
      const sentimentResponse = await ApiService.post('/ai/sentiment/analyze-survey', {
        surveyId,
      });

      if (sentimentResponse.data) {
        setAnalysisData(sentimentResponse.data);
      }
    } catch (err) {
      console.error('Error loading survey analysis:', err);
      setError('Failed to load survey analysis');
    } finally {
      setLoading(false);
    }
  };

  const analyzeSurveyComments = async () => {
    try {
      setLoading(true);
      const surveyId = selectedSurvey === 'latest' ? surveys[0]?.id : parseInt(selectedSurvey);

      const response = await ApiService.post('/ai/sentiment/analyze-comments', {
        surveyId,
        includeThemes: true,
        includeEmotionalDrivers: true,
      });

      if (response.data) {
        setAnalysisData(prev => ({
          ...prev,
          ...response.data,
        }));
      }
    } catch (err) {
      console.error('Error analyzing comments:', err);
      setError('Failed to analyze survey comments');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return '#4caf50';
      case 'negative':
        return '#f44336';
      default:
        return '#ff9800';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return <PositiveIcon sx={{ color: '#4caf50' }} />;
      case 'negative':
        return <NegativeIcon sx={{ color: '#f44336' }} />;
      default:
        return <NeutralIcon sx={{ color: '#ff9800' }} />;
    }
  };

  if (loading && !analysisData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Engagement Survey Analysis
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          AI-powered analysis of employee feedback and sentiment
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Survey Selection and Controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Survey</InputLabel>
            <Select
              value={selectedSurvey}
              label="Survey"
              onChange={(e) => setSelectedSurvey(e.target.value)}
            >
              <MenuItem value="latest">Latest Survey</MenuItem>
              {surveys.map(survey => (
                <MenuItem key={survey.id} value={survey.id.toString()}>
                  {survey.title} - {new Date(survey.createdAt).toLocaleDateString()}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Button
            variant="contained"
            startIcon={<AiIcon />}
            onClick={analyzeSurveyComments}
            disabled={loading}
          >
            Analyze Comments
          </Button>
          <Button
            variant="outlined"
            startIcon={<AnalyticsIcon />}
            onClick={loadSurveyAnalysis}
            disabled={loading}
          >
            Refresh Analysis
          </Button>
        </Box>
      </Paper>

      {/* Analysis Results */}
      {analysisData && (
        <>
          {/* Overview Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Overall Sentiment
                      </Typography>
                      <Typography variant="h4">
                        {analysisData.overallScore?.toFixed(1) || '0.0'}
                      </Typography>
                    </Box>
                    {getSentimentIcon(analysisData.overallSentiment || 'neutral')}
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Based on {analysisData.totalResponses || 0} responses
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Positive Sentiment
                      </Typography>
                      <Typography variant="h4" color="success.main">
                        {((analysisData.sentimentDistribution?.positive || 0) * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <PositiveIcon sx={{ color: '#4caf50', fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Negative Sentiment
                      </Typography>
                      <Typography variant="h4" color="error.main">
                        {((analysisData.sentimentDistribution?.negative || 0) * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <NegativeIcon sx={{ color: '#f44336', fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Key Themes
                      </Typography>
                      <Typography variant="h4">
                        {analysisData.topThemes?.length || 0}
                      </Typography>
                    </Box>
                    <InsightsIcon color="primary" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Detailed Analysis Tabs */}
          <Paper sx={{ width: '100%' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="survey analysis tabs"
            >
              <Tab label="Sentiment Analysis" />
              <Tab label="Theme Analysis" />
              <Tab label="Emotional Drivers" />
              <Tab label="Comments" />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={3}>
                {/* Sentiment Distribution */}
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Sentiment Distribution
                  </Typography>
                  {analysisData.sentimentDistribution && (
                    <Box>
                      <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">Positive</Typography>
                        <Typography variant="body2">
                          {(analysisData.sentimentDistribution.positive * 100).toFixed(1)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={analysisData.sentimentDistribution.positive * 100}
                        sx={{ mb: 2, '& .MuiLinearProgress-bar': { backgroundColor: '#4caf50' } }}
                      />

                      <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">Neutral</Typography>
                        <Typography variant="body2">
                          {(analysisData.sentimentDistribution.neutral * 100).toFixed(1)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={analysisData.sentimentDistribution.neutral * 100}
                        sx={{ mb: 2, '& .MuiLinearProgress-bar': { backgroundColor: '#ff9800' } }}
                      />

                      <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">Negative</Typography>
                        <Typography variant="body2">
                          {(analysisData.sentimentDistribution.negative * 100).toFixed(1)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={analysisData.sentimentDistribution.negative * 100}
                        sx={{ '& .MuiLinearProgress-bar': { backgroundColor: '#f44336' } }}
                      />
                    </Box>
                  )}
                </Grid>

                {/* Sentiment Trends */}
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Sentiment Insights
                  </Typography>
                  {analysisData.insights && (
                    <List>
                      {analysisData.insights.slice(0, 5).map((insight: any, index: number) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <InsightsIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary={insight.message}
                            secondary={`Confidence: ${(insight.confidence * 100).toFixed(0)}%`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  )}
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Top Themes
              </Typography>
              {analysisData.topThemes && (
                <Grid container spacing={2}>
                  {analysisData.topThemes.map((theme: any, index: number) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle1" gutterBottom>
                          {theme.theme}
                        </Typography>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                          <Typography variant="body2">Frequency</Typography>
                          <Chip
                            label={`${theme.percentage.toFixed(1)}%`}
                            size="small"
                            color="primary"
                          />
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={theme.percentage}
                        />
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          {theme.count} mentions
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Emotional Drivers
              </Typography>
              {analysisData.emotionalDrivers && (
                <Grid container spacing={2}>
                  {analysisData.emotionalDrivers.map((driver: any, index: number) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Paper sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="subtitle1" gutterBottom>
                          {driver.type.charAt(0).toUpperCase() + driver.type.slice(1)}
                        </Typography>
                        <Typography
                          variant="h5"
                          color={driver.averageImpact > 0 ? 'success.main' : 'error.main'}
                          gutterBottom
                        >
                          {driver.averageImpact > 0 ? '+' : ''}{driver.averageImpact.toFixed(2)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {driver.count} mentions
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom>
                Sample Comments Analysis
              </Typography>
              {analysisData.sampleComments && (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Comment</TableCell>
                        <TableCell align="center">Sentiment</TableCell>
                        <TableCell align="right">Score</TableCell>
                        <TableCell>Themes</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {analysisData.sampleComments.slice(0, 10).map((comment: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell sx={{ maxWidth: 300 }}>
                            <Typography variant="body2" noWrap>
                              {comment.text}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            {getSentimentIcon(comment.sentiment)}
                          </TableCell>
                          <TableCell align="right">
                            {comment.score.toFixed(2)}
                          </TableCell>
                          <TableCell>
                            <Box display="flex" gap={0.5} flexWrap="wrap">
                              {comment.themes?.slice(0, 2).map((theme: string, idx: number) => (
                                <Chip
                                  key={idx}
                                  label={theme}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </TabPanel>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default EngagementSurveyAnalysis;
