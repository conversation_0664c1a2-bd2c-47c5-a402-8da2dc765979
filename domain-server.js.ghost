const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8080;

// Simple proxy to React dev server
app.use('/', createProxyMiddleware({
  target: 'http://localhost:3080',
  changeOrigin: true,
  ws: true,
  logLevel: 'silent'
}));

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 EHRX Application Server running on port ${PORT}`);
  console.log(`🌐 Access via: http://dev.trusthansen.dk:${PORT}`);
  console.log(`🔗 Local access: http://localhost:${PORT}`);
  console.log(`📱 External access: http://*************:${PORT}`);
});
