const mysql = require('mysql2/promise');

async function populateMonthlySubmissions() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'debian-sys-maint',
      password: 'a500cd12558364e3f91966f73b1fe8ea009531ff669bf74c',
      database: 'ehrx'
    });

    console.log('🔧 Connected to database');

    // Get teams, managers, and KPIs
    const [teams] = await connection.execute('SELECT id, name FROM organizational_units WHERE type = "team"');
    const [managers] = await connection.execute('SELECT id FROM users WHERE role IN ("hr_admin", "manager") LIMIT 3');
    const [kpis] = await connection.execute('SELECT id, name, target_value FROM monthly_dashboard_kpis WHERE is_active = 1');

    if (teams.length === 0 || managers.length === 0 || kpis.length === 0) {
      console.log('❌ Missing required data. Please run populate-monthly-dashboard-data.js first');
      process.exit(1);
    }

    console.log(`📊 Creating submissions for ${teams.length} teams, last 12 months...`);

    // Generate data for last 12 months
    const currentDate = new Date();
    const months = [];
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      months.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        monthName: date.toLocaleString('default', { month: 'long' })
      });
    }

    // Create submissions for each team and month
    for (const team of teams) {
      for (const monthData of months) {
        const managerId = managers[Math.floor(Math.random() * managers.length)].id;
        
        // Create submission
        const [submissionResult] = await connection.execute(`
          INSERT INTO monthly_dashboard_submissions (
            organizational_unit_id, submitted_by_user_id, submission_month, submission_year,
            submission_date, completion_date, status, notes, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          ON DUPLICATE KEY UPDATE updated_at = NOW()
        `, [
          team.id,
          managerId,
          monthData.month,
          monthData.year,
          new Date(monthData.year, monthData.month - 1, Math.floor(Math.random() * 5) + 12), // Submitted between 12-16th
          new Date(monthData.year, monthData.month - 1, Math.floor(Math.random() * 3) + 14), // Completed between 14-16th
          'submitted',
          `${monthData.monthName} ${monthData.year} submission for ${team.name}`
        ]);

        const submissionId = submissionResult.insertId || (await connection.execute(
          'SELECT id FROM monthly_dashboard_submissions WHERE organizational_unit_id = ? AND submission_month = ? AND submission_year = ?',
          [team.id, monthData.month, monthData.year]
        ))[0][0]?.id;

        if (!submissionId) continue;

        // Create KPI values for this submission
        for (const kpi of kpis) {
          let value, targetValue, trafficLightStatus;
          
          // Generate realistic sample data based on KPI type
          switch (kpi.name) {
            case 'FTE':
              value = Math.floor(Math.random() * 15) + 5; // 5-20 people
              targetValue = value; // Auto-calculated, so target = actual
              trafficLightStatus = 'green';
              break;
              
            case 'ATTRITION':
              value = Math.random() * 12; // 0-12% annual rate
              targetValue = kpi.target_value || 5.0;
              break;
              
            case 'SLA':
              value = 85 + Math.random() * 15; // 85-100%
              targetValue = kpi.target_value || 100.0;
              break;
              
            case 'UTILIZATION':
              value = 70 + Math.random() * 25; // 70-95%
              targetValue = kpi.target_value || 85.0;
              break;
              
            case 'TIME_REGISTRATION':
              value = 80 + Math.random() * 20; // 80-100%
              targetValue = kpi.target_value || 100.0;
              break;
              
            case 'COMPLIANCE':
              value = Math.random() < 0.8 ? 0 : Math.floor(Math.random() * 3); // Usually 0, sometimes 1-2
              targetValue = kpi.target_value || 0.0;
              break;
              
            case 'AB':
              value = (Math.random() - 0.5) * 200000; // -100k to +100k variance
              targetValue = kpi.target_value || 0.0;
              break;
              
            case 'PTO':
              value = 60 + Math.random() * 30; // 60-90% of expected
              targetValue = kpi.target_value || 75.0;
              break;
              
            case 'RTO':
              value = 70 + Math.random() * 30; // 70-100%
              targetValue = kpi.target_value || 100.0;
              break;
              
            default:
              value = Math.random() * 100;
              targetValue = kpi.target_value || 50.0;
          }

          // Calculate traffic light status if not set
          if (!trafficLightStatus) {
            if (kpi.name === 'COMPLIANCE') {
              trafficLightStatus = value >= 1 ? 'red' : 'green';
            } else {
              const deviation = targetValue === 0 ? 0 : Math.abs((value - targetValue) / targetValue) * 100;
              if (deviation <= 5) trafficLightStatus = 'green';
              else if (deviation <= 10) trafficLightStatus = 'yellow';
              else trafficLightStatus = 'red';
            }
          }

          // Additional data for complex KPIs
          let additionalData = null;
          if (kpi.name === 'ATTRITION') {
            additionalData = JSON.stringify({
              resignations_this_month: Math.floor(Math.random() * 3),
              annual_rate: value
            });
          } else if (kpi.name === 'PTO') {
            additionalData = JSON.stringify({
              days_taken_this_month: Math.floor(Math.random() * 10) + 1,
              ytd_percentage: value
            });
          } else if (kpi.name === 'RTO') {
            additionalData = JSON.stringify({
              raw_attendance_percentage: value - 5,
              leave_days_adjustment: Math.floor(Math.random() * 5),
              adjusted_percentage: value
            });
          }

          await connection.execute(`
            INSERT INTO monthly_dashboard_kpi_values (
              submission_id, kpi_id, value, target_value, traffic_light_status,
              additional_data, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
            ON DUPLICATE KEY UPDATE 
              value = VALUES(value),
              target_value = VALUES(target_value),
              traffic_light_status = VALUES(traffic_light_status),
              additional_data = VALUES(additional_data),
              updated_at = NOW()
          `, [
            submissionId,
            kpi.id,
            value,
            targetValue,
            trafficLightStatus,
            additionalData
          ]);
        }

        console.log(`✅ Created submission for ${team.name} - ${monthData.monthName} ${monthData.year}`);
      }
    }

    console.log('🎉 All monthly submissions created successfully!');

    await connection.end();
    console.log('🔧 Database connection closed');

  } catch (error) {
    console.error('❌ Error populating monthly submissions:', error.message);
    process.exit(1);
  }
}

populateMonthlySubmissions();
