import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON><PERSON>er,
  CardContent,
  Box,
  Typography,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Collapse,
  Divider,
  Badge,
} from '@mui/material';
import {
  Psychology as AiIcon,
  Lightbulb as InsightIcon,
  TrendingUp as TrendIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Close as DismissIcon,
  ThumbUp as AcknowledgeIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface AiInsightsWidgetProps {
  insights: any[];
  detailed?: boolean;
}

interface Insight {
  id: string;
  category: string;
  message: string;
  confidence: number;
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  acknowledged: boolean;
  dismissed: boolean;
  createdAt: string;
  metadata?: any;
}

const AiInsightsWidget: React.FC<AiInsightsWidgetProps> = ({ insights: initialInsights, detailed = false }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [insights, setInsights] = useState<Insight[]>(initialInsights || []);
  const [expandedInsights, setExpandedInsights] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState<string>('all');

  useEffect(() => {
    if (!initialInsights || initialInsights.length === 0) {
      loadInsights();
    } else {
      setInsights(initialInsights);
    }
  }, [initialInsights]);

  const loadInsights = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ApiService.get('/ai/insights', {
        params: {
          limit: detailed ? 50 : 10,
          includeAcknowledged: detailed,
          includeDismissed: false,
        },
      });

      if (response.data) {
        setInsights(response.data || []);
      }
    } catch (err) {
      console.error('Error loading AI insights:', err);
      setError('Failed to load AI insights');
    } finally {
      setLoading(false);
    }
  };

  const generateNewInsights = async () => {
    try {
      setLoading(true);
      const response = await ApiService.post('/ai/insights/generate');
      if (response.data) {
        await loadInsights();
      }
    } catch (err) {
      console.error('Error generating insights:', err);
      setError('Failed to generate new insights');
    } finally {
      setLoading(false);
    }
  };

  const acknowledgeInsight = async (insightId: string) => {
    try {
      const response = await ApiService.post(`/ai/insights/${insightId}/acknowledge`);
      if (response.data) {
        setInsights(prev => prev.map(insight =>
          insight.id === insightId ? { ...insight, acknowledged: true } : insight
        ));
      }
    } catch (err) {
      console.error('Error acknowledging insight:', err);
    }
  };

  const dismissInsight = async (insightId: string) => {
    try {
      const response = await ApiService.post(`/ai/insights/${insightId}/dismiss`);
      if (response.data) {
        setInsights(prev => prev.filter(insight => insight.id !== insightId));
      }
    } catch (err) {
      console.error('Error dismissing insight:', err);
    }
  };

  const toggleExpanded = (insightId: string) => {
    setExpandedInsights(prev => {
      const newSet = new Set(prev);
      if (newSet.has(insightId)) {
        newSet.delete(insightId);
      } else {
        newSet.add(insightId);
      }
      return newSet;
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'attrition':
        return <WarningIcon color="error" />;
      case 'engagement':
        return <TrendIcon color="primary" />;
      case 'performance':
        return <CheckIcon color="success" />;
      default:
        return <InsightIcon color="primary" />;
    }
  };

  const filteredInsights = insights.filter(insight => {
    if (filter === 'all') return true;
    if (filter === 'actionable') return insight.actionable;
    if (filter === 'high-priority') return insight.priority === 'high';
    return insight.category.toLowerCase() === filter;
  });

  const insightCounts = {
    total: insights.length,
    actionable: insights.filter(i => i.actionable).length,
    highPriority: insights.filter(i => i.priority === 'high').length,
    unacknowledged: insights.filter(i => !i.acknowledged).length,
  };

  if (loading && insights.length === 0) {
    return (
      <Card>
        <CardHeader title="AI Insights" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="AI Insights"
        subheader="AI-generated insights and recommendations for your workforce"
        action={
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={generateNewInsights}
            disabled={loading}
            size="small"
          >
            Generate New
          </Button>
        }
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Insight Summary */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" color="primary">
                {insightCounts.total}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Insights
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Badge badgeContent={insightCounts.unacknowledged} color="error">
                <Typography variant="h6" color="warning.main">
                  {insightCounts.actionable}
                </Typography>
              </Badge>
              <Typography variant="body2" color="text.secondary">
                Actionable
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" color="error">
                {insightCounts.highPriority}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                High Priority
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" color="info.main">
                {insightCounts.unacknowledged}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                New
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Filter Chips */}
        {detailed && (
          <Box sx={{ mb: 2 }}>
            <Box display="flex" gap={1} flexWrap="wrap">
              {['all', 'actionable', 'high-priority', 'attrition', 'engagement', 'performance'].map((filterOption) => (
                <Chip
                  key={filterOption}
                  label={filterOption.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  onClick={() => setFilter(filterOption)}
                  color={filter === filterOption ? 'primary' : 'default'}
                  variant={filter === filterOption ? 'filled' : 'outlined'}
                  size="small"
                />
              ))}
            </Box>
          </Box>
        )}

        {/* Insights List */}
        <List>
          {filteredInsights.slice(0, detailed ? undefined : 5).map((insight, index) => (
            <React.Fragment key={insight.id}>
              <ListItem
                sx={{
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 1,
                  mb: 1,
                  backgroundColor: insight.acknowledged ? 'action.hover' : 'background.paper',
                }}
              >
                <ListItemIcon>
                  {getCategoryIcon(insight.category)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="subtitle2">
                        {insight.message}
                      </Typography>
                      <Chip
                        label={insight.priority}
                        color={getPriorityColor(insight.priority) as any}
                        size="small"
                      />
                      {insight.actionable && (
                        <Chip
                          label="Actionable"
                          color="success"
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Category: {insight.category} | Confidence: {(insight.confidence * 100).toFixed(0)}%
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(insight.createdAt).toLocaleDateString()}
                      </Typography>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Box display="flex" alignItems="center" gap={1}>
                    {!insight.acknowledged && (
                      <IconButton
                        edge="end"
                        onClick={() => acknowledgeInsight(insight.id)}
                        size="small"
                        title="Acknowledge"
                      >
                        <AcknowledgeIcon />
                      </IconButton>
                    )}
                    <IconButton
                      edge="end"
                      onClick={() => dismissInsight(insight.id)}
                      size="small"
                      title="Dismiss"
                    >
                      <DismissIcon />
                    </IconButton>
                    {detailed && (
                      <IconButton
                        edge="end"
                        onClick={() => toggleExpanded(insight.id)}
                        size="small"
                      >
                        {expandedInsights.has(insight.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    )}
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>

              {/* Expanded Details */}
              {detailed && (
                <Collapse in={expandedInsights.has(insight.id)}>
                  <Paper sx={{ p: 2, ml: 4, mr: 4, mb: 1 }}>
                    {insight.metadata && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Additional Details:
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {JSON.stringify(insight.metadata, null, 2)}
                        </Typography>
                      </Box>
                    )}
                  </Paper>
                </Collapse>
              )}

              {index < filteredInsights.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>

        {filteredInsights.length === 0 && (
          <Box textAlign="center" py={4}>
            <AiIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No insights available
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Generate new insights to see AI-powered recommendations
            </Typography>
          </Box>
        )}

        {!detailed && filteredInsights.length > 5 && (
          <Box textAlign="center" mt={2}>
            <Button variant="outlined" size="small">
              View All Insights ({filteredInsights.length})
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default AiInsightsWidget;
