import { Entity, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AssessmentCriteria } from './assessment-criteria.entity';

export enum CriteriaLevelType {
  HR_LEVEL = 'hr_level',
  ORGANIZATIONAL_LEVEL = 'organizational_level',
  TEAM_LEVEL = 'team_level'
}

@Entity('assessment_criteria_levels')
export class AssessmentCriteriaLevel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: CriteriaLevelType,
    name: 'level_type'
  })
  levelType: CriteriaLevelType;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'priority_order', default: 1 })
  priorityOrder: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @OneToMany(() => AssessmentCriteria, criteria => criteria.criteriaLevel)
  criteria: AssessmentCriteria[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
