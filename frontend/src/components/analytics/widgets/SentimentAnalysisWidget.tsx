import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardHeader,
  CardContent,
  Box,
  Typography,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Button,
} from '@mui/material';
import {
  SentimentSatisfied as PositiveIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as NegativeIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Psychology as PsychologyIcon,
  Lightbulb as InsightIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface SentimentAnalysisWidgetProps {
  filters: any;
}

interface SentimentData {
  positive: number;
  neutral: number;
  negative: number;
}

interface Theme {
  theme: string;
  count: number;
  percentage: number;
}

interface EmotionalDriver {
  type: string;
  count: number;
  averageImpact: number;
}

const SentimentAnalysisWidget: React.FC<SentimentAnalysisWidgetProps> = ({ filters }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sentimentTrends, setSentimentTrends] = useState<any>(null);
  const [keyDrivers, setKeyDrivers] = useState<any[]>([]);
  const [recentAnalysis, setRecentAnalysis] = useState<any>(null);

  useEffect(() => {
    loadSentimentData();
  }, [filters]);

  const loadSentimentData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load sentiment trends
      const trendsResponse = await ApiService.get('/ai/sentiment/trends', {
        params: {
          period: filters.timeRange,
          teamId: filters.team !== 'all' ? filters.team : undefined,
        },
      });
      if (trendsResponse.data) {
        setSentimentTrends(trendsResponse.data);
      }

      // Load key drivers
      const driversResponse = await ApiService.get('/ai/sentiment/key-drivers', {
        params: {
          period: filters.timeRange,
        },
      });
      if (driversResponse.data) {
        setKeyDrivers(driversResponse.data || []);
      }

    } catch (err) {
      console.error('Error loading sentiment data:', err);
      setError('Failed to load sentiment analysis data');
    } finally {
      setLoading(false);
    }
  };

  const analyzeSurvey = async (surveyId: number) => {
    try {
      setLoading(true);
      const response = await ApiService.post('/ai/sentiment/analyze-survey', { surveyId });
      if (response.data) {
        setRecentAnalysis(response.data);
      }
    } catch (err) {
      console.error('Error analyzing survey:', err);
      setError('Failed to analyze survey sentiment');
    } finally {
      setLoading(false);
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return '#4caf50';
      case 'negative':
        return '#f44336';
      default:
        return '#ff9800';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return <PositiveIcon sx={{ color: '#4caf50' }} />;
      case 'negative':
        return <NegativeIcon sx={{ color: '#f44336' }} />;
      default:
        return <NeutralIcon sx={{ color: '#ff9800' }} />;
    }
  };

  if (loading && !sentimentTrends) {
    return (
      <Card>
        <CardHeader title="Sentiment Analysis" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Sentiment Analysis"
        subheader="AI-powered analysis of employee sentiment and engagement drivers"
        action={
          <Button
            variant="outlined"
            startIcon={<PsychologyIcon />}
            onClick={() => {/* Trigger new analysis */ }}
            size="small"
          >
            Analyze Latest Survey
          </Button>
        }
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Current Sentiment Overview */}
        {sentimentTrends && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Current Sentiment Score
                </Typography>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="h3" color="primary">
                    {sentimentTrends.currentScore || 0}
                  </Typography>
                  <Box>
                    {sentimentTrends.trend === 'improving' ? (
                      <TrendingUpIcon color="success" />
                    ) : sentimentTrends.trend === 'declining' ? (
                      <TrendingDownIcon color="error" />
                    ) : null}
                    <Typography variant="body2" color="text.secondary">
                      {sentimentTrends.trend || 'stable'} trend
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Based on {sentimentTrends.trendData?.length || 0} recent surveys
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Sentiment Distribution
                </Typography>
                {recentAnalysis?.overallSentiment && (
                  <Box>
                    <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <PositiveIcon sx={{ color: '#4caf50' }} />
                        <Typography variant="body2">Positive</Typography>
                      </Box>
                      <Typography variant="body2">
                        {(recentAnalysis.overallSentiment.positive * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={recentAnalysis.overallSentiment.positive * 100}
                      sx={{ mb: 1, '& .MuiLinearProgress-bar': { backgroundColor: '#4caf50' } }}
                    />

                    <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <NeutralIcon sx={{ color: '#ff9800' }} />
                        <Typography variant="body2">Neutral</Typography>
                      </Box>
                      <Typography variant="body2">
                        {(recentAnalysis.overallSentiment.neutral * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={recentAnalysis.overallSentiment.neutral * 100}
                      sx={{ mb: 1, '& .MuiLinearProgress-bar': { backgroundColor: '#ff9800' } }}
                    />

                    <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <NegativeIcon sx={{ color: '#f44336' }} />
                        <Typography variant="body2">Negative</Typography>
                      </Box>
                      <Typography variant="body2">
                        {(recentAnalysis.overallSentiment.negative * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={recentAnalysis.overallSentiment.negative * 100}
                      sx={{ '& .MuiLinearProgress-bar': { backgroundColor: '#f44336' } }}
                    />
                  </Box>
                )}
              </Paper>
            </Grid>
          </Grid>
        )}

        {/* Key Engagement Drivers */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Key Engagement Drivers
              </Typography>
              <List dense>
                {keyDrivers.slice(0, 5).map((driver, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <InsightIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={driver.driver}
                      secondary={`Impact: ${driver.averageImpact?.toFixed(2)} | Frequency: ${driver.frequency}`}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Top Themes
              </Typography>
              {recentAnalysis?.topThemes && (
                <Box>
                  {recentAnalysis.topThemes.slice(0, 5).map((theme: Theme, index: number) => (
                    <Box key={index} sx={{ mb: 1 }}>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="body2">{theme.theme}</Typography>
                        <Chip
                          label={`${theme.percentage.toFixed(1)}%`}
                          size="small"
                          color="primary"
                        />
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={theme.percentage}
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  ))}
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Emotional Drivers */}
        {recentAnalysis?.emotionalDrivers && (
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Emotional Drivers
            </Typography>
            <Grid container spacing={2}>
              {recentAnalysis.emotionalDrivers.map((driver: EmotionalDriver, index: number) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Box
                    sx={{
                      p: 2,
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      textAlign: 'center',
                    }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      {driver.type.charAt(0).toUpperCase() + driver.type.slice(1)}
                    </Typography>
                    <Typography variant="h6" color={driver.averageImpact > 0 ? 'success.main' : 'error.main'}>
                      {driver.averageImpact > 0 ? '+' : ''}{driver.averageImpact.toFixed(2)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {driver.count} mentions
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        )}

        {/* Sentiment Trend Chart */}
        {sentimentTrends?.trendData && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Sentiment Trends Over Time
            </Typography>
            <Box sx={{ height: 200, display: 'flex', alignItems: 'end', gap: 1 }}>
              {sentimentTrends.trendData.map((point: any, index: number) => (
                <Box
                  key={index}
                  sx={{
                    flex: 1,
                    height: `${(point.score / 100) * 100}%`,
                    backgroundColor: point.score >= 70 ? '#4caf50' : point.score >= 50 ? '#ff9800' : '#f44336',
                    borderRadius: '4px 4px 0 0',
                    minHeight: '20px',
                    position: 'relative',
                  }}
                  title={`${new Date(point.date).toLocaleDateString()}: ${point.score}`}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      position: 'absolute',
                      bottom: -20,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      fontSize: '10px',
                    }}
                  >
                    {new Date(point.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        )}

        {/* AI Insights */}
        {recentAnalysis?.insights && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              AI-Generated Insights
            </Typography>
            {recentAnalysis.insights.map((insight: any, index: number) => (
              <Alert
                key={index}
                severity={insight.priority === 'high' ? 'error' : insight.priority === 'medium' ? 'warning' : 'info'}
                sx={{ mb: 1 }}
              >
                <Typography variant="subtitle2">{insight.message}</Typography>
                <Typography variant="body2">{insight.recommendation}</Typography>
              </Alert>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SentimentAnalysisWidget;
