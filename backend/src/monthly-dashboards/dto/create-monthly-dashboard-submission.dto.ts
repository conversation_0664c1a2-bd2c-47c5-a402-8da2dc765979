import { IsN<PERSON>ber, IsOptional, IsString, IsDateString, IsEnum, IsArray, ValidateNested, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { SubmissionStatus } from '../entities/monthly-dashboard-submission.entity';

export class CreateKpiValueDto {
  @IsNumber()
  kpiId: number;

  @IsOptional()
  @IsNumber()
  value?: number;

  @IsOptional()
  @IsNumber()
  targetValue?: number;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  additionalData?: object;
}

export class CreateMonthlyDashboardSubmissionDto {
  @IsNumber()
  organizationalUnitId: number;

  @IsNumber()
  @Min(1)
  @Max(12)
  submissionMonth: number;

  @IsNumber()
  @Min(2020)
  @Max(2030)
  submissionYear: number;

  @IsOptional()
  @IsDateString()
  completionDate?: string;

  @IsOptional()
  @IsEnum(SubmissionStatus)
  status?: SubmissionStatus;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateKpiValueDto)
  kpiValues?: CreateKpiValueDto[];
}
