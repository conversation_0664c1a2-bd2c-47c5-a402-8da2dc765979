import React, { useState } from 'react';
import { ActionItemStatus } from './ActionItemsDashboard';
import api from '../../services/api';

interface ActionItem {
  id: number;
  title: string;
  description: string;
  status: ActionItemStatus;
  progressPercentage: number;
  dueDate: string;
  estimatedHours?: number;
  actualHours?: number;
  assignedTo: {
    firstName: string;
    lastName: string;
  };
  updates?: ActionItemUpdate[];
}

interface ActionItemUpdate {
  id: number;
  previousProgress: number;
  newProgress: number;
  comments: string;
  hoursLogged?: number;
  updatedBy: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
}

interface ProgressTrackerProps {
  actionItem: ActionItem;
  onProgressUpdate: (updatedItem: ActionItem) => void;
  onClose: () => void;
  canEdit: boolean;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  actionItem,
  onProgressUpdate,
  onClose,
  canEdit
}) => {
  const [progressPercentage, setProgressPercentage] = useState(actionItem.progressPercentage);
  const [comments, setComments] = useState('');
  const [hoursLogged, setHoursLogged] = useState<number | undefined>();
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleProgressUpdate = async () => {
    if (!canEdit) return;

    try {
      setIsUpdating(true);
      setError(null);

      const updateData = {
        progressPercentage,
        comments: comments.trim() || undefined,
        hoursLogged: hoursLogged || undefined
      };

      const response = await api.patch(`/action-items/${actionItem.id}/progress`, updateData);
      
      onProgressUpdate(response.data);
      
      // Reset form
      setComments('');
      setHoursLogged(undefined);
    } catch (err: any) {
      console.error('Error updating progress:', err);
      setError(err.response?.data?.message || 'Failed to update progress');
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusColor = (status: ActionItemStatus) => {
    switch (status) {
      case ActionItemStatus.OPEN:
        return 'bg-gray-100 text-gray-800';
      case ActionItemStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case ActionItemStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case ActionItemStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      case ActionItemStatus.OVERDUE:
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isOverdue = actionItem.dueDate && new Date(actionItem.dueDate) < new Date();

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-3xl shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">Progress Tracker</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Action Item Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-start mb-3">
            <h4 className="text-md font-medium text-gray-900">{actionItem.title}</h4>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(actionItem.status)}`}>
              {actionItem.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>
          
          <p className="text-sm text-gray-600 mb-3">{actionItem.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Assigned to:</span>
              <p className="text-gray-600">{actionItem.assignedTo.firstName} {actionItem.assignedTo.lastName}</p>
            </div>
            
            <div>
              <span className="font-medium text-gray-700">Due Date:</span>
              <p className={`${isOverdue ? 'text-red-600 font-medium' : 'text-gray-600'}`}>
                {actionItem.dueDate ? new Date(actionItem.dueDate).toLocaleDateString() : 'No due date'}
              </p>
            </div>
            
            <div>
              <span className="font-medium text-gray-700">Hours:</span>
              <p className="text-gray-600">
                {actionItem.actualHours || 0} / {actionItem.estimatedHours || 'N/A'}
              </p>
            </div>
          </div>
        </div>

        {/* Current Progress */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Current Progress</span>
            <span className="text-sm text-gray-600">{actionItem.progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-blue-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${actionItem.progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Progress Update Form */}
        {canEdit && (
          <div className="border-t pt-6 mb-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Update Progress</h4>
            
            {error && (
              <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Progress Percentage (0-100)
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={progressPercentage}
                    onChange={(e) => setProgressPercentage(parseInt(e.target.value))}
                    className="flex-1"
                  />
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={progressPercentage}
                    onChange={(e) => setProgressPercentage(parseInt(e.target.value) || 0)}
                    className="w-20 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-600">%</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Hours Logged (optional)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.5"
                  value={hoursLogged || ''}
                  onChange={(e) => setHoursLogged(parseFloat(e.target.value) || undefined)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Comments
                </label>
                <textarea
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  rows={3}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Add comments about your progress..."
                />
              </div>

              <div className="flex justify-end">
                <button
                  onClick={handleProgressUpdate}
                  disabled={isUpdating || progressPercentage === actionItem.progressPercentage}
                  className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${
                    isUpdating || progressPercentage === actionItem.progressPercentage
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {isUpdating ? 'Updating...' : 'Update Progress'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Progress History */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Progress History</h4>
          
          {actionItem.updates && actionItem.updates.length > 0 ? (
            <div className="space-y-4 max-h-64 overflow-y-auto">
              {actionItem.updates.map((update) => (
                <div key={update.id} className="border-l-4 border-blue-500 pl-4 py-2">
                  <div className="flex justify-between items-start mb-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        {update.updatedBy.firstName} {update.updatedBy.lastName}
                      </span>
                      <span className="text-sm text-gray-500">
                        updated progress from {update.previousProgress}% to {update.newProgress}%
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {new Date(update.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  
                  {update.hoursLogged && (
                    <p className="text-sm text-gray-600 mb-1">
                      Logged {update.hoursLogged} hours
                    </p>
                  )}
                  
                  {update.comments && (
                    <p className="text-sm text-gray-700">{update.comments}</p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No progress updates yet.</p>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end pt-6 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
