import { IsString, IsOptional, Is<PERSON><PERSON>ber, IsEnum, IsBoolean, IsObject, Min, Max } from 'class-validator';
import { CalculationMethod } from '../entities/monthly-dashboard-kpi.entity';

export class CreateKpiDto {
  @IsString()
  name: string;

  @IsString()
  displayName: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  targetValue?: number;

  @IsOptional()
  @IsString()
  unit?: string;

  @IsEnum(CalculationMethod)
  calculationMethod: CalculationMethod;

  @IsString()
  helpText: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;

  @IsOptional()
  @IsNumber()
  @Min(-100)
  @Max(100)
  trafficLightGreenMin?: number;

  @IsOptional()
  @IsNumber()
  @Min(-100)
  @Max(100)
  trafficLightGreenMax?: number;

  @IsOptional()
  @IsNumber()
  @Min(-100)
  @Max(100)
  trafficLightYellowMin?: number;

  @IsOptional()
  @IsNumber()
  @Min(-100)
  @Max(100)
  trafficLightYellowMax?: number;

  @IsOptional()
  @IsObject()
  specialRules?: object;
}
