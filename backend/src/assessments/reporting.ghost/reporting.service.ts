import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import * as PDFDocument from 'pdfkit';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
// import { createObjectCsvWriter } from 'csv-writer';
import { AssessmentInstance, AssessmentStatus } from '../entities/assessment-instance.entity';
import { AssessmentResponse } from '../entities/assessment-response.entity';
import { UserRole } from '../../users/entities/user.entity';
import { AssessmentsService } from '../assessments/assessments.service';
import { CsvExportService } from './csv-export.service';

@Injectable()
export class ReportingService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(AssessmentResponse)
    private responseRepository: Repository<AssessmentResponse>,
    private csvExportService: CsvExportService,
    private readonly assessmentsService: AssessmentsService,
  ) {}

  // Generate employee report with assessment summaries and trends
  async generateEmployeeReport(
    employeeId: number,
    startDate: string,
    endDate: string,
    userId: number,
    userRole: string,
  ) {
    const hasPermission = 
      userRole === UserRole.HR_ADMIN || 
      userRole === UserRole.MANAGER;
    
    if (!hasPermission) {
      throw new ForbiddenException('You do not have permission to access this report');
    }

    const dateFilter = this.getDateFilter(startDate, endDate);
    
    const assessments = await this.assessmentRepository.find({
      where: {
        employeeId,
        ...dateFilter,
      },
      relations: ['responses'],
      order: { assessmentDate: 'DESC' },
    });

    if (!assessments.length) {
      return {
        employeeId,
        assessmentCount: 0,
        averageScore: 0,
        scoreHistory: [],
        latestAssessment: null,
      };
    }

    // Calculate metrics
    const averageScore = assessments.reduce((sum, assessment) => 
      sum + Number(assessment.totalScore), 0) / assessments.length;

    const scoreHistory = assessments.map(assessment => ({
      id: assessment.id,
      date: assessment.assessmentDate,
      score: assessment.totalScore,
      status: assessment.status,
    }));

    return {
      employeeId,
      assessmentCount: assessments.length,
      averageScore,
      scoreHistory,
      latestAssessment: scoreHistory[0],
    };
  }

  // Generate team performance report
  async generateTeamReport(
    teamId: number,
    startDate: string,
    endDate: string,
    userId: number,
    userRole: string,
  ) {
    const hasPermission = 
      userRole === UserRole.HR_ADMIN || 
      userRole === UserRole.MANAGER;
    
    if (!hasPermission) {
      throw new ForbiddenException('You do not have permission to access this report');
    }

    const dateFilter = this.getDateFilter(startDate, endDate);
    
    // This would require a join with users table to get team members
    // For now we'll simulate with a query that would join users and filter by teamId
    const assessments = await this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.responses', 'response')
      .leftJoin('users', 'user', 'assessment.employeeId = user.id')
      .where('user.teamId = :teamId', { teamId })
      .andWhere(startDate && endDate ? 'assessment.assessmentDate BETWEEN :startDate AND :endDate' : '1=1', {
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
      })
      .orderBy('assessment.assessmentDate', 'DESC')
      .getMany();

    if (!assessments.length) {
      return {
        teamId,
        assessmentCount: 0,
        averageScore: 0,
        employeeScores: [],
        performanceDistribution: {
          excellent: 0,
          good: 0,
          satisfactory: 0,
          needsImprovement: 0,
          poor: 0,
        },
      };
    }

    // Calculate metrics
    const averageScore = assessments.reduce((sum, assessment) => 
      sum + Number(assessment.totalScore), 0) / assessments.length;

    // Group by employee
    const employeeScores = this.groupBy(assessments, 'employeeId')
      .map(group => ({
        employeeId: group[0].employeeId,
        averageScore: group.reduce((sum, a) => sum + Number(a.totalScore), 0) / group.length,
        assessmentCount: group.length,
      }));

    // Calculate performance distribution
    const performanceDistribution = this.calculatePerformanceDistribution(assessments);

    return {
      teamId,
      assessmentCount: assessments.length,
      averageScore,
      employeeScores,
      performanceDistribution,
    };
  }

  // Generate organization-wide report (HR Admin only)
  async generateOrganizationReport(
    startDate: string,
    endDate: string,
    userId: number,
    userRole: string,
  ) {
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR Administrators can access organization reports');
    }

    const dateFilter = this.getDateFilter(startDate, endDate);
    
    const assessments = await this.assessmentRepository.find({
      where: dateFilter,
      relations: ['responses'],
      order: { assessmentDate: 'DESC' },
    });

    if (!assessments.length) {
      return {
        assessmentCount: 0,
        averageScore: 0,
        completionRate: 0,
        statusBreakdown: {
          draft: 0,
          inProgress: 0,
          completed: 0,
          approved: 0,
          rejected: 0,
        },
        performanceDistribution: {
          excellent: 0,
          good: 0,
          satisfactory: 0,
          needsImprovement: 0,
          poor: 0,
        },
      };
    }

    // Calculate metrics
    const averageScore = assessments.reduce((sum, assessment) => 
      sum + Number(assessment.totalScore), 0) / assessments.length;

    // Calculate status breakdown
    const statusBreakdown = this.calculateStatusBreakdown(assessments);
    
    // Calculate completion rate (completed + approved / total)
    const completedCount = statusBreakdown.completed + statusBreakdown.approved;
    const completionRate = completedCount / assessments.length;

    // Calculate performance distribution
    const performanceDistribution = this.calculatePerformanceDistribution(assessments);

    return {
      assessmentCount: assessments.length,
      averageScore,
      completionRate,
      statusBreakdown,
      performanceDistribution,
    };
  }

  // Generate PDF for a single assessment
  async generateAssessmentPdf(
    id: number,
    userId: number,
    userRole: string,
  ) {
    // Get assessment with permission check
    const assessment = await this.assessmentsService.findOne(id, userId, userRole);
    
    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    const responses = await this.responseRepository.find({
      where: { assessmentId: id },
    });

    // Create temporary file path
    const tempDir = os.tmpdir();
    const filename = `assessment_${id}_${Date.now()}.pdf`;
    const filePath = path.join(tempDir, filename);
    
    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    const writeStream = fs.createWriteStream(filePath);
    doc.pipe(writeStream);
    
    // Add content to PDF
    doc.fontSize(25).text('Performance Assessment Report', { align: 'center' });
    doc.moveDown();
    doc.fontSize(14).text(`Assessment ID: ${assessment.id}`);
    doc.fontSize(14).text(`Date: ${assessment.assessmentDate.toDateString()}`);
    doc.fontSize(14).text(`Template: ${(assessment.templateSnapshot as any)?.name || 'Unknown'}`);
    doc.fontSize(14).text(`Status: ${assessment.status}`);
    doc.moveDown();
    doc.fontSize(16).text('Overall Score', { underline: true });
    doc.fontSize(14).text(`${(assessment.totalScore * 100).toFixed(2)}%`);
    doc.moveDown();

    // Add assessment areas and responses
    doc.fontSize(16).text('Assessment Areas', { underline: true });
    doc.moveDown();

    for (const response of responses) {
      doc.fontSize(14).text((response.areaSnapshot as any)?.name || 'Unknown Area');
      doc.fontSize(12).text(`Weight: ${response.areaWeight}`);
      doc.fontSize(12).text(`Score: ${response.score} / ${(response.areaSnapshot as any)?.maxScore || 0}`);
      doc.fontSize(12).text(`Weighted Score: ${(response.weightedScore * 100).toFixed(2)}%`);
      
      if (response.evaluatorComments) {
        doc.fontSize(12).text('Evaluator Comments:', { underline: true });
        doc.fontSize(10).text(response.evaluatorComments);
      }
      
      if (response.employeeComments) {
        doc.fontSize(12).text('Employee Comments:', { underline: true });
        doc.fontSize(10).text(response.employeeComments);
      }
      
      doc.moveDown();
    }
    
    // Add notes if available
    if (assessment.notes) {
      doc.fontSize(16).text('Additional Notes', { underline: true });
      doc.fontSize(12).text(assessment.notes);
    }
    
    // Finalize the PDF
    doc.end();
    
    // Wait for PDF to be written
    await new Promise((resolve) => writeStream.on('finish', resolve));
    
    // Read the file and return it
    const file = fs.readFileSync(filePath);
    
    // Clean up temporary file
    fs.unlinkSync(filePath);
    
    return { 
      file,
      filename: `Assessment_${assessment.id}_${assessment.assessmentDate.toISOString().split('T')[0]}.pdf`,
    };
  }

  private async getFilteredAssessments(filters: any, userId: number, userRole: string): Promise<AssessmentInstance[]> {
    const query = this.assessmentRepository.createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.employee', 'employee')
      .leftJoinAndSelect('assessment.evaluator', 'evaluator')
      .leftJoinAndSelect('employee.organizationalUnit', 'organizationalUnit');

    // Apply role-based filtering
    if (userRole !== UserRole.HR_ADMIN) {
      query.andWhere('assessment.evaluatorId = :userId', { userId });
    }

    // Apply additional filters if provided
    if (filters.status) {
      query.andWhere('assessment.status = :status', { status: filters.status });
    }

    if (filters.employeeId) {
      query.andWhere('assessment.employeeId = :employeeId', { employeeId: filters.employeeId });
    }

    if (filters.templateId) {
      query.andWhere('assessment.templateId = :templateId', { templateId: filters.templateId });
    }

    return query.getMany();
  }

  // Export assessment data as JSON
  async exportAssessmentsCsv(
    filters: any,
    userId: number,
    userRole: string
  ): Promise<{ file: Buffer; filename: string }> {
    // Use CSV export service to generate the file with filters
    return await this.csvExportService.exportAssessmentsCsv({
      startDate: filters.startDate,
      endDate: filters.endDate,
      employeeIds: filters.employeeIds,
      teamIds: filters.teamIds,
      statuses: filters.statuses
    });
  }

  async exportAssessmentJson(
    id: number,
    userId: number,
    userRole: string,
  ) {
    // Get assessment with permission check
    const assessment = await this.assessmentsService.findOne(id, userId, userRole);
    
    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    const responses = await this.responseRepository.find({
      where: { assessmentId: id },
    });

    const assessmentData = {
      ...assessment,
      responses,
    };

    return { 
      data: assessmentData,
      filename: `Assessment_${assessment.id}_${assessment.assessmentDate.toISOString().split('T')[0]}.json`,
    };
  }

  // Additional helper methods
  private getDateFilter(startDate?: string, endDate?: string) {
    if (startDate && endDate) {
      return {
        assessmentDate: Between(new Date(startDate), new Date(endDate)),
      };
    }
    return {};
  }

  private calculateStatusBreakdown(assessments: AssessmentInstance[]) {
    const statusBreakdown = {
      draft: 0,
      inProgress: 0,
      completed: 0,
      approved: 0,
      rejected: 0,
    };

    for (const assessment of assessments) {
      switch (assessment.status.toLowerCase()) {
        case 'draft':
          statusBreakdown.draft++;
          break;
        case 'in_progress':
          statusBreakdown.inProgress++;
          break;
        case 'completed':
          statusBreakdown.completed++;
          break;
        case 'approved':
          statusBreakdown.approved++;
          break;
        case 'rejected':
          statusBreakdown.rejected++;
          break;
      }
    }

    return statusBreakdown;
  }

  private calculatePerformanceDistribution(assessments: AssessmentInstance[]) {
    const distribution = {
      excellent: 0,
      good: 0,
      satisfactory: 0,
      needsImprovement: 0,
      poor: 0,
    };

    for (const assessment of assessments) {
      const score = assessment.totalScore;
      
      if (score >= 0.9) {
        distribution.excellent++;
      } else if (score >= 0.8) {
        distribution.good++;
      } else if (score >= 0.7) {
        distribution.satisfactory++;
      } else if (score >= 0.6) {
        distribution.needsImprovement++;
      } else {
        distribution.poor++;
      }
    }

    return distribution;
  }

  private groupBy(array: any[], key: string): any[][] {
    return Object.values(
      array.reduce((result, item) => {
        const groupKey = item[key];
        if (!result[groupKey]) {
          result[groupKey] = [];
        }
        result[groupKey].push(item);
        return result;
      }, {} as Record<string, any[]>)
    );
  }

  async getDashboardMetrics(
    filters: any,
    userId: number,
    userRole: string
  ): Promise<any> {
    const assessments = await this.getFilteredAssessments(filters, userId, userRole);

    return {
      totalAssessments: assessments.length,
      completedAssessments: assessments.filter(a => a.status === AssessmentStatus.COMPLETED || a.status === AssessmentStatus.APPROVED).length,
      pendingAssessments: assessments.filter(a => a.status === AssessmentStatus.DRAFT || a.status === AssessmentStatus.IN_PROGRESS).length,
      averageScore: assessments.length > 0
        ? assessments.reduce((sum, a) => sum + a.totalScore, 0) / assessments.length
        : 0,
      assessmentsByStatus: this.groupBy(assessments, 'status').map(group => ({
        status: group[0]?.status,
        count: group.length
      }))
    };
  }

  async getPerformanceTrends(
    filters: any,
    userId: number,
    userRole: string
  ): Promise<any> {
    const assessments = await this.getFilteredAssessments(filters, userId, userRole);

    // Group by month for trend analysis
    const monthlyData = assessments.reduce((acc, assessment) => {
      const month = new Date(assessment.assessmentDate).toISOString().substring(0, 7); // YYYY-MM
      if (!acc[month]) {
        acc[month] = { month, assessments: [], totalScore: 0, count: 0 };
      }
      acc[month].assessments.push(assessment);
      acc[month].totalScore += assessment.totalScore;
      acc[month].count++;
      return acc;
    }, {} as Record<string, any>);

    return Object.values(monthlyData).map((data: any) => ({
      month: data.month,
      averageScore: data.count > 0 ? data.totalScore / data.count : 0,
      assessmentCount: data.count
    }));
  }

  async getTeamBenchmarks(
    filters: any,
    userId: number,
    userRole: string
  ): Promise<any> {
    const assessments = await this.getFilteredAssessments(filters, userId, userRole);

    // Group by organizational unit
    const teamData = assessments.reduce((acc, assessment) => {
      const teamId = assessment.employee?.organizationalUnitId || 'unassigned';
      if (!acc[teamId]) {
        acc[teamId] = {
          teamId,
          teamName: assessment.employee?.organizationalUnit?.name || 'Unassigned',
          assessments: [],
          totalScore: 0,
          count: 0
        };
      }
      acc[teamId].assessments.push(assessment);
      acc[teamId].totalScore += assessment.totalScore;
      acc[teamId].count++;
      return acc;
    }, {} as Record<string, any>);

    return Object.values(teamData).map((team: any) => ({
      teamId: team.teamId,
      teamName: team.teamName,
      averageScore: team.count > 0 ? team.totalScore / team.count : 0,
      assessmentCount: team.count,
      topPerformers: team.assessments
        .sort((a: any, b: any) => b.totalScore - a.totalScore)
        .slice(0, 3)
        .map((a: any) => ({
          employeeName: `${a.employee?.firstName} ${a.employee?.lastName}`,
          score: a.totalScore
        }))
    }));
  }
}
