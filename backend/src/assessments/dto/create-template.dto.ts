import { IsNotEmpty, IsString, IsOptional, IsBoolean, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateAssessmentAreaDto } from './create-assessment-area.dto';

export class CreateTemplateDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isGlobal?: boolean;

  @IsOptional()
  parentTemplateId?: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAssessmentAreaDto)
  areas: CreateAssessmentAreaDto[];
}
