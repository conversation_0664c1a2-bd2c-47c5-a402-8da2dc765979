import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('roles')
export class RolesController {

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.CEO)
  async getRoles() {
    // Return all available user roles for Settings menu
    const roles = [
      {
        id: 'ceo',
        name: 'CEO',
        description: 'Chief Executive Officer',
        level: 'executive',
        permissions: ['all']
      },
      {
        id: 'vp',
        name: 'Vice President',
        description: 'Vice President',
        level: 'executive',
        permissions: ['manage_departments', 'view_all_reports', 'manage_users']
      },
      {
        id: 'director',
        name: 'Director',
        description: 'Department Director',
        level: 'management',
        permissions: ['manage_department', 'view_department_reports', 'manage_team_leads']
      },
      {
        id: 'manager',
        name: 'Manager',
        description: 'Team Manager',
        level: 'management',
        permissions: ['manage_team', 'view_team_reports', 'conduct_assessments']
      },
      {
        id: 'senior_engineer',
        name: 'Senior Engineer',
        description: 'Senior Software Engineer',
        level: 'senior',
        permissions: ['mentor_juniors', 'technical_decisions', 'code_reviews']
      },
      {
        id: 'engineer',
        name: 'Engineer',
        description: 'Software Engineer',
        level: 'individual',
        permissions: ['develop_features', 'participate_assessments']
      },
      {
        id: 'junior_engineer',
        name: 'Junior Engineer',
        description: 'Junior Software Engineer',
        level: 'individual',
        permissions: ['develop_features', 'participate_assessments']
      },
      {
        id: 'intern',
        name: 'Intern',
        description: 'Software Engineering Intern',
        level: 'trainee',
        permissions: ['learn', 'participate_assessments']
      },
      {
        id: 'hr_admin',
        name: 'HR Administrator',
        description: 'Human Resources Administrator',
        level: 'admin',
        permissions: ['manage_hr', 'view_all_assessments', 'manage_templates']
      },
      {
        id: 'guest',
        name: 'Guest',
        description: 'Guest User',
        level: 'limited',
        permissions: ['view_public']
      },
      {
        id: 'employee',
        name: 'Employee',
        description: 'General Employee',
        level: 'individual',
        permissions: ['participate_assessments', 'view_own_data']
      }
    ];

    return {
      success: true,
      data: roles,
      total: roles.length
    };
  }
}
