import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  TextField,
  Autocomplete,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Snackbar
} from '@mui/material';
import { ApiService, OrganizationalUnit, User, CreateUserDto, CreateOrganizationalUnitDto } from '../../services/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const OrganizationalManagement: React.FC = () => {
  // State Management
  const [organizationalUnits, setOrganizationalUnits] = useState<OrganizationalUnit[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal States
  const [editingUnit, setEditingUnit] = useState<OrganizationalUnit | null>(null);
  const [showMemberSelector, setShowMemberSelector] = useState<{ unit: OrganizationalUnit; unitId: number } | null>(null);
  const [showAddUnit, setShowAddUnit] = useState<{ parentId?: number } | null>(null);

  // Tab State
  const [editModalTab, setEditModalTab] = useState(0);

  // Form States
  const [newMemberForm, setNewMemberForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    title: '',
    role: 'engineer',
    password: 'defaultPassword123'
  });

  // Available options for dropdowns
  const availableRoles = ['ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest'];
  const availableTitles = [
    'Chief Executive Officer', 'Chief Technology Officer', 'VP of Technology', 'VP of Engineering',
    'Director of Engineering', 'Engineering Manager', 'Senior Frontend Engineer', 'Senior Backend Engineer',
    'Frontend Developer', 'Backend Developer', 'Full Stack Developer', 'DevOps Engineer',
    'Data Engineer', 'Security Engineer', 'QA Engineer', 'Product Manager', 'Project Manager',
    'HR Manager', 'Office Manager', 'Secretary', 'Administrative Assistant'
  ];

  // Load Data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [unitsData, usersData] = await Promise.all([
        ApiService.getOrganizationalUnits(),
        ApiService.getUsers()
      ]);

      setOrganizationalUnits(unitsData);
      setUsers(usersData);
    } catch (err: any) {
      setError(err.message || 'Failed to load data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Unit Management Functions
  const handleCreateUnit = async (data: CreateOrganizationalUnitDto) => {
    try {
      const newUnit = await ApiService.createOrganizationalUnit(data);
      setOrganizationalUnits(prev => [...prev, newUnit]);
      setShowAddUnit(null);
      setSuccess('Unit created successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to create unit');
    }
  };

  const handleUpdateUnit = async (id: number, data: Partial<CreateOrganizationalUnitDto>) => {
    try {
      const updatedUnit = await ApiService.updateOrganizationalUnit(id, data);
      setOrganizationalUnits(prev =>
        prev.map(unit => unit.id === id ? updatedUnit : unit)
      );
      setEditingUnit(null);
      setSuccess('Unit updated successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to update unit');
    }
  };

  const handleDeleteUnit = async (id: number) => {
    try {
      await ApiService.deleteOrganizationalUnit(id);
      setOrganizationalUnits(prev => prev.filter(unit => unit.id !== id));
      setEditingUnit(null);
      setSuccess('Unit deleted successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to delete unit');
    }
  };

  // Member Management Functions
  const handleCreateMember = async (data: CreateUserDto, unitId: number) => {
    try {
      const memberData = {
        ...data,
        organizationalUnitId: unitId
      };

      const newMember = await ApiService.createUser(memberData);
      setUsers(prev => [...prev, newMember]);
      setShowMemberSelector(null);
      setNewMemberForm({
        firstName: '',
        lastName: '',
        email: '',
        title: '',
        role: 'engineer',
        password: 'defaultPassword123'
      });
      setSuccess('Member created and added successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to create member');
    }
  };

  const handleAssignExistingMember = async (memberId: number, unitId: number) => {
    try {
      await ApiService.updateUser(memberId, { organizationalUnitId: unitId });

      // Update local state
      setUsers(prev =>
        prev.map(user =>
          user.id === memberId
            ? { ...user, organizationalUnitId: unitId }
            : user
        )
      );

      setShowMemberSelector(null);
      setSuccess('Member assigned successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to assign member');
    }
  };

  const handleRemoveMember = async (memberId: number) => {
    try {
      await ApiService.updateUser(memberId, { organizationalUnitId: undefined });

      // Update local state
      setUsers(prev =>
        prev.map(user =>
          user.id === memberId
            ? { ...user, organizationalUnitId: undefined }
            : user
        )
      );

      setSuccess('Member removed successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to remove member');
    }
  };

  // Helper Functions
  const getUnitMembers = (unitId: number): User[] => {
    return users.filter(user => user.organizationalUnitId === unitId);
  };

  const getAvailableMembers = (unitId: number): User[] => {
    return users.filter(user => !user.organizationalUnitId || user.organizationalUnitId !== unitId);
  };

  const getUnitChildren = (parentId: number): OrganizationalUnit[] => {
    return organizationalUnits.filter(unit => unit.parentId === parentId);
  };

  const getRootUnits = (): OrganizationalUnit[] => {
    return organizationalUnits.filter(unit => !unit.parentId);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading organizational data...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
        🏢 Organizational Management
      </Typography>
      <Typography variant="h6" gutterBottom color="text.secondary" sx={{ mb: 4 }}>
        Enterprise Database-Connected Team & Member Management
      </Typography>

      {/* Action Buttons */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={() => setShowAddUnit({ parentId: undefined })}
        >
          ➕ Add Root Unit
        </Button>
        <Button
          variant="outlined"
          onClick={loadData}
        >
          🔄 Refresh Data
        </Button>
      </Box>

      {/* Organizational Units Grid */}
      <Grid container spacing={3}>
        {getRootUnits().map((unit) => (
          <Grid item xs={12} sm={6} md={4} key={unit.id}>
            <Card elevation={3} sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    {unit.name}
                  </Typography>
                  <Chip label={unit.type} color="primary" size="small" />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {unit.description || 'No description'}
                </Typography>

                <Typography variant="body2" sx={{ mb: 1 }}>
                  👥 {getUnitMembers(unit.id).length} members
                </Typography>

                <Typography variant="body2" sx={{ mb: 2 }}>
                  💰 Budget: ${unit.budget?.toLocaleString() || '0'}
                </Typography>

                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button
                    size="small"
                    variant="contained"
                    color="primary"
                    onClick={() => setEditingUnit(unit)}
                  >
                    ✏️ Edit
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => setShowAddUnit({ parentId: unit.id })}
                  >
                    ➕ Add Sub-unit
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Edit Unit Modal */}
      {editingUnit && (
        <Dialog open={true} onClose={() => setEditingUnit(null)} maxWidth="md" fullWidth>
          <DialogTitle>
            ✏️ Edit {editingUnit.name}
          </DialogTitle>
          <DialogContent>
            <Tabs value={editModalTab} onChange={(e, newValue) => setEditModalTab(newValue)}>
              <Tab label="📋 Basic Info" />
              <Tab label="👥 Members" />
              <Tab label="🏢 Subunits" />
            </Tabs>

            <TabPanel value={editModalTab} index={0}>
              <Box sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="Unit Name"
                  defaultValue={editingUnit.name}
                  id="edit-unit-name"
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Description"
                  defaultValue={editingUnit.description}
                  id="edit-unit-description"
                  multiline
                  rows={3}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Budget"
                  type="number"
                  defaultValue={editingUnit.budget}
                  id="edit-unit-budget"
                  sx={{ mb: 2 }}
                />
              </Box>
            </TabPanel>

            <TabPanel value={editModalTab} index={1}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Current Members ({getUnitMembers(editingUnit.id).length})
              </Typography>
              <Grid container spacing={2}>
                {getUnitMembers(editingUnit.id).map((member) => (
                  <Grid item xs={12} sm={6} key={member.id}>
                    <Card variant="outlined">
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {member.firstName} {member.lastName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {member.title} • {member.role}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          📧 {member.email}
                        </Typography>
                        <Box sx={{ mt: 1 }}>
                          <Button
                            size="small"
                            color="error"
                            onClick={() => handleRemoveMember(member.id)}
                          >
                            Remove
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}

                {/* Add Member Button */}
                <Grid item xs={12} sm={6}>
                  <Card
                    variant="outlined"
                    sx={{
                      border: '2px dashed #ccc',
                      cursor: 'pointer',
                      '&:hover': { borderColor: '#2196f3' }
                    }}
                    onClick={() => setShowMemberSelector({ unit: editingUnit, unitId: editingUnit.id })}
                  >
                    <CardContent sx={{ textAlign: 'center', p: 3 }}>
                      <Typography variant="h4" sx={{ mb: 1 }}>➕</Typography>
                      <Typography variant="body2" color="primary" sx={{ fontWeight: 'bold' }}>
                        Add Member
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={editModalTab} index={2}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Subunits ({getUnitChildren(editingUnit.id).length})
              </Typography>
              <Grid container spacing={2}>
                {getUnitChildren(editingUnit.id).map((subunit) => (
                  <Grid item xs={12} sm={6} key={subunit.id}>
                    <Card variant="outlined">
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {subunit.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {subunit.type} • {getUnitMembers(subunit.id).length} members
                        </Typography>
                        <Box sx={{ mt: 1 }}>
                          <Button
                            size="small"
                            onClick={() => setEditingUnit(subunit)}
                          >
                            Edit
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}

                {/* Add Subunit Button */}
                <Grid item xs={12} sm={6}>
                  <Card
                    variant="outlined"
                    sx={{
                      border: '2px dashed #ccc',
                      cursor: 'pointer',
                      '&:hover': { borderColor: '#2196f3' }
                    }}
                    onClick={() => setShowAddUnit({ parentId: editingUnit.id })}
                  >
                    <CardContent sx={{ textAlign: 'center', p: 3 }}>
                      <Typography variant="h4" sx={{ mb: 1 }}>➕</Typography>
                      <Typography variant="body2" color="primary" sx={{ fontWeight: 'bold' }}>
                        Add Subunit
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>
          </DialogContent>
          <DialogActions>
            {editModalTab === 0 && (
              <>
                <Button
                  color="error"
                  onClick={() => {
                    if (confirm(`Delete "${editingUnit.name}"? This will remove all subunits and members.`)) {
                      handleDeleteUnit(editingUnit.id);
                    }
                  }}
                >
                  🗑️ Delete Unit
                </Button>
                <Button onClick={() => setEditingUnit(null)}>Cancel</Button>
                <Button
                  variant="contained"
                  onClick={() => {
                    const name = (document.getElementById('edit-unit-name') as HTMLInputElement)?.value;
                    const description = (document.getElementById('edit-unit-description') as HTMLInputElement)?.value;
                    const budget = (document.getElementById('edit-unit-budget') as HTMLInputElement)?.value;

                    if (name) {
                      handleUpdateUnit(editingUnit.id, {
                        name,
                        description,
                        budget: budget ? parseFloat(budget) : editingUnit.budget
                      });
                    }
                  }}
                >
                  Save Changes
                </Button>
              </>
            )}
            {editModalTab !== 0 && (
              <Button variant="contained" onClick={() => setEditingUnit(null)}>
                Close
              </Button>
            )}
          </DialogActions>
        </Dialog>
      )}

      {/* Member Selector Modal */}
      {showMemberSelector && (
        <Dialog open={true} onClose={() => setShowMemberSelector(null)} maxWidth="md" fullWidth>
          <DialogTitle>
            👥 Add Member to {showMemberSelector.unit.name}
          </DialogTitle>
          <DialogContent>
            <Tabs value={0}>
              <Tab label="📋 Select Existing Member" />
              <Tab label="➕ Create New Member" />
            </Tabs>

            {/* Existing Members */}
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Available Members ({getAvailableMembers(showMemberSelector.unitId).length})
              </Typography>
              <Grid container spacing={2} sx={{ maxHeight: '300px', overflow: 'auto' }}>
                {getAvailableMembers(showMemberSelector.unitId).map((member) => (
                  <Grid item xs={12} sm={6} key={member.id}>
                    <Card
                      variant="outlined"
                      sx={{ cursor: 'pointer', '&:hover': { borderColor: '#2196f3' } }}
                      onClick={() => handleAssignExistingMember(member.id, showMemberSelector.unitId)}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {member.firstName} {member.lastName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {member.title} • {member.role}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          📧 {member.email}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
                Create New Member
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="First Name *"
                    value={newMemberForm.firstName}
                    onChange={(e) => setNewMemberForm(prev => ({ ...prev, firstName: e.target.value }))}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Last Name *"
                    value={newMemberForm.lastName}
                    onChange={(e) => setNewMemberForm(prev => ({ ...prev, lastName: e.target.value }))}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email *"
                    type="email"
                    value={newMemberForm.email}
                    onChange={(e) => setNewMemberForm(prev => ({ ...prev, email: e.target.value }))}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    options={availableTitles}
                    value={newMemberForm.title}
                    onChange={(event, newValue) => {
                      setNewMemberForm(prev => ({ ...prev, title: newValue || '' }));
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Job Title *"
                        placeholder="Search titles..."
                      />
                    )}
                    freeSolo
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    options={availableRoles}
                    value={newMemberForm.role}
                    onChange={(event, newValue) => {
                      setNewMemberForm(prev => ({ ...prev, role: newValue || 'engineer' }));
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Role"
                        placeholder="Search roles..."
                      />
                    )}
                    getOptionLabel={(option) => option.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  />
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowMemberSelector(null)}>Cancel</Button>
            <Button
              variant="contained"
              onClick={() => {
                const { firstName, lastName, email, title, role, password } = newMemberForm;
                if (firstName && lastName && email && title) {
                  handleCreateMember({
                    firstName,
                    lastName,
                    email,
                    title,
                    role,
                    password
                  }, showMemberSelector.unitId);
                } else {
                  setError('Please fill in all required fields');
                }
              }}
            >
              Create & Add Member
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Add Unit Modal */}
      {showAddUnit && (
        <Dialog open={true} onClose={() => setShowAddUnit(null)} maxWidth="sm" fullWidth>
          <DialogTitle>
            ➕ Add New {showAddUnit.parentId ? 'Sub-unit' : 'Root Unit'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <TextField
                fullWidth
                label="Unit Name *"
                id="new-unit-name"
                sx={{ mb: 2 }}
              />
              <Autocomplete
                options={['organization', 'division', 'department', 'team', 'squad', 'unit']}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Unit Type *"
                    id="new-unit-type"
                  />
                )}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Description"
                id="new-unit-description"
                multiline
                rows={3}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Budget"
                type="number"
                id="new-unit-budget"
                sx={{ mb: 2 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowAddUnit(null)}>Cancel</Button>
            <Button
              variant="contained"
              onClick={() => {
                const name = (document.getElementById('new-unit-name') as HTMLInputElement)?.value;
                const type = (document.getElementById('new-unit-type') as HTMLInputElement)?.value;
                const description = (document.getElementById('new-unit-description') as HTMLInputElement)?.value;
                const budget = (document.getElementById('new-unit-budget') as HTMLInputElement)?.value;

                if (name && type) {
                  handleCreateUnit({
                    name,
                    type: type as "organization" | "division" | "department" | "team" | "squad" | "unit",
                    description,
                    parentId: showAddUnit.parentId,
                    budget: budget ? parseFloat(budget) : 0
                  });
                } else {
                  setError('Please fill in required fields: Name and Type');
                }
              }}
            >
              Create Unit
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Success/Error Messages */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default OrganizationalManagement;
