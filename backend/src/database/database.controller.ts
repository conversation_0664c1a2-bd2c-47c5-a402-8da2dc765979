import { Controller, Get, Post, Put, Delete, Param, Body, UseGuards, Query } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { DatabaseService } from './database.service';

@Controller('database')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.HR_ADMIN) // Only HR admins can access database management
export class DatabaseController {
  constructor(private readonly databaseService: DatabaseService) {}

  @Get('tables/:tableName')
  async getTableData(
    @Param('tableName') tableName: string,
    @Query('page') page = 1,
    @Query('limit') limit = 100
  ) {
    return this.databaseService.getTableData(tableName, +page, +limit);
  }

  @Get('tables/:tableName/schema')
  async getTableSchema(@Param('tableName') tableName: string) {
    return this.databaseService.getTableSchema(tableName);
  }

  @Post('tables/:tableName')
  async createRecord(
    @Param('tableName') tableName: string,
    @Body() recordData: any
  ) {
    return this.databaseService.createRecord(tableName, recordData);
  }

  @Put('tables/:tableName/:id')
  async updateRecord(
    @Param('tableName') tableName: string,
    @Param('id') id: string,
    @Body() recordData: any
  ) {
    return this.databaseService.updateRecord(tableName, +id, recordData);
  }

  @Delete('tables/:tableName/:id')
  async deleteRecord(
    @Param('tableName') tableName: string,
    @Param('id') id: string
  ) {
    return this.databaseService.deleteRecord(tableName, +id);
  }

  @Get('tables')
  async getAvailableTables() {
    return this.databaseService.getAvailableTables();
  }
}
