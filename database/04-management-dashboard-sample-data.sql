-- Management Dashboard Sample Data
-- This file populates the new management dashboard tables with realistic sample data

USE ehrx;

-- Insert sample analytics dashboards
INSERT INTO analytics_dashboards (user_id, name, description, layout_config, widget_config, is_default, is_shared) VALUES
(1, 'HR Executive Dashboard', 'Comprehensive HR analytics for executive leadership', 
 '{"layout": "grid", "columns": 3, "rows": 4}',
 '{"widgets": [{"type": "performance_overview", "position": {"x": 0, "y": 0, "w": 2, "h": 1}}, {"type": "attrition_risk", "position": {"x": 2, "y": 0, "w": 1, "h": 1}}, {"type": "engagement_trends", "position": {"x": 0, "y": 1, "w": 3, "h": 2}}]}',
 TRUE, TRUE),
(2, 'Team Manager Dashboard', 'Team performance and engagement tracking',
 '{"layout": "grid", "columns": 2, "rows": 3}',
 '{"widgets": [{"type": "team_performance", "position": {"x": 0, "y": 0, "w": 1, "h": 1}}, {"type": "recognition_feed", "position": {"x": 1, "y": 0, "w": 1, "h": 2}}]}',
 TRUE, FALSE),
(3, 'Individual Performance Dashboard', 'Personal performance and development tracking',
 '{"layout": "grid", "columns": 2, "rows": 2}',
 '{"widgets": [{"type": "my_performance", "position": {"x": 0, "y": 0, "w": 1, "h": 1}}, {"type": "career_path", "position": {"x": 1, "y": 0, "w": 1, "h": 1}}]}',
 TRUE, FALSE);

-- Insert sample performance metrics
INSERT INTO performance_metrics (user_id, organizational_unit_id, metric_type, metric_name, metric_value, period_start, period_end, metadata) VALUES
-- Individual metrics (using existing user IDs)
(1, 1, 'individual', 'overall_performance_score', 8.5, '2024-01-01', '2024-03-31', '{"assessment_id": 1, "areas": ["technical", "communication", "leadership"]}'),
(2, 1, 'individual', 'overall_performance_score', 7.8, '2024-01-01', '2024-03-31', '{"assessment_id": 2, "areas": ["technical", "communication", "teamwork"]}'),
(1, 1, 'individual', 'overall_performance_score', 9.2, '2024-04-01', '2024-06-30', '{"assessment_id": 3, "areas": ["technical", "innovation", "mentoring"]}'),
(2, 1, 'individual', 'overall_performance_score', 8.1, '2024-04-01', '2024-06-30', '{"assessment_id": 4, "areas": ["technical", "problem_solving"]}'),

-- Team metrics
(NULL, 1, 'team', 'average_performance_score', 8.15, '2024-01-01', '2024-03-31', '{"team_size": 2, "completion_rate": 100}'),
(NULL, 1, 'team', 'average_performance_score', 8.65, '2024-04-01', '2024-06-30', '{"team_size": 2, "completion_rate": 100}'),

-- Engagement metrics
(1, 1, 'individual', 'engagement_score', 8.2, '2024-01-01', '2024-03-31', '{"survey_id": 1, "response_rate": 100}'),
(2, 1, 'individual', 'engagement_score', 7.5, '2024-01-01', '2024-03-31', '{"survey_id": 1, "response_rate": 100}'),
(1, 1, 'individual', 'engagement_score', 9.1, '2024-04-01', '2024-06-30', '{"survey_id": 1, "response_rate": 100}'),

-- Department metrics
(NULL, 1, 'department', 'average_engagement_score', 8.3, '2024-01-01', '2024-03-31', '{"department": "Technology Division", "response_rate": 95}'),
(NULL, 1, 'department', 'average_engagement_score', 8.7, '2024-04-01', '2024-06-30', '{"department": "Product Division", "response_rate": 98}');

-- Insert sample engagement surveys
INSERT INTO engagement_surveys (title, description, survey_type, questions, target_audience, start_date, end_date, created_by_id, status) VALUES
('Q1 2024 Pulse Survey', 'Quarterly engagement and satisfaction survey', 'pulse',
 '{"questions": [{"id": 1, "type": "rating", "question": "How satisfied are you with your current role?", "scale": 10}, {"id": 2, "type": "rating", "question": "How likely are you to recommend this company as a great place to work?", "scale": 10}, {"id": 3, "type": "text", "question": "What could we improve to make your work experience better?"}]}',
 '{"roles": ["all"], "departments": ["all"]}',
 '2024-01-15', '2024-01-29', 1, 'completed'),

('Annual Culture Survey 2024', 'Comprehensive annual culture and engagement assessment', 'annual',
 '{"questions": [{"id": 1, "type": "rating", "question": "I feel valued for my contributions", "scale": 5}, {"id": 2, "type": "rating", "question": "My manager provides clear direction", "scale": 5}, {"id": 3, "type": "rating", "question": "I have opportunities for career growth", "scale": 5}, {"id": 4, "type": "text", "question": "What motivates you most in your work?"}]}',
 '{"roles": ["all"], "departments": ["all"]}',
 '2024-02-01', '2024-02-15', 1, 'completed'),

('New Hire Onboarding Survey', 'Feedback on the onboarding experience', 'onboarding',
 '{"questions": [{"id": 1, "type": "rating", "question": "How well did the onboarding process prepare you for your role?", "scale": 10}, {"id": 2, "type": "rating", "question": "How supported did you feel during your first week?", "scale": 10}, {"id": 3, "type": "text", "question": "What could we improve about the onboarding process?"}]}',
 '{"tenure": "< 90 days"}',
 '2024-03-01', '2024-12-31', 1, 'active');

-- Insert sample survey responses
INSERT INTO survey_responses (survey_id, respondent_id, responses, completion_time, submitted_at) VALUES
(1, 1, '{"1": 8, "2": 9, "3": "More flexible working hours would be great"}', 180, '2024-01-20 10:30:00'),
(1, 2, '{"1": 7, "2": 8, "3": "Better communication between teams"}', 165, '2024-01-21 14:15:00'),
(1, 1, '{"1": 9, "2": 10, "3": "More opportunities for technical training"}', 145, '2024-01-22 09:45:00'),
(1, 2, '{"1": 8, "2": 8, "3": "Improved development tools and infrastructure"}', 200, '2024-01-23 16:20:00'),

(2, 1, '{"1": 4, "2": 4, "3": 4, "4": "Working on challenging technical problems and seeing the impact"}', 320, '2024-02-05 11:00:00'),
(2, 2, '{"1": 3, "2": 4, "3": 3, "4": "Collaborating with talented colleagues and learning new technologies"}', 285, '2024-02-06 15:30:00'),
(2, 1, '{"1": 5, "2": 5, "3": 5, "4": "Leading innovative projects and mentoring junior developers"}', 275, '2024-02-07 13:45:00');

-- Insert sample recognition badges
INSERT INTO recognition_badges (name, description, icon_url, badge_type, point_value, criteria, created_by_id) VALUES
('Team Player', 'Awarded for exceptional collaboration and teamwork', '/icons/team-player.svg', 'appreciation', 50, '{"criteria": "Demonstrates outstanding collaboration skills"}', 1),
('Innovation Champion', 'Recognizes creative problem-solving and innovative thinking', '/icons/innovation.svg', 'achievement', 100, '{"criteria": "Introduces innovative solutions or processes"}', 1),
('Mentor', 'Acknowledges dedication to helping others grow and learn', '/icons/mentor.svg', 'skill', 75, '{"criteria": "Actively mentors colleagues and shares knowledge"}', 1),
('Customer Hero', 'Celebrates exceptional customer service and satisfaction', '/icons/customer-hero.svg', 'achievement', 80, '{"criteria": "Goes above and beyond for customer satisfaction"}', 1),
('Code Quality Champion', 'Recognizes commitment to high-quality, maintainable code', '/icons/code-quality.svg', 'skill', 60, '{"criteria": "Consistently delivers high-quality code and reviews"}', 1),
('Problem Solver', 'Awarded for tackling complex challenges with creative solutions', '/icons/problem-solver.svg', 'achievement', 70, '{"criteria": "Solves complex technical or business problems"}', 1),
('5 Years of Excellence', 'Milestone recognition for 5 years of dedicated service', '/icons/5-years.svg', 'milestone', 200, '{"criteria": "5 years of employment"}', 1);

-- Insert sample recognition instances
INSERT INTO recognition_instances (badge_id, giver_id, receiver_id, message, points_awarded, given_at) VALUES
(1, 2, 1, 'Outstanding collaboration on the Q1 project delivery. Your teamwork made all the difference!', 50, '2024-01-15 14:30:00'),
(2, 1, 2, 'Your innovative approach to the API optimization saved us weeks of development time. Brilliant work!', 100, '2024-01-20 16:45:00'),
(3, 1, 2, 'Thank you for taking the time to mentor our new team members. Your guidance is invaluable.', 75, '2024-02-01 10:15:00'),
(4, 2, 1, 'Exceptional handling of the client escalation. You turned a difficult situation into a success story.', 80, '2024-02-10 11:30:00'),
(5, 1, 2, 'Your code reviews are thorough and educational. Thanks for maintaining our high standards!', 60, '2024-02-15 09:20:00'),
(6, 2, 1, 'Incredible problem-solving on the database performance issue. You saved the day!', 70, '2024-02-20 15:10:00'),
(1, 2, 1, 'Great teamwork during the sprint. Your positive attitude keeps everyone motivated!', 50, '2024-02-25 13:45:00'),
(2, 1, 2, 'Your suggestion for the new deployment pipeline was game-changing. Innovation at its best!', 100, '2024-03-01 12:00:00');

-- Insert sample user gamification data
INSERT INTO user_gamification (user_id, total_points, current_level, badges_earned, recognitions_given, recognitions_received, last_activity_date) VALUES
(1, 330, 4, 4, 4, 4, '2024-03-01'),
(2, 415, 5, 4, 4, 4, '2024-03-01');

-- Insert sample attrition predictions
INSERT INTO attrition_predictions (user_id, risk_score, risk_level, contributing_factors, prediction_date, model_version, confidence_score, recommended_actions) VALUES
(1, 0.2500, 'low', '{"factors": ["high_performance", "recent_recognition", "good_engagement"], "weights": [0.4, 0.3, 0.3]}', '2024-03-01', 'v1.2', 0.8500, '{"actions": ["continue_current_engagement", "consider_stretch_projects"]}'),
(2, 0.6500, 'medium', '{"factors": ["average_performance", "limited_growth_opportunities", "workload_concerns"], "weights": [0.3, 0.4, 0.3]}', '2024-03-01', 'v1.2', 0.7800, '{"actions": ["career_development_discussion", "workload_review", "skill_development_opportunities"]}');

-- Insert sample micro feedback
INSERT INTO micro_feedback (giver_id, receiver_id, feedback_type, feedback_value, context_type, context_id, given_at) VALUES
(2, 1, 'thumbs_up', 'great_job', 'task', 1001, '2024-02-28 14:30:00'),
(1, 2, 'emoji', '🚀', 'project', 2001, '2024-02-28 16:45:00'),
(1, 2, 'quick_poll', 'helpful', 'meeting', 3001, '2024-03-01 10:15:00'),
(2, 1, 'thumbs_up', 'excellent_work', 'task', 1002, '2024-03-01 11:30:00'),
(1, 2, 'emoji', '💡', 'general', NULL, '2024-03-01 15:20:00'),
(2, 1, 'check_in', 'positive', 'general', NULL, '2024-03-01 17:00:00');

-- Insert sample AI insights
INSERT INTO ai_insights (insight_type, target_type, target_id, insight_data, confidence_score, priority, status, generated_at, expires_at) VALUES
('attrition_risk', 'team', 1, '{"message": "Team shows 15% higher attrition risk than company average", "details": {"risk_factors": ["workload", "limited_growth"], "affected_members": 1}}', 0.8500, 'medium', 'new', '2024-03-01 08:00:00', '2024-03-31 23:59:59'),
('engagement_trend', 'department', 1, '{"message": "Technology Division engagement trending upward (+12% vs last quarter)", "details": {"trend": "positive", "key_drivers": ["recognition_program", "flexible_work"]}}', 0.9200, 'low', 'acknowledged', '2024-03-01 08:30:00', '2024-03-31 23:59:59'),
('skill_gap', 'organization', 1, '{"message": "Critical skill gap identified in Cloud Security across teams", "details": {"skill": "cloud_security", "gap_percentage": 65, "affected_teams": [1]}}', 0.7800, 'high', 'new', '2024-03-01 09:00:00', '2024-06-30 23:59:59'),
('performance_anomaly', 'individual', 2, '{"message": "Performance decline detected for user over last 2 months", "details": {"trend": "declining", "confidence": 0.75, "suggested_action": "manager_check_in"}}', 0.7500, 'medium', 'new', '2024-03-01 09:30:00', '2024-04-01 23:59:59');
