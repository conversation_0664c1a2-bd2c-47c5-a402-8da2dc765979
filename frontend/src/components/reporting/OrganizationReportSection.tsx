import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Grid, 
  Paper, 
  CircularProgress,
  Card,
  CardContent,
  CardHeader,
  Divider,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';
import { ReportingService } from '../../services/reporting';
import { OrganizationReport, PerformanceTrend, TeamBenchmark } from '../../types/reporting';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface OrganizationReportSectionProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateRangeChange: (newDateRange: { startDate: string; endDate: string }) => void;
  onError: (error: string | null) => void;
}

const OrganizationReportSection: React.FC<OrganizationReportSectionProps> = ({
  dateRange,
  onDateRangeChange,
  onError
}) => {
  const [orgReport, setOrgReport] = useState<OrganizationReport | null>(null);
  const [trendData, setTrendData] = useState<PerformanceTrend[]>([]);
  const [benchmarks, setBenchmarks] = useState<TeamBenchmark[]>([]);
  const [loading, setLoading] = useState(false);
  const [trendPeriod, setTrendPeriod] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly');

  const fetchOrganizationData = async () => {
    setLoading(true);
    try {
      // Fetch all three datasets in parallel
      const [reportData, trendsData, benchmarksData] = await Promise.all([
        ReportingService.getOrganizationReport(dateRange.startDate, dateRange.endDate),
        ReportingService.getPerformanceTrends(trendPeriod, 12),
        ReportingService.getTeamBenchmarks([], dateRange.startDate, dateRange.endDate)
      ]);
      
      setOrgReport(reportData);
      setTrendData(trendsData);
      setBenchmarks(benchmarksData);
    } catch (error) {
      console.error('Error fetching organization data:', error);
      onError('Failed to fetch organization data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      onDateRangeChange({
        ...dateRange,
        startDate: date.toISOString().split('T')[0]
      });
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      onDateRangeChange({
        ...dateRange,
        endDate: date.toISOString().split('T')[0]
      });
    }
  };

  const renderPerformanceDistribution = () => {
    if (!orgReport?.performanceDistribution) return null;

    const distribution = orgReport.performanceDistribution;
    
    const chartData = {
      labels: ['Excellent', 'Good', 'Satisfactory', 'Needs Improvement', 'Poor'],
      datasets: [
        {
          data: [
            distribution.excellent,
            distribution.good,
            distribution.satisfactory,
            distribution.needsImprovement,
            distribution.poor,
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(255, 99, 132, 0.6)',
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(255, 99, 132, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };

    const chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'right' as const,
        },
        title: {
          display: true,
          text: 'Performance Distribution',
        },
      },
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Pie data={chartData} options={chartOptions} />
      </Box>
    );
  };

  const renderStatusBreakdown = () => {
    if (!orgReport?.statusBreakdown) return null;

    const statusData = orgReport.statusBreakdown;

    const chartData = {
      labels: ['Draft', 'In Progress', 'Completed', 'Approved', 'Rejected'],
      datasets: [
        {
          label: 'Assessment Status',
          data: [
            statusData.draft,
            statusData.inProgress,
            statusData.completed,
            statusData.approved,
            statusData.rejected,
          ],
          backgroundColor: [
            'rgba(201, 203, 207, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(255, 205, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)',
          ],
          borderColor: [
            'rgb(201, 203, 207)',
            'rgb(255, 159, 64)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
            'rgb(255, 99, 132)',
          ],
          borderWidth: 1,
        },
      ],
    };

    const chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: 'Assessment Status Distribution',
        },
      },
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Bar data={chartData} options={chartOptions} />
      </Box>
    );
  };

  const renderPerformanceTrends = () => {
    if (!trendData || trendData.length === 0) return null;

    const chartData = {
      labels: trendData.map(trend => trend.period).reverse(),
      datasets: [
        {
          label: 'Average Score',
          data: trendData.map(trend => trend.averageScore * 100).reverse(), // Convert to percentage
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          yAxisID: 'y',
        },
        {
          label: 'Completion Rate',
          data: trendData.map(trend => trend.completionRate * 100).reverse(), // Convert to percentage
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          yAxisID: 'y',
        },
      ],
    };

    const chartOptions = {
      responsive: true,
      interaction: {
        mode: 'index' as const,
        intersect: false,
      },
      plugins: {
        title: {
          display: true,
          text: 'Performance Trends Over Time',
        },
      },
      scales: {
        y: {
          type: 'linear' as const,
          display: true,
          position: 'left' as const,
          title: {
            display: true,
            text: 'Percentage'
          },
          min: 0,
          max: 100,
        },
      },
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Line data={chartData} options={chartOptions} />
      </Box>
    );
  };

  const renderTeamBenchmarks = () => {
    if (!benchmarks || benchmarks.length === 0) return null;

    const chartData = {
      labels: benchmarks.map(team => team.teamName),
      datasets: [
        {
          label: 'Average Score',
          data: benchmarks.map(team => team.averageScore * 100), // Convert to percentage
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgb(54, 162, 235)',
          borderWidth: 1,
        },
      ],
    };

    const chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: 'Team Performance Benchmarks',
        },
      },
      scales: {
        y: {
          min: 0,
          max: 100,
          title: {
            display: true,
            text: 'Score (%)'
          }
        }
      }
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Bar data={chartData} options={chartOptions} />
      </Box>
    );
  };

  const renderOrganizationReport = () => {
    if (!orgReport && !trendData.length && !benchmarks.length) return null;

    return (
      <Box sx={{ mt: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h5" component="h2" gutterBottom>
              Organization Performance Report
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" gutterBottom>
              Period: {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
            </Typography>
          </Grid>

          {orgReport && (
            <>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardHeader title="Summary" />
                  <Divider />
                  <CardContent>
                    <Typography variant="body1" gutterBottom>
                      <strong>Total Assessments:</strong> {orgReport.assessmentCount}
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      <strong>Average Score:</strong> {(orgReport.averageScore * 100).toFixed(2)}%
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      <strong>Completion Rate:</strong> {(orgReport.completionRate * 100).toFixed(2)}%
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardHeader title="Performance Distribution" />
                  <Divider />
                  <CardContent>
                    {renderPerformanceDistribution()}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardHeader title="Assessment Status" />
                  <Divider />
                  <CardContent>
                    {renderStatusBreakdown()}
                  </CardContent>
                </Card>
              </Grid>
            </>
          )}

          {trendData.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Performance Trends" />
                <Divider />
                <CardContent>
                  <Box sx={{ mb: 2 }}>
                    <Button 
                      variant={trendPeriod === 'monthly' ? 'contained' : 'outlined'}
                      onClick={() => setTrendPeriod('monthly')}
                      sx={{ mr: 1 }}
                      size="small"
                    >
                      Monthly
                    </Button>
                    <Button 
                      variant={trendPeriod === 'quarterly' ? 'contained' : 'outlined'}
                      onClick={() => setTrendPeriod('quarterly')}
                      sx={{ mr: 1 }}
                      size="small"
                    >
                      Quarterly
                    </Button>
                    <Button 
                      variant={trendPeriod === 'yearly' ? 'contained' : 'outlined'}
                      onClick={() => setTrendPeriod('yearly')}
                      size="small"
                    >
                      Yearly
                    </Button>
                  </Box>
                  {renderPerformanceTrends()}
                </CardContent>
              </Card>
            </Grid>
          )}

          {benchmarks.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Team Benchmarks" />
                <Divider />
                <CardContent>
                  {renderTeamBenchmarks()}
                </CardContent>
              </Card>
            </Grid>
          )}

          <Grid item xs={12}>
            <Box display="flex" justifyContent="flex-end" mt={2}>
              <Button 
                variant="contained"
                onClick={() => {
                  window.open(
                    `${window.location.origin}/api/reports/organization/export/pdf?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`,
                    '_blank'
                  );
                }}
                sx={{ mr: 2 }}
              >
                Export PDF
              </Button>
              <Button 
                variant="outlined"
                onClick={() => {
                  const csvFilters = {
                    startDate: dateRange.startDate,
                    endDate: dateRange.endDate
                  };
                  ReportingService.exportAssessmentsCsv(csvFilters);
                }}
              >
                Export CSV
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Initial data load
  useEffect(() => {
    fetchOrganizationData();
  }, []);

  // Reload data when period changes
  useEffect(() => {
    if (orgReport) { // Only reload if we've already done the initial load
      fetchOrganizationData();
    }
  }, [trendPeriod]);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Organization Performance Report
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={5}>
            <Typography variant="body1">
              View organization-wide performance metrics and trends
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Start Date"
                value={new Date(dateRange.startDate)}
                onChange={handleStartDateChange}
                slotProps={{ textField: { variant: 'outlined', fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="End Date"
                value={new Date(dateRange.endDate)}
                onChange={handleEndDateChange}
                slotProps={{ textField: { variant: 'outlined', fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} md={1}>
            <Button
              variant="contained"
              fullWidth
              onClick={fetchOrganizationData}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Run'}
            </Button>
          </Grid>
        </Grid>
      </Paper>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : renderOrganizationReport()}
    </Box>
  );
};

export default OrganizationReportSection;
