import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssessmentCriteria } from '../entities/assessment-criteria.entity';
import { AssessmentCriteriaLevel, CriteriaLevelType } from '../entities/assessment-criteria-level.entity';
import { AssessmentCriteriaInheritance, InheritanceType } from '../entities/assessment-criteria-inheritance.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';

export interface CriteriaForTeam {
  hrCriteria: AssessmentCriteria[];
  organizationalCriteria: AssessmentCriteria[];
  teamCriteria: AssessmentCriteria[];
  allCriteria: AssessmentCriteria[];
}

@Injectable()
export class AssessmentCriteriaService {
  constructor(
    @InjectRepository(AssessmentCriteria)
    private criteriaRepository: Repository<AssessmentCriteria>,
    @InjectRepository(AssessmentCriteriaLevel)
    private criteriaLevelRepository: Repository<AssessmentCriteriaLevel>,
    @InjectRepository(AssessmentCriteriaInheritance)
    private inheritanceRepository: Repository<AssessmentCriteriaInheritance>,
    @InjectRepository(OrganizationalUnit)
    private organizationalUnitRepository: Repository<OrganizationalUnit>,
  ) {}

  /**
   * Get all assessment criteria that apply to a specific team
   * This includes HR level, organizational level, and team-specific criteria
   */
  async getCriteriaForTeam(teamId: number): Promise<CriteriaForTeam> {
    // Get the team and its hierarchy
    const team = await this.organizationalUnitRepository.findOne({
      where: { id: teamId },
      relations: ['parent', 'parent.parent', 'parent.parent.parent']
    });

    if (!team) {
      throw new Error(`Team with ID ${teamId} not found`);
    }

    // Get all criteria that apply to this team through inheritance
    const inheritances = await this.inheritanceRepository.find({
      where: { 
        targetOrganizationalUnitId: teamId,
        isActive: true 
      },
      relations: ['criteria', 'criteria.criteriaLevel']
    });

    // Separate criteria by level
    const hrCriteria: AssessmentCriteria[] = [];
    const organizationalCriteria: AssessmentCriteria[] = [];
    const teamCriteria: AssessmentCriteria[] = [];

    for (const inheritance of inheritances) {
      const criteria = inheritance.criteria;
      
      switch (inheritance.inheritanceType) {
        case InheritanceType.HR_MANDATED:
          hrCriteria.push(criteria);
          break;
        case InheritanceType.ORGANIZATIONAL_INHERITED:
          organizationalCriteria.push(criteria);
          break;
        case InheritanceType.TEAM_SPECIFIC:
          teamCriteria.push(criteria);
          break;
      }
    }

    // Sort each category by order_index
    const sortByOrder = (a: AssessmentCriteria, b: AssessmentCriteria) => a.orderIndex - b.orderIndex;
    hrCriteria.sort(sortByOrder);
    organizationalCriteria.sort(sortByOrder);
    teamCriteria.sort(sortByOrder);

    // Combine all criteria in hierarchical order
    const allCriteria = [...hrCriteria, ...organizationalCriteria, ...teamCriteria];

    return {
      hrCriteria,
      organizationalCriteria,
      teamCriteria,
      allCriteria
    };
  }

  /**
   * Get criteria hierarchy for a team showing the source of each criteria
   */
  async getCriteriaHierarchy(teamId: number) {
    const criteriaForTeam = await this.getCriteriaForTeam(teamId);
    const team = await this.organizationalUnitRepository.findOne({
      where: { id: teamId },
      relations: ['parent', 'parent.parent']
    });

    return {
      team: {
        id: team.id,
        name: team.name,
        type: team.type
      },
      hierarchy: {
        hrLevel: {
          source: 'HR Department',
          description: 'Company-wide mandatory criteria',
          criteria: criteriaForTeam.hrCriteria.map(c => ({
            id: c.id,
            name: c.name,
            description: c.description,
            weight: c.weight,
            maxScore: c.maxScore,
            isMandatory: c.isMandatory,
            scoringMethod: c.scoringMethod
          }))
        },
        organizationalLevel: {
          source: team.parent?.name || 'Parent Organization',
          description: 'Organizational unit specific criteria',
          criteria: criteriaForTeam.organizationalCriteria.map(c => ({
            id: c.id,
            name: c.name,
            description: c.description,
            weight: c.weight,
            maxScore: c.maxScore,
            isMandatory: c.isMandatory,
            scoringMethod: c.scoringMethod
          }))
        },
        teamLevel: {
          source: team.name,
          description: 'Team-specific criteria',
          criteria: criteriaForTeam.teamCriteria.map(c => ({
            id: c.id,
            name: c.name,
            description: c.description,
            weight: c.weight,
            maxScore: c.maxScore,
            isMandatory: c.isMandatory,
            scoringMethod: c.scoringMethod
          }))
        }
      },
      totalCriteria: criteriaForTeam.allCriteria.length,
      totalWeight: criteriaForTeam.allCriteria.reduce((sum, c) => sum + c.weight, 0)
    };
  }

  /**
   * Create new team-specific criteria
   */
  async createTeamCriteria(teamId: number, criteriaData: Partial<AssessmentCriteria>, createdById: number) {
    // Get team-level criteria level
    const teamLevel = await this.criteriaLevelRepository.findOne({
      where: { levelType: CriteriaLevelType.TEAM_LEVEL }
    });

    // Create the criteria
    const criteria = this.criteriaRepository.create({
      ...criteriaData,
      criteriaLevelId: teamLevel.id,
      organizationalUnitId: teamId,
      createdById,
      effectiveFrom: new Date()
    });

    const savedCriteria = await this.criteriaRepository.save(criteria);

    // Create inheritance record
    const inheritance = this.inheritanceRepository.create({
      criteriaId: savedCriteria.id,
      targetOrganizationalUnitId: teamId,
      inheritanceType: InheritanceType.TEAM_SPECIFIC,
      isOverridable: false
    });

    await this.inheritanceRepository.save(inheritance);

    return savedCriteria;
  }

  /**
   * Get all criteria levels
   */
  async getCriteriaLevels() {
    return this.criteriaLevelRepository.find({
      where: { isActive: true },
      order: { priorityOrder: 'ASC' }
    });
  }

  /**
   * Calculate weighted score for an assessment based on criteria responses
   */
  calculateWeightedScore(criteriaResponses: any[]): number {
    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const response of criteriaResponses) {
      const weightedScore = (response.score / response.maxPossibleScore) * response.weightApplied;
      totalWeightedScore += weightedScore;
      totalWeight += response.weightApplied;
    }

    return totalWeight > 0 ? (totalWeightedScore / totalWeight) * 100 : 0;
  }
}
