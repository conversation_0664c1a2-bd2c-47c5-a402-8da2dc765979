import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Assessment as AssessmentIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as TrendingUpIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';

interface MonthlyAnalyticsDashboardProps {
  onNavigate?: (page: string) => void;
}

const MonthlyAnalyticsDashboard: React.FC<MonthlyAnalyticsDashboardProps> = ({ onNavigate }) => {
  const [dashboardType, setDashboardType] = React.useState('overview');
  const [selectedMonth, setSelectedMonth] = React.useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = React.useState(new Date().getFullYear());
  const [loading, setLoading] = React.useState(false);

  const getMonthName = (monthIndex: number) => {
    const months = ['January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'];
    return months[monthIndex];
  };

  // Enhanced widgets with icons, descriptions, and loading states
  const widgets = dashboardType === 'monthly' ? [
    {
      title: 'Monthly Performance',
      value: '8.3',
      subtitle: `${getMonthName(selectedMonth)} Average`,
      color: 'primary.main',
      icon: <AssessmentIcon />,
      description: 'Average performance score for the month',
      loading: loading
    },
    {
      title: 'Engagement Rate',
      value: '87%',
      subtitle: 'Engagement Rate',
      color: 'success.main',
      icon: <DashboardIcon />,
      description: 'Employee engagement percentage this month',
      loading: loading
    },
    {
      title: 'Assessments Completed',
      value: '47',
      subtitle: 'Completed',
      color: 'info.main',
      icon: <AssessmentIcon />,
      description: 'Number of performance assessments completed',
      loading: loading
    },
    {
      title: 'Recognition Given',
      value: '156',
      subtitle: 'Total Given',
      color: 'warning.main',
      icon: <DashboardIcon />,
      description: 'Recognition and badge activity this month',
      loading: loading
    },
    {
      title: 'Attrition Rate',
      value: '2.1%',
      subtitle: 'Attrition Rate',
      color: 'error.main',
      icon: <AssessmentIcon />,
      description: 'Employee turnover rate for the month',
      loading: loading
    },
    {
      title: 'New Hires',
      value: '8',
      subtitle: 'New Employees',
      color: 'success.main',
      icon: <DashboardIcon />,
      description: 'New employees hired this month',
      loading: loading
    },
  ] : [
    {
      title: 'Performance Overview',
      value: '8.7',
      subtitle: 'Your Performance',
      color: 'primary.main',
      icon: <AssessmentIcon />,
      description: 'Overall performance tracking',
      loading: loading
    },
    {
      title: 'Engagement Trends',
      value: '+15%',
      subtitle: 'Engagement Up',
      color: 'success.main',
      icon: <DashboardIcon />,
      description: 'Employee engagement trending data',
      loading: loading
    },
    {
      title: 'Team Performance',
      value: '8.4',
      subtitle: 'Team Average',
      color: 'info.main',
      icon: <AssessmentIcon />,
      description: 'Team member performance tracking',
      loading: loading
    },
    {
      title: 'Recognition Activity',
      value: '32',
      subtitle: 'Recognitions',
      color: 'warning.main',
      icon: <DashboardIcon />,
      description: 'Recent recognition and badge activity',
      loading: loading
    },
  ];

  // Load dashboard data when dashboard type or time period changes
  React.useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // In a real implementation, this would be actual API calls
        console.log(`Loading ${dashboardType} data for ${getMonthName(selectedMonth)} ${selectedYear}`);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [dashboardType, selectedMonth, selectedYear]);

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Box display="flex" alignItems="center">
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <DashboardIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" gutterBottom>
              {dashboardType === 'monthly' ? 'Monthly Analytics Dashboard' : 'Analytics Dashboard'}
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              {dashboardType === 'monthly'
                ? `${getMonthName(selectedMonth)} ${selectedYear} - Monthly performance insights`
                : 'Performance metrics, engagement trends, and AI insights'
              }
            </Typography>
          </Box>
        </Box>

        <Box display="flex" gap={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Dashboard Type</InputLabel>
            <Select
              value={dashboardType}
              label="Dashboard Type"
              onChange={(e) => setDashboardType(e.target.value)}
            >
              <MenuItem value="overview">Overview</MenuItem>
              <MenuItem value="monthly">Monthly</MenuItem>
            </Select>
          </FormControl>

          {dashboardType === 'monthly' && (
            <>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Month</InputLabel>
                <Select
                  value={selectedMonth}
                  label="Month"
                  onChange={(e) => setSelectedMonth(Number(e.target.value))}
                >
                  {Array.from({ length: 12 }, (_, i) => (
                    <MenuItem key={i} value={i}>{getMonthName(i)}</MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 100 }}>
                <InputLabel>Year</InputLabel>
                <Select
                  value={selectedYear}
                  label="Year"
                  onChange={(e) => setSelectedYear(Number(e.target.value))}
                >
                  {Array.from({ length: 5 }, (_, i) => {
                    const year = new Date().getFullYear() - 2 + i;
                    return <MenuItem key={year} value={year}>{year}</MenuItem>;
                  })}
                </Select>
              </FormControl>
            </>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        {widgets.map((widget, index) => (
          <Grid item xs={12} md={6} lg={dashboardType === 'monthly' ? 4 : 3} key={index}>
            <Card sx={{ height: 240, transition: 'transform 0.2s', '&:hover': { transform: 'translateY(-2px)' } }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <Avatar sx={{ bgcolor: widget.color, mr: 2 }}>
                    {widget.icon}
                  </Avatar>
                  <Box flex={1}>
                    <Typography variant="h6">{widget.title}</Typography>
                    <Typography variant="caption" color="textSecondary">
                      {widget.description}
                    </Typography>
                  </Box>
                </Box>
                {widget.loading ? (
                  <Box display="flex" alignItems="center" justifyContent="center" height={60}>
                    <CircularProgress size={24} />
                  </Box>
                ) : (
                  <>
                    <Typography variant="h3" color={widget.color} gutterBottom>
                      {widget.value}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {widget.subtitle}
                    </Typography>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}

        {/* Monthly Dashboards Card */}
        <Grid item xs={12} md={6} lg={dashboardType === 'monthly' ? 4 : 3}>
          <Card
            sx={{
              height: 240,
              transition: 'transform 0.2s',
              '&:hover': { transform: 'translateY(-2px)', cursor: 'pointer' },
              border: '2px solid',
              borderColor: 'primary.main',
              background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
            }}
            onClick={() => onNavigate?.('monthly-dashboards')}
          >
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <CalendarIcon />
                </Avatar>
                <Box flex={1}>
                  <Typography variant="h6">Monthly Dashboards</Typography>
                  <Typography variant="caption" color="textSecondary">
                    Team KPI submissions and performance tracking
                  </Typography>
                </Box>
                <ArrowForwardIcon color="primary" />
              </Box>
              <Typography variant="h4" color="primary.main" gutterBottom>
                <TrendingUpIcon sx={{ fontSize: '2rem', mr: 1 }} />
                KPIs
              </Typography>
              <Typography variant="body2" color="textSecondary">
                View team submissions, KPI performance, and monthly reports
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {dashboardType === 'monthly' ? 'Monthly Report Overview' : 'Analytics Overview'}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {dashboardType === 'monthly'
                  ? `Comprehensive monthly analytics for ${getMonthName(selectedMonth)} ${selectedYear}. All backend APIs are functional and ready for real-time data integration.`
                  : 'Welcome to the EHRX Analytics Dashboard! All backend APIs are functional and ready. Real-time data integration is active!'
                }
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MonthlyAnalyticsDashboard;
