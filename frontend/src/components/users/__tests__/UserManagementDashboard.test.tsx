import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import UserManagementDashboard from '../UserManagementDashboard';
import { UserRole } from '../../../types';
import api from '../../../services/api';

// Mock the API
jest.mock('../../../services/api');
const mockedApi = api as jest.Mocked<typeof api>;

const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    firstName: 'HR',
    lastName: 'Admin',
    role: UserRole.HR_ADMIN,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    email: '<EMAIL>',
    firstName: 'Team',
    lastName: 'Manager',
    role: UserRole.MANAGER,
    isActive: true,
    createdAt: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'Employee',
    role: UserRole.EMPLOYEE,
    isActive: false,
    createdAt: '2024-01-03T00:00:00Z'
  }
];

const mockStatistics = {
  totalUsers: 3,
  activeUsers: 2,
  inactiveUsers: 1,
  byRole: {
    [UserRole.HR_ADMIN]: 1,
    [UserRole.MANAGER]: 1,
    [UserRole.EMPLOYEE]: 1
  },
  recentlyCreated: 3,
  recentlyUpdated: 0
};

describe('UserManagementDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful API responses
    mockedApi.get.mockImplementation((url) => {
      if (url.includes('/users/search/advanced')) {
        return Promise.resolve({
          data: {
            users: mockUsers,
            total: 3,
            page: 1,
            totalPages: 1
          }
        });
      }
      if (url === '/users/statistics/overview') {
        return Promise.resolve({ data: mockStatistics });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });
  });

  it('renders dashboard for HR admin', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getByText('Add User')).toBeInTheDocument();
    expect(screen.getByText('Bulk Import')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Total Users')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });
  });

  it('renders dashboard for manager with limited features', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.MANAGER} />);
    
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.queryByText('Add User')).not.toBeInTheDocument();
    expect(screen.queryByText('Bulk Import')).not.toBeInTheDocument();
  });

  it('denies access for employees', () => {
    render(<UserManagementDashboard currentUserRole={UserRole.EMPLOYEE} />);
    
    expect(screen.getByText("You don't have permission to view user management.")).toBeInTheDocument();
  });

  it('displays user statistics correctly', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      expect(screen.getByText('Total Users')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
      expect(screen.getByText('Active Users')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
      expect(screen.getByText('Inactive Users')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
    });
  });

  it('displays users in table format', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      expect(screen.getByText('HR Admin')).toBeInTheDocument();
      expect(screen.getByText('Team Manager')).toBeInTheDocument();
      expect(screen.getByText('Test Employee')).toBeInTheDocument();
    });
  });

  it('handles search functionality', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    const searchInput = screen.getByPlaceholderText('Search by name or email...');
    fireEvent.change(searchInput, { target: { value: 'admin' } });
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining('search=admin')
      );
    });
  });

  it('handles role filtering', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    const roleSelect = screen.getByDisplayValue('All Roles');
    fireEvent.change(roleSelect, { target: { value: UserRole.MANAGER } });
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining(`role=${UserRole.MANAGER}`)
      );
    });
  });

  it('handles status filtering', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    const statusSelect = screen.getByDisplayValue('All Status');
    fireEvent.change(statusSelect, { target: { value: 'true' } });
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining('isActive=true')
      );
    });
  });

  it('clears filters when clear button is clicked', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    // Set some filters first
    const searchInput = screen.getByPlaceholderText('Search by name or email...');
    fireEvent.change(searchInput, { target: { value: 'test' } });
    
    const clearButton = screen.getByText('Clear Filters');
    fireEvent.click(clearButton);
    
    expect(searchInput).toHaveValue('');
  });

  it('handles user selection for bulk operations', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);
    });
    
    const userCheckbox = screen.getAllByRole('checkbox')[1]; // First user checkbox (skip select all)
    fireEvent.click(userCheckbox);
    
    await waitFor(() => {
      expect(screen.getByText('1 user(s) selected')).toBeInTheDocument();
      expect(screen.getByText('Deactivate')).toBeInTheDocument();
      expect(screen.getByText('Reactivate')).toBeInTheDocument();
    });
  });

  it('handles select all functionality', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(selectAllCheckbox);
    });
    
    await waitFor(() => {
      expect(screen.getByText('3 user(s) selected')).toBeInTheDocument();
    });
  });

  it('handles bulk deactivation', async () => {
    mockedApi.post.mockResolvedValue({
      data: { deactivated: [{ id: 1 }], errors: [] }
    });
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      const userCheckbox = screen.getAllByRole('checkbox')[1];
      fireEvent.click(userCheckbox);
    });
    
    const deactivateButton = screen.getByText('Deactivate');
    fireEvent.click(deactivateButton);
    
    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/users/bulk/deactivate', {
        userIds: [1]
      });
    });
  });

  it('handles bulk reactivation', async () => {
    mockedApi.post.mockResolvedValue({
      data: { reactivated: [{ id: 3 }], errors: [] }
    });
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      const userCheckbox = screen.getAllByRole('checkbox')[3]; // Inactive user
      fireEvent.click(userCheckbox);
    });
    
    const reactivateButton = screen.getByText('Reactivate');
    fireEvent.click(reactivateButton);
    
    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/users/bulk/reactivate', {
        userIds: [3]
      });
    });
  });

  it('displays correct role badges', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      expect(screen.getByText('HR ADMIN')).toBeInTheDocument();
      expect(screen.getByText('MANAGER')).toBeInTheDocument();
      expect(screen.getByText('EMPLOYEE')).toBeInTheDocument();
    });
  });

  it('displays correct status badges', async () => {
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      const activeStatuses = screen.getAllByText('Active');
      const inactiveStatuses = screen.getAllByText('Inactive');
      
      expect(activeStatuses.length).toBe(2);
      expect(inactiveStatuses.length).toBe(1);
    });
  });

  it('handles pagination', async () => {
    // Mock paginated response
    mockedApi.get.mockResolvedValue({
      data: {
        users: mockUsers,
        total: 10,
        page: 1,
        totalPages: 2
      }
    });
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      expect(screen.getByText('Showing page 1 of 2')).toBeInTheDocument();
    });
    
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);
    
    await waitFor(() => {
      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining('page=2')
      );
    });
  });

  it('handles API errors gracefully', async () => {
    mockedApi.get.mockRejectedValue(new Error('API Error'));
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to fetch users')).toBeInTheDocument();
    });
  });

  it('shows loading state', () => {
    // Mock delayed response
    mockedApi.get.mockImplementation(() => new Promise(() => {}));
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
  });

  it('shows empty state when no users found', async () => {
    mockedApi.get.mockResolvedValue({
      data: {
        users: [],
        total: 0,
        page: 1,
        totalPages: 0
      }
    });
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      expect(screen.getByText('No users found matching your criteria.')).toBeInTheDocument();
    });
  });

  it('displays success messages after bulk operations', async () => {
    mockedApi.post.mockResolvedValue({
      data: { deactivated: [{ id: 1 }], errors: [] }
    });
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      const userCheckbox = screen.getAllByRole('checkbox')[1];
      fireEvent.click(userCheckbox);
    });
    
    const deactivateButton = screen.getByText('Deactivate');
    fireEvent.click(deactivateButton);
    
    await waitFor(() => {
      expect(screen.getByText('Successfully deactivated 1 users')).toBeInTheDocument();
    });
  });

  it('handles bulk operation errors', async () => {
    mockedApi.post.mockRejectedValue({
      response: { data: { message: 'Bulk operation failed' } }
    });
    
    render(<UserManagementDashboard currentUserRole={UserRole.HR_ADMIN} />);
    
    await waitFor(() => {
      const userCheckbox = screen.getAllByRole('checkbox')[1];
      fireEvent.click(userCheckbox);
    });
    
    const deactivateButton = screen.getByText('Deactivate');
    fireEvent.click(deactivateButton);
    
    await waitFor(() => {
      expect(screen.getByText('Bulk operation failed')).toBeInTheDocument();
    });
  });
});
