import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { AssessmentInstance } from '../../assessments/entities/assessment-instance.entity';
import { ImprovementPlan } from '../../improvement-plans/entities/improvement-plan.entity';

export enum ActionItemStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue'
}

export enum ActionItemPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ActionItemCategory {
  PERFORMANCE_IMPROVEMENT = 'performance_improvement',
  SKILL_DEVELOPMENT = 'skill_development',
  PROCESS_IMPROVEMENT = 'process_improvement',
  COMPLIANCE = 'compliance',
  SAFETY = 'safety',
  QUALITY = 'quality',
  TRAINING = 'training',
  GOAL_SETTING = 'goal_setting',
  OTHER = 'other'
}

@Entity('action_items')
export class ActionItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: ActionItemStatus,
    default: ActionItemStatus.OPEN
  })
  status: ActionItemStatus;

  @Column({
    type: 'enum',
    enum: ActionItemPriority,
    default: ActionItemPriority.MEDIUM
  })
  priority: ActionItemPriority;

  @Column({
    type: 'enum',
    enum: ActionItemCategory,
    default: ActionItemCategory.OTHER
  })
  category: ActionItemCategory;

  @Column({ name: 'due_date', type: 'date', nullable: true })
  dueDate: Date;

  @Column({ name: 'completed_date', type: 'timestamp', nullable: true })
  completedDate: Date;

  @Column({ name: 'assigned_to_id' })
  assignedToId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_to_id' })
  assignedTo: User;

  @Column({ name: 'created_by_id' })
  createdById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @Column({ name: 'assessment_id', nullable: true })
  assessmentId: number;

  @ManyToOne(() => AssessmentInstance, { nullable: true })
  @JoinColumn({ name: 'assessment_id' })
  assessment: AssessmentInstance;

  @Column({ name: 'improvement_plan_id', nullable: true })
  improvementPlanId: number;

  @ManyToOne(() => ImprovementPlan, { nullable: true })
  @JoinColumn({ name: 'improvement_plan_id' })
  improvementPlan: ImprovementPlan;

  @Column({ name: 'progress_percentage', type: 'int', default: 0 })
  progressPercentage: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'estimated_hours', type: 'decimal', precision: 5, scale: 2, nullable: true })
  estimatedHours: number;

  @Column({ name: 'actual_hours', type: 'decimal', precision: 5, scale: 2, nullable: true })
  actualHours: number;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ name: 'is_recurring', type: 'boolean', default: false })
  isRecurring: boolean;

  @Column({ name: 'recurrence_pattern', type: 'varchar', length: 100, nullable: true })
  recurrencePattern: string;

  @Column({ name: 'parent_action_item_id', nullable: true })
  parentActionItemId: number;

  @ManyToOne(() => ActionItem, actionItem => actionItem.subItems, { nullable: true })
  @JoinColumn({ name: 'parent_action_item_id' })
  parentActionItem: ActionItem;

  @OneToMany(() => ActionItem, actionItem => actionItem.parentActionItem)
  subItems: ActionItem[];

  @OneToMany(() => ActionItemUpdate, update => update.actionItem)
  updates: ActionItemUpdate[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}

@Entity('action_item_updates')
export class ActionItemUpdate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'action_item_id' })
  actionItemId: number;

  @ManyToOne(() => ActionItem, actionItem => actionItem.updates)
  @JoinColumn({ name: 'action_item_id' })
  actionItem: ActionItem;

  @Column({ name: 'updated_by_id' })
  updatedById: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by_id' })
  updatedBy: User;

  @Column({ name: 'previous_status', type: 'enum', enum: ActionItemStatus, nullable: true })
  previousStatus: ActionItemStatus;

  @Column({ name: 'new_status', type: 'enum', enum: ActionItemStatus })
  newStatus: ActionItemStatus;

  @Column({ name: 'previous_progress', type: 'int', nullable: true })
  previousProgress: number;

  @Column({ name: 'new_progress', type: 'int' })
  newProgress: number;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({ name: 'hours_logged', type: 'decimal', precision: 5, scale: 2, nullable: true })
  hoursLogged: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
