import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RecognitionService } from './recognition.service';
import { CreateBadgeDto, UpdateBadgeDto } from './dto/badge.dto';
import { GiveRecognitionDto } from './dto/recognition.dto';
import { MicroFeedbackDto } from './dto/micro-feedback.dto';

@Controller('recognition')
@UseGuards(JwtAuthGuard)
export class RecognitionController {
  constructor(private readonly recognitionService: RecognitionService) { }

  // Badge management endpoints
  @Get('badges')
  async getBadges(@Query('type') type?: string) {
    return this.recognitionService.getBadges(type);
  }

  @Get('badges/:id')
  async getBadge(@Param('id') id: number) {
    return this.recognitionService.getBadge(id);
  }

  @Post('badges')
  async createBadge(@Body() createBadgeDto: CreateBadgeDto, @Request() req) {
    return this.recognitionService.createBadge(createBadgeDto, req.user.id);
  }

  @Put('badges/:id')
  async updateBadge(
    @Param('id') id: number,
    @Body() updateBadgeDto: UpdateBadgeDto,
    @Request() req
  ) {
    return this.recognitionService.updateBadge(id, updateBadgeDto, req.user.id);
  }

  @Delete('badges/:id')
  async deleteBadge(@Param('id') id: number, @Request() req) {
    return this.recognitionService.deleteBadge(id, req.user.id);
  }

  // Recognition giving/receiving endpoints
  @Post('give')
  async giveRecognition(@Body() giveRecognitionDto: GiveRecognitionDto, @Request() req) {
    return this.recognitionService.giveRecognition(giveRecognitionDto, req.user.id);
  }

  @Get('received')
  async getReceivedRecognitions(
    @Request() req,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0
  ) {
    return this.recognitionService.getReceivedRecognitions(req.user.id, limit, offset);
  }

  @Get('given')
  async getGivenRecognitions(
    @Request() req,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0
  ) {
    return this.recognitionService.getGivenRecognitions(req.user.id, limit, offset);
  }

  @Get('feed')
  async getRecognitionFeed(
    @Query('limit') limit: number = 50,
    @Query('offset') offset: number = 0
  ) {
    return this.recognitionService.getPublicRecognitionFeed(limit, offset);
  }

  @Get('leaderboard')
  async getLeaderboard(@Query('period') period: string = 'month') {
    return this.recognitionService.getLeaderboard(period);
  }

  // Gamification endpoints
  @Get('gamification/me')
  async getMyGamification(@Request() req) {
    return this.recognitionService.getUserGamification(req.user.id);
  }

  @Get('gamification/:userId')
  async getUserGamificationById(@Param('userId') userId: number) {
    return this.recognitionService.getUserGamification(userId);
  }

  @Get('gamification/leaderboard/points')
  async getPointsLeaderboard(@Query('limit') limit: number = 10) {
    return this.recognitionService.getPointsLeaderboard(limit);
  }

  @Get('gamification/leaderboard/badges')
  async getBadgesLeaderboard(@Query('limit') limit: number = 10) {
    return this.recognitionService.getBadgesLeaderboard(limit);
  }

  // Micro feedback endpoints
  @Post('feedback')
  async giveMicroFeedback(@Body() microFeedbackDto: MicroFeedbackDto, @Request() req) {
    return this.recognitionService.giveMicroFeedback(microFeedbackDto, req.user.id);
  }

  @Get('feedback/received')
  async getReceivedFeedback(
    @Request() req,
    @Query('limit') limit: number = 20,
    @Query('type') type?: string
  ) {
    return this.recognitionService.getReceivedMicroFeedback(req.user.id, limit, type);
  }

  @Get('feedback/given')
  async getGivenFeedback(
    @Request() req,
    @Query('limit') limit: number = 20,
    @Query('type') type?: string
  ) {
    return this.recognitionService.getGivenMicroFeedback(req.user.id, limit, type);
  }

  // Analytics endpoints
  @Get('analytics/summary')
  async getRecognitionAnalytics(@Request() req) {
    return this.recognitionService.getRecognitionAnalytics(req.user);
  }

  @Get('analytics/trends')
  async getRecognitionTrends(
    @Query('period') period: string = '6months',
    @Query('orgUnitId') orgUnitId?: number
  ) {
    return this.recognitionService.getRecognitionTrends(period, orgUnitId);
  }
}
