import { IsString, IsOptional, <PERSON><PERSON><PERSON>, <PERSON>N<PERSON><PERSON> } from 'class-validator';
import { FeedbackType, FeedbackStatus, FeedbackSource } from '../../entities/stakeholder-feedback.entity';

export class CreateStakeholderFeedbackDto {
  @IsString()
  title: string;

  @IsString()
  content: string;

  @IsEnum(FeedbackType)
  @IsOptional()
  type?: FeedbackType;

  @IsEnum(FeedbackStatus)
  @IsOptional()
  status?: FeedbackStatus;

  @IsEnum(FeedbackSource)
  @IsOptional()
  source?: FeedbackSource;

  @IsString()
  @IsOptional()
  stakeholderName?: string;

  @IsString()
  @IsOptional()
  stakeholderRole?: string;

  @IsString()
  @IsOptional()
  stakeholderContact?: string;

  @IsNumber()
  @IsOptional()
  assignedToId?: number;

  @IsNumber()
  @IsOptional()
  assessmentId?: number;
}
