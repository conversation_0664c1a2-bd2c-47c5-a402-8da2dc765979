#!/bin/bash

# EHRX Authentication System Test Script
# This script tests the new authentication system implementation

echo "🔐 EHRX Authentication System Test"
echo "=================================="
echo ""

# Check if backend is running
echo "📡 Checking backend status..."
if curl -s http://localhost:4000/auth/health > /dev/null; then
    echo "✅ Backend is running on port 4000"
else
    echo "❌ Backend is not running. Please start the backend first."
    echo "   Run: cd backend && npm run start:dev"
    exit 1
fi

echo ""

# Test authentication endpoint
echo "🔑 Testing authentication endpoint..."
AUTH_RESPONSE=$(curl -s -X POST http://localhost:4000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"AdministratorX123"}')

if echo "$AUTH_RESPONSE" | grep -q "access_token"; then
    echo "✅ Authentication endpoint is working"
    echo "📋 Response: $AUTH_RESPONSE"
else
    echo "❌ Authentication failed"
    echo "📋 Response: $AUTH_RESPONSE"
fi

echo ""

# Check frontend
echo "🌐 Checking frontend status..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend is running on port 3000"
else
    echo "❌ Frontend is not running. Please start the frontend."
    echo "   Run: cd frontend && npm start"
fi

echo ""

# Database connection test
echo "🗄️ Testing database connection..."
if command -v mysql &> /dev/null; then
    # Test database connection (adjust credentials as needed)
    if mysql -h localhost -u root -p -e "USE ehrx; SHOW TABLES;" 2>/dev/null | grep -q "users"; then
        echo "✅ Database connection successful"
        echo "📊 Users table exists"
    else
        echo "❌ Database connection failed or users table missing"
        echo "   Please run the database migration scripts"
    fi
else
    echo "⚠️  MySQL client not found. Cannot test database connection."
fi

echo ""
echo "🎯 Test Summary:"
echo "==============="
echo "1. Backend API: Check above for status"
echo "2. Frontend App: Check above for status"
echo "3. Database: Check above for status"
echo ""
echo "📋 Default Login Credentials:"
echo "Email: <EMAIL>"
echo "Password: AdministratorX123"
echo ""
echo "🔒 Security Features Implemented:"
echo "• Account lockout after 5 failed attempts"
echo "• Mandatory password change on first login"
echo "• Session timeout after 1 hour"
echo "• Comprehensive audit logging"
echo "• NIS2 compliant security controls"
echo ""
echo "🚀 Access the application at: http://localhost:3000"
echo "📚 API documentation at: http://localhost:4000/api"
