import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Info as InfoIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { monthlyDashboardsApi } from '../../services/api';
import { toast } from '../ui/toast';

interface KpiTrendsProps {
  kpiId?: number;
  organizationalUnitId?: number;
  onClose?: () => void;
}

interface TrendData {
  kpiId: number;
  kpiName: string;
  kpiDisplayName: string;
  unit?: string;
  targetValue?: number;
  organizationalUnitId?: number;
  months: number;
  periodStart: string;
  periodEnd: string;
  timeSeries: TimeSeriesPoint[];
  teamTrends?: { [key: string]: TeamTrendPoint[] };
  overallMetrics: OverallMetrics;
  summary: TrendSummary;
}

interface TimeSeriesPoint {
  period: string;
  year: number;
  month: number;
  monthName: string;
  teamCount: number;
  average: number;
  min: number;
  max: number;
  median: number;
  targetAverage?: number;
  variance?: number;
  trafficLightDistribution: {
    counts: { green: number; yellow: number; red: number; na: number };
    percentages: { green: number; yellow: number; red: number; na: number };
  };
}

interface TeamTrendPoint {
  period: string;
  year: number;
  month: number;
  value: number;
  targetValue?: number;
  trafficLight: string;
  teamId: number;
  teamName: string;
}

interface OverallMetrics {
  trendDirection: 'improving' | 'declining' | 'stable' | 'insufficient_data';
  trendSlope: number;
  performanceRating: 'excellent' | 'good' | 'average' | 'needs_improvement' | 'unknown';
  volatility: number;
  consistency: number;
  averageValue: number;
  latestValue: number;
  changeFromFirst: number;
}

interface TrendSummary {
  totalPeriods: number;
  averageTeamsPerPeriod: number;
  trendDirection: string;
  performanceRating: string;
}

const TRAFFIC_LIGHT_COLORS = {
  green: '#4caf50',
  yellow: '#ff9800',
  red: '#f44336',
  na: '#9e9e9e',
};

export const KpiTrendsAnalysis: React.FC<KpiTrendsProps> = ({
  kpiId,
  organizationalUnitId,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [trendData, setTrendData] = useState<TrendData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedKpiId, setSelectedKpiId] = useState<number>(kpiId || 0);
  const [selectedOrgUnitId, setSelectedOrgUnitId] = useState<number | undefined>(organizationalUnitId);
  const [months, setMonths] = useState<number>(12);
  const [availableKpis, setAvailableKpis] = useState<any[]>([]);
  const [availableOrgUnits, setAvailableOrgUnits] = useState<any[]>([]);

  useEffect(() => {
    fetchAvailableOptions();
  }, []);

  useEffect(() => {
    if (selectedKpiId > 0) {
      fetchTrendData();
    }
  }, [selectedKpiId, selectedOrgUnitId, months]);

  const fetchAvailableOptions = async () => {
    try {
      const [kpisResponse] = await Promise.all([
        monthlyDashboardsApi.getAllKpis(),
        // Assuming we have an API to get organizational units
        // monthlyDashboardsApi.getOrganizationalUnits(),
      ]);

      setAvailableKpis(kpisResponse.data || []);
      // setAvailableOrgUnits(orgUnitsResponse.data || []);
    } catch (error) {
      console.error('Error fetching available options:', error);
      toast.error('Failed to load available options');
    }
  };

  const fetchTrendData = async () => {
    if (!selectedKpiId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await monthlyDashboardsApi.getKpiTrends({
        kpiId: selectedKpiId,
        organizationalUnitId: selectedOrgUnitId,
        months,
      });

      setTrendData(response.data);
    } catch (error) {
      console.error('Error fetching trend data:', error);
      setError('Failed to load trend data');
      toast.error('Failed to load trend data');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'improving':
        return <TrendingUpIcon color="success" />;
      case 'declining':
        return <TrendingDownIcon color="error" />;
      case 'stable':
        return <TrendingFlatIcon color="info" />;
      default:
        return <InfoIcon color="disabled" />;
    }
  };

  const getPerformanceColor = (rating: string) => {
    switch (rating) {
      case 'excellent':
        return 'success';
      case 'good':
        return 'info';
      case 'average':
        return 'warning';
      case 'needs_improvement':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatChartData = (timeSeries: TimeSeriesPoint[]) => {
    return timeSeries.map(point => ({
      period: `${point.monthName.substring(0, 3)} ${point.year}`,
      average: Number(point.average.toFixed(2)),
      target: point.targetAverage ? Number(point.targetAverage.toFixed(2)) : null,
      min: Number(point.min.toFixed(2)),
      max: Number(point.max.toFixed(2)),
      median: Number(point.median.toFixed(2)),
      teamCount: point.teamCount,
    }));
  };

  const formatTrafficLightData = (timeSeries: TimeSeriesPoint[]) => {
    return timeSeries.map(point => ({
      period: `${point.monthName.substring(0, 3)} ${point.year}`,
      green: point.trafficLightDistribution.percentages.green,
      yellow: point.trafficLightDistribution.percentages.yellow,
      red: point.trafficLightDistribution.percentages.red,
      na: point.trafficLightDistribution.percentages.na,
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading trend analysis...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button onClick={fetchTrendData} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" gutterBottom>
          KPI Trends Analysis
        </Typography>
        <Box display="flex" gap={2}>
          <IconButton onClick={fetchTrendData} disabled={loading}>
            <RefreshIcon />
          </IconButton>
          {onClose && (
            <Button variant="outlined" onClick={onClose}>
              Close
            </Button>
          )}
        </Box>
      </Box>

      {/* Controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>KPI</InputLabel>
              <Select
                value={selectedKpiId}
                onChange={(e) => setSelectedKpiId(Number(e.target.value))}
                label="KPI"
              >
                {availableKpis.map((kpi) => (
                  <MenuItem key={kpi.id} value={kpi.id}>
                    {kpi.displayName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Time Period</InputLabel>
              <Select
                value={months}
                onChange={(e) => setMonths(Number(e.target.value))}
                label="Time Period"
              >
                <MenuItem value={6}>Last 6 Months</MenuItem>
                <MenuItem value={12}>Last 12 Months</MenuItem>
                <MenuItem value={18}>Last 18 Months</MenuItem>
                <MenuItem value={24}>Last 24 Months</MenuItem>
                <MenuItem value={36}>Last 36 Months</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Button
              variant="contained"
              onClick={fetchTrendData}
              disabled={!selectedKpiId || loading}
              fullWidth
            >
              Analyze Trends
            </Button>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => {
                // TODO: Implement export functionality
                toast.info('Export functionality coming soon');
              }}
              fullWidth
            >
              Export
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Trend Data Display */}
      {trendData && (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Trend Direction
                      </Typography>
                      <Box display="flex" alignItems="center">
                        {getTrendIcon(trendData.overallMetrics.trendDirection)}
                        <Typography variant="h6" sx={{ ml: 1, textTransform: 'capitalize' }}>
                          {trendData.overallMetrics.trendDirection.replace('_', ' ')}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Performance Rating
                  </Typography>
                  <Chip
                    label={trendData.overallMetrics.performanceRating.replace('_', ' ').toUpperCase()}
                    color={getPerformanceColor(trendData.overallMetrics.performanceRating) as any}
                    variant="filled"
                  />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Consistency Rate
                  </Typography>
                  <Typography variant="h4">
                    {trendData.overallMetrics.consistency.toFixed(1)}%
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Periods meeting target
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Change from Start
                  </Typography>
                  <Typography
                    variant="h4"
                    color={trendData.overallMetrics.changeFromFirst >= 0 ? 'success.main' : 'error.main'}
                  >
                    {trendData.overallMetrics.changeFromFirst >= 0 ? '+' : ''}
                    {trendData.overallMetrics.changeFromFirst.toFixed(1)}%
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Main Trend Chart */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {trendData.kpiDisplayName} Trend Over Time
              {trendData.unit && (
                <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  ({trendData.unit})
                </Typography>
              )}
            </Typography>
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={formatChartData(trendData.timeSeries)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <RechartsTooltip
                  formatter={(value, name) => [
                    typeof value === 'number' ? value.toFixed(2) : value,
                    name === 'average' ? 'Average' :
                      name === 'target' ? 'Target' :
                        name === 'min' ? 'Minimum' :
                          name === 'max' ? 'Maximum' : name
                  ]}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="average"
                  stroke="#2196f3"
                  strokeWidth={3}
                  name="Average"
                  dot={{ fill: '#2196f3', strokeWidth: 2, r: 4 }}
                />
                {trendData.targetValue && (
                  <Line
                    type="monotone"
                    dataKey="target"
                    stroke="#4caf50"
                    strokeDasharray="5 5"
                    name="Target"
                    dot={{ fill: '#4caf50', strokeWidth: 2, r: 3 }}
                  />
                )}
                <Line
                  type="monotone"
                  dataKey="min"
                  stroke="#ff9800"
                  strokeWidth={1}
                  name="Minimum"
                  dot={{ fill: '#ff9800', strokeWidth: 1, r: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="max"
                  stroke="#f44336"
                  strokeWidth={1}
                  name="Maximum"
                  dot={{ fill: '#f44336', strokeWidth: 1, r: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>

          {/* Traffic Light Distribution Chart */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Performance Distribution Over Time
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={formatTrafficLightData(trendData.timeSeries)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis label={{ value: 'Percentage', angle: -90, position: 'insideLeft' }} />
                <RechartsTooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, '']} />
                <Legend />
                <Bar dataKey="green" stackId="a" fill={TRAFFIC_LIGHT_COLORS.green} name="Green (Good)" />
                <Bar dataKey="yellow" stackId="a" fill={TRAFFIC_LIGHT_COLORS.yellow} name="Yellow (Warning)" />
                <Bar dataKey="red" stackId="a" fill={TRAFFIC_LIGHT_COLORS.red} name="Red (Critical)" />
                <Bar dataKey="na" stackId="a" fill={TRAFFIC_LIGHT_COLORS.na} name="N/A" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>

          {/* Detailed Statistics Table */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Detailed Period Statistics
            </Typography>
            <Box sx={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ backgroundColor: '#f5f5f5' }}>
                    <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Period</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #ddd' }}>Teams</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #ddd' }}>Average</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #ddd' }}>Target</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #ddd' }}>Variance</th>
                    <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {trendData.timeSeries.map((point, index) => (
                    <tr key={point.period} style={{ backgroundColor: index % 2 === 0 ? '#fafafa' : 'white' }}>
                      <td style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
                        {point.monthName} {point.year}
                      </td>
                      <td style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #eee' }}>
                        {point.teamCount}
                      </td>
                      <td style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #eee' }}>
                        {point.average.toFixed(2)}
                        {trendData.unit && <span style={{ fontSize: '0.8em', color: '#666' }}> {trendData.unit}</span>}
                      </td>
                      <td style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #eee' }}>
                        {point.targetAverage ? point.targetAverage.toFixed(2) : 'N/A'}
                        {point.targetAverage && trendData.unit && <span style={{ fontSize: '0.8em', color: '#666' }}> {trendData.unit}</span>}
                      </td>
                      <td style={{
                        padding: '12px',
                        textAlign: 'right',
                        borderBottom: '1px solid #eee',
                        color: point.variance && point.variance >= 0 ? '#4caf50' : '#f44336'
                      }}>
                        {point.variance !== null && point.variance !== undefined ?
                          `${point.variance >= 0 ? '+' : ''}${point.variance.toFixed(2)}` : 'N/A'}
                      </td>
                      <td style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                        <Box display="flex" justifyContent="center" gap={0.5}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              backgroundColor: TRAFFIC_LIGHT_COLORS.green,
                              opacity: point.trafficLightDistribution.percentages.green > 0 ? 1 : 0.3,
                            }}
                          />
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              backgroundColor: TRAFFIC_LIGHT_COLORS.yellow,
                              opacity: point.trafficLightDistribution.percentages.yellow > 0 ? 1 : 0.3,
                            }}
                          />
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              backgroundColor: TRAFFIC_LIGHT_COLORS.red,
                              opacity: point.trafficLightDistribution.percentages.red > 0 ? 1 : 0.3,
                            }}
                          />
                        </Box>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </Box>
          </Paper>

          {/* Key Insights */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Key Insights
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Trend Analysis
                  </Typography>
                  <Typography variant="body2">
                    The KPI shows a <strong>{trendData.overallMetrics.trendDirection}</strong> trend over the selected period.
                    {trendData.overallMetrics.changeFromFirst !== 0 && (
                      <> Performance has {trendData.overallMetrics.changeFromFirst > 0 ? 'improved' : 'declined'} by{' '}
                        <strong>{Math.abs(trendData.overallMetrics.changeFromFirst).toFixed(1)}%</strong> since the start of the period.</>
                    )}
                  </Typography>
                </Alert>
              </Grid>
              <Grid item xs={12} md={6}>
                <Alert severity={trendData.overallMetrics.consistency >= 70 ? 'success' : 'warning'} sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Consistency Rating
                  </Typography>
                  <Typography variant="body2">
                    Teams meet their targets <strong>{trendData.overallMetrics.consistency.toFixed(1)}%</strong> of the time.
                    {trendData.overallMetrics.consistency >= 70 ?
                      ' This indicates good consistent performance.' :
                      ' Consider reviewing targets or providing additional support.'}
                  </Typography>
                </Alert>
              </Grid>
              <Grid item xs={12}>
                <Alert severity="info">
                  <Typography variant="subtitle2" gutterBottom>
                    Performance Summary
                  </Typography>
                  <Typography variant="body2">
                    Current performance rating: <strong>{trendData.overallMetrics.performanceRating.replace('_', ' ')}</strong>.
                    Average value across all periods: <strong>{trendData.overallMetrics.averageValue.toFixed(2)}</strong>
                    {trendData.unit && <> {trendData.unit}</>}.
                    Latest recorded value: <strong>{trendData.overallMetrics.latestValue.toFixed(2)}</strong>
                    {trendData.unit && <> {trendData.unit}</>}.
                  </Typography>
                </Alert>
              </Grid>
            </Grid>
          </Paper>
        </>
      )}
    </Box>
  );
};
