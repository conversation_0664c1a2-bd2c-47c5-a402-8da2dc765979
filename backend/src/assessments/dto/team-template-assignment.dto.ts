import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsBoolean, IsArray } from 'class-validator';

export class TeamTemplateAssignmentDto {
  @IsNotEmpty()
  @IsNumber()
  templateId: number;

  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  teamIds: number[];

  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @IsOptional()
  @IsBoolean()
  allowCustomization?: boolean;
}

export class CustomizeTemplateForTeamDto {
  @IsNotEmpty()
  @IsNumber()
  sourceTemplateId: number;

  @IsNotEmpty()
  @IsNumber()
  teamId: number;

  @IsOptional()
  customizations?: {
    hiddenAreas?: number[];
    modifiedAreas?: {
      areaId: number;
      weight?: number;
      maxScore?: number;
      description?: string;
    }[];
    additionalAreas?: {
      name: string;
      description?: string;
      weight: number;
      maxScore: number;
      orderIndex: number;
    }[];
  };
}
