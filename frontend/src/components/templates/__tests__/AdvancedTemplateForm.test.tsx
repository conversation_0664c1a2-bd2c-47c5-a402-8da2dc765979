import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { jest } from '@jest/globals';
import AdvancedTemplateForm from '../AdvancedTemplateForm';
import { UserRole, RuleType } from '../../../types';
import api from '../../../services/api';

// Mock the API
jest.mock('../../../services/api');
const mockedApi = api as jest.Mocked<typeof api>;

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('AdvancedTemplateForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    userRole: UserRole.HR_ADMIN,
    onSave: jest.fn(),
    onCancel: jest.fn(),
  };

  it('renders basic information tab by default', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    expect(screen.getByText('Create Assessment Template')).toBeInTheDocument();
    expect(screen.getByText('Basic Information')).toBeInTheDocument();
    expect(screen.getByLabelText('Template Name *')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
  });

  it('shows global template option for HR_ADMIN', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    expect(screen.getByText('Make this template available to all teams (global template)')).toBeInTheDocument();
  });

  it('hides global template option for MANAGER', () => {
    renderWithRouter(
      <AdvancedTemplateForm {...defaultProps} userRole={UserRole.MANAGER} />
    );
    
    expect(screen.queryByText('Make this template available to all teams (global template)')).not.toBeInTheDocument();
  });

  it('navigates between tabs', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Click on Assessment Areas tab
    fireEvent.click(screen.getByText('Assessment Areas (1)'));
    expect(screen.getByText('Add Area')).toBeInTheDocument();
    
    // Click on Preview tab
    fireEvent.click(screen.getByText('Preview'));
    expect(screen.getByText('Template Preview')).toBeInTheDocument();
  });

  it('adds and removes assessment areas', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Navigate to areas tab
    fireEvent.click(screen.getByText('Assessment Areas (1)'));
    
    // Initially has one area
    expect(screen.getByText('Area 1')).toBeInTheDocument();
    
    // Add another area
    fireEvent.click(screen.getByText('Add Area'));
    expect(screen.getByText('Area 2')).toBeInTheDocument();
    
    // Remove an area (should keep at least one)
    const deleteButtons = screen.getAllByTitle('Remove area');
    fireEvent.click(deleteButtons[0]);
    
    // Should still have one area
    expect(screen.getByText('Area 1')).toBeInTheDocument();
  });

  it('validates area weights', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Navigate to areas tab
    fireEvent.click(screen.getByText('Assessment Areas (1)'));
    
    // Set invalid weight
    const weightInput = screen.getByDisplayValue('0.25');
    fireEvent.change(weightInput, { target: { value: '0.5' } });
    
    // Should show weight warning
    expect(screen.getByText('Weight Validation Warning')).toBeInTheDocument();
    expect(screen.getByText(/Area weights don't sum to 1.0/)).toBeInTheDocument();
  });

  it('adds and configures scoring rules', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Navigate to areas tab
    fireEvent.click(screen.getByText('Assessment Areas (1)'));
    
    // Add a scoring rule
    fireEvent.click(screen.getByText('Add Rule'));
    
    expect(screen.getByText('Rule 1')).toBeInTheDocument();
    expect(screen.getByDisplayValue('addition')).toBeInTheDocument();
  });

  it('shows conditional rule fields when conditional type is selected', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Navigate to areas tab
    fireEvent.click(screen.getByText('Assessment Areas (1)'));
    
    // Add a scoring rule
    fireEvent.click(screen.getByText('Add Rule'));
    
    // Change to conditional rule
    const ruleTypeSelect = screen.getByDisplayValue('addition');
    fireEvent.change(ruleTypeSelect, { target: { value: 'conditional' } });
    
    // Should show conditional fields
    expect(screen.getByText('Condition Field')).toBeInTheDocument();
    expect(screen.getByText('Operator')).toBeInTheDocument();
    expect(screen.getByText('Value')).toBeInTheDocument();
  });

  it('validates form before submission', async () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Try to submit without filling required fields
    fireEvent.click(screen.getByText('Create Template'));
    
    await waitFor(() => {
      expect(screen.getByText('Template name is required')).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    mockedApi.post.mockResolvedValue({
      data: { id: 1, name: 'Test Template' }
    });

    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Fill in basic information
    fireEvent.change(screen.getByLabelText('Template Name *'), {
      target: { value: 'Test Template' }
    });
    
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Test description' }
    });
    
    // Navigate to areas and fix weight
    fireEvent.click(screen.getByText('Assessment Areas (1)'));
    
    const areaNameInput = screen.getByPlaceholderText('e.g., Communication Skills');
    fireEvent.change(areaNameInput, { target: { value: 'Communication' } });
    
    const weightInput = screen.getByDisplayValue('0.25');
    fireEvent.change(weightInput, { target: { value: '1' } });
    
    // Submit form
    fireEvent.click(screen.getByText('Create Template'));
    
    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/templates', expect.objectContaining({
        name: 'Test Template',
        description: 'Test description',
        areas: expect.arrayContaining([
          expect.objectContaining({
            name: 'Communication',
            weight: 1
          })
        ])
      }));
    });
  });

  it('shows preview with template summary', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Fill in basic information
    fireEvent.change(screen.getByLabelText('Template Name *'), {
      target: { value: 'Preview Test' }
    });
    
    // Navigate to preview
    fireEvent.click(screen.getByText('Preview'));
    
    expect(screen.getByText('Template Preview')).toBeInTheDocument();
    expect(screen.getByText('Preview Test')).toBeInTheDocument();
    expect(screen.getByText('Assessment Areas (1)')).toBeInTheDocument();
  });

  it('handles edit mode correctly', () => {
    const existingTemplate = {
      id: 1,
      name: 'Existing Template',
      description: 'Existing description',
      isGlobal: true,
      areas: [
        {
          id: 1,
          name: 'Communication',
          description: 'Communication skills',
          weight: 0.5,
          maxScore: 100,
          orderIndex: 1,
          scoringRules: []
        },
        {
          id: 2,
          name: 'Technical',
          description: 'Technical skills',
          weight: 0.5,
          maxScore: 100,
          orderIndex: 2,
          scoringRules: []
        }
      ]
    };

    renderWithRouter(
      <AdvancedTemplateForm {...defaultProps} template={existingTemplate} />
    );
    
    expect(screen.getByText('Edit Assessment Template')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Existing Template')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Existing description')).toBeInTheDocument();
    expect(screen.getByText('Assessment Areas (2)')).toBeInTheDocument();
  });

  it('calls onCancel when cancel button is clicked', () => {
    const onCancel = jest.fn();
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} onCancel={onCancel} />);
    
    fireEvent.click(screen.getByText('Cancel'));
    expect(onCancel).toHaveBeenCalled();
  });

  it('disables submit button when weights are invalid', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Fill in name
    fireEvent.change(screen.getByLabelText('Template Name *'), {
      target: { value: 'Test Template' }
    });
    
    // Navigate to areas and set invalid weight
    fireEvent.click(screen.getByText('Assessment Areas (1)'));
    
    const weightInput = screen.getByDisplayValue('0.25');
    fireEvent.change(weightInput, { target: { value: '0.5' } });
    
    // Submit button should be disabled
    const submitButton = screen.getByText('Create Template');
    expect(submitButton).toBeDisabled();
  });

  it('shows next/previous navigation buttons', () => {
    renderWithRouter(<AdvancedTemplateForm {...defaultProps} />);
    
    // Should show Next button on first tab
    expect(screen.getByText('Next')).toBeInTheDocument();
    
    // Navigate to second tab
    fireEvent.click(screen.getByText('Next'));
    
    // Should show both Previous and Next
    expect(screen.getByText('Previous')).toBeInTheDocument();
    expect(screen.getByText('Next')).toBeInTheDocument();
    
    // Navigate to last tab
    fireEvent.click(screen.getByText('Next'));
    
    // Should show Previous and Create Template
    expect(screen.getByText('Previous')).toBeInTheDocument();
    expect(screen.getByText('Create Template')).toBeInTheDocument();
  });
});
