export interface LoggingConfig {
  // File settings
  maxFileSize: number; // bytes
  maxFiles: number; // number of files to keep
  maxLogAge: number; // days
  maxTotalSize: number; // bytes
  
  // Compression settings
  compressionEnabled: boolean;
  compressionDelay: number; // days before compression
  
  // Log levels
  enabledLevels: string[];
  
  // Performance settings
  batchSize: number;
  flushInterval: number; // milliseconds
  
  // Cleanup schedule
  cleanupCron: string;
  
  // Security settings
  sanitizeUserData: boolean;
  maxMessageLength: number;
  maxDetailsSize: number;
}

export const defaultLoggingConfig: LoggingConfig = {
  // File settings - 10MB per file, keep 30 files, max 30 days, max 100MB total
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 30,
  maxLogAge: 30, // days
  maxTotalSize: 100 * 1024 * 1024, // 100MB
  
  // Compression settings
  compressionEnabled: true,
  compressionDelay: 1, // compress after 1 day
  
  // Log levels (production should exclude debug/trace)
  enabledLevels: process.env.NODE_ENV === 'production' 
    ? ['error', 'warn', 'info']
    : ['error', 'warn', 'info', 'debug', 'trace'],
  
  // Performance settings
  batchSize: 100,
  flushInterval: 5000, // 5 seconds
  
  // Cleanup schedule - every day at 2 AM
  cleanupCron: '0 2 * * *',
  
  // Security settings
  sanitizeUserData: true,
  maxMessageLength: 1000,
  maxDetailsSize: 10000, // 10KB
};

export const getLoggingConfig = (): LoggingConfig => {
  return {
    ...defaultLoggingConfig,
    // Override with environment variables if available
    maxFileSize: parseInt(process.env.LOG_MAX_FILE_SIZE || '') || defaultLoggingConfig.maxFileSize,
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '') || defaultLoggingConfig.maxFiles,
    maxLogAge: parseInt(process.env.LOG_MAX_AGE || '') || defaultLoggingConfig.maxLogAge,
    compressionEnabled: process.env.LOG_COMPRESSION_ENABLED !== 'false',
    enabledLevels: process.env.LOG_LEVELS?.split(',') || defaultLoggingConfig.enabledLevels,
  };
};
