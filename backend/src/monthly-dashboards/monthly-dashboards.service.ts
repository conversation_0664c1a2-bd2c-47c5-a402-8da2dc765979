import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { MonthlyDashboardSubmission, SubmissionStatus } from './entities/monthly-dashboard-submission.entity';
import { MonthlyDashboardKpi } from './entities/monthly-dashboard-kpi.entity';
import { MonthlyDashboardKpiValue } from './entities/monthly-dashboard-kpi-value.entity';
import { MonthlyDashboardTeamTarget } from './entities/monthly-dashboard-team-target.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';
import { User, UserRole } from '../users/entities/user.entity';
import { CreateMonthlyDashboardSubmissionDto } from './dto/create-monthly-dashboard-submission.dto';
import { UpdateMonthlyDashboardSubmissionDto } from './dto/update-monthly-dashboard-submission.dto';
import { MonthlyDashboardQueryDto, DashboardOverviewQueryDto } from './dto/monthly-dashboard-query.dto';
import { CreateKpiDto } from './dto/create-kpi.dto';
import { UpdateKpiDto } from './dto/update-kpi.dto';

@Injectable()
export class MonthlyDashboardsService {
  constructor(
    @InjectRepository(MonthlyDashboardSubmission)
    private submissionRepository: Repository<MonthlyDashboardSubmission>,

    @InjectRepository(MonthlyDashboardKpi)
    private kpiRepository: Repository<MonthlyDashboardKpi>,

    @InjectRepository(MonthlyDashboardKpiValue)
    private kpiValueRepository: Repository<MonthlyDashboardKpiValue>,

    @InjectRepository(MonthlyDashboardTeamTarget)
    private teamTargetRepository: Repository<MonthlyDashboardTeamTarget>,

    @InjectRepository(OrganizationalUnit)
    private organizationalUnitRepository: Repository<OrganizationalUnit>,

    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) { }

  // Get all active KPIs
  async getKpis(): Promise<MonthlyDashboardKpi[]> {
    return this.kpiRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC', name: 'ASC' }
    });
  }

  // Get team-specific targets for KPIs
  async getTeamTargets(organizationalUnitId: number): Promise<MonthlyDashboardTeamTarget[]> {
    const currentDate = new Date();
    return this.teamTargetRepository.find({
      where: {
        organizationalUnitId,
      },
      relations: ['kpi', 'organizationalUnit']
    });
  }

  // Create or update a dashboard submission
  async createSubmission(
    createDto: CreateMonthlyDashboardSubmissionDto,
    userId: number
  ): Promise<MonthlyDashboardSubmission> {
    // Check if submission already exists for this team/period
    const existingSubmission = await this.submissionRepository.findOne({
      where: {
        organizationalUnitId: createDto.organizationalUnitId,
        submissionMonth: createDto.submissionMonth,
        submissionYear: createDto.submissionYear
      }
    });

    if (existingSubmission) {
      throw new BadRequestException('Dashboard submission already exists for this period');
    }

    // Verify user has access to this organizational unit
    await this.verifyUserAccess(userId, createDto.organizationalUnitId);

    const submission = this.submissionRepository.create({
      ...createDto,
      submittedByUserId: userId,
      completionDate: createDto.completionDate ? new Date(createDto.completionDate) : new Date()
    });

    const savedSubmission = await this.submissionRepository.save(submission);

    // Create KPI values if provided
    if (createDto.kpiValues && createDto.kpiValues.length > 0) {
      await this.updateKpiValues(savedSubmission.id, createDto.kpiValues);
    }

    return this.getSubmissionById(savedSubmission.id);
  }

  // Update a dashboard submission
  async updateSubmission(
    id: number,
    updateDto: UpdateMonthlyDashboardSubmissionDto,
    userId: number
  ): Promise<MonthlyDashboardSubmission> {
    const submission = await this.submissionRepository.findOne({
      where: { id },
      relations: ['organizationalUnit', 'submittedByUser']
    });

    if (!submission) {
      throw new NotFoundException('Dashboard submission not found');
    }

    // Verify user has access
    await this.verifyUserAccess(userId, submission.organizationalUnitId);

    // Check if submission is editable
    if (!submission.isEditable()) {
      throw new ForbiddenException('This submission cannot be edited in its current status');
    }

    // Update submission
    Object.assign(submission, updateDto);

    if (updateDto.status === SubmissionStatus.APPROVED) {
      submission.approvedByUserId = userId;
      submission.approvedAt = new Date();
    }

    const updatedSubmission = await this.submissionRepository.save(submission);

    // Update KPI values if provided
    if (updateDto.kpiValues && updateDto.kpiValues.length > 0) {
      await this.updateKpiValues(id, updateDto.kpiValues);
    }

    return this.getSubmissionById(id);
  }

  // Get dashboard submissions with filtering
  async getSubmissions(query: MonthlyDashboardQueryDto, userId: number): Promise<{
    submissions: MonthlyDashboardSubmission[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { year, month, organizationalUnitId, status, page = 1, limit = 20 } = query;

    // Get user's accessible organizational units
    const accessibleUnits = await this.getUserAccessibleUnits(userId);

    const queryBuilder = this.submissionRepository.createQueryBuilder('submission')
      .leftJoinAndSelect('submission.organizationalUnit', 'orgUnit')
      .leftJoinAndSelect('submission.submittedByUser', 'submittedBy')
      .leftJoinAndSelect('submission.kpiValues', 'kpiValues')
      .leftJoinAndSelect('kpiValues.kpi', 'kpi');

    // Only add the WHERE clause if there are accessible units and user is not admin
    if (accessibleUnits.length > 0) {
      queryBuilder.where('submission.organizationalUnitId IN (:...accessibleUnits)', { accessibleUnits });
    }
    // If no accessible units but user is admin, don't restrict access

    if (year) {
      queryBuilder.andWhere('submission.submissionYear = :year', { year });
    }

    if (month) {
      queryBuilder.andWhere('submission.submissionMonth = :month', { month });
    }

    if (organizationalUnitId) {
      queryBuilder.andWhere('submission.organizationalUnitId = :organizationalUnitId', { organizationalUnitId });
    }

    if (status) {
      queryBuilder.andWhere('submission.status = :status', { status });
    }

    const total = await queryBuilder.getCount();

    const submissions = await queryBuilder
      .orderBy('submission.submissionYear', 'DESC')
      .addOrderBy('submission.submissionMonth', 'DESC')
      .addOrderBy('orgUnit.name', 'ASC')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return { submissions, total, page, limit };
  }

  // Get a specific submission by ID
  async getSubmissionById(id: number): Promise<MonthlyDashboardSubmission> {
    const submission = await this.submissionRepository.findOne({
      where: { id },
      relations: [
        'organizationalUnit',
        'submittedByUser',
        'approvedByUser',
        'kpiValues',
        'kpiValues.kpi'
      ]
    });

    if (!submission) {
      throw new NotFoundException('Dashboard submission not found');
    }

    return submission;
  }

  // Get dashboard overview for leadership
  async getDashboardOverview(query: DashboardOverviewQueryDto, userId: number): Promise<any> {
    const { year = new Date().getFullYear(), month, divisionId, view = 'team' } = query;

    // If month is provided, return the new dashboard summary format
    if (month) {
      return this.getDashboardSummary({ year, month });
    }

    // Get user's accessible organizational units
    const accessibleUnits = await this.getUserAccessibleUnits(userId);

    const queryBuilder = this.submissionRepository.createQueryBuilder('submission')
      .leftJoinAndSelect('submission.organizationalUnit', 'orgUnit')
      .leftJoinAndSelect('submission.kpiValues', 'kpiValues')
      .leftJoinAndSelect('kpiValues.kpi', 'kpi')
      .where('submission.submissionYear = :year', { year });

    // Only add the organizational unit filter if there are accessible units and user is not admin
    if (accessibleUnits.length > 0) {
      queryBuilder.andWhere('submission.organizationalUnitId IN (:...accessibleUnits)', { accessibleUnits });
    }
    // If no accessible units but user is admin, don't restrict access

    if (divisionId) {
      // Filter by division hierarchy
      const divisionUnits = await this.getOrganizationalUnitHierarchy(divisionId);
      queryBuilder.andWhere('submission.organizationalUnitId IN (:...divisionUnits)', { divisionUnits });
    }

    const submissions = await queryBuilder
      .orderBy('submission.submissionYear', 'DESC')
      .addOrderBy('submission.submissionMonth', 'DESC')
      .addOrderBy('orgUnit.name', 'ASC')
      .getMany();

    // Group and aggregate data based on view type
    return this.aggregateDashboardData(submissions, view);
  }

  // Get dashboard summary in the format similar to the Excel image
  async getDashboardSummary(query: { year: number; month: number }): Promise<any> {
    const { year, month } = query;

    // Get all active KPIs for headers
    const activeKpis = await this.kpiRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC', displayName: 'ASC' }
    });

    // Get all submissions for the specified period with their teams and KPI values
    const submissions = await this.submissionRepository.find({
      where: {
        submissionYear: year,
        submissionMonth: month,
        status: In([SubmissionStatus.SUBMITTED, SubmissionStatus.APPROVED])
      },
      relations: ['organizationalUnit', 'organizationalUnit.manager', 'kpiValues', 'kpiValues.kpi']
    });

    console.log(`Found ${submissions.length} submissions for ${month}/${year}`);

    // If no submissions, return sample data
    if (submissions.length === 0) {
      return this.generateSampleDashboardData(activeKpis, year, month);
    }

    // Group teams by division based on existing data structure
    const teamsByDivision = new Map<string, any[]>();

    // Process each submission to build the dashboard structure
    for (const submission of submissions) {
      const team = submission.organizationalUnit;

      // Determine division name - use a simplified approach
      let divisionName = 'Technology Division'; // Default division

      // Try to map team names to divisions based on patterns
      if (team.name.includes('2nd Line') || team.name.includes('AIS') || team.name.includes('Unix') || team.name.includes('Windows')) {
        divisionName = 'CHOI';
      } else if (team.name.includes('IAM') || team.name.includes('Security') || team.name.includes('Network')) {
        divisionName = 'THNQ';
      } else if (team.name.includes('ALDW') || team.name.includes('Automation')) {
        divisionName = 'ALDW';
      }

      // Calculate KPI values with traffic light status
      const kpiValues = activeKpis.map(kpi => {
        const kpiValue = submission.kpiValues.find(kv => kv.kpi.id === kpi.id);

        if (!kpiValue) {
          return {
            kpiId: kpi.id,
            kpiName: kpi.name,
            displayName: kpi.displayName,
            value: 0,
            targetValue: kpi.targetValue,
            variance: null,
            trafficLight: 'red' as const,
            unit: kpi.unit
          };
        }

        // Use the traffic light status from the database if available
        let trafficLight = kpiValue.trafficLightStatus || 'red';

        // Calculate variance if target value exists
        const variance = kpi.targetValue && kpiValue.value !== null ?
          ((kpiValue.value - kpi.targetValue) / kpi.targetValue) * 100 : null;

        return {
          kpiId: kpi.id,
          kpiName: kpi.name,
          displayName: kpi.displayName,
          value: kpiValue.value,
          targetValue: kpi.targetValue,
          variance,
          trafficLight,
          unit: kpi.unit
        };
      });

      const teamData = {
        teamId: team.id,
        teamName: team.name,
        managerName: team.manager ? `${team.manager.firstName} ${team.manager.lastName}` : 'N/A',
        lastUpdate: submission.updatedAt.toISOString().split('T')[0],
        kpiValues
      };

      // Add team to division
      if (!teamsByDivision.has(divisionName)) {
        teamsByDivision.set(divisionName, []);
      }
      teamsByDivision.get(divisionName).push(teamData);
    }

    // Convert map to divisions array
    const divisions = Array.from(teamsByDivision.entries()).map(([divisionName, teams]) => ({
      divisionId: divisionName.toLowerCase().replace(/\s+/g, '_'),
      divisionName,
      teams
    }));

    console.log(`Created ${divisions.length} divisions with teams:`, divisions.map(d => `${d.divisionName}: ${d.teams.length} teams`));

    return {
      month: String(month).padStart(2, '0'),
      year,
      divisions,
      kpiHeaders: activeKpis.map(kpi => ({
        id: kpi.id,
        name: kpi.name,
        displayName: kpi.displayName,
        unit: kpi.unit
      }))
    };
  }

  // Private helper methods
  private async updateKpiValues(submissionId: number, kpiValues: any[]): Promise<void> {
    // Delete existing KPI values
    await this.kpiValueRepository.delete({ submissionId });

    // Create and save new KPI values
    for (const kpiValue of kpiValues) {
      const newKpiValue = this.kpiValueRepository.create({
        submissionId,
        ...kpiValue
      });
      await this.kpiValueRepository.save(newKpiValue);
    }
  }

  private async verifyUserAccess(userId: number, organizationalUnitId: number): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user is admin, manager of the unit, or has explicit permission
    const accessibleUnits = await this.getUserAccessibleUnits(userId);

    if (!accessibleUnits.includes(organizationalUnitId)) {
      throw new ForbiddenException('You do not have access to this organizational unit');
    }
  }

  private async getUserAccessibleUnits(userId: number): Promise<number[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['organizationalUnit']
    });

    if (!user) {
      return [];
    }

    // For now, return units based on user role and organizational unit
    // This can be expanded with more complex permission logic
    const accessibleUnits = [user.organizationalUnit?.id].filter(Boolean);

    // If user is admin/director/CEO, give broader access
    if ([UserRole.CEO, UserRole.DIRECTOR, UserRole.HR_ADMIN].includes(user.role)) {
      const allUnits = await this.organizationalUnitRepository.find();
      return allUnits.map(unit => unit.id);
    }

    // If user is manager, include their team and sub-teams
    if (user.role === UserRole.MANAGER && user.organizationalUnit?.id) {
      const hierarchy = await this.getOrganizationalUnitHierarchy(user.organizationalUnit.id);
      accessibleUnits.push(...hierarchy);
    }

    return [...new Set(accessibleUnits)];
  }

  private async getOrganizationalUnitHierarchy(unitId: number): Promise<number[]> {
    if (!unitId) {
      return [];
    }
    // This would recursively get all child units
    // For now, return just the unit itself
    return [unitId];
  }

  private aggregateDashboardData(submissions: MonthlyDashboardSubmission[], view: string): any {
    // Aggregate submissions by organizational unit and calculate summary statistics
    const aggregated = submissions.reduce((acc, submission) => {
      const unitId = submission.organizationalUnit.id;
      const unitName = submission.organizationalUnit.name;

      if (!acc[unitId]) {
        acc[unitId] = {
          organizationalUnit: submission.organizationalUnit,
          submissions: [],
          overallStatus: 'na',
          kpiSummary: {}
        };
      }

      acc[unitId].submissions.push(submission);
      return acc;
    }, {});

    // Calculate overall status and KPI summaries for each unit
    Object.values(aggregated).forEach((unitData: any) => {
      unitData.overallStatus = this.calculateOverallStatus(unitData.submissions);
      unitData.kpiSummary = this.calculateKpiSummary(unitData.submissions);
    });

    return Object.values(aggregated);
  }

  private calculateOverallStatus(submissions: MonthlyDashboardSubmission[]): string {
    if (submissions.length === 0) return 'na';

    const statuses = submissions.map(s => s.getOverallTrafficLightStatus());

    if (statuses.includes('red')) return 'red';
    if (statuses.includes('yellow')) return 'yellow';
    if (statuses.includes('green')) return 'green';
    return 'na';
  }

  private calculateKpiSummary(submissions: MonthlyDashboardSubmission[]): any {
    const kpiSummary = {};

    submissions.forEach(submission => {
      submission.kpiValues?.forEach(kpiValue => {
        const kpiName = kpiValue.kpi.name;
        if (!kpiSummary[kpiName]) {
          kpiSummary[kpiName] = {
            values: [],
            averageValue: 0,
            averageTarget: 0,
            status: 'na'
          };
        }

        if (kpiValue.value !== null) {
          kpiSummary[kpiName].values.push(kpiValue.value);
        }
      });
    });

    // Calculate averages
    Object.keys(kpiSummary).forEach(kpiName => {
      const values = kpiSummary[kpiName].values;
      if (values.length > 0) {
        kpiSummary[kpiName].averageValue = values.reduce((a, b) => a + b, 0) / values.length;
      }
    });

    return kpiSummary;
  }

  // KPI Management Methods
  async createKpi(createKpiDto: CreateKpiDto): Promise<MonthlyDashboardKpi> {
    const kpi = this.kpiRepository.create({
      ...createKpiDto,
      isActive: createKpiDto.isActive ?? true,
      sortOrder: createKpiDto.sortOrder ?? 0,
      trafficLightGreenMin: createKpiDto.trafficLightGreenMin ?? -5,
      trafficLightGreenMax: createKpiDto.trafficLightGreenMax ?? 5,
      trafficLightYellowMin: createKpiDto.trafficLightYellowMin ?? -10,
      trafficLightYellowMax: createKpiDto.trafficLightYellowMax ?? 10,
    });

    return this.kpiRepository.save(kpi);
  }

  async getAllKpis(includeInactive: boolean = false): Promise<MonthlyDashboardKpi[]> {
    const queryBuilder = this.kpiRepository.createQueryBuilder('kpi');

    if (!includeInactive) {
      queryBuilder.where('kpi.isActive = :isActive', { isActive: true });
    }

    return queryBuilder
      .orderBy('kpi.sortOrder', 'ASC')
      .addOrderBy('kpi.displayName', 'ASC')
      .getMany();
  }

  async getKpiById(id: number): Promise<MonthlyDashboardKpi> {
    const kpi = await this.kpiRepository.findOne({ where: { id } });

    if (!kpi) {
      throw new NotFoundException(`KPI with ID ${id} not found`);
    }

    return kpi;
  }

  async updateKpi(id: number, updateKpiDto: UpdateKpiDto): Promise<MonthlyDashboardKpi> {
    const kpi = await this.getKpiById(id);

    Object.assign(kpi, updateKpiDto);

    return this.kpiRepository.save(kpi);
  }

  async toggleKpiStatus(id: number): Promise<MonthlyDashboardKpi> {
    const kpi = await this.getKpiById(id);

    kpi.isActive = !kpi.isActive;

    return this.kpiRepository.save(kpi);
  }

  // Generate sample dashboard data for demonstration
  private generateSampleDashboardData(activeKpis: any[], year: number, month: number) {
    const sampleDivisions = [
      {
        divisionId: 1,
        divisionName: 'CHOI',
        teams: [
          { teamId: 1, teamName: '2nd Line Global', managerName: 'John Smith' },
          { teamId: 2, teamName: 'Patch Planning with Server Order', managerName: 'Sarah Johnson' },
          { teamId: 3, teamName: 'Change Management - SLM', managerName: 'Mike Wilson' },
          { teamId: 4, teamName: 'Automation & AI', managerName: 'Lisa Chen' },
        ]
      },
      {
        divisionId: 2,
        divisionName: 'THNQ',
        teams: [
          { teamId: 5, teamName: 'IAM Operations', managerName: 'David Brown' },
          { teamId: 6, teamName: 'Security Operations', managerName: 'Emma Davis' },
          { teamId: 7, teamName: 'Network Operations & NetScaler', managerName: 'Tom Anderson' },
        ]
      },
      {
        divisionId: 3,
        divisionName: 'ALDW',
        teams: [
          { teamId: 8, teamName: 'AIS Operations', managerName: 'Alex Martinez' },
          { teamId: 9, teamName: 'Windows Services', managerName: 'Rachel Green' },
          { teamId: 10, teamName: 'Unix/Linux Services', managerName: 'Chris Taylor' },
        ]
      }
    ];

    const divisions = sampleDivisions.map(division => ({
      ...division,
      teams: division.teams.map(team => ({
        ...team,
        lastUpdate: new Date().toISOString().split('T')[0],
        kpiValues: activeKpis.map(kpi => {
          // Generate realistic sample values
          let value: number;
          let trafficLight: 'green' | 'yellow' | 'red';

          switch (kpi.name) {
            case 'FTE':
              value = Math.floor(Math.random() * 30) + 10;
              trafficLight = 'green';
              break;
            case 'ATTRITION':
              value = Math.floor(Math.random() * 15) + 2;
              trafficLight = value <= 5 ? 'green' : value <= 10 ? 'yellow' : 'red';
              break;
            case 'UTILIZATION':
              value = Math.floor(Math.random() * 30) + 70;
              trafficLight = value >= 85 ? 'green' : value >= 75 ? 'yellow' : 'red';
              break;
            case 'SLA':
              value = Math.floor(Math.random() * 20) + 80;
              trafficLight = value >= 95 ? 'green' : value >= 90 ? 'yellow' : 'red';
              break;
            default:
              value = Math.floor(Math.random() * 100) + 1;
              trafficLight = value >= 80 ? 'green' : value >= 60 ? 'yellow' : 'red';
          }

          return {
            kpiId: kpi.id,
            kpiName: kpi.name,
            value,
            targetValue: kpi.targetValue,
            variance: kpi.targetValue ? ((value - kpi.targetValue) / kpi.targetValue) * 100 : null,
            trafficLight,
            unit: kpi.unit
          };
        })
      }))
    }));

    return {
      month: String(month).padStart(2, '0'),
      year,
      divisions,
      kpiHeaders: activeKpis.map(kpi => ({
        id: kpi.id,
        name: kpi.name,
        displayName: kpi.displayName,
        unit: kpi.unit
      }))
    };
  }

  // =====================================================
  // HISTORICAL TREND ANALYSIS METHODS
  // =====================================================

  /**
   * Get KPI trends over time for historical analysis
   * @param kpiId - The KPI to analyze
   * @param organizationalUnitId - Optional team filter
   * @param months - Number of months to look back (default 12)
   * @param userId - User requesting the data (for access control)
   */
  async getKpiTrends(
    kpiId: number,
    organizationalUnitId?: number,
    months: number = 12,
    userId?: number
  ): Promise<any> {
    try {
      // Verify KPI exists
      const kpi = await this.kpiRepository.findOne({ where: { id: kpiId } });
      if (!kpi) {
        throw new NotFoundException(`KPI with ID ${kpiId} not found`);
      }

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      // Build query for KPI values over time
      const queryBuilder = this.kpiValueRepository.createQueryBuilder('kpiValue')
        .leftJoinAndSelect('kpiValue.submission', 'submission')
        .leftJoinAndSelect('submission.organizationalUnit', 'orgUnit')
        .leftJoinAndSelect('kpiValue.kpi', 'kpi')
        .where('kpiValue.kpiId = :kpiId', { kpiId })
        .andWhere('submission.status IN (:...statuses)', {
          statuses: [SubmissionStatus.SUBMITTED, SubmissionStatus.APPROVED]
        })
        .andWhere('submission.submissionYear >= :startYear', { startYear: startDate.getFullYear() })
        .andWhere(
          '(submission.submissionYear > :startYear OR ' +
          '(submission.submissionYear = :startYear AND submission.submissionMonth >= :startMonth))',
          { startYear: startDate.getFullYear(), startMonth: startDate.getMonth() + 1 }
        )
        .andWhere('submission.submissionYear <= :endYear', { endYear: endDate.getFullYear() })
        .andWhere(
          '(submission.submissionYear < :endYear OR ' +
          '(submission.submissionYear = :endYear AND submission.submissionMonth <= :endMonth))',
          { endYear: endDate.getFullYear(), endMonth: endDate.getMonth() + 1 }
        );

      // Filter by organizational unit if specified
      if (organizationalUnitId) {
        // Get user's accessible units for security
        if (userId) {
          const accessibleUnits = await this.getUserAccessibleUnits(userId);
          const hasAccess = accessibleUnits.includes(organizationalUnitId);
          if (!hasAccess) {
            throw new ForbiddenException('Access denied to this organizational unit');
          }
        }
        queryBuilder.andWhere('submission.organizationalUnitId = :organizationalUnitId', { organizationalUnitId });
      } else if (userId) {
        // Apply user access restrictions
        const accessibleUnits = await this.getUserAccessibleUnits(userId);
        if (accessibleUnits.length > 0) {
          queryBuilder.andWhere('submission.organizationalUnitId IN (:...unitIds)', { unitIds: accessibleUnits });
        }
      }

      queryBuilder.orderBy('submission.submissionYear', 'ASC')
        .addOrderBy('submission.submissionMonth', 'ASC')
        .addOrderBy('orgUnit.name', 'ASC');

      const kpiValues = await queryBuilder.getMany();

      // Process data for trend analysis
      const trendData = this.processTrendData(kpiValues, kpi, months, organizationalUnitId);

      return {
        kpiId,
        kpiName: kpi.name,
        kpiDisplayName: kpi.displayName,
        unit: kpi.unit,
        targetValue: kpi.targetValue,
        organizationalUnitId,
        months,
        periodStart: startDate.toISOString().split('T')[0],
        periodEnd: endDate.toISOString().split('T')[0],
        ...trendData
      };

    } catch (error) {
      console.error('Error getting KPI trends:', error);
      throw error;
    }
  }

  /**
   * Process raw KPI values into trend analysis data
   */
  private processTrendData(kpiValues: any[], kpi: any, months: number, organizationalUnitId?: number): any {
    // Group data by period (year-month)
    const periodMap = new Map<string, any[]>();

    kpiValues.forEach(kpiValue => {
      const periodKey = `${kpiValue.submission.submissionYear}-${String(kpiValue.submission.submissionMonth).padStart(2, '0')}`;
      if (!periodMap.has(periodKey)) {
        periodMap.set(periodKey, []);
      }
      periodMap.get(periodKey).push(kpiValue);
    });

    // Generate time series data
    const timeSeries = [];
    const teamTrends = new Map<string, any[]>();

    // Sort periods chronologically
    const sortedPeriods = Array.from(periodMap.keys()).sort();

    for (const period of sortedPeriods) {
      const periodValues = periodMap.get(period);
      const [year, month] = period.split('-').map(Number);

      // Calculate aggregate statistics for this period
      const values = periodValues.map(pv => pv.value).filter(v => v !== null);
      const targetValues = periodValues.map(pv => pv.targetValue || kpi.targetValue).filter(v => v !== null);

      if (values.length > 0) {
        const periodData = {
          period,
          year,
          month,
          monthName: new Date(year, month - 1).toLocaleString('default', { month: 'long' }),
          teamCount: periodValues.length,
          average: values.reduce((sum, val) => sum + val, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          median: this.calculateMedian(values),
          targetAverage: targetValues.length > 0 ? targetValues.reduce((sum, val) => sum + val, 0) / targetValues.length : kpi.targetValue,
          variance: targetValues.length > 0 ?
            ((values.reduce((sum, val) => sum + val, 0) / values.length) - (targetValues.reduce((sum, val) => sum + val, 0) / targetValues.length)) : null,
          trafficLightDistribution: this.calculateTrafficLightDistribution(periodValues)
        };

        timeSeries.push(periodData);

        // Track individual team trends if not filtering by specific team
        if (!organizationalUnitId) {
          periodValues.forEach(pv => {
            const teamKey = `${pv.submission.organizationalUnit.id}-${pv.submission.organizationalUnit.name}`;
            if (!teamTrends.has(teamKey)) {
              teamTrends.set(teamKey, []);
            }
            teamTrends.get(teamKey).push({
              period,
              year,
              month,
              value: pv.value,
              targetValue: pv.targetValue || kpi.targetValue,
              trafficLight: pv.trafficLightStatus,
              teamId: pv.submission.organizationalUnit.id,
              teamName: pv.submission.organizationalUnit.name
            });
          });
        }
      }
    }

    // Calculate overall trend metrics
    const overallMetrics = this.calculateOverallTrendMetrics(timeSeries);

    return {
      timeSeries,
      teamTrends: organizationalUnitId ? null : Object.fromEntries(teamTrends),
      overallMetrics,
      summary: {
        totalPeriods: timeSeries.length,
        averageTeamsPerPeriod: timeSeries.length > 0 ?
          timeSeries.reduce((sum, ts) => sum + ts.teamCount, 0) / timeSeries.length : 0,
        trendDirection: overallMetrics.trendDirection,
        performanceRating: overallMetrics.performanceRating
      }
    };
  }

  /**
   * Calculate median value from array of numbers
   */
  private calculateMedian(values: number[]): number {
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
  }

  /**
   * Calculate traffic light distribution for a period
   */
  private calculateTrafficLightDistribution(periodValues: any[]): any {
    const distribution = { green: 0, yellow: 0, red: 0, na: 0 };

    periodValues.forEach(pv => {
      const status = pv.trafficLightStatus || 'na';
      distribution[status] = (distribution[status] || 0) + 1;
    });

    const total = periodValues.length;
    return {
      counts: distribution,
      percentages: {
        green: total > 0 ? (distribution.green / total) * 100 : 0,
        yellow: total > 0 ? (distribution.yellow / total) * 100 : 0,
        red: total > 0 ? (distribution.red / total) * 100 : 0,
        na: total > 0 ? (distribution.na / total) * 100 : 0
      }
    };
  }

  /**
   * Calculate overall trend metrics and performance indicators
   */
  private calculateOverallTrendMetrics(timeSeries: any[]): any {
    if (timeSeries.length < 2) {
      return {
        trendDirection: 'insufficient_data',
        trendSlope: 0,
        performanceRating: 'unknown',
        volatility: 0,
        consistency: 0
      };
    }

    // Calculate trend slope using linear regression
    const n = timeSeries.length;
    const xValues = timeSeries.map((_, index) => index);
    const yValues = timeSeries.map(ts => ts.average);

    const sumX = xValues.reduce((sum, x) => sum + x, 0);
    const sumY = yValues.reduce((sum, y) => sum + y, 0);
    const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
    const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

    // Determine trend direction
    let trendDirection = 'stable';
    if (Math.abs(slope) > 0.1) {
      trendDirection = slope > 0 ? 'improving' : 'declining';
    }

    // Calculate volatility (standard deviation of values)
    const mean = sumY / n;
    const variance = yValues.reduce((sum, y) => sum + Math.pow(y - mean, 2), 0) / n;
    const volatility = Math.sqrt(variance);

    // Calculate consistency (percentage of periods meeting target)
    const periodsWithTarget = timeSeries.filter(ts => ts.targetAverage !== null && ts.average >= ts.targetAverage);
    const consistency = (periodsWithTarget.length / timeSeries.length) * 100;

    // Determine performance rating
    let performanceRating = 'average';
    if (consistency >= 80 && trendDirection === 'improving') {
      performanceRating = 'excellent';
    } else if (consistency >= 60 && trendDirection !== 'declining') {
      performanceRating = 'good';
    } else if (consistency < 40 || trendDirection === 'declining') {
      performanceRating = 'needs_improvement';
    }

    return {
      trendDirection,
      trendSlope: slope,
      performanceRating,
      volatility,
      consistency,
      averageValue: mean,
      latestValue: timeSeries[timeSeries.length - 1]?.average || 0,
      changeFromFirst: timeSeries.length > 1 ?
        ((timeSeries[timeSeries.length - 1].average - timeSeries[0].average) / timeSeries[0].average) * 100 : 0
    };
  }
}
