import React from 'react';
import { Chip, ChipProps } from '@mui/material';

interface BadgeProps extends Omit<ChipProps, 'variant' | 'children'> {
  variant?: 'default' | 'secondary' | 'outline';
  children: React.ReactNode;
}

export const Badge: React.FC<BadgeProps> = ({ variant = 'default', children, ...props }) => {
  const getVariant = () => {
    switch (variant) {
      case 'outline':
        return 'outlined';
      default:
        return 'filled';
    }
  };

  const getColor = () => {
    switch (variant) {
      case 'secondary':
        return 'secondary';
      default:
        return 'primary';
    }
  };

  return (
    <Chip
      variant={getVariant() as any}
      color={getColor() as any}
      size="small"
      label={children}
      {...props}
    />
  );
};
